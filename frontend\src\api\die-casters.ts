import { get, post, put, del } from '@/utils/api'
import type { DieCaster } from '@/types'

/**
 * 获取所有压铸机
 */
export const getDieCasters = (): Promise<DieCaster[]> => {
  return get<DieCaster[]>('/die-casters/')
}

/**
 * 获取特定压铸机
 */
export const getDieCaster = (id: number): Promise<DieCaster> => {
  return get<DieCaster>(`/die-casters/${id}/`)
}

/**
 * 创建新压铸机
 */
export const createDieCaster = (data: Partial<DieCaster>): Promise<DieCaster> => {
  return post<DieCaster>('/die-casters/', data)
}

/**
 * 更新压铸机
 */
export const updateDieCaster = (id: number, data: Partial<DieCaster>): Promise<DieCaster> => {
  return put<DieCaster>(`/die-casters/${id}/`, data)
}

/**
 * 删除压铸机
 */
export const deleteDieCaster = (id: number): Promise<DieCaster> => {
  return del<DieCaster>(`/die-casters/${id}/`)
} 
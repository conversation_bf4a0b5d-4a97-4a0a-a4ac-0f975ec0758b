"""
系统日志数据库服务
"""

from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_, func
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import logging

from app.models.models import SystemLog
from app.db.session import get_db

logger = logging.getLogger(__name__)

class SystemLogService:
    """系统日志数据库服务"""
    
    @staticmethod
    def create_log(db: Session, log_record) -> SystemLog:
        """创建日志记录"""
        try:
            log_entry = SystemLog.from_log_record(log_record)
            db.add(log_entry)
            db.commit()
            db.refresh(log_entry)
            return log_entry
        except Exception as e:
            logger.error(f"创建日志记录失败: {e}")
            try:
                db.rollback()
            except Exception as rollback_error:
                logger.error(f"回滚失败: {rollback_error}")
            raise
    
    @staticmethod
    def get_logs(
        db: Session,
        level: Optional[str] = None,
        module: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0
    ) -> tuple[List[SystemLog], int]:
        """获取日志列表"""
        try:
            query = db.query(SystemLog)
            
            # 构建过滤条件
            filters = []
            
            if level:
                filters.append(SystemLog.level == level.lower())
            
            if module:
                filters.append(SystemLog.module.contains(module))
            
            if start_time:
                filters.append(SystemLog.timestamp >= start_time)
            
            if end_time:
                filters.append(SystemLog.timestamp <= end_time)
            
            if filters:
                query = query.filter(and_(*filters))
            
            # 获取总数
            total = query.count()
            
            # 分页和排序
            logs = query.order_by(desc(SystemLog.timestamp)).offset(offset).limit(limit).all()
            
            return logs, total
            
        except Exception as e:
            logger.error(f"获取日志列表失败: {e}")
            raise
    
    @staticmethod
    def get_log_levels(db: Session) -> Dict[str, int]:
        """获取日志级别统计"""
        try:
            result = db.query(
                SystemLog.level,
                func.count(SystemLog.id).label('count')
            ).group_by(SystemLog.level).all()
            
            return {level: count for level, count in result}
            
        except Exception as e:
            logger.error(f"获取日志级别统计失败: {e}")
            raise
    
    @staticmethod
    def get_log_modules(db: Session) -> List[str]:
        """获取模块列表"""
        try:
            result = db.query(SystemLog.module).distinct().all()
            return [module[0] for module in result]
            
        except Exception as e:
            logger.error(f"获取模块列表失败: {e}")
            raise
    
    @staticmethod
    def get_log_statistics(db: Session) -> Dict[str, Any]:
        """获取日志统计信息"""
        try:
            now = datetime.now()
            last_24h = now - timedelta(hours=24)
            
            # 总日志数
            total_logs = db.query(func.count(SystemLog.id)).scalar()
            
            # 最近24小时日志数
            recent_24h = db.query(func.count(SystemLog.id)).filter(
                SystemLog.timestamp >= last_24h
            ).scalar()
            
            # 按级别统计最近24小时
            level_stats = db.query(
                SystemLog.level,
                func.count(SystemLog.id).label('count')
            ).filter(
                SystemLog.timestamp >= last_24h
            ).group_by(SystemLog.level).all()
            
            level_distribution = {level: count for level, count in level_stats}
            
            # 按小时统计最近24小时
            hourly_stats = db.query(
                func.strftime('%H:00', SystemLog.timestamp).label('hour'),
                func.count(SystemLog.id).label('count')
            ).filter(
                SystemLog.timestamp >= last_24h
            ).group_by(func.strftime('%H:00', SystemLog.timestamp)).all()
            
            hourly_distribution = {hour: count for hour, count in hourly_stats}
            
            return {
                'total_logs': total_logs or 0,
                'recent_24h': recent_24h or 0,
                'level_distribution': level_distribution,
                'hourly_distribution': hourly_distribution
            }
            
        except Exception as e:
            logger.error(f"获取日志统计信息失败: {e}")
            raise
    
    @staticmethod
    def clear_logs(db: Session, before_date: Optional[datetime] = None) -> int:
        """清空日志"""
        try:
            query = db.query(SystemLog)
            
            if before_date:
                query = query.filter(SystemLog.timestamp < before_date)
            
            count = query.count()
            query.delete()
            db.commit()
            
            logger.info(f"已清空 {count} 条日志记录")
            return count
            
        except Exception as e:
            logger.error(f"清空日志失败: {e}")
            db.rollback()
            raise
    
    @staticmethod
    def cleanup_old_logs(db: Session, days: int = 30) -> int:
        """清理旧日志（保留指定天数）"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            return SystemLogService.clear_logs(db, cutoff_date)
            
        except Exception as e:
            logger.error(f"清理旧日志失败: {e}")
            raise
    
    @staticmethod
    def get_log_by_id(db: Session, log_id: int) -> Optional[SystemLog]:
        """根据ID获取日志"""
        try:
            return db.query(SystemLog).filter(SystemLog.id == log_id).first()
            
        except Exception as e:
            logger.error(f"获取日志失败: {e}")
            raise
    
    @staticmethod
    def search_logs(
        db: Session,
        search_term: str,
        limit: int = 100,
        offset: int = 0
    ) -> tuple[List[SystemLog], int]:
        """搜索日志"""
        try:
            query = db.query(SystemLog).filter(
                or_(
                    SystemLog.message.contains(search_term),
                    SystemLog.module.contains(search_term),
                    SystemLog.filename.contains(search_term)
                )
            )
            
            total = query.count()
            logs = query.order_by(desc(SystemLog.timestamp)).offset(offset).limit(limit).all()
            
            return logs, total
            
        except Exception as e:
            logger.error(f"搜索日志失败: {e}")
            raise

# 项目算法文件分析报告
经过仔细分析当前项目，我已找到并整理了您要求的三个核心算法文件。以下是详细汇报：

## 1. 运动检测算法
主要文件位置： `algorithms.py`

核心算法类：

- `MotionDetector` ：基于背景减除法的运动检测
- `FrameDifferencer` ：基于帧差法的运动检测
算法特点：

- 支持两种运动检测算法：背景减除法（MOG2）和帧差法
- 集成GPU加速处理，提升检测性能
- 支持ROI区域检测，可处理矩形和多边形区域
- 可配置参数包括：检测阈值、最小检测面积、学习率等
## 2. 方向检测算法
主要文件位置： `algorithms.py`

核心算法类：

- `DirectionDetector` ：检测物体运动方向（向上/向下/静止）
算法特点：

- 通过跟踪运动中心点的垂直位移来判断方向
- 内置状态机，支持连续检测和惯性判断
- 可检测三种状态：'STATIONARY'（静止）、'MOVING_UP'（向上）、'MOVING_DOWN'（向下）
- 支持触发条件判断，当连续检测到向下运动达到阈值时触发
- 可配置参数：连续检测阈值、最小位移、最大耐心值等
## 3. 卡料检测算法
主要文件位置： `detection_manager.py`

核心算法类：

- `DetectionManager` ：卡料检测的主控制器
算法逻辑：

1. 初始状态（IDLE） ：持续监控压铸机（yazhu）ROI的运动
2. 触发检测 ：当检测到压铸机向下运动时，进入监测状态
3. 延时监测（MONITORING） ：开始卡料延时计时，同时监测所有排料口（pailiao）ROI
4. 结果判定 ：如果在延时期间某个排料口未检测到运动，则判定为卡料
5. 冷却状态（COOLDOWN） ：检测完成后进入冷却期，避免重复报警
算法特点：

- 多ROI协同检测：结合压铸机ROI和排料口ROI
- 状态机管理：IDLE → MONITORING → COOLDOWN 的完整检测流程
- 支持设备暂停检测：当设备长时间无运动时自动暂停检测
- 可配置参数：卡料延时时间、设备暂停阈值、冷却时间等
- 实时报警：检测到卡料时立即发送报警信号和截图
## 算法文件架构
基础架构：

- `BaseProcessor` ：所有检测算法的基类，提供GPU加速和ROI处理功能
- `roi.py` ：ROI区域定义和管理
- GPU加速支持：集成CUDA加速处理，提升算法性能
配置管理：

- 支持JSON配置文件加载算法参数
- 动态参数调整，无需重启系统
- 默认参数设置，确保算法稳定运行
这三个算法协同工作，构成了完整的工业产线卡料检测系统，能够实现7x24小时无人值守监测。



## GPU加速器文件说明
`gpu_accelerator.py` 是一个GPU加速器模块，主要功能包括：

### 核心功能
1. GPU检测与管理
   
   - 检测系统中支持CUDA的GPU设备
   - 获取GPU设备信息（设备ID、版本、内存等）
   - 管理GPU设备的选择和切换
2. 智能调度策略
   
   - 基于图像大小和算法类型智能选择CPU或GPU处理
   - 设置不同的性能阈值来优化处理效率
   - 提供安全的GPU内存管理机制
3. GPU加速的图像处理算法
   
   - 图像缩放 ( resize_gpu ): 大图像优先使用GPU处理
   - 双边滤波 ( bilateral_filter_gpu ): 强烈推荐GPU，可获得26倍加速
   - 颜色转换 ( cvt_color_gpu ): 支持GPU加速的颜色空间转换
   - 图像差分 ( abs_diff_gpu ): GPU加速的图像差分运算
   - 阈值处理 ( threshold_gpu ): GPU加速的图像阈值化
   - 形态学运算 ( morphology_gpu ): 为避免内存问题，目前使用CPU处理
   - 高斯模糊 ( gaussian_blur_gpu ): 为确保算法一致性，目前使用CPU处理
### 设计特点
- 安全优先 : 针对GPU内存问题采用保守策略，确保系统稳定性
- 自动回退 : GPU处理失败时自动回退到CPU处理
- 性能优化 : 根据实际测试结果调整GPU使用策略
- 全局单例 : 提供 get_gpu_accelerator() 函数获取全局实例
### 在项目中的作用
这个模块为整个压铸件智能检测系统提供GPU加速支持，特别是在运动检测、方向检测和卡料检测算法中的图像处理环节，能够显著提升处理性能，尤其是在处理高分辨率视频流时。
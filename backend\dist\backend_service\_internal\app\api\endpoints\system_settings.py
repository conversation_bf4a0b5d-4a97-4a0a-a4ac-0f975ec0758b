from typing import Any, List, Dict

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.deps import get_current_active_user
from app.db.session import get_db
from app.models.models import User, SystemSettings
from app.schemas.system_settings import (
    SystemSettings as SystemSettingsSchema,
    SystemSettingsCreate,
    SystemSettingsUpdate,
    BasicSettingsRequest,
    StorageSettingsRequest,
    BasicSettingsResponse,
    StorageSettingsResponse
)

router = APIRouter()


# 获取基本设置
@router.get("/basic", response_model=BasicSettingsResponse)
def get_basic_settings(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取基本设置
    """
    settings = {}
    basic_keys = ["system_name", "system_description", "admin_email", "log_retention_days"]
    
    for key in basic_keys:
        setting = db.query(SystemSettings).filter(SystemSettings.setting_key == key).first()
        if setting:
            if setting.setting_type == "integer":
                settings[key] = int(setting.setting_value)
            elif setting.setting_type == "boolean":
                settings[key] = setting.setting_value.lower() == "true"
            else:
                settings[key] = setting.setting_value
        else:
            # 默认值
            default_values = {
                "system_name": "压铸件智能检测系统",
                "system_description": "用于压铸件生产过程的智能监控、检测、报警和管理的系统",
                "admin_email": "<EMAIL>",
                "log_retention_days": 30
            }
            settings[key] = default_values.get(key, "")
    
    return BasicSettingsResponse(**settings)


# 更新基本设置
@router.put("/basic", response_model=BasicSettingsResponse)
def update_basic_settings(
    *,
    db: Session = Depends(get_db),
    settings_in: BasicSettingsRequest,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    更新基本设置
    """
    settings_data = settings_in.dict(exclude_unset=True)
    
    for key, value in settings_data.items():
        setting = db.query(SystemSettings).filter(SystemSettings.setting_key == key).first()
        
        if setting:
            setting.setting_value = str(value)
        else:
            # 创建新设置项
            setting_type = "integer" if isinstance(value, int) else "string"
            new_setting = SystemSettings(
                setting_key=key,
                setting_value=str(value),
                setting_type=setting_type,
                category="basic",
                description=f"基本设置 - {key}"
            )
            db.add(new_setting)
    
    db.commit()
    
    # 返回更新后的设置
    return get_basic_settings(db=db, current_user=current_user)


# 获取存储设置
@router.get("/storage", response_model=StorageSettingsResponse)
def get_storage_settings(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取存储设置
    """
    settings = {}
    storage_keys = ["video_storage_path", "detection_image_path", "auto_cleanup", "file_retention_days", "disk_space_threshold"]
    
    for key in storage_keys:
        setting = db.query(SystemSettings).filter(SystemSettings.setting_key == key).first()
        if setting:
            if setting.setting_type == "integer":
                settings[key] = int(setting.setting_value)
            elif setting.setting_type == "boolean":
                settings[key] = setting.setting_value.lower() == "true"
            else:
                settings[key] = setting.setting_value
        else:
            # 默认值
            default_values = {
                "video_storage_path": "/data/videos",
                "detection_image_path": "/data/detection_images",
                "auto_cleanup": True,
                "file_retention_days": 90,
                "disk_space_threshold": 80
            }
            settings[key] = default_values.get(key, "")
    
    return StorageSettingsResponse(**settings)


# 更新存储设置
@router.put("/storage", response_model=StorageSettingsResponse)
def update_storage_settings(
    *,
    db: Session = Depends(get_db),
    settings_in: StorageSettingsRequest,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    更新存储设置
    """
    settings_data = settings_in.dict(exclude_unset=True)
    
    for key, value in settings_data.items():
        setting = db.query(SystemSettings).filter(SystemSettings.setting_key == key).first()
        
        if setting:
            setting.setting_value = str(value)
        else:
            # 创建新设置项
            if isinstance(value, int):
                setting_type = "integer"
            elif isinstance(value, bool):
                setting_type = "boolean"
            else:
                setting_type = "string"
                
            new_setting = SystemSettings(
                setting_key=key,
                setting_value=str(value),
                setting_type=setting_type,
                category="storage",
                description=f"存储设置 - {key}"
            )
            db.add(new_setting)
    
    db.commit()
    
    # 返回更新后的设置
    return get_storage_settings(db=db, current_user=current_user)


# 获取所有设置
@router.get("/", response_model=List[SystemSettingsSchema])
def get_all_settings(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取所有系统设置
    """
    settings = db.query(SystemSettings).offset(skip).limit(limit).all()
    return settings


# 创建或更新单个设置
@router.post("/", response_model=SystemSettingsSchema)
def create_or_update_setting(
    *,
    db: Session = Depends(get_db),
    setting_in: SystemSettingsCreate,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    创建或更新单个设置项
    """
    setting = db.query(SystemSettings).filter(SystemSettings.setting_key == setting_in.setting_key).first()
    
    if setting:
        # 更新现有设置
        setting.setting_value = setting_in.setting_value
        if setting_in.setting_type:
            setting.setting_type = setting_in.setting_type
        if setting_in.description:
            setting.description = setting_in.description
        if setting_in.category:
            setting.category = setting_in.category
    else:
        # 创建新设置
        setting = SystemSettings(
            setting_key=setting_in.setting_key,
            setting_value=setting_in.setting_value,
            setting_type=setting_in.setting_type or "string",
            description=setting_in.description,
            category=setting_in.category or "basic"
        )
        db.add(setting)
    
    db.commit()
    db.refresh(setting)
    return setting
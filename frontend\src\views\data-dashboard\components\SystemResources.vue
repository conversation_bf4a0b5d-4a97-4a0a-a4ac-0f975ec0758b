<template>
  <div class="dashboard-card system-resources-card">
    <div class="card-header" @click="toggle" style="cursor: pointer;">
      <h3 class="card-title">
        <el-icon><Platform /></el-icon>
        系统资源
      </h3>
      <el-button type="primary" link>
        {{ isCollapsed ? '展开' : '收起' }}
        <el-icon>
          <component :is="isCollapsed ? ArrowDownBold : ArrowUpBold" />
        </el-icon>
      </el-button>
    </div>
    <el-collapse-transition>
      <div v-show="!isCollapsed" class="card-content">
        <div v-if="!performance">
          <el-empty description="暂无性能数据" />
        </div>
        <template v-else>
          <div v-for="resource in resources" :key="resource.name" class="resource-item">
            <span class="resource-name">{{ resource.name }}</span>
            <el-progress :percentage="resource.usage" :color="resource.color" class="resource-progress" />
            <span class="resource-value">{{ resource.value }}</span>
          </div>
        </template>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { PropType } from 'vue'
import { Platform, ArrowDownBold, ArrowUpBold } from '@element-plus/icons-vue'
import type { SystemPerformance } from '../types/dashboard.types'

const props = defineProps({
  performance: {
    type: Object as PropType<SystemPerformance | null>,
    required: true
  }
})

const isCollapsed = ref(true)

const getUsageColor = (usage: number) => {
  if (usage > 90) return '#f56c6c' // danger
  if (usage > 70) return '#e6a23c' // warning
  return '#67c23a' // success
}

const resources = computed(() => {
  if (!props.performance) return []
  return [
    {
      name: 'CPU',
      usage: Math.round(props.performance.cpu_usage),
      value: `${Math.round(props.performance.cpu_usage)}%`,
      color: getUsageColor(props.performance.cpu_usage)
    },
    {
      name: '内存',
      usage: Math.round(props.performance.memory_usage),
      value: `${Math.round(props.performance.memory_usage)}%`,
      color: getUsageColor(props.performance.memory_usage)
    },
    {
      name: '磁盘',
      usage: Math.round(props.performance.disk_usage),
      value: `${Math.round(props.performance.disk_usage)}%`,
      color: getUsageColor(props.performance.disk_usage)
    }
  ]
})

const toggle = () => {
  isCollapsed.value = !isCollapsed.value
}
</script>

<style scoped>
.system-resources-card {
  grid-column: 1 / -1;
  grid-row: 5;
}

.resource-item {
  display: grid;
  grid-template-columns: 60px 1fr 60px;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.resource-name {
  font-weight: 500;
}

.resource-value {
  text-align: right;
  font-weight: 500;
}

.card-content {
  padding-top: 16px;
}
</style> 
import { post, get } from './request'
import type { User } from '@/api/users'

interface LoginRequest {
  username: string
  password: string
}

interface TokenResponse {
  access_token: string
  token_type: string
}

/**
 * 用户登录
 */
export const login = async (data: LoginRequest): Promise<TokenResponse> => {
  const response = await post<TokenResponse>('/auth/login/json/', data)
  return response.data
}

/**
 * 获取当前用户信息
 */
export const getCurrentUser = async (): Promise<User> => {
  const response = await get<User>('/auth/me/')
  return response.data
}

/**
 * 退出登录
 */
export const logout = async () => {
  localStorage.removeItem('token')
  const response = await post<void>('/auth/logout/')
  return response.data
}
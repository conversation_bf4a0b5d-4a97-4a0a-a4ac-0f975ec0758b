# -*- mode: python ; coding: utf-8 -*-
# 简化版压铸检测系统后端打包配置

import os
from pathlib import Path

# 获取当前目录
current_dir = Path.cwd()

a = Analysis(
    ['app_launcher_standalone.py'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=[
        # 应用核心文件
        ('app', 'app'),
        
        # 数据库迁移文件
        ('alembic', 'alembic'),
        ('alembic.ini', '.'),
        
        # 静态文件和上传目录（如果存在）
        ('static', 'static'),
        ('uploads', 'uploads'),
        ('detection_images', 'detection_images'),
    ],
    hiddenimports=[
        # 核心依赖
        'fastapi',
        'fastapi.applications',
        'fastapi.routing',
        'fastapi.responses',
        'fastapi.exceptions',
        'fastapi.dependencies',
        'fastapi.security',
        'fastapi.middleware',
    'fastapi.middleware.cors',
    'fastapi.middleware.trustedhost',
    'fastapi.middleware.gzip',
    'fastapi.staticfiles',
    'pydantic_settings',
    'passlib',
    'passlib.handlers',
    'passlib.handlers.bcrypt',
    'uvicorn',
    'sqlalchemy',
    'sqlalchemy.ext',
    'sqlalchemy.ext.declarative',
    'PIL',
    'PIL.Image',
    'alembic',
        'pydantic',
        'starlette',
        
        # 认证
        'passlib.context',
        'jose.jwt',
        
        # 文件处理
        'python_multipart',
        'cv2',
        'PIL',
        
        # WebSocket
        'websockets',
        
        # 其他
        'psutil',
        'python_dotenv',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'scipy',
        'pandas',
        'jupyter',
        'IPython',
        'notebook',
    ],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='backend_service',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='backend_service'
)
import type { 
  WebSocketMessage, 
  FrameMessage, 
  DetectionResultMessage, 
  ConfigUpdateMessage 
} from '@/types'

export class VideoDetectionWebSocket {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private clientId: string
  private isConnecting = false
  private messageQueue: any[] = []
  
  // 事件回调
  private onFrameCallback?: (frame: FrameMessage) => void
  private onDetectionResultCallback?: (result: DetectionResultMessage) => void
  private onConnectionStatusCallback?: (status: 'connecting' | 'connected' | 'disconnected' | 'error') => void
  private onErrorCallback?: (error: string) => void
  
  constructor(clientId?: string) {
    this.clientId = clientId || this.generateClientId()
  }
  
  private generateClientId(): string {
    return 'client_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now()
  }
  
  // 连接WebSocket
  async connect(): Promise<boolean> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return true
    }
    
    this.isConnecting = true
    this.onConnectionStatusCallback?.('connecting')
    
    try {
      // 动态获取WebSocket URL
      const hostname = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' 
        ? 'localhost' 
        : window.location.hostname
      const wsUrl = `ws://${hostname}:8000/ws/video-detection/${this.clientId}`
      console.log('Connecting to WebSocket:', wsUrl)
      this.ws = new WebSocket(wsUrl)
      
      return new Promise((resolve, reject) => {
        if (!this.ws) {
          reject(new Error('Failed to create WebSocket'))
          return
        }
        
        this.ws.onopen = () => {
          console.log('WebSocket connected')
          this.isConnecting = false
          this.reconnectAttempts = 0
          this.onConnectionStatusCallback?.('connected')
          
          // 发送队列中的消息
          this.flushMessageQueue()
          
          resolve(true)
        }
        
        this.ws.onmessage = (event) => {
          this.handleMessage(event.data)
        }
        
        this.ws.onclose = (event) => {
          console.log('WebSocket closed:', event.code, event.reason)
          this.isConnecting = false
          this.onConnectionStatusCallback?.('disconnected')
          
          // 自动重连
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect()
          }
        }
        
        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error)
          this.isConnecting = false
          this.onConnectionStatusCallback?.('error')
          this.onErrorCallback?.('WebSocket connection error')
          reject(error)
        }
        
        // 连接超时
        setTimeout(() => {
          if (this.isConnecting) {
            this.isConnecting = false
            reject(new Error('Connection timeout'))
          }
        }, 10000)
      })
    } catch (error) {
      this.isConnecting = false
      this.onConnectionStatusCallback?.('error')
      console.error('Failed to connect WebSocket:', error)
      return false
    }
  }
  
  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
    this.reconnectAttempts = this.maxReconnectAttempts // 阻止重连
  }
  
  // 发送消息
  private sendMessage(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      // 连接未就绪，加入队列
      this.messageQueue.push(message)
    }
  }
  
  // 发送队列中的消息
  private flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      this.sendMessage(message)
    }
  }
  
  // 处理接收到的消息
  private handleMessage(data: string) {
    try {
      const message: WebSocketMessage = JSON.parse(data)
      
      switch (message.type) {
        case 'connection_established':
          console.log('Connection established:', message)
          break
          
        case 'frame':
          this.onFrameCallback?.(message as FrameMessage)
          break
          
        case 'detection_result':
          this.onDetectionResultCallback?.(message as DetectionResultMessage)
          break
          
        case 'detection_started':
          console.log('Detection started:', message)
          break
          
        case 'detection_stopped':
          console.log('Detection stopped:', message)
          break
          
        case 'config_updated':
          console.log('Config updated:', message)
          break
          
        case 'error':
          console.error('Server error:', message.message)
          this.onErrorCallback?.(message.message || 'Unknown server error')
          break
          
        case 'detection_error':
          console.error('Detection error:', message.message)
          this.onErrorCallback?.(message.message || 'Detection error')
          break
          
        case 'pong':
          // 心跳响应
          break
          
        default:
          console.warn('Unknown message type:', message.type)
      }
    } catch (error) {
      console.error('Failed to parse message:', error)
    }
  }
  
  // 安排重连
  private scheduleReconnect() {
    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`)
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnect failed:', error)
      })
    }, delay)
  }
  
  // 启动检测
  startDetection(videoSourceId: number) {
    this.sendMessage({
      type: 'start_detection',
      video_source_id: videoSourceId
    })
  }
  
  // 停止检测
  stopDetection() {
    this.sendMessage({
      type: 'stop_detection'
    })
  }
  
  // 更新配置
  updateConfig(config: any) {
    this.sendMessage({
      type: 'update_config',
      config
    })
  }
  
  // 发送心跳
  ping() {
    this.sendMessage({
      type: 'ping',
      timestamp: new Date().toISOString()
    })
  }
  
  // 发送消息(公共方法)
  sendFrame(data: any) {
    this.sendMessage({
      type: 'frame',
      ...data
    })
  }
  
  // 设置事件回调
  onFrame(callback: (frame: FrameMessage) => void) {
    this.onFrameCallback = callback
  }
  
  onDetectionResult(callback: (result: DetectionResultMessage) => void) {
    this.onDetectionResultCallback = callback
  }
  
  onConnectionStatus(callback: (status: 'connecting' | 'connected' | 'disconnected' | 'error') => void) {
    this.onConnectionStatusCallback = callback
  }
  
  onError(callback: (error: string) => void) {
    this.onErrorCallback = callback
  }
  
  // 获取连接状态
  getConnectionState(): string {
    if (!this.ws) return 'disconnected'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting'
      case WebSocket.OPEN:
        return 'connected'
      case WebSocket.CLOSING:
        return 'disconnecting'
      case WebSocket.CLOSED:
        return 'disconnected'
      default:
        return 'unknown'
    }
  }
  
  // 检查是否已连接
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }
}

// 创建全局WebSocket实例
export const videoDetectionWS = new VideoDetectionWebSocket()
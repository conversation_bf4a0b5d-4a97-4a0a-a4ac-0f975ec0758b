<template>
  <div class="algorithm-config-section">
    <h4>算法配置参数</h4>
    
    <div class="config-content">
      <div class="config-group">
        <h5>检测模式</h5>
        <div class="config-item">
          <label>
            <input type="radio" name="detection-mode" value="static" checked> 
            静态检测模式
          </label>
          <label>
            <input type="radio" name="detection-mode" value="custom" disabled> 
            自定义算法 <span class="badge">开发中</span>
          </label>
        </div>
      </div>
      
      <div class="config-group">
        <h5>检测参数</h5>
        <div class="config-item">
          <label>检测灵敏度</label>
          <div class="slider-container">
            <input type="range" min="1" max="10" value="5" class="slider">
            <span>5</span>
          </div>
        </div>
        <div class="config-item">
          <label>检测阈值</label>
          <div class="slider-container">
            <input type="range" min="1" max="100" value="50" class="slider">
            <span>50%</span>
          </div>
        </div>
      </div>
      
      <div class="config-group">
        <h5>报警设置</h5>
        <div class="config-item">
          <label>
            <input type="checkbox" checked> 
            启用声音报警
          </label>
        </div>
        <div class="config-item">
          <label>
            <input type="checkbox" checked> 
            启用视觉报警
          </label>
        </div>
      </div>
      
      <div class="config-actions">
        <button class="btn-apply">应用设置</button>
        <button class="btn-reset">重置默认</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 该组件用于算法配置界面
// 目前为静态展示，未实现实际功能
</script>


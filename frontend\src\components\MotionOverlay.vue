<template>
  <div class="motion-overlay">
    <!-- 运动检测结果叠加画布 -->
    <canvas
      ref="overlayCanvas"
      class="overlay-canvas"
      :width="videoWidth"
      :height="videoHeight"
    ></canvas>
    
    <!-- 检测状态显示 -->
    <div class="detection-status" v-if="false">
      <div class="status-item">
        <span class="status-label">运动检测:</span>
        <span :class="['status-value', motionDetected ? 'active' : 'inactive']">
          {{ motionDetected ? '检测到运动' : '无运动' }}
        </span>
      </div>
      
      <div class="status-item" v-if="direction">
        <span class="status-label">运动方向:</span>
        <span class="status-value">
          {{ formatDirection(direction) }}
        </span>
      </div>
      
      <div class="status-item" v-if="roiViolations.length > 0">
        <span class="status-label">ROI违规:</span>
        <span class="status-value violation">
          {{ roiViolations.length }} 个区域
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'

interface Point {
  x: number
  y: number
}

interface MotionContour {
  points: Point[]
  type?: string        // 轮廓类型：normal, direction_arrow
  color?: string       // 轮廓颜色：green, red
  direction?: string   // 方向信息：MOVING_UP, MOVING_DOWN, STATIONARY
}

interface Direction {
  angle: number
  magnitude: number
  vector: Point
}

interface ROIViolation {
  roi_id: string
  roi_name: string
  violation_type: string
}

interface DetectionResult {
  motion_detected: boolean
  contours: MotionContour[]
  direction?: Direction
  roi_violations: ROIViolation[]
}

const props = defineProps<{
  videoWidth: number
  videoHeight: number
  detectionResult?: DetectionResult
  showStatus?: boolean
}>()

// 响应式数据
const overlayCanvas = ref<HTMLCanvasElement>()
const motionDetected = ref(false)
const direction = ref<Direction | null>(null)
const roiViolations = ref<ROIViolation[]>([])

onMounted(() => {
  initCanvas()
})

// 监听检测结果变化
watch(() => props.detectionResult, (newResult) => {
  if (newResult) {
    updateDetectionResult(newResult)
  }
}, { deep: true })

const initCanvas = () => {
  nextTick(() => {
    if (overlayCanvas.value) {
      const canvas = overlayCanvas.value
      canvas.width = props.videoWidth || 640
      canvas.height = props.videoHeight || 360
    }
  })
}

const updateDetectionResult = (result: DetectionResult) => {
  motionDetected.value = result.motion_detected
  direction.value = result.direction || null
  roiViolations.value = result.roi_violations || []
  
  drawDetectionResult(result)
}

const drawDetectionResult = (result: DetectionResult) => {
  const canvas = overlayCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')!
  
  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  
  // 绘制运动轮廓
  if (result.motion_detected && result.contours) {
    drawMotionContours(ctx, result.contours)
  }
  
  // 绘制运动方向
  if (result.direction) {
    drawMotionDirection(ctx, result.direction)
  }
  
  // 绘制ROI违规提示
  if (result.roi_violations && result.roi_violations.length > 0) {
    drawROIViolations(ctx, result.roi_violations)
  }
}

const drawMotionContours = (ctx: CanvasRenderingContext2D, contours: MotionContour[]) => {
  contours.forEach(contour => {
    if (contour.points && contour.points.length > 0) {
      // 根据轮廓类型和颜色设置样式
      if (contour.type === 'direction_arrow') {
        // 方向箭头：红色，较粗线条
        ctx.strokeStyle = contour.color === 'red' ? '#ff0000' : '#ff0000'
        ctx.fillStyle = 'rgba(255, 0, 0, 0.3)'
        ctx.lineWidth = 3
      } else {
        // 普通运动轮廓：绿色
        ctx.strokeStyle = contour.color === 'green' ? '#00ff00' : '#00ff00'
        ctx.fillStyle = 'rgba(0, 255, 0, 0.2)'
        ctx.lineWidth = 2
      }

      ctx.beginPath()
      ctx.moveTo(contour.points[0].x, contour.points[0].y)

      for (let i = 1; i < contour.points.length; i++) {
        ctx.lineTo(contour.points[i].x, contour.points[i].y)
      }

      ctx.closePath()
      ctx.fill()
      ctx.stroke()

      // 如果是方向箭头，添加额外的视觉效果
      if (contour.type === 'direction_arrow') {
        // 添加箭头标识文字
        const centerX = contour.points.reduce((sum, p) => sum + p.x, 0) / contour.points.length
        const centerY = contour.points.reduce((sum, p) => sum + p.y, 0) / contour.points.length

        ctx.fillStyle = '#ffffff'
        ctx.font = 'bold 12px Arial'
        ctx.textAlign = 'center'
        ctx.strokeStyle = '#000000'
        ctx.lineWidth = 1

        const directionText = contour.direction === 'MOVING_UP' ? '↑' :
                             contour.direction === 'MOVING_DOWN' ? '↓' : '●'

        // 绘制文字背景
        ctx.strokeText(directionText, centerX, centerY + 4)
        ctx.fillText(directionText, centerX, centerY + 4)
      }
    }
  })
}

const drawMotionDirection = (ctx: CanvasRenderingContext2D, dir: Direction) => {
  if (!dir.vector || dir.magnitude < 5) return
  
  // 在画布中心绘制方向箭头
  const centerX = ctx.canvas.width / 2
  const centerY = ctx.canvas.height / 2
  
  // 计算箭头终点
  const arrowLength = Math.min(100, dir.magnitude * 2)
  const endX = centerX + dir.vector.x * arrowLength
  const endY = centerY + dir.vector.y * arrowLength
  
  // 绘制箭头
  ctx.strokeStyle = '#ff0000'
  ctx.fillStyle = '#ff0000'
  ctx.lineWidth = 3
  
  // 箭头主线
  ctx.beginPath()
  ctx.moveTo(centerX, centerY)
  ctx.lineTo(endX, endY)
  ctx.stroke()
  
  // 箭头头部
  const headLength = 15
  const headAngle = Math.PI / 6
  const angle = Math.atan2(endY - centerY, endX - centerX)
  
  ctx.beginPath()
  ctx.moveTo(endX, endY)
  ctx.lineTo(
    endX - headLength * Math.cos(angle - headAngle),
    endY - headLength * Math.sin(angle - headAngle)
  )
  ctx.moveTo(endX, endY)
  ctx.lineTo(
    endX - headLength * Math.cos(angle + headAngle),
    endY - headLength * Math.sin(angle + headAngle)
  )
  ctx.stroke()
  
  // 绘制方向文字
  ctx.fillStyle = '#ff0000'
  ctx.font = 'bold 14px Arial'
  ctx.textAlign = 'center'
  ctx.fillText(
    `${Math.round(dir.angle)}°`,
    centerX,
    centerY - 20
  )
}

const drawROIViolations = (ctx: CanvasRenderingContext2D, violations: ROIViolation[]) => {
  // 在画布顶部绘制违规警告
  ctx.fillStyle = 'rgba(255, 0, 0, 0.8)'
  ctx.fillRect(0, 0, ctx.canvas.width, 40)
  
  ctx.fillStyle = '#ffffff'
  ctx.font = 'bold 16px Arial'
  ctx.textAlign = 'center'
  ctx.fillText(
    `⚠️ ROI违规: ${violations.length} 个区域`,
    ctx.canvas.width / 2,
    25
  )
  
  // 闪烁效果
  setTimeout(() => {
    ctx.clearRect(0, 0, ctx.canvas.width, 40)
  }, 500)
}

const formatDirection = (dir: Direction): string => {
  if (!dir) return '无'
  
  const angle = dir.angle
  const magnitude = dir.magnitude
  
  let directionText = ''
  
  if (angle >= -22.5 && angle < 22.5) {
    directionText = '向右'
  } else if (angle >= 22.5 && angle < 67.5) {
    directionText = '右下'
  } else if (angle >= 67.5 && angle < 112.5) {
    directionText = '向下'
  } else if (angle >= 112.5 && angle < 157.5) {
    directionText = '左下'
  } else if (angle >= 157.5 || angle < -157.5) {
    directionText = '向左'
  } else if (angle >= -157.5 && angle < -112.5) {
    directionText = '左上'
  } else if (angle >= -112.5 && angle < -67.5) {
    directionText = '向上'
  } else if (angle >= -67.5 && angle < -22.5) {
    directionText = '右上'
  }
  
  return `${directionText} (${Math.round(magnitude)}px/s)`
}

// 暴露方法给父组件
defineExpose({
  clearOverlay: () => {
    const canvas = overlayCanvas.value
    if (canvas) {
      const ctx = canvas.getContext('2d')!
      ctx.clearRect(0, 0, canvas.width, canvas.height)
    }
  }
})
</script>

<style scoped>
.motion-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.overlay-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.detection-status {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 6px;
  border-radius: 3px;
  font-size: 10;
  min-width: 120px;
  scale: 0.6;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.status-label {
  font-weight: bold;
}

.status-value {
  color: #ccc;
}

.status-value.active {
  color: #00ff00;
}

.status-value.inactive {
  color: #666;
}

.status-value.violation {
  color: #ff0000;
  font-weight: bold;
}
</style>

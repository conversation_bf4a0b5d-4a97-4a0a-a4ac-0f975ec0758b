import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

/**
 * WebSDK功能管理Hook
 * 负责处理WebSDK的初始化、登录、视频预览等功能
 * @param addOperationInfo 操作日志记录函数
 */
export function useWebSDK(addOperationInfo: (message: string) => void) {
  // WebSDK实例
  let webVideoCtrl: any = null
  
  // 状态管理
  const videoSources = ref<any[]>([])
  const selectedVideoSource = ref('')
  const isConnecting = ref(false)
  const isPreviewActive = ref(false)
  const videoContainer = ref<HTMLElement>()
  
  // WebSDK相关
  let g_iWndIndex = 0 // 当前窗口索引
  let currentDevice: any = null
  let currentChannel: any = null
  let isWebSDKInitialized = false
  
  // 视频容器样式 - 16:9比例自适应
  const videoContainerStyle = computed(() => {
    return {
      aspectRatio: '16/9',
      width: '100%',
      maxWidth: '1200px'
    }
  })
  
  // 计算属性
  const canStartPreview = computed(() => {
    return selectedVideoSource.value && !isPreviewActive.value && isWebSDKInitialized
  })

  /**
   * 获取WebSDK实例
   */
  const getWebVideoCtrl = () => {
    if (!webVideoCtrl) {
      webVideoCtrl = (window as any).WebVideoCtrl
    }
    return webVideoCtrl
  }

  /**
   * 初始化WebSDK
   */
  const initWebSDK = async () => {
    try {
      // 检查WebSDK是否已加载
      if (!(window as any).WebVideoCtrl) {
        throw new Error('WebSDK未加载，请检查脚本引用')
      }

      webVideoCtrl = (window as any).WebVideoCtrl

      // 检查浏览器支持
      const iRet = webVideoCtrl.I_SupportNoPlugin()
      if (!iRet) {
        throw new Error('当前浏览器版本过低，不支持无插件，请升级后再试！')
      }

      addOperationInfo('浏览器支持检查通过')

      // 初始化插件参数及插入无插件
      webVideoCtrl.I_InitPlugin("100%", "100%", {
        bWndFull: true,     // 是否支持单窗口双击全屏
        iPackageType: 2,    // 2:PS 11:MP4
        iWndowType: 1,
        bNoPlugin: true,

        // 窗口选择回调
        cbSelWnd: (xmlDoc: any) => {
          try {
            // 检查jQuery是否可用
            if (typeof $ === 'undefined') {
              console.error('jQuery未加载，无法解析XML')
              addOperationInfo('[ERROR] jQuery未加载，无法解析XML')
              return
            }

            // 使用jQuery方式解析
            const windowText = $(xmlDoc).find("SelectWnd").eq(0).text()
            g_iWndIndex = parseInt(windowText, 10)
            addOperationInfo('当前选择的窗口编号：' + g_iWndIndex)
          } catch (error) {
            console.error('解析窗口选择XML失败:', error)
            addOperationInfo('[ERROR] 解析窗口选择XML失败')
          }
        },

        // 双击窗口回调
        cbDoubleClickWnd: (iWndIndex: number, bFullScreen: boolean) => {
          const szInfo = bFullScreen
            ? `当前放大的窗口编号：${iWndIndex}`
            : `当前还原的窗口编号：${iWndIndex}`
          console.log(szInfo)
          addOperationInfo(szInfo)
        },

        // 事件回调
        cbEvent: (iEventType: number, iParam1: number, iParam2: number) => {
          let eventMsg = ''
          if (2 == iEventType) { // 回放正常结束
            eventMsg = `窗口${iParam1}回放结束！`
          } else if (-1 == iEventType) {
            eventMsg = `设备${iParam1}网络错误！`
          } else if (3001 == iEventType) {
            eventMsg = `录像结束事件：窗口${iParam1}`
          } else {
            eventMsg = `事件回调：类型${iEventType}，参数1：${iParam1}，参数2：${iParam2}`
          }
          console.log(eventMsg)
          addOperationInfo(eventMsg)
        },

        // 远程配置回调
        cbRemoteConfig: () => {
          console.log('关闭远程配置库！')
        },

        // 插件初始化完成回调
        cbInitPluginComplete: () => {
          console.log('WebSDK插件初始化完成')
          isWebSDKInitialized = true
          addOperationInfo('[PLUGIN] WebSDK插件初始化完成')

          // 嵌入插件到指定容器
          const container = document.getElementById('divPlugin')
          if (container) {
            try {
              webVideoCtrl.I_InsertOBJECTPlugin('divPlugin')
              console.log('WebSDK插件嵌入完成')
              addOperationInfo('[PLUGIN] WebSDK插件嵌入完成')
            } catch (error) {
              console.error('插件嵌入失败:', error)
              addOperationInfo('[ERROR] 插件嵌入失败')
            }
          } else {
            console.error('未找到播放器容器元素 divPlugin')
            addOperationInfo('[ERROR] 未找到播放器容器元素 divPlugin')
          }
        },

        // 插件错误处理回调
        cbPluginErrorHandler: (iWndIndex: number, iErrorCode: number, oError: any) => {
          const ErrorCodes: Record<number, string> = {
            1001: "码流传输过程异常",
            1002: "回放结束",
            1003: "取流失败，连接被动断开",
            // ...其他错误代码
            1022: "采集音频失败，可能是在非https/localhost域下使用对讲导致,或者没有插耳机等"
          }
          const errorMsg = ErrorCodes[iErrorCode] || `未知错误(${iErrorCode})`
          console.error(`窗口${iWndIndex}：${errorMsg}`, oError)
          addOperationInfo(`[ERROR] 窗口${iWndIndex}：${errorMsg}`)

          // 获取窗口状态并停止播放
          const oWndInfo = webVideoCtrl.I_GetWindowStatus(iWndIndex)
          if (oWndInfo != null) {
            webVideoCtrl.I_Stop({
              success: () => {
                console.log(`窗口${iWndIndex}停止预览成功`)
                addOperationInfo(`[STOP] 窗口${iWndIndex}停止预览成功`)
              },
              error: () => {
                console.log(`窗口${iWndIndex}停止预览失败`)
                addOperationInfo(`[ERROR] 窗口${iWndIndex}停止预览失败`)
              }
            })
          }
        },

        // 性能不足回调
        cbPerformanceLack: () => {
          console.log('性能不足！')
          addOperationInfo('[WARNING] 性能不足！')
        },

        // 码流加密秘钥错误回调
        cbSecretKeyError: (iWndIndex: number) => {
          console.error(`窗口${iWndIndex}：码流加密秘钥错误！`)
          addOperationInfo(`[ERROR] 窗口${iWndIndex}：码流加密秘钥错误！`)

          const oWndInfo = webVideoCtrl.I_GetWindowStatus(iWndIndex)
          if (oWndInfo != null) {
            webVideoCtrl.I_Stop({
              success: () => {
                console.log(`窗口${iWndIndex}停止预览成功`)
                addOperationInfo(`[STOP] 窗口${iWndIndex}停止预览成功`)
              },
              error: () => {
                console.log(`窗口${iWndIndex}停止预览失败`)
                addOperationInfo(`[ERROR] 窗口${iWndIndex}停止预览失败`)
              }
            })
          }
        }
      })

    } catch (error) {
      console.error('WebSDK初始化失败:', error)
      addOperationInfo(`[ERROR] WebSDK初始化失败: ${error instanceof Error ? error.message : String(error)}`)
      throw error
    }
  }

  /**
   * 刷新视频源列表
   */
  const refreshVideoSources = async () => {
    try {
      addOperationInfo('[API] 开始获取视频源列表...')
      // 从localStorage获取认证token
      const token = localStorage.getItem('token')

      const response = await fetch('/api/video-sources/', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        }
      })

      if (response.ok) {
        const data = await response.json()
        // 只显示WebSDK设备类型的视频源
        const websdkSources = data.filter((source: any) => source.source_type === 'websdk_device')

        // 转换数据格式以适配WebSDK登录需求
        videoSources.value = websdkSources.map((source: any) => ({
          id: source.id.toString(),
          name: source.name || (source.description + ' - ' + source.device_ip),
          description: source.description || '',
          ip: source.device_ip,
          port: source.device_port || 80,
          username: source.device_username || 'admin',
          password: source.device_password || '',
          protocol: source.device_protocol || 'HTTP',
          channelId: source.channel_id || 1,
          streamType: source.stream_type || 1
        }))

        console.log('获取到WebSDK视频源列表:', videoSources.value)
        ElMessage.success(`已加载 ${videoSources.value.length} 个WebSDK设备`)
        addOperationInfo(`[API] 成功加载 ${videoSources.value.length} 个WebSDK设备`)
      } else {
        console.error('获取视频源失败:', response.status, response.statusText)

        if (response.status === 401) {
          ElMessage.warning('请先登录系统后再获取视频源列表')
          addOperationInfo('[WARNING] 请先登录系统后再获取视频源列表')
        } else {
          ElMessage.error('获取视频源列表失败')
          addOperationInfo('[ERROR] 获取视频源列表失败')
          videoSources.value = []
        }
      }
    } catch (error) {
      console.error('刷新视频源失败:', error)
      ElMessage.error('网络错误，无法获取视频源列表')
      addOperationInfo('[ERROR] 网络错误，无法获取视频源列表')
    }
  }

  /**
   * 处理视频源选择变化
   * @param sourceId 可选参数，如果传入则更新selectedVideoSource
   */
  const onVideoSourceChange = async (sourceId?: string) => {
    // 如果传入了sourceId，则更新selectedVideoSource
    if (sourceId !== undefined) {
      selectedVideoSource.value = sourceId
    }
    
    if (!selectedVideoSource.value) {
      addOperationInfo('[SOURCE] 清空视频源选择')
      return
    }

    const source = videoSources.value.find(s => s.id === selectedVideoSource.value)
    if (!source) {
      addOperationInfo('[ERROR] 未找到选择的视频源')
      return
    }

    console.log('选择视频源:', selectedVideoSource.value)
    addOperationInfo(`[SOURCE] 选择视频源: ${source.name} (${source.ip}:${source.port})`)
    addOperationInfo('[AUTO] 开始自动化流程: 登录设备 → 获取通道 → 开始预览')

    // 如果当前正在预览，先停止
    if (isPreviewActive.value) {
      addOperationInfo('[AUTO] 检测到正在预览，先停止当前预览')
      stopPreview()
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    // 自动开始预览
    setTimeout(() => {
      startPreview()
    }, 100)
  }

  /**
   * 设备登录
   * @param source 视频源信息
   */
  const loginDevice = async (source: any): Promise<boolean> => {
    return new Promise((resolve) => {
      const ctrl = getWebVideoCtrl()
      if (!ctrl) {
        addOperationInfo('[ERROR] WebSDK未初始化，无法登录设备')
        resolve(false)
        return
      }

      const szIP = source.ip
      const szPort = source.port
      const szUsername = source.username
      const szPassword = source.password
      const szDeviceIdentify = `${szIP}_${szPort}`
      const protocol = source.protocol === 'HTTPS' ? 2 : 1

      addOperationInfo(`[LOGIN] 正在连接设备: ${szIP}:${szPort} (${source.protocol})`)

      // 保存登录信息
      sessionStorage.setItem('loginip', szIP)
      sessionStorage.setItem('password', szPassword)
      localStorage.setItem('currentWebSDKDevice', JSON.stringify({
        ip: szIP,
        port: szPort,
        username: szUsername,
        password: szPassword
      }))
      
      // 设置Cookie以供Vite代理使用
      document.cookie = `websdk_ip=${szIP}; path=/; SameSite=Lax`
      document.cookie = `websdk_port=${szPort}; path=/; SameSite=Lax`
      console.log(`[COOKIE] 设置设备信息Cookie: IP=${szIP}, Port=${szPort}`)
      addOperationInfo(`[COOKIE] 设置设备信息Cookie: IP=${szIP}, Port=${szPort}`)

      // 调用WebSDK登录API
      const iRet = ctrl.I_Login(szIP, protocol, parseInt(szPort, 10), szUsername, szPassword, {
        success: (xmlDoc: any) => {
          console.log('设备登录成功:', xmlDoc)
          addOperationInfo(`[LOGIN] 设备登录成功: ${szIP}`)

          // 解析登录成功的XML响应
          try {
            const parser = new DOMParser()
            const doc = parser.parseFromString(xmlDoc, 'text/xml')
            const sessionID = doc.getElementsByTagName('sessionID')[0]?.textContent
            if (sessionID) {
              addOperationInfo(`[LOGIN] 获得会话ID: ${sessionID}`)
            }
          } catch (parseError) {
            console.warn('解析登录响应XML失败:', parseError)
          }

          currentDevice = {
            id: szDeviceIdentify,
            ip: szIP,
            port: szPort,
            username: szUsername,
            password: szPassword,
            protocol: source.protocol,
            xmlDoc
          }

          // 登录成功后获取通道信息
          addOperationInfo('[LOGIN] 登录成功，正在获取通道信息...')
          setTimeout(() => {
            getChannelInfo(szDeviceIdentify, source.channelId)
          }, 100)

          addOperationInfo('[LOGIN] 设备连接完成，可以开始预览')
          resolve(true)
        },
        error: (status: any, xmlDoc: any) => {
          console.error('设备登录失败:', status, xmlDoc)

          // 解析错误信息
          let errorMsg = `登录失败 (状态码: ${status})`
          try {
            if (xmlDoc) {
              const parser = new DOMParser()
              const doc = parser.parseFromString(xmlDoc, 'text/xml')
              const statusString = doc.getElementsByTagName('statusString')[0]?.textContent
              const subStatusCode = doc.getElementsByTagName('subStatusCode')[0]?.textContent

              if (statusString) {
                errorMsg = `登录失败: ${statusString}`
              }
              if (subStatusCode) {
                errorMsg += ` (子状态码: ${subStatusCode})`
              }
            }
          } catch (parseError) {
            console.warn('解析错误响应XML失败:', parseError)
          }

          addOperationInfo(`[ERROR] ${errorMsg}`)
          resolve(false)
        }
      })

      if (iRet === -1) {
        addOperationInfo(`[LOGIN] 设备 ${szDeviceIdentify} 已登录过！`)
        resolve(true)
      }
    })
  }

  /**
   * 获取通道信息
   * @param deviceIdentify 设备标识
   * @param targetChannelId 目标通道ID
   */
  const getChannelInfo = (deviceIdentify: string, targetChannelId: number) => {
    const ctrl = getWebVideoCtrl()
    if (!ctrl) {
      addOperationInfo('[ERROR] WebSDK不可用，无法获取通道信息')
      return
    }

    let hasFoundChannel = false
    addOperationInfo('[CHANNEL] 正在获取通道信息（优先选择模拟通道）...')

    // 获取模拟通道信息
    ctrl.I_GetAnalogChannelInfo(deviceIdentify, {
      async: false,
      success: (xmlDoc: any) => {
        console.log('获取模拟通道信息成功:', xmlDoc)
        addOperationInfo('[CHANNEL] 成功获取模拟通道信息')

        if (!hasFoundChannel) {
          try {
            // 使用jQuery解析XML
            if (typeof $ !== 'undefined') {
              const $doc = $(xmlDoc)
              const channels = $doc.find('VideoInputChannel')

              addOperationInfo(`[CHANNEL] 找到 ${channels.length} 个模拟通道`)

              if (channels.length > 0) {
                // 查找指定通道或使用第一个通道
                let targetChannel = null
                channels.each(function(index) {
                  const channelId = $(this).find('id').text()
                  if (channelId && parseInt(channelId) === targetChannelId) {
                    targetChannel = $(this)
                    return false // 跳出循环
                  }
                })

                if (!targetChannel) {
                  targetChannel = channels.eq(0) // 使用第一个通道
                }

                if (targetChannel && targetChannel.length > 0) {
                  const channelId = targetChannel.find('id').text()
                  let channelName = targetChannel.find('name').text()

                  // 如果名称为空，使用默认名称格式
                  if (!channelName) {
                    const index = parseInt(channelId || '1') - 1
                    channelName = 'Camera ' + (index < 9 ? '0' + (index + 1) : (index + 1))
                  }

                  if (channelId) {
                    currentChannel = {
                      id: channelId,
                      name: channelName,
                      channelNo: channelId,
                      type: 'analog',
                      deviceId: deviceIdentify
                    }
                    hasFoundChannel = true
                    addOperationInfo(`[CHANNEL] 自动选择模拟通道: ${channelName} (ID: ${channelId})`)
                    console.log('自动选择模拟通道:', currentChannel)
                  }
                }
              }
            } else {
              // 如果jQuery不可用，使用原生DOM解析
              const parser = new DOMParser()
              const doc = parser.parseFromString(xmlDoc, 'text/xml')
              const channels = doc.querySelectorAll('VideoInputChannel')

              addOperationInfo(`[CHANNEL] 找到 ${channels.length} 个模拟通道`)

              // 原生DOM解析逻辑...
            }
          } catch (error) {
            console.error('解析模拟通道信息失败:', error)
            addOperationInfo('[ERROR] 解析模拟通道信息失败')
          }
        }
      },
      error: (status: any, xmlDoc: any) => {
        console.warn('获取模拟通道信息失败:', { status, xmlDoc })
        addOperationInfo('[WARNING] 获取模拟通道信息失败')
      }
    })

    // 获取数字通道信息
    ctrl.I_GetDigitalChannelInfo(deviceIdentify, {
      async: false,
      success: (xmlDoc: any) => {
        console.log('获取数字通道信息成功:', xmlDoc)
        addOperationInfo('[CHANNEL] 成功获取数字通道信息')

        if (!hasFoundChannel) {
          // 数字通道处理逻辑...
        }
      },
      error: (status: any, xmlDoc: any) => {
        console.warn('获取数字通道信息失败:', { status, xmlDoc })
        addOperationInfo('[WARNING] 获取数字通道信息失败')
      }
    })

    // 如果没有找到任何通道，使用默认值
    setTimeout(() => {
      if (!hasFoundChannel) {
        const defaultChannelId = targetChannelId || 1
        currentChannel = {
          id: defaultChannelId.toString(),
          name: '默认通道' + defaultChannelId,
          channelNo: defaultChannelId.toString(),
          type: 'default',
          deviceId: deviceIdentify
        }
        addOperationInfo('[CHANNEL] 未找到通道信息，使用默认通道: ' + defaultChannelId)
        console.log('使用默认通道:', currentChannel)
      }
      addOperationInfo('[CHANNEL] 通道配置完成，当前选择通道: ' + (currentChannel?.name || '未知'))
    }, 300)
  }

  /**
   * 开始预览
   */
  const startPreview = async () => {
    if (!selectedVideoSource.value) {
      ElMessage.warning('请先选择视频源')
      addOperationInfo('[WARNING] 请先选择视频源')
      return
    }

    const source = videoSources.value.find(s => s.id === selectedVideoSource.value)
    if (!source) {
      ElMessage.warning('视频源不存在')
      addOperationInfo('[ERROR] 视频源不存在')
      return
    }

    isConnecting.value = true
    addOperationInfo('[PREVIEW] 开始连接设备...')

    try {
      // 1. 登录设备
      addOperationInfo('[PREVIEW] 正在登录设备...')
      const loginSuccess = await loginDevice(source)
      if (!loginSuccess) {
        isConnecting.value = false
        addOperationInfo('[ERROR] 设备登录失败，无法开始预览')
        return
      }

      // 等待通道信息获取完成
      await new Promise(resolve => setTimeout(resolve, 1000))

      if (!currentDevice || !currentChannel) {
        addOperationInfo('[ERROR] 获取设备通道信息失败，无法开始预览')
        isConnecting.value = false
        return
      }

      // 2. 开始预览
      const ctrl = getWebVideoCtrl()
      if (!ctrl) {
        addOperationInfo('[ERROR] WebSDK不可用，无法开始预览')
        isConnecting.value = false
        return
      }

      const szDeviceIdentify = currentDevice.id
      const iChannelID = parseInt(currentChannel.channelNo || currentChannel.id)
      const iStreamType = 1 // 自动选择主码流
      const bZeroChannel = currentChannel.type === 'zero'

      // 关键修复：先获取设备端口信息
      addOperationInfo('[PREVIEW] 正在获取设备端口信息...')
      const oPort = ctrl.I_GetDevicePort(szDeviceIdentify)
      let iRtspPort = parseInt(source.rtspPort || '554', 10) // 默认值

      if (oPort != null) {
        iRtspPort = oPort.iRtspPort || iRtspPort
        addOperationInfo(`[PREVIEW] 获取设备端口成功: RTSP端口=${iRtspPort}, 设备端口=${oPort.iDevicePort}`)
      } else {
        addOperationInfo(`[PREVIEW] 获取设备端口失败，使用默认RTSP端口: ${iRtspPort}`)
      }

      addOperationInfo(`[PREVIEW] 开始播放通道 ${iChannelID} (${currentChannel.name}) - 主码流`)

      // 定义预览函数
      const startRealPlay = () => {
        addOperationInfo('[PREVIEW] 尝试直连模式 (bProxy: false)...')

        ctrl.I_StartRealPlay(szDeviceIdentify, {
          iWndIndex: g_iWndIndex,
          iStreamType: iStreamType,
          iChannelID: iChannelID,
          bZeroChannel: bZeroChannel,
          iRtspPort: iRtspPort,
          bProxy: false,
          success: () => {
            console.log('直连模式预览成功')
            addOperationInfo('[PREVIEW] ✅ 直连模式预览成功')
            isPreviewActive.value = true
            isConnecting.value = false
            ElMessage.success('预览成功（直连模式）')
          },
          error: (status: any, xmlDoc: any) => {
            console.error('直连模式失败:', { status, xmlDoc })

            // 详细的错误信息解析
            let errorDetail = `状态码: ${status || 'undefined'}`
            if (xmlDoc) {
              try {
                const parser = new DOMParser()
                const doc = parser.parseFromString(xmlDoc, 'text/xml')
                const statusString = doc.getElementsByTagName('statusString')[0]?.textContent
                const subStatusCode = doc.getElementsByTagName('subStatusCode')[0]?.textContent
                if (statusString) errorDetail += `, 错误: ${statusString}`
                if (subStatusCode) errorDetail += `, 子状态: ${subStatusCode}`
              } catch (e) {
                console.warn('解析错误XML失败:', e)
              }
            }

            addOperationInfo(`[PREVIEW] ❌ 直连模式失败: ${errorDetail}`)

            if (status === 403 || status === undefined) {
              // 尝试代理模式
              addOperationInfo('[PREVIEW] 设备不支持直连，尝试代理模式 (bProxy: true)...')

              ctrl.I_StartRealPlay(szDeviceIdentify, {
                iWndIndex: g_iWndIndex,
                iStreamType: iStreamType,
                iChannelID: iChannelID,
                bZeroChannel: bZeroChannel,
                iRtspPort: iRtspPort,
                bProxy: true,
                success: () => {
                  console.log('代理模式预览成功')
                  addOperationInfo('[PREVIEW] ✅ 代理模式预览成功')
                  isPreviewActive.value = true
                  isConnecting.value = false
                  ElMessage.success('预览成功（代理模式）')
                },
                error: (status2: any, xmlDoc2: any) => {
                  console.error('代理模式也失败:', { status2, xmlDoc2 })

                  // 解析代理模式错误
                  let proxyErrorDetail = `状态码: ${status2}`
                  if (xmlDoc2) {
                    try {
                      const parser = new DOMParser()
                      const doc = parser.parseFromString(xmlDoc2, 'text/xml')
                      const statusString = doc.getElementsByTagName('statusString')[0]?.textContent
                      if (statusString) proxyErrorDetail += `, 错误: ${statusString}`
                    } catch (e) {
                      console.warn('解析代理错误XML失败:', e)
                    }
                  }

                  addOperationInfo(`[ERROR] 代理模式也失败: ${proxyErrorDetail}`)
                  addOperationInfo('[ERROR] 设备不支持WebSocket取流，请检查设备配置')
                  ElMessage.error('设备不支持WebSocket取流')
                  isConnecting.value = false
                }
              })
            } else {
              addOperationInfo(`[ERROR] 预览失败: ${errorDetail}`)
              ElMessage.error(`预览失败: ${status}`)
              isConnecting.value = false
            }
          }
        })
      }

      // 检查当前窗口状态，如果正在播放则先停止
      const currentWndInfo = ctrl.I_GetWindowStatus(g_iWndIndex)
      if (currentWndInfo && currentWndInfo.bPlaying) {
        addOperationInfo('[PREVIEW] 当前窗口正在播放，先停止...')
        ctrl.I_Stop({
          success: () => {
            addOperationInfo('[PREVIEW] 停止成功，开始新的预览')
            startRealPlay()
          },
          error: () => {
            addOperationInfo('[ERROR] 停止当前播放失败')
            isConnecting.value = false
          }
        })
      } else {
        startRealPlay()
      }

    } catch (error) {
      console.error('开始预览异常:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      addOperationInfo(`[ERROR] 开始预览异常: ${errorMessage}`)
      ElMessage.error(`开始预览失败: ${errorMessage}`)
      isConnecting.value = false
    }
  }

  /**
   * 停止预览
   */
  const stopPreview = () => {
    const ctrl = getWebVideoCtrl()
    if (!ctrl) {
      ElMessage.error('WebSDK未初始化')
      addOperationInfo('[ERROR] WebSDK未初始化，无法停止预览')
      return
    }

    try {
      addOperationInfo('[PREVIEW] 正在停止预览...')

      // 始终调用I_Stop并传入回调，不管窗口状态如何
      // 根据WebSDK3.4_无插件开发包编程指南 5.3.3节要求使用options对象传递回调
      ctrl.I_Stop({
        iWndIndex: g_iWndIndex, // 明确指定窗口
        success: () => {
          console.log('停止预览成功')
          addOperationInfo('[PREVIEW] 视频预览已停止')
          ElMessage.success('视频预览已停止')
          isPreviewActive.value = false
          addOperationInfo('[PREVIEW] 预览停止完成')
        },
        error: (errorCode: any) => {
          console.error('停止预览失败，错误码:', errorCode)
          addOperationInfo(`[ERROR] 停止预览失败: 错误码 ${errorCode}`)
          ElMessage.error(`停止预览失败: 错误码 ${errorCode}`)
          
          // 即使出错也重置预览状态
          isPreviewActive.value = false
        }
      })
    } catch (error) {
      console.error('停止预览失败:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      ElMessage.error(`停止预览失败: ${errorMessage}`)
      addOperationInfo(`[ERROR] 停止预览失败: ${errorMessage}`)

      // 强制重置状态
      isPreviewActive.value = false
    }
  }

  return {
    webVideoCtrl,
    videoSources,
    selectedVideoSource,
    isConnecting,
    isPreviewActive,
    videoContainer,
    videoContainerStyle,
    canStartPreview,
    getWebVideoCtrl,
    initWebSDK,
    refreshVideoSources,
    onVideoSourceChange,
    loginDevice,
    getChannelInfo,
    startPreview,
    stopPreview
  }
}
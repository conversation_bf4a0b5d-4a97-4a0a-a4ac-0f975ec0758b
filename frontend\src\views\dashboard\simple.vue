<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1 class="dashboard-title">压铸件智能检测系统 - 监控看板</h1>
      <div class="header-actions">
        <el-button type="primary" @click="refreshAll">
          <el-icon><refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="toggleFullscreen">
          <el-icon><full-screen /></el-icon>
          全屏显示
        </el-button>
      </div>
    </div>

    <!-- 系统概览卡片 -->
    <div class="overview-section">
      <SystemOverview :overview-data="overviewData" />
    </div>

    <!-- 检测信息轮播 - 重要信息优先展示 -->
    <div class="detection-carousel-section">
      <DetectionCarousel />
    </div>

    <!-- 底部统计区域 -->
    <div class="bottom-section">
      <StatisticsPanel :statistics="statisticsData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, FullScreen } from '@element-plus/icons-vue'

// 导入组件
import SystemOverview from './components/SystemOverview.vue'
import DetectionCarousel from './components/DetectionCarousel.vue'
import StatisticsPanel from './components/StatisticsPanel.vue'

// 响应式数据
const overviewData = ref({
  totalDevices: 4,
  activeDevices: 3,
  totalDetectionGroups: 9,
  activeDetectionGroups: 6,
  totalAlarms: 12,
  unhandledAlarms: 3,
  systemStatus: 'normal' // normal, warning, error
})

const statisticsData = ref({
  todayDetections: 1250,
  todayAlarms: 8,
  jamRate: 0.64,
  avgProcessingTime: 85,
  systemUptime: '15天 8小时 32分钟',
  detectionAccuracy: 98.5
})

// 方法
const refreshAll = () => {
  ElMessage.success('数据刷新完成')
  // 这里添加实际的数据刷新逻辑
}

const toggleFullscreen = () => {
  const dashboardElement = document.querySelector('.dashboard-container')
  if (!document.fullscreenElement) {
    if (dashboardElement && dashboardElement.requestFullscreen) {
      dashboardElement.requestFullscreen()
    } else {
      document.documentElement.requestFullscreen()
    }
  } else {
    document.exitFullscreen()
  }
}

// 定时刷新数据
let refreshTimer: number | null = null

onMounted(() => {
  // 每30秒刷新一次数据
  refreshTimer = setInterval(() => {
    // 这里添加实际的数据更新逻辑
    console.log('定时刷新数据')
  }, 30000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style>
@import './dashboard-theme.css';
</style>

<style scoped>
.dashboard-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
  color: var(--text-color);
  overflow: hidden;
  margin: -20px;
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: var(--bg-color-soft);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
  border-radius: 8px;
  margin-bottom: 16px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.overview-section {
  flex-shrink: 0;
  padding: 0 24px 12px 24px;
  height: 120px;
}

.detection-carousel-section {
  flex: 1;
  padding: 0 24px 12px 24px;
  min-height: 300px;
  overflow: hidden;
}

.detection-carousel-section .detection-carousel {
  height: 100%;
  overflow: hidden;
}

.bottom-section {
  flex-shrink: 0;
  padding: 0 24px 12px 24px;
  height: 200px;
  overflow: hidden;
}

.panel-section {
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .detection-carousel-section {
    min-height: 350px;
    max-height: 60vh;
  }
}

@media (max-width: 1200px) {
  .dashboard-container {
    margin: -16px;
    padding: 16px;
  }

  .dashboard-header {
    padding: 12px 16px;
    margin-bottom: 12px;
  }

  .dashboard-title {
    font-size: 20px;
  }

  .overview-section {
    padding: 0 16px 12px 16px;
  }

  .detection-carousel-section {
    padding: 0 16px 12px 16px;
    min-height: 300px;
    max-height: 50vh;
  }

  .bottom-section {
    padding: 0 16px 12px 16px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    margin: -12px;
    padding: 12px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
    padding: 8px 12px;
    margin-bottom: 8px;
  }

  .dashboard-title {
    font-size: 18px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .overview-section {
    padding: 0 12px 8px 12px;
  }

  .detection-carousel-section {
    padding: 0 12px 8px 12px;
    min-height: 250px;
    max-height: 40vh;
  }

  .bottom-section {
    padding: 0 12px 8px 12px;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    margin: -8px;
    padding: 8px;
  }

  .dashboard-header {
    padding: 6px 8px;
    margin-bottom: 6px;
  }

  .dashboard-title {
    font-size: 16px;
  }

  .header-actions .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }

  .overview-section,
  .detection-carousel-section,
  .bottom-section {
    padding: 0 8px 6px 8px;
  }

  .detection-carousel-section {
    min-height: 200px;
    max-height: 35vh;
  }
}

/* 确保轮播组件在小屏幕上可见 */
@media (max-height: 600px) {
  .detection-carousel-section {
    min-height: 180px;
    max-height: 30vh;
  }
}

@media (max-height: 500px) {
  .detection-carousel-section {
    min-height: 150px;
    max-height: 25vh;
  }

  .overview-section {
    display: none; /* 在极小屏幕上隐藏概览，优先显示轮播 */
  }
}
</style>

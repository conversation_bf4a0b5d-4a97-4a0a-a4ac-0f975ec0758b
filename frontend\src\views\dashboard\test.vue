<template>
  <div class="test-dashboard">
    <h1>监控看板测试页面</h1>
    <p>如果您能看到这个页面，说明路由配置正确。</p>
    
    <el-card>
      <h2>基本信息</h2>
      <p>当前时间: {{ currentTime }}</p>
      <p>页面路径: {{ $route.path }}</p>
      <p>页面名称: {{ $route.name }}</p>
    </el-card>

    <el-button type="primary" @click="testClick">测试按钮</el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const currentTime = ref(new Date().toLocaleString())

const testClick = () => {
  ElMessage.success('测试按钮点击成功！')
}

onMounted(() => {
  console.log('监控看板测试页面已加载')
  setInterval(() => {
    currentTime.value = new Date().toLocaleString()
  }, 1000)
})
</script>

<style scoped>
.test-dashboard {
  padding: 20px;
}

.test-dashboard h1 {
  color: var(--primary-color);
  margin-bottom: 20px;
}

.test-dashboard .el-card {
  margin: 20px 0;
}
</style>

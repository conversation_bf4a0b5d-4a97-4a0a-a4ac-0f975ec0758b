/* Element Plus 暗色模式样式覆盖 */
.dark-theme {
  /* 基础颜色变量覆盖 */
  --el-color-white: var(--bg-color) !important;
  --el-color-black: var(--text-color) !important;
  --el-bg-color: var(--bg-color) !important;
  --el-bg-color-page: var(--bg-color-mute) !important;
  --el-bg-color-overlay: var(--bg-color-soft) !important;
  --el-text-color-primary: var(--text-color) !important;
  --el-text-color-regular: var(--text-color-soft) !important;
  --el-text-color-secondary: var(--text-color-mute) !important;
  --el-border-color: var(--border-color) !important;
  --el-border-color-light: var(--border-color) !important;
  --el-border-color-lighter: var(--border-color) !important;
  --el-border-color-extra-light: var(--border-color) !important;
  --el-fill-color: var(--bg-color-soft) !important;
  --el-fill-color-light: var(--bg-color-soft) !important;
  --el-fill-color-blank: var(--bg-color) !important;
  --el-mask-color: rgba(0, 0, 0, 0.8) !important;
  --el-mask-color-extra-light: rgba(0, 0, 0, 0.3) !important;

  /* 组件覆盖 */
  --el-menu-bg-color: var(--bg-color-soft) !important;
  --el-menu-text-color: var(--text-color-soft) !important;
  --el-menu-hover-bg-color: var(--bg-color-mute) !important;
  --el-menu-active-color: var(--primary-color) !important;
  
  --el-button-bg-color: var(--bg-color-soft) !important;
  --el-button-text-color: var(--text-color) !important;
  --el-button-border-color: var(--border-color) !important;
  --el-button-hover-bg-color: var(--primary-color) !important;
  --el-button-hover-text-color: white !important;
  
  --el-card-bg-color: var(--bg-color) !important;
  
  --el-dropdown-menu-bg-color: var(--bg-color-soft) !important;
  --el-dropdown-menuItem-hover-color: var(--bg-color-mute) !important;
}

/* 菜单组件暗色模式覆盖 */
.dark-theme .el-menu {
  background-color: var(--bg-color-soft) !important;
  border-right-color: var(--border-color) !important;
}

.dark-theme .el-menu-item {
  color: var(--text-color-soft) !important;
}

.dark-theme .el-menu-item:hover {
  background-color: var(--bg-color-mute) !important;
}

.dark-theme .el-menu-item.is-active {
  color: var(--primary-color) !important;
  background-color: var(--bg-color-mute) !important;
}

/* 表格组件暗色模式覆盖 */
.dark-theme .el-table {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
}

.dark-theme .el-table tr {
  background-color: var(--bg-color) !important;
}

.dark-theme .el-table th {
  background-color: var(--bg-color-soft) !important;
  color: var(--text-color) !important;
}

.dark-theme .el-table td {
  border-bottom-color: var(--border-color) !important;
}

.dark-theme .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: var(--bg-color-soft) !important;
}

.dark-theme .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: var(--bg-color-mute) !important;
}

/* 表单组件暗色模式覆盖 */
.dark-theme .el-input__wrapper {
  background-color: var(--bg-color) !important;
  box-shadow: 0 0 0 1px var(--border-color) inset !important;
}

.dark-theme .el-input__inner {
  color: var(--text-color) !important;
}

.dark-theme .el-textarea__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

/* 按钮组件暗色模式覆盖 */
.dark-theme .el-button {
  background-color: var(--bg-color-soft) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.dark-theme .el-button:hover {
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.dark-theme .el-button--primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

.dark-theme .el-button--primary:hover {
  background-color: var(--primary-color-light) !important;
  border-color: var(--primary-color-light) !important;
  color: white !important;
}

/* 卡片组件暗色模式覆盖 */
.dark-theme .el-card {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

/* 下拉菜单暗色模式覆盖 */
.dark-theme .el-dropdown-menu {
  background-color: var(--bg-color-soft) !important;
}

.dark-theme .el-dropdown-menu__item {
  color: var(--text-color) !important;
}

.dark-theme .el-dropdown-menu__item:hover {
  background-color: var(--bg-color-mute) !important;
  color: var(--primary-color) !important;
}

/* 对话框暗色模式覆盖 */
.dark-theme .el-dialog {
  background-color: var(--bg-color) !important;
}

.dark-theme .el-dialog__title {
  color: var(--text-color) !important;
}

/* 标签页暗色模式覆盖 */
.dark-theme .el-tabs__item {
  color: var(--text-color-soft) !important;
}

.dark-theme .el-tabs__item.is-active {
  color: var(--primary-color) !important;
}

.dark-theme .el-tabs__active-bar {
  background-color: var(--primary-color) !important;
}

/* 面包屑暗色模式覆盖 */
.dark-theme .el-breadcrumb__item {
  color: var(--text-color-soft) !important;
}

.dark-theme .el-breadcrumb__item:last-child {
  color: var(--text-color) !important;
}

/* 分页器暗色模式覆盖 */
.dark-theme .el-pagination {
  color: var(--text-color) !important;
}

.dark-theme .el-pagination button {
  background-color: var(--bg-color-soft) !important;
}

.dark-theme .el-pagination .el-pager li {
  background-color: var(--bg-color-soft) !important;
  color: var(--text-color) !important;
}

.dark-theme .el-pagination .el-pager li.active {
  color: var(--primary-color) !important;
}

/* 弹出框暗色模式覆盖 */
.dark-theme .el-message-box {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
}

.dark-theme .el-message-box__title {
  color: var(--text-color) !important;
}

.dark-theme .el-message-box__content {
  color: var(--text-color-soft) !important;
}

/* 消息提示暗色模式覆盖 */
.dark-theme .el-message {
  background-color: var(--bg-color-soft) !important;
  border-color: var(--border-color) !important;
}

.dark-theme .el-message--success {
  border-color: var(--success-color) !important;
}

.dark-theme .el-message--warning {
  border-color: var(--warning-color) !important;
}

.dark-theme .el-message--error {
  border-color: var(--danger-color) !important;
}

/* 下拉选择框暗色模式覆盖 */
.dark-theme .el-select-dropdown {
  background-color: var(--bg-color-soft) !important;
  border-color: var(--border-color) !important;
}

.dark-theme .el-select-dropdown__item {
  color: var(--text-color) !important;
}

.dark-theme .el-select-dropdown__item.hover,
.dark-theme .el-select-dropdown__item:hover {
  background-color: var(--bg-color-mute) !important;
}

.dark-theme .el-select-dropdown__item.selected {
  color: var(--primary-color) !important;
  font-weight: bold;
}

/* 日期选择器暗色模式覆盖 */
.dark-theme .el-picker__popper {
  background-color: var(--bg-color-soft) !important;
  border-color: var(--border-color) !important;
}

.dark-theme .el-date-table th,
.dark-theme .el-date-table td {
  color: var(--text-color) !important;
}

.dark-theme .el-date-table td.current:not(.disabled) span {
  background-color: var(--primary-color) !important;
  color: white !important;
} 
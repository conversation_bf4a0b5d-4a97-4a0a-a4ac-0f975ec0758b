// 预设检测计划API接口

import { get, post, put, del } from '@/api/request'
import type { 
  PresetSchedule, 
  PresetScheduleCreate, 
  PresetScheduleUpdate,
  ScheduleExecutionStatus,
  ScheduleConflict
} from '@/types/preset-schedule'

// 获取所有预设计划
export const getPresetSchedules = async (): Promise<PresetSchedule[]> => {
  const response = await get<PresetSchedule[]>('/preset-schedules/')
  return response.data
}

// 获取特定预设计划
export const getPresetSchedule = async (id: string): Promise<PresetSchedule> => {
  const response = await get<PresetSchedule>(`/preset-schedules/${id}/`)
  return response.data
}

// 创建预设计划
export const createPresetSchedule = async (data: PresetScheduleCreate): Promise<PresetSchedule> => {
  const response = await post<PresetSchedule>('/preset-schedules/', data)
  return response.data
}

// 更新预设计划
export const updatePresetSchedule = async (id: string, data: PresetScheduleUpdate): Promise<PresetSchedule> => {
  const response = await put<PresetSchedule>(`/preset-schedules/${id}/`, data)
  return response.data
}

// 删除预设计划
export const deletePresetSchedule = async (id: string): Promise<void> => {
  const response = await del<void>(`/preset-schedules/${id}/`)
  return response.data
}

// 启用/禁用预设计划
export const togglePresetSchedule = async (id: string, enabled: boolean): Promise<PresetSchedule> => {
  const response = await put<PresetSchedule>(`/preset-schedules/${id}/toggle/`, { isEnabled: enabled })
  return response.data
}

// 检查时间冲突
export const checkScheduleConflict = async (data: {
  date: string
  startTime: string
  endTime: string
  excludeId?: string
}): Promise<ScheduleConflict> => {
  const response = await post<ScheduleConflict>('/preset-schedules/check-conflict', data)
  return response.data
}

// 手动执行预设计划
export const executePresetSchedule = async (id: string): Promise<ScheduleExecutionStatus> => {
  const response = await post<ScheduleExecutionStatus>(`/preset-schedules/${id}/execute`, {})
  return response.data
}

// 停止正在执行的预设计划
export const stopPresetSchedule = async (id: string): Promise<ScheduleExecutionStatus> => {
  const response = await post<ScheduleExecutionStatus>(`/preset-schedules/${id}/stop`, {})
  return response.data
}

// 获取预设计划执行状态
export const getScheduleExecutionStatus = async (id: string): Promise<ScheduleExecutionStatus> => {
  const response = await get<ScheduleExecutionStatus>(`/preset-schedules/${id}/status`)
  return response.data
}

// 获取今日的预设计划
export const getTodaySchedules = async (): Promise<PresetSchedule[]> => {
  const today = new Date().toISOString().split('T')[0]
  const response = await get<PresetSchedule[]>(`/preset-schedules/by-date/${today}`)
  return response.data
}

// 获取指定日期范围的预设计划
export const getSchedulesByDateRange = async (startDate: string, endDate: string): Promise<PresetSchedule[]> => {
  const response = await get<PresetSchedule[]>(`/preset-schedules/by-date-range/?start_date=${startDate}&end_date=${endDate}`)
  return response.data
}
import { request } from './request'

/**
 * 运动检测API
 */

// 启动运动检测
export const startMotionDetection = (videoSourceId: number, config: any) => {
  return request({
    url: '/api/motion-detection/start',
    method: 'post',
    data: {
      video_source_id: videoSourceId,
      rtsp_url: config.rtsp_url,
      sensitivity: config.sensitivity || 50,
      min_area: config.min_area || 500,
      enable_direction: config.enable_direction || true,
      rois: config.rois || []
    }
  })
}

// 停止运动检测
export const stopMotionDetection = (videoSourceId: number) => {
  return request({
    url: `/api/motion-detection/stop/${videoSourceId}`,
    method: 'post'
  })
}

// 获取检测状态
export const getDetectionStatus = (videoSourceId: number) => {
  return request({
    url: `/api/motion-detection/status/${videoSourceId}`,
    method: 'get'
  })
}

// 更新检测配置
export const updateDetectionConfig = (videoSourceId: number, config: any) => {
  return request({
    url: `/api/motion-detection/config/${videoSourceId}`,
    method: 'put',
    data: config
  })
}

// 保存ROI配置
export const saveRoiConfig = (videoSourceId: number, rois: any[]) => {
  return request({
    url: `/api/motion-detection/roi/${videoSourceId}`,
    method: 'post',
    data: rois
  })
}

// 获取ROI配置
export const getRoiConfig = (videoSourceId: number) => {
  return request({
    url: `/api/motion-detection/roi/${videoSourceId}`,
    method: 'get'
  })
}

// 获取检测统计信息
export const getDetectionStatistics = (videoSourceId: number) => {
  return request({
    url: `/api/motion-detection/statistics/${videoSourceId}`,
    method: 'get'
  })
} 
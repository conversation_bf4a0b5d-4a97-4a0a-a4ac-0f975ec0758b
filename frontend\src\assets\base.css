/* color palette from <https://github.com/vuejs/theme> */
:root {
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #f8f8f8;
  --vt-c-white-mute: #f2f2f2;

  --vt-c-black: #181818;
  --vt-c-black-soft: #222222;
  --vt-c-black-mute: #282828;

  --vt-c-indigo: #2c3e50;

  --vt-c-divider-light-1: rgba(60, 60, 60, 0.29);
  --vt-c-divider-light-2: rgba(60, 60, 60, 0.12);
  --vt-c-divider-dark-1: rgba(84, 84, 84, 0.65);
  --vt-c-divider-dark-2: rgba(84, 84, 84, 0.48);

  --vt-c-text-light-1: var(--vt-c-indigo);
  --vt-c-text-light-2: rgba(60, 60, 60, 0.66);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: rgba(235, 235, 235, 0.64);
}

/* semantic color variables for this project */
:root {
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);

  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);

  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);

  --section-gap: 160px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-black);
    --color-background-soft: var(--vt-c-black-soft);
    --color-background-mute: var(--vt-c-black-mute);

    --color-border: var(--vt-c-divider-dark-2);
    --color-border-hover: var(--vt-c-divider-dark-1);

    --color-heading: var(--vt-c-text-dark-1);
    --color-text: var(--vt-c-text-dark-2);
  }
}

/* 主题色变量定义 */
:root {
  /* 亮色主题变量 */
  --primary-color: rgb(191, 46, 41);
  --primary-color-light: rgb(217, 83, 79);
  --primary-color-dark: rgb(165, 40, 35);
  
  /* RGB格式的主题色，用于rgba()函数 */
  --primary-color-rgb: 191, 46, 41;
  --primary-color-light-rgb: 217, 83, 79;
  --primary-color-dark-rgb: 165, 40, 35;
  
  --bg-color: rgb(255, 255, 253);
  --bg-color-soft: rgb(248, 248, 246);
  --bg-color-mute: rgb(242, 242, 240);
  --bg-color-hover: rgb(235, 235, 233);
  --bg-color-rgb: 255, 255, 253;
  
  --text-color: rgb(23, 23, 21);
  --text-color-soft: rgb(60, 60, 58);
  --text-color-mute: rgb(100, 100, 98);
  --text-color-inverse: #ffffff;
  
  --border-color: rgba(23, 23, 21, 0.12);
  --border-color-hover: rgba(23, 23, 21, 0.29);
  
  --shadow-color: rgba(0, 0, 0, 0.1);
  --shadow-color-hover: rgba(0, 0, 0, 0.15);
  
  --text-color-lighter: rgb(149, 165, 166);
  
  --success-color: #67c23a;
  --success-color-light: #85ce61;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --danger-color-light: #f78989;
  --error-color: #f56c6c;
  --info-color: #909399;
  
  /* RGB格式的状态颜色，用于rgba()函数 */
  --success-color-rgb: 103, 194, 58;
  --warning-color-rgb: 230, 162, 60;
  --danger-color-rgb: 245, 108, 108;
  --error-color-rgb: 245, 108, 108;
  --info-color-rgb: 144, 147, 153;
  
  /* RGB格式的状态颜色，用于rgba()函数 */
  --success-color-rgb: 103, 194, 58;
  --warning-color-rgb: 230, 162, 60;
  --danger-color-rgb: 245, 108, 108;
  --error-color-rgb: 245, 108, 108;
  --info-color-rgb: 144, 147, 153;
  
  --section-gap: 40px;
}

/* 暗色主题变量 */
.dark-theme {
  --primary-color: rgb(191, 46, 41);
  --primary-color-light: rgb(217, 83, 79);
  --primary-color-dark: rgb(165, 40, 35);
  
  /* RGB格式的主题色，用于rgba()函数 */
  --primary-color-rgb: 191, 46, 41;
  --primary-color-light-rgb: 217, 83, 79;
  --primary-color-dark-rgb: 165, 40, 35;
  
  --bg-color: rgb(23, 23, 21);
  --bg-color-soft: rgb(35, 35, 33);
  --bg-color-mute: rgb(45, 45, 43);
  --bg-color-hover: rgb(55, 55, 53);
  --bg-color-rgb: 23, 23, 21;
  
  --text-color: rgb(255, 255, 253);
  --text-color-soft: rgb(220, 220, 218);
  --text-color-mute: rgb(180, 180, 178);
  --text-color-inverse: #ffffff;
  
  --border-color: rgba(255, 255, 253, 0.12);
  --border-color-hover: rgba(255, 255, 253, 0.29);
  
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-color-hover: rgba(0, 0, 0, 0.4);
  
  --text-color-lighter: rgb(149, 165, 166);
  
  --success-color: #67c23a;
  --success-color-light: #85ce61;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --danger-color-light: #f78989;
  --error-color: #f56c6c;
  --info-color: #909399;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
  color: var(--text-color);
  background: var(--bg-color);
  transition: color 0.3s, background-color 0.3s;
  line-height: 1.6;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: var(--primary-color-light);
}

/* 常用辅助类 */
.text-primary {
  color: var(--primary-color);
}

.bg-primary {
  background-color: var(--primary-color);
  color: white;
}

.border-primary {
  border-color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-info {
  color: var(--info-color);
}

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel


# 检测模板基础Schema
class DetectionTemplateBase(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = "enabled"


# 创建检测模板Schema
class DetectionTemplateCreate(DetectionTemplateBase):
    name: str
    description: Optional[str] = None


# 更新检测模板Schema
class DetectionTemplateUpdate(DetectionTemplateBase):
    pass


# 检测模板数据库基础Schema
class DetectionTemplateInDBBase(DetectionTemplateBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 检测模板Schema（用于API响应）
class DetectionTemplate(DetectionTemplateInDBBase):
    pass


# 模板-压铸机关联基础Schema
class TemplateDieCasterBase(BaseModel):
    template_id: int
    die_caster_id: int


# 创建模板-压铸机关联Schema
class TemplateDieCasterCreate(TemplateDieCasterBase):
    pass


# 模板-压铸机关联Schema（用于API响应）
class TemplateDieCaster(TemplateDieCasterBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


# 带关联压铸机的检测模板Schema
class DetectionTemplateWithDieCasters(DetectionTemplate):
    die_caster_ids: List[int] = []


# 批量关联压铸机Schema
class BatchAssociateDieCasters(BaseModel):
    die_caster_ids: List[int]
from datetime import datetime
from typing import Optional, Dict, Any

from pydantic import BaseModel


# 系统设置基础属性
class SystemSettingsBase(BaseModel):
    setting_key: Optional[str] = None
    setting_value: Optional[str] = None
    setting_type: Optional[str] = "string"
    description: Optional[str] = None
    category: Optional[str] = "basic"


# 创建系统设置
class SystemSettingsCreate(SystemSettingsBase):
    setting_key: str
    setting_value: str


# 更新系统设置
class SystemSettingsUpdate(SystemSettingsBase):
    setting_value: Optional[str] = None


# API响应中包含的属性
class SystemSettingsInDBBase(SystemSettingsBase):
    id: int
    setting_key: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# 返回给API的系统设置信息
class SystemSettings(SystemSettingsInDBBase):
    pass


# 基本设置请求模型
class BasicSettingsRequest(BaseModel):
    system_name: Optional[str] = None
    system_description: Optional[str] = None
    admin_email: Optional[str] = None
    log_retention_days: Optional[int] = None


# 存储设置请求模型
class StorageSettingsRequest(BaseModel):
    video_storage_path: Optional[str] = None
    detection_image_path: Optional[str] = None
    auto_cleanup: Optional[bool] = None
    file_retention_days: Optional[int] = None
    disk_space_threshold: Optional[int] = None


# 基本设置响应模型
class BasicSettingsResponse(BaseModel):
    system_name: str
    system_description: str
    admin_email: str
    log_retention_days: int


# 存储设置响应模型
class StorageSettingsResponse(BaseModel):
    video_storage_path: str
    detection_image_path: str
    auto_cleanup: bool
    file_retention_days: int
    disk_space_threshold: int
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.models import PresetSchedule, DetectionTemplate

def check_preset_schedules():
    """检查预设检测计划数据"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # 查询所有预设检测计划
        schedules = db.query(PresetSchedule).all()
        print(f"数据库中共有 {len(schedules)} 个预设检测计划:")
        
        for schedule in schedules:
            print(f"ID: {schedule.id}, 名称: {schedule.name}, 日期: {schedule.date}, 时间: {schedule.start_time}-{schedule.end_time}, 状态: {schedule.status}, 启用: {schedule.is_enabled}")
        
        # 查询检测模板
        templates = db.query(DetectionTemplate).all()
        print(f"\n数据库中共有 {len(templates)} 个检测模板:")
        
        for template in templates:
            print(f"ID: {template.id}, 名称: {template.name}, 状态: {template.status}")
        
        db.close()
        
    except Exception as e:
        print(f"检查预设检测计划失败: {str(e)}")
        raise

if __name__ == "__main__":
    check_preset_schedules()
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>WebSDK快速测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .video-container { width: 640px; height: 360px; border: 2px solid #ccc; margin: 20px 0; background: #000; }
        .controls { margin: 20px 0; }
        .controls input, .controls button { margin: 5px; padding: 8px 12px; }
        .btn { background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #6c757d; cursor: not-allowed; }
        .log { height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f8f9fa; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebSDK快速测试工具</h1>
        <p>用于快速验证WebSDK基础功能和设备兼容性</p>
        
        <div id="status" class="status info">
            ⏳ 正在初始化WebSDK...
        </div>
        
        <div class="controls">
            <h3>📡 设备连接测试</h3>
            <input type="text" id="ip" placeholder="设备IP" value="************">
            <input type="text" id="port" placeholder="端口" value="80">
            <input type="text" id="username" placeholder="用户名" value="admin">
            <input type="password" id="password" placeholder="密码" value="admin123">
            <br>
            <button class="btn" onclick="quickTest()">🚀 一键测试</button>
            <button class="btn" onclick="testLogin()">🔑 仅测试登录</button>
            <button class="btn" onclick="clearLog()">🗑️ 清空日志</button>
        </div>
        
        <div id="videoContainer" class="video-container"></div>
        
        <div class="log" id="logContainer"></div>
    </div>

    <!-- WebSDK脚本 -->
    <script src="/websdk/codebase/jsPlugin/jquery.min.js"></script>
    <script src="/websdk/codebase/encryption/AES.js"></script>
    <script src="/websdk/codebase/encryption/cryptico.min.js"></script>
    <script src="/websdk/codebase/encryption/crypto-3.1.2.min.js"></script>
    <script>
        if (typeof CryptoJS !== 'undefined' && CryptoJS.MD5) {
            window.MD5 = function(message) {
                return CryptoJS.MD5(message).toString();
            };
        }
    </script>
    <script src="/websdk/codebase/webVideoCtrl.js"></script>
    <script src="/websdk/codebase/jsPlugin/jsPlugin-3.0.0.min.js"></script>

    <script>
        let g_iWndIndex = 0;
        let currentDevice = null;
        let isInitialized = false;
        let testResults = {
            websdkLoaded: false,
            jqueryLoaded: false,
            browserSupport: false,
            pluginInit: false,
            deviceLogin: false,
            channelInfo: false,
            websocketSupport: false
        };

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.innerHTML = message;
        }

        function addLog(message, type = 'info') {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const logItem = document.createElement('div');
            logItem.style.marginBottom = '3px';
            logItem.innerHTML = `<span style="color: #666;">[${timeStr}]</span> ${message}`;
            logContainer.appendChild(logItem);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }

        function checkEnvironment() {
            addLog('🔍 开始环境检查...');
            
            // 检查WebSDK
            if (window.WebVideoCtrl) {
                testResults.websdkLoaded = true;
                addLog('✅ WebSDK已加载');
            } else {
                addLog('❌ WebSDK未加载');
                return false;
            }

            // 检查jQuery
            if (typeof $ !== 'undefined') {
                testResults.jqueryLoaded = true;
                addLog('✅ jQuery已加载');
            } else {
                addLog('❌ jQuery未加载');
            }

            // 检查浏览器支持
            const iRet = WebVideoCtrl.I_SupportNoPlugin();
            if (iRet) {
                testResults.browserSupport = true;
                addLog('✅ 浏览器支持无插件模式');
            } else {
                addLog('❌ 浏览器不支持无插件模式');
                return false;
            }

            return true;
        }

        function initWebSDK() {
            if (!checkEnvironment()) {
                updateStatus('❌ 环境检查失败', 'error');
                return false;
            }

            addLog('🔧 初始化WebSDK插件...');
            
            WebVideoCtrl.I_InitPlugin("100%", "100%", {
                bWndFull: true,
                iPackageType: 2,
                iWndowType: 1,
                bNoPlugin: true,
                cbSelWnd: function(xmlDoc) {
                    g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
                    addLog(`🎯 选择窗口: ${g_iWndIndex}`);
                },
                cbInitPluginComplete: function() {
                    addLog('✅ WebSDK插件初始化完成');
                    WebVideoCtrl.I_InsertOBJECTPlugin("videoContainer");
                    addLog('✅ 插件嵌入完成');
                    testResults.pluginInit = true;
                    isInitialized = true;
                    updateStatus('✅ WebSDK初始化成功，可以开始测试', 'success');
                },
                cbPluginErrorHandler: function(iWndIndex, iErrorCode, oError) {
                    addLog(`❌ 插件错误: 窗口${iWndIndex}, 错误码${iErrorCode}`);
                }
            });

            return true;
        }

        function testLogin() {
            if (!isInitialized) {
                addLog('❌ WebSDK未初始化');
                return;
            }

            const ip = document.getElementById('ip').value;
            const port = document.getElementById('port').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!ip || !port) {
                addLog('❌ 请填写IP和端口');
                return;
            }

            const deviceIdentify = `${ip}_${port}`;
            addLog(`🔑 正在登录设备: ${ip}:${port}`);

            WebVideoCtrl.I_Login(ip, 1, parseInt(port), username, password, {
                success: function(xmlDoc) {
                    addLog(`✅ 设备登录成功: ${ip}`);
                    testResults.deviceLogin = true;
                    currentDevice = { ip, port, username, password, deviceIdentify };
                    updateStatus('✅ 设备登录成功', 'success');
                },
                error: function(status, xmlDoc) {
                    addLog(`❌ 设备登录失败: ${status}`);
                    updateStatus('❌ 设备登录失败', 'error');
                }
            });
        }

        function quickTest() {
            addLog('🚀 开始一键测试...');
            updateStatus('🚀 正在执行一键测试...', 'info');
            
            // 先测试登录
            testLogin();
            
            // 延迟测试其他功能
            setTimeout(() => {
                if (testResults.deviceLogin) {
                    testChannelInfo();
                    setTimeout(() => {
                        if (testResults.channelInfo) {
                            testWebSocketSupport();
                        }
                    }, 2000);
                }
            }, 2000);
        }

        function testChannelInfo() {
            if (!currentDevice) {
                addLog('❌ 请先登录设备');
                return;
            }

            addLog('📡 正在获取通道信息...');
            
            // 测试模拟通道
            WebVideoCtrl.I_GetAnalogChannelInfo(currentDevice.deviceIdentify, {
                async: false,
                success: function(xmlDoc) {
                    const channels = $(xmlDoc).find("VideoInputChannel");
                    addLog(`✅ 获取到 ${channels.length} 个模拟通道`);
                    if (channels.length > 0) {
                        testResults.channelInfo = true;
                    }
                },
                error: function(status, xmlDoc) {
                    addLog(`⚠️ 获取模拟通道失败: ${status}`);
                }
            });

            // 测试数字通道
            WebVideoCtrl.I_GetDigitalChannelInfo(currentDevice.deviceIdentify, {
                async: false,
                success: function(xmlDoc) {
                    const channels = $(xmlDoc).find("InputProxyChannelStatus");
                    addLog(`✅ 获取到 ${channels.length} 个数字通道`);
                    if (channels.length > 0) {
                        testResults.channelInfo = true;
                    }
                },
                error: function(status, xmlDoc) {
                    addLog(`⚠️ 获取数字通道失败: ${status}`);
                }
            });
        }

        function testWebSocketSupport() {
            if (!currentDevice) {
                addLog('❌ 请先登录设备');
                return;
            }

            addLog('🌐 测试WebSocket取流支持...');
            
            WebVideoCtrl.I_StartRealPlay(currentDevice.deviceIdentify, {
                iRtspPort: 554,
                iStreamType: 1,
                iChannelID: 1,
                bZeroChannel: false,
                bProxy: false,
                success: function() {
                    addLog('✅ WebSocket直连取流成功！');
                    testResults.websocketSupport = true;
                    updateStatus('🎉 所有测试通过！设备完全兼容', 'success');
                    setTimeout(() => {
                        WebVideoCtrl.I_Stop();
                        addLog('⏹️ 测试预览已停止');
                    }, 3000);
                },
                error: function(status, xmlDoc) {
                    if (status === 403) {
                        addLog('⚠️ 设备不支持WebSocket直连，尝试代理模式...');
                        // 尝试代理模式
                        WebVideoCtrl.I_StartRealPlay(currentDevice.deviceIdentify, {
                            iRtspPort: 554,
                            iStreamType: 1,
                            iChannelID: 1,
                            bZeroChannel: false,
                            bProxy: true,
                            success: function() {
                                addLog('✅ WebSocket代理模式取流成功！');
                                testResults.websocketSupport = true;
                                updateStatus('✅ 设备支持代理模式取流', 'success');
                                setTimeout(() => {
                                    WebVideoCtrl.I_Stop();
                                    addLog('⏹️ 测试预览已停止');
                                }, 3000);
                            },
                            error: function(status2, xmlDoc2) {
                                addLog(`❌ 代理模式也失败: ${status2}`);
                                updateStatus('❌ 设备不支持WebSocket取流', 'error');
                            }
                        });
                    } else {
                        addLog(`❌ WebSocket测试失败: ${status}`);
                        updateStatus('❌ WebSocket测试失败', 'error');
                    }
                }
            });
        }

        // 页面加载完成后初始化
        window.onload = function() {
            addLog('🌟 WebSDK快速测试工具启动');
            setTimeout(() => {
                if (initWebSDK()) {
                    addLog('✅ 初始化成功，可以开始测试');
                } else {
                    updateStatus('❌ 初始化失败', 'error');
                }
            }, 500);
        };
    </script>
</body>
</html>

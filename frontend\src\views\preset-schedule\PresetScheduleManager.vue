<template>
  <div class="preset-schedule-manager">
    <!-- 头部操作栏 -->
    <div class="header-actions">
      <div class="title-section">
        <h2>预设检测计划</h2>
        <p class="subtitle">
          管理自动化检测模板切换计划
          <el-tag type="success" size="small" style="margin-left: 10px;">
            <el-icon><Timer /></el-icon>
            自动检查已启用
          </el-tag>
        </p>
      </div>
      <div class="action-buttons">
        <el-button 
          type="primary" 
          :icon="Plus" 
          @click="showCreateDialog = true"
        >
          新建计划
        </el-button>
        <el-button 
          type="success" 
          :icon="Timer" 
          @click="manualCheck"
          size="default"
        >
          立即检查
        </el-button>
        <el-button 
          :icon="Refresh" 
          @click="loadSchedules"
          :loading="loading"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ schedules.length }}</div>
          <div class="stat-label">总计划数</div>
        </div>
        <el-icon class="stat-icon"><Calendar /></el-icon>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ activeSchedules.length }}</div>
          <div class="stat-label">活跃计划</div>
        </div>
        <el-icon class="stat-icon"><Timer /></el-icon>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ todaySchedules.length }}</div>
          <div class="stat-label">今日计划</div>
        </div>
        <el-icon class="stat-icon"><Clock /></el-icon>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ upcomingSchedules.length }}</div>
          <div class="stat-label">待执行</div>
        </div>
        <el-icon class="stat-icon"><Bell /></el-icon>
      </el-card>
    </div>

    <!-- 今日计划快速预览 -->
    <el-card v-if="todaySchedules.length > 0" class="today-schedules">
      <template #header>
        <div class="card-header">
          <span>今日计划</span>
          <el-tag type="info" size="small">{{ todaySchedules.length }} 个计划</el-tag>
        </div>
      </template>
      <div class="today-schedule-list">
        <div 
          v-for="schedule in todaySchedules" 
          :key="schedule.id"
          class="today-schedule-item"
          :class="{
            'active': schedule.status === 'active',
            'pending': schedule.status === 'pending',
            'completed': schedule.status === 'completed'
          }"
        >
          <div class="schedule-time">
            {{ schedule.startTime }} - {{ schedule.endTime }}
          </div>
          <div class="schedule-info">
            <div class="schedule-name">{{ schedule.name }}</div>
            <div class="schedule-template">{{ schedule.templateName }}</div>
          </div>
          <div class="schedule-status">
            <el-tag 
              :type="getStatusTagType(schedule.status)" 
              size="small"
            >
              {{ getStatusText(schedule.status) }}
            </el-tag>
          </div>
          <div class="schedule-actions">
            <el-button 
              v-if="schedule.status === 'pending'"
              type="primary" 
              size="small" 
              @click="executeSchedule(schedule.id)"
            >
              立即执行
            </el-button>
            <el-button 
              v-if="schedule.status === 'active'"
              type="danger" 
              size="small" 
              @click="stopSchedule(schedule.id)"
            >
              停止
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 计划列表 -->
    <el-card class="schedule-list">
      <template #header>
        <div class="card-header">
          <span>所有计划</span>
          <div class="header-filters">
            <el-select 
              v-model="statusFilter" 
              placeholder="状态筛选" 
              clearable
              size="small"
              style="width: 120px"
            >
              <el-option label="待执行" value="pending" />
              <el-option label="执行中" value="active" />
              <el-option label="已完成" value="completed" />
              <el-option label="已禁用" value="disabled" />
            </el-select>
            <el-date-picker
              v-model="dateFilter"
              type="date"
              placeholder="日期筛选"
              size="small"
              clearable
              style="width: 140px"
            />
          </div>
        </div>
      </template>
      
      <el-table 
        :data="filteredSchedules" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="name" label="计划名称" min-width="150">
          <template #default="{ row }">
            <div class="schedule-name-cell">
              <span class="name">{{ row.name }}</span>
              <span v-if="row.description" class="description">{{ row.description }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="date" label="执行日期" width="120" />
        
        <el-table-column label="执行时间" width="140">
          <template #default="{ row }">
            {{ row.startTime }} - {{ row.endTime }}
          </template>
        </el-table-column>
        
        <el-table-column prop="templateName" label="检测模板" min-width="120" />
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusTagType(row.status)" 
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="启用状态" width="100">
          <template #default="{ row }">
            <el-switch 
              v-model="row.isEnabled"
              @change="toggleSchedule(row.id, row.isEnabled)"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="table-actions">
              <el-button 
                v-if="row.status === 'pending' && row.isEnabled"
                type="primary" 
                size="small" 
                @click="showExecuteConfirm(row)"
              >
                执行
              </el-button>
              <el-button 
                v-if="row.status === 'active'"
                type="danger" 
                size="small" 
                @click="stopSchedule(row.id)"
              >
                停止
              </el-button>
              <el-button 
                type="info" 
                size="small" 
                @click="editSchedule(row)"
              >
                编辑
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                @click="deleteSchedule(row.id)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingSchedule ? '编辑预设计划' : '新建预设计划'"
      width="900px"
      @close="resetForm"
    >
      <PresetScheduleForm 
        :schedule="editingSchedule"
        @submit="handleFormSubmit"
        @cancel="showCreateDialog = false"
      />
    </el-dialog>

    <!-- 执行确认弹窗 -->
    <ExecuteConfirmDialog
      v-model="showExecuteDialog"
      :schedule="executingSchedule"
      :loading="executeLoading"
      @confirm="handleExecuteConfirm"
      @cancel="handleExecuteCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Plus, Refresh, Calendar, Timer, Clock, Bell } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { usePresetSchedule } from '@/composables/usePresetSchedule'
import PresetScheduleForm from './components/PresetScheduleForm.vue'
import ExecuteConfirmDialog from '@/components/ExecuteConfirmDialog.vue'
import type { PresetSchedule } from '@/types/preset-schedule'

// 使用预设计划组合式函数
const {
  schedules,
  loading,
  todaySchedules,
  activeSchedules,
  upcomingSchedules,
  loadSchedules,
  createSchedule,
  updateSchedule,
  deleteSchedule,
  toggleSchedule,
  executeSchedule,
  stopSchedule,
  checkAndExecuteSchedules
} = usePresetSchedule()

// 响应式数据
const showCreateDialog = ref(false)
const editingSchedule = ref<PresetSchedule | null>(null)
const statusFilter = ref('')
const dateFilter = ref('')
const showExecuteDialog = ref(false)
const executingSchedule = ref<PresetSchedule | null>(null)
const executeLoading = ref(false)

// 计算属性 - 过滤后的计划列表
const filteredSchedules = computed(() => {
  let filtered = schedules.value
  
  if (statusFilter.value) {
    filtered = filtered.filter(schedule => {
      if (statusFilter.value === 'disabled') {
        return !schedule.isEnabled
      }
      return schedule.status === statusFilter.value
    })
  }
  
  if (dateFilter.value) {
    const filterDate = new Date(dateFilter.value).toISOString().split('T')[0]
    filtered = filtered.filter(schedule => schedule.date === filterDate)
  }
  
  return filtered.sort((a, b) => {
    const dateCompare = b.date.localeCompare(a.date)
    if (dateCompare !== 0) return dateCompare
    return a.startTime.localeCompare(b.startTime)
  })
})

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'pending': return 'warning'
    case 'active': return 'success'
    case 'running': return 'success'
    case 'completed': return 'info'
    case 'stopped': return 'info'
    case 'failed': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待执行'
    case 'active': return '执行中'
    case 'running': return '执行中'
    case 'completed': return '已完成'
    case 'stopped': return '已停止'
    case 'failed': return '执行失败'
    default: return '未知'
  }
}

// 编辑计划
const editSchedule = (schedule: PresetSchedule) => {
  editingSchedule.value = { ...schedule }
  showCreateDialog.value = true
}

// 处理表单提交
const handleFormSubmit = async (formData: any) => {
  let success = false
  
  if (editingSchedule.value) {
    success = await updateSchedule(editingSchedule.value.id, formData)
  } else {
    success = await createSchedule(formData)
  }
  
  if (success) {
    showCreateDialog.value = false
    resetForm()
  }
}

// 重置表单
const resetForm = () => {
  editingSchedule.value = null
}

// 显示执行确认弹窗
const showExecuteConfirm = (schedule: PresetSchedule) => {
  executingSchedule.value = schedule
  showExecuteDialog.value = true
}

// 处理执行确认
const handleExecuteConfirm = async (scheduleId: number) => {
  executeLoading.value = true
  try {
    const success = await executeSchedule(scheduleId)
    if (success) {
      showExecuteDialog.value = false
      executingSchedule.value = null
      ElMessage.success('计划执行成功')
    }
  } catch (error) {
    console.error('执行失败:', error)
    ElMessage.error('计划执行失败')
  } finally {
    executeLoading.value = false
  }
}

// 处理执行取消
const handleExecuteCancel = () => {
  showExecuteDialog.value = false
  executingSchedule.value = null
}

// 手动检查预设计划
const manualCheck = async () => {
  try {
    await checkAndExecuteSchedules()
    ElMessage.success('预设计划检查完成')
  } catch (error) {
    console.error('手动检查失败:', error)
    ElMessage.error('预设计划检查失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadSchedules()
})
</script>

<style scoped>
.preset-schedule-manager {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: 100vh;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.title-section h2 {
  margin: 0 0 5px 0;
  color: var(--el-text-color-primary);
  font-size: 24px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: var(--el-box-shadow-light);
}

.stat-card :deep(.el-card__body) {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: var(--el-color-primary);
  line-height: 1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.stat-icon {
  font-size: 32px;
  color: var(--el-color-primary);
  opacity: 0.3;
}

.today-schedules {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-filters {
  display: flex;
  gap: 10px;
}

.today-schedule-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.today-schedule-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  border-left: 4px solid var(--el-color-info);
  transition: all 0.3s ease;
}

.today-schedule-item.pending {
  border-left-color: var(--el-color-warning);
  background: var(--el-color-warning-light-9);
}

.today-schedule-item.active {
  border-left-color: var(--el-color-success);
  background: var(--el-color-success-light-9);
}

.today-schedule-item.completed {
  border-left-color: var(--el-color-info);
  background: var(--el-fill-color-lighter);
}

.schedule-time {
  min-width: 120px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.schedule-info {
  flex: 1;
  margin-left: 16px;
}

.schedule-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.schedule-template {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.schedule-status {
  margin: 0 16px;
}

.schedule-actions {
  display: flex;
  gap: 8px;
}

.schedule-list {
  box-shadow: var(--el-box-shadow-light);
}

.schedule-name-cell .name {
  display: block;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.schedule-name-cell .description {
  display: block;
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 2px;
}

.table-actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.table-actions .el-button {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preset-schedule-manager {
    padding: 10px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 15px;
  }
  
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .today-schedule-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .schedule-time {
    min-width: auto;
  }
  
  .schedule-info {
    margin-left: 0;
    width: 100%;
  }
  
  .schedule-actions {
    margin: 0;
    width: 100%;
    justify-content: flex-end;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .today-schedule-item {
    background: var(--el-fill-color-dark);
  }
  
  .today-schedule-item.pending {
    background: rgba(var(--el-color-warning-rgb), 0.1);
  }
  
  .today-schedule-item.active {
    background: rgba(var(--el-color-success-rgb), 0.1);
  }
}
</style>
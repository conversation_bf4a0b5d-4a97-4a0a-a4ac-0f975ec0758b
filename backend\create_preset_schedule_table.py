#!/usr/bin/env python3
"""
创建PresetSchedule表的数据库迁移脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from app.core.config import settings
from app.models.models import Base, PresetSchedule

def create_preset_schedule_table():
    """创建PresetSchedule表"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.DATABASE_URL)
        
        # 创建PresetSchedule表
        PresetSchedule.__table__.create(engine, checkfirst=True)
        
        print("PresetSchedule表创建成功！")
        
    except Exception as e:
        print(f"创建PresetSchedule表失败: {str(e)}")
        raise

if __name__ == "__main__":
    create_preset_schedule_table()
2025-07-10 18:32:30,841 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/Security/sessionLogin/capabilities
2025-07-10 18:32:30,870 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:32:30,896 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/Security/sessionLogin/capabilities
2025-07-10 18:32:30,916 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:32:30,932 - app.api.endpoints.websdk_proxy - INFO - 代理请求: POST http://192.168.1.64:80/ISAPI/Security/sessionLogin
2025-07-10 18:32:30,948 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:32:30,958 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/capabilities
2025-07-10 18:32:31,130 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:32:31,142 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/Security/capabilities
2025-07-10 18:32:31,154 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:32:31,255 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/Video/inputs/channels
2025-07-10 18:32:31,271 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:32:31,286 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/ContentMgmt/InputProxy/channels
2025-07-10 18:32:31,297 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 403
2025-07-10 18:32:32,144 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/Network/PPPoE/1/status
2025-07-10 18:32:32,154 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:32:32,163 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/Network/Bond
2025-07-10 18:32:32,175 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 404
2025-07-10 18:32:32,183 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/Network/interfaces
2025-07-10 18:32:32,203 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:32:32,213 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/Security/adminAccesses
2025-07-10 18:32:32,227 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:32:32,245 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/Security/token
2025-07-10 18:32:32,268 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:32:40,297 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:32:41,626 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:32:42,941 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:32:44,256 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:32:45,569 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:32:46,893 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:32:48,214 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:32:49,529 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:32:51,453 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:32:53,456 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:32:55,458 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:32:57,468 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:32:59,454 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:01,154 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/deviceInfo
2025-07-10 18:33:01,186 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:33:01,455 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:03,454 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:05,477 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:07,453 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:09,456 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:11,460 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:13,456 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:15,467 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:17,453 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:19,454 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:21,453 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:23,453 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:25,461 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:27,462 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:29,463 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:31,166 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/deviceInfo
2025-07-10 18:33:31,185 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:33:31,473 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:33,459 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:35,460 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:37,457 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:39,455 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:41,465 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:43,461 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:45,473 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:47,465 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:49,471 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:51,454 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:53,454 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:55,461 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:57,461 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:33:58,783 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:00,094 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:01,141 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/deviceInfo
2025-07-10 18:34:01,160 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:34:01,408 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:02,721 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:04,054 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:05,384 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:06,704 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:08,053 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:09,376 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:11,462 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:13,451 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:15,479 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:17,458 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:19,461 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:21,470 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:22,786 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:26,093 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:34:26,093 - app.services.gpu_accelerator - INFO - 未检测到CUDA支持的GPU设备，将使用CPU处理
2025-07-10 18:34:26,094 - app.services.detection_manager - INFO - 检测管理器初始化完成，GPU加速: True
2025-07-10 18:34:26,098 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:26,172 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:27,173 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:28,163 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:29,185 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:30,164 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:31,175 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:31,193 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/deviceInfo
2025-07-10 18:34:31,212 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:34:32,163 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:33,163 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:34,162 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:35,161 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:36,160 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:37,170 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:38,158 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:39,156 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:40,173 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:41,171 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:42,175 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:43,169 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:44,161 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:45,161 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:46,166 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:47,165 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:48,161 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:49,158 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:50,167 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:51,165 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:52,163 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:53,169 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:54,161 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:55,184 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:56,165 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:57,167 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:34:58,165 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:00,477 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:35:00,479 - app.services.gpu_accelerator - INFO - 未检测到CUDA支持的GPU设备，将使用CPU处理
2025-07-10 18:35:00,480 - app.services.detection_manager - INFO - 检测管理器初始化完成，GPU加速: True
2025-07-10 18:35:00,482 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:01,168 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:01,175 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/deviceInfo
2025-07-10 18:35:01,196 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:35:02,173 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:03,165 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:04,162 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:05,180 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:06,168 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:07,174 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:08,163 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:09,171 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:10,168 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:11,170 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:12,160 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:13,172 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:14,166 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:15,159 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:16,168 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:17,174 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:18,173 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:19,159 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:20,163 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:21,171 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:22,158 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:35:31,152 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/deviceInfo
2025-07-10 18:35:31,169 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:35:40,165 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:36:40,165 - app.services.websocket_manager - ERROR - Error sending personal message: 'str' object has no attribute 'send_text'
2025-07-10 18:36:40,182 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/deviceInfo
2025-07-10 18:36:40,203 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:36:40,235 - app.services.websocket_manager - INFO - WebSocket client disconnected: client_u7o3hbmv7_1752143559989
2025-07-10 18:36:42,125 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:36:42,127 - app.services.gpu_accelerator - INFO - 未检测到CUDA支持的GPU设备，将使用CPU处理
2025-07-10 18:36:42,127 - app.services.detection_manager - INFO - 检测管理器初始化完成，GPU加速: True
2025-07-10 18:36:44,370 - app.services.websocket_manager - INFO - WebSocket client disconnected: client_u7o3hbmv7_1752143559989
2025-07-10 18:36:50,453 - app.services.websocket_manager - INFO - WebSocket client connected: client_u7o3hbmv7_1752143559989
2025-07-10 18:36:50,455 - app.services.gpu_accelerator - INFO - 未检测到CUDA支持的GPU设备，将使用CPU处理
2025-07-10 18:36:50,456 - app.services.detection_manager - INFO - 检测管理器初始化完成，GPU加速: True
2025-07-10 18:37:01,142 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/deviceInfo
2025-07-10 18:37:01,166 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:37:04,078 - app.services.websocket_manager - INFO - WebSocket client disconnected: client_u7o3hbmv7_1752143559989
2025-07-10 18:37:08,360 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/Security/sessionLogin/capabilities
2025-07-10 18:37:08,398 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:37:08,424 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/Security/sessionLogin/capabilities
2025-07-10 18:37:08,442 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:37:08,458 - app.api.endpoints.websdk_proxy - INFO - 代理请求: POST http://192.168.1.64:80/ISAPI/Security/sessionLogin
2025-07-10 18:37:08,477 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:37:08,490 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/capabilities
2025-07-10 18:37:08,675 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:37:08,687 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/Security/capabilities
2025-07-10 18:37:08,716 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:37:08,787 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/Video/inputs/channels
2025-07-10 18:37:08,804 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:37:08,817 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/ContentMgmt/InputProxy/channels
2025-07-10 18:37:08,830 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 403
2025-07-10 18:37:09,687 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/Network/PPPoE/1/status
2025-07-10 18:37:09,701 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:37:09,713 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/Network/Bond
2025-07-10 18:37:09,723 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 404
2025-07-10 18:37:09,735 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/Network/interfaces
2025-07-10 18:37:09,749 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:37:09,761 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/Security/adminAccesses
2025-07-10 18:37:09,773 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:37:09,792 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/Security/token
2025-07-10 18:37:09,805 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:37:16,479 - app.services.websocket_manager - INFO - WebSocket client connected: client_465838voi_1752143836163
2025-07-10 18:37:16,480 - app.services.detection_manager - INFO - 检测管理器初始化完成，GPU加速: True
2025-07-10 18:37:39,174 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://192.168.1.64:80/ISAPI/System/deviceInfo
2025-07-10 18:37:39,191 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-10 18:38:02,904 - app.services.websocket_manager - INFO - WebSocket client disconnected: client_465838voi_1752143836163

/* 综合功能测试页面样式 */
.comprehensive-test {
  padding: 20px;
  background-color: var(--bg-color);
  min-height: 100vh;
}

/* 配置区域 */
.config-section {
  margin-bottom: 20px;
}

.config-card {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-color-mute);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-card .el-card__header {
  background-color: var(--bg-color-soft);
  border-bottom: 1px solid var(--border-color);
  padding: 16px 20px;
}

.config-card .el-card__body {
  padding: 20px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--text-color);
}

.test-controls {
  display: flex;
  gap: 8px;
}

.test-controls .el-button {
  border-color: var(--border-color);
  color: var(--text-color);
  background-color: var(--bg-color);
  transition: all 0.3s ease;
}

.test-controls .el-button:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background-color: var(--bg-color-soft);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.test-controls .el-button.el-button--primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.test-controls .el-button.el-button--warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  color: white;
}

.test-controls .el-button.el-button--info {
  background-color: var(--info-color);
  border-color: var(--info-color);
  color: white;
}

/* 进度区域 */
.progress-section {
  margin-bottom: 20px;
}

.progress-card,
.stats-card {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-color-mute);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 400px;
}

.progress-card .el-card__header,
.stats-card .el-card__header {
  background-color: var(--bg-color-soft);
  border-bottom: 1px solid var(--border-color);
  padding: 16px 20px;
}

.progress-card .el-card__body,
.stats-card .el-card__body {
  padding: 20px;
  height: calc(100% - 60px);
  overflow: hidden;
}

.progress-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: var(--text-color-soft);
  font-weight: normal;
}

/* 进度内容 */
.progress-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.overall-progress {
  margin-bottom: 30px;
}

.progress-label {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 12px;
}

.test-items-progress {
  flex: 1;
  overflow-y: auto;
}

.test-item-progress {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.test-item-progress:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-name {
  font-weight: 600;
  color: var(--text-color);
  font-size: 14px;
}

/* 统计内容 */
.stats-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.stat-item.success {
  border-color: var(--success-color);
  background-color: rgba(103, 194, 58, 0.1);
}

.stat-item.failed {
  border-color: var(--error-color);
  background-color: rgba(245, 108, 108, 0.1);
}

.stat-label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  font-size: 18px;
  color: var(--primary-color);
}

.stat-item.success .stat-value {
  color: var(--success-color);
}

.stat-item.failed .stat-value {
  color: var(--error-color);
}

/* 结果区域 */
.results-section {
  margin-top: 20px;
}

.results-card,
.log-card {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-color-mute);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 500px;
}

.results-card .el-card__header,
.log-card .el-card__header {
  background-color: var(--bg-color-soft);
  border-bottom: 1px solid var(--border-color);
  padding: 16px 20px;
}

.results-card .el-card__body,
.log-card .el-card__body {
  padding: 20px;
  height: calc(100% - 60px);
  overflow: hidden;
}

.result-controls {
  display: flex;
  gap: 8px;
}

.result-controls .el-button {
  border-color: var(--border-color);
  transition: all 0.3s ease;
}

.result-controls .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 结果内容 */
.results-content {
  height: 100%;
  overflow-y: auto;
}

.no-results {
  text-align: center;
  color: var(--text-color-soft);
  padding: 40px 20px;
  font-size: 14px;
}

.result-item {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
  background-color: var(--bg-color);
  transition: all 0.3s ease;
}

.result-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.result-item.success {
  border-left: 4px solid var(--success-color);
}

.result-item.failed {
  border-left: 4px solid var(--error-color);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.result-name {
  font-weight: 600;
  color: var(--text-color);
  font-size: 14px;
}

.result-time {
  font-size: 12px;
  color: var(--text-color-soft);
  font-family: 'Courier New', monospace;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-duration {
  font-size: 12px;
  color: var(--text-color-soft);
  font-family: 'Courier New', monospace;
}

.result-details {
  border-top: 1px solid var(--border-color-light);
  padding-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detail-item {
  display: flex;
  font-size: 12px;
  line-height: 1.4;
}

.detail-item.error {
  color: var(--error-color);
}

.detail-label {
  font-weight: 500;
  color: var(--text-color);
  min-width: 80px;
  margin-right: 8px;
}

/* 日志容器 */
.log-container {
  height: 100%;
  overflow-y: auto;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
}

.no-logs {
  text-align: center;
  color: var(--text-color-soft);
  padding: 40px 20px;
  font-size: 14px;
}

.log-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color-light);
  font-size: 12px;
  line-height: 1.4;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: var(--text-color-soft);
  font-family: 'Courier New', monospace;
  white-space: nowrap;
  min-width: 80px;
}

.log-message {
  color: var(--text-color);
  flex: 1;
  word-break: break-word;
}

/* 不同类型的日志颜色 */
.log-item.success .log-message {
  color: var(--success-color);
}

.log-item.warning .log-message {
  color: var(--warning-color);
}

.log-item.error .log-message {
  color: var(--error-color);
}

.log-item.info .log-message {
  color: var(--text-color);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .progress-card,
  .stats-card {
    height: 350px;
  }
  
  .results-card,
  .log-card {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .comprehensive-test {
    padding: 10px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .test-controls,
  .result-controls {
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .test-controls .el-button,
  .result-controls .el-button {
    font-size: 12px;
    padding: 6px 12px;
  }
  
  .progress-info {
    flex-direction: column;
    gap: 8px;
  }
  
  .progress-card,
  .stats-card {
    height: 300px;
  }
  
  .results-card,
  .log-card {
    height: 350px;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .result-status {
    align-self: flex-end;
  }
  
  .stat-item {
    padding: 12px;
  }
  
  .stat-label {
    font-size: 13px;
  }
  
  .stat-value {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .test-item-progress {
    padding: 12px;
  }
  
  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .result-item {
    padding: 12px;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 2px;
  }
  
  .detail-label {
    min-width: auto;
    margin-right: 0;
    font-weight: 600;
  }
  
  .log-item {
    font-size: 11px;
  }
  
  .log-time {
    min-width: 70px;
    font-size: 10px;
  }
}

/* 动画效果 */
.result-item,
.test-item-progress,
.stat-item {
  animation: itemFadeIn 0.3s ease-out;
}

@keyframes itemFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 进度条动画 */
.el-progress__bar {
  transition: all 0.3s ease;
}

/* 平滑过渡效果 */
.comprehensive-test *,
.el-button,
.el-card {
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 测试运行时的脉冲效果 */
.test-controls .el-button.el-button--primary.is-loading {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

/* 成功/失败状态的视觉反馈 */
.result-item.success {
  animation: successGlow 0.5s ease-out;
}

.result-item.failed {
  animation: errorGlow 0.5s ease-out;
}

@keyframes successGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.5);
  }
  50% {
    box-shadow: 0 0 0 5px rgba(103, 194, 58, 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

@keyframes errorGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.5);
  }
  50% {
    box-shadow: 0 0 0 5px rgba(245, 108, 108, 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}
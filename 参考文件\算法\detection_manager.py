import time
import cv2
import numpy as np
from PySide6.QtCore import QObject, Signal, Slot, QTimer
from PySide6.QtGui import QPixmap, QImage
from .algorithms import MotionDetector, DirectionDetector, FrameDifferencer
from .video_worker import VideoWorker
from .gpu_accelerator import get_gpu_accelerator
from core.roi import ROI
import uuid
import copy

# 未来版本将添加的算法模块
# from .algorithms import BackgroundSubtractor, OpticalFlowTracker

class DetectionManager(QObject):
    """
    检测管理器 - 核心检测控制类

    该类负责协调和管理整个检测流程，包括：
    - 视频流处理和帧分发
    - 多ROI检测任务调度
    - 状态机管理（空闲、监测、冷却等）
    - 检测结果处理和信号发送
    - 性能优化（GPU加速、帧跳跃等）

    主要功能：
    1. 视频源管理：支持本地文件和RTSP流
    2. ROI检测：支持压铸机（yazhu）和排料口（pailiao）检测
    3. 状态管理：实现完整的检测状态机
    4. 性能优化：GPU加速、帧跳跃、分辨率缩放
    5. 结果输出：实时帧显示、检测结果通知
    """

    # 信号定义 - 用于与GUI和其他组件通信
    processed_frame = Signal(QPixmap, int)  # 处理后的帧, 检测组ID
    detection_result = Signal(str, str, object, int)  # ROI名称, 状态, 帧, 检测组ID
    monitoring_started = Signal()  # 监测开始信号
    status_update = Signal(str)  # 状态更新信号
    debug_info = Signal(str)  # 调试信息信号
    error = Signal(str)  # 错误报告信号（如视频流失败）
    log_message = Signal(str)  # 日志消息信号

    def __init__(self, config, parent=None):
        """
        初始化检测管理器

        Args:
            config (dict): 配置字典，包含以下关键字段：
                - detection_group_id: 检测组ID（用于仪表板模式）
                - video_source: 视频源配置
                - config: 包含ROI配置和全局设置的子配置
            parent: Qt父对象
        """
        super().__init__(parent)
        self.config = config
        self.detection_group_id = config.get("detection_group_id", -1)  # 仪表板模式使用

        # 视频源配置
        self.video_source = config.get("video_source")

        # 在仪表板模式下，ROI作为配置数据传入，需要转换为ROI对象
        roi_data_list = config.get("config", {}).get("rois", [])
        self.rois = [ROI.from_dict(data) for data in roi_data_list]

        # 全局设置参数（仪表板模式下也在config中）
        global_settings = config.get("config", {}).get("global_settings", {})
        self._delay_time_seconds = global_settings.get("delay_time", 5)  # 卡料延时时间
        self._pause_threshold_seconds = global_settings.get("pause_threshold", 15)  # 设备暂停阈值
        self._cooldown_time_seconds = global_settings.get("cooldown_time", 3)  # 检测冷却时间

        # 运行状态控制
        self._is_running = True  # 是否正在运行
        self._stop_flag = False  # 停止标志
        self.debug_roi_id = None  # 当前调试的ROI ID
        self.video_worker = None  # 视频工作线程

        # 性能优化相关属性
        self._processing_size = None  # 处理分辨率 (width, height) 或 None 表示原始分辨率
        self._frame_skip_rate = 0  # 帧跳跃率，默认处理每一帧
        self._frame_counter = 0  # 帧计数器

        # GPU加速初始化
        self.gpu_accelerator = get_gpu_accelerator()
        if self.gpu_accelerator.is_gpu_available():
            gpu_info = self.gpu_accelerator.get_gpu_info()
            if gpu_info:
                self.log_message.emit(f"GPU加速已启用: {gpu_info[0]['name']}")
        else:
            self.log_message.emit("未检测到GPU，使用CPU处理")

        # 绘制缓存（用于跳帧时的绘制）
        self._last_draw_data = {}  # 缓存轮廓、状态等绘制数据

        # 外层状态机
        self.state = 'IDLE'  # 当前状态：空闲、监测、冷却、设备暂停
        self.state_end_time = 0  # 状态结束时间
        self.discharge_motion_detected = {}  # 排料口运动检测状态
        self.last_die_caster_motion_time = time.time()  # 最后一次压铸机运动时间

        # 根据加载的ROI设置处理器
        self._setup_processors()

        self.log_message.emit(f"检测管理器 (ID: {self.detection_group_id}) 初始化完成。")

    def run(self):
        """
        启动检测管理器的视频处理

        创建视频工作线程并开始处理视频流。
        适用于独立运行模式。
        """
        if not self.video_source:
            print(f"错误: 检测管理器缺少视频源，配置: {self.config}")
            return

        self.video_worker = VideoWorker(self.video_source)
        self.video_worker.frame_ready.connect(self.process_frame)
        # 可以在这里添加错误处理
        self.video_worker.run()  # 启动处理循环

    def run_in_dashboard_mode(self):
        """
        仪表板模式下的运行入口点

        在仪表板模式下，检测管理器在独立线程中运行。
        该方法负责创建和管理VideoWorker，简化线程管理。
        """
        # 仪表板可以单独创建VideoWorker，但为了简化，让管理器自己创建
        from core.video_worker import VideoWorker
        self.log_message.emit(f"准备为检测组 {self.detection_group_id} 创建VideoWorker...")
        self.video_worker = VideoWorker(self.video_source)
        self.log_message.emit(f"VideoWorker已创建，视频源: {self.video_source}")

        # 连接信号
        self.video_worker.frame_ready.connect(self.process_frame)
        self.video_worker.error.connect(lambda msg: self.error.emit(msg))
        self.video_worker.finished.connect(
            lambda: self.log_message.emit(f"检测组 {self.detection_group_id} 的VideoWorker已完成。")
        )

        self.log_message.emit("VideoWorker信号已连接，准备运行...")
        self.video_worker.run()  # 阻塞运行直到视频结束或被停止
        self.log_message.emit(f"检测组 {self.detection_group_id} 的VideoWorker.run()已退出。")

    def update_rois(self, new_rois_data):
        """
        安全更新ROI列表的公共方法

        该方法确保self.rois始终是ROI对象的列表，
        支持从字典数据或ROI对象列表进行更新。

        Args:
            new_rois_data (list): 新的ROI数据，可以是字典列表或ROI对象列表
        """
        self.rois = []  # 重置ROI列表
        if not new_rois_data:
            self._setup_processors()
            return

        # 确保我们有ROI对象列表
        if isinstance(new_rois_data[0], dict):
            # 数据来自JSON，将字典列表转换为ROI对象
            self.rois = [ROI.from_dict(data) for data in new_rois_data]
        else:
            # 数据已经是ROI对象列表，直接使用
            self.rois = new_rois_data

        self._setup_processors()

    @Slot(tuple, int)
    def update_performance_settings(self, size, skip_rate):
        """
        从UI更新性能设置的公共槽函数

        Args:
            size (tuple): 处理分辨率 (width, height) 或 None 表示原始分辨率
            skip_rate (int): 跳帧率（0表示处理每一帧）
        """
        # 如果分辨率改变，重置处理器以避免尺寸不匹配问题
        if self._processing_size != size:
            self.log_message.emit("分辨率改变，正在重置算法处理器...")
            for processor in self.roi_processors.values():
                if hasattr(processor, 'reset'):
                    processor.reset()
            self._last_draw_data = {}  # 同时清空绘制缓存

        self._processing_size = size
        self._frame_skip_rate = skip_rate
        self.log_message.emit(f"性能设置已更新: 分辨率={size}, 跳帧率={skip_rate}")

    def update_delay_time(self, seconds):
        """
        更新卡料延时时间

        Args:
            seconds (int): 延时时间（秒）
        """
        self._delay_time_seconds = seconds
        self.status_update.emit(f"卡料延时已更新为: {seconds} 秒")

    def update_pause_threshold(self, seconds):
        """
        更新设备暂停阈值

        Args:
            seconds (int): 暂停阈值时间（秒）
        """
        self._pause_threshold_seconds = seconds
        self.status_update.emit(f"设备暂停阈值已更新为: {seconds} 秒")

    def update_cooldown_time(self, seconds):
        """
        更新检测冷却时间

        Args:
            seconds (int): 冷却时间（秒）
        """
        self._cooldown_time_seconds = seconds
        self.status_update.emit(f"检测冷却时间已更新为: {seconds} 秒")

    def _setup_processors(self):
        """
        根据ROI配置设置检测处理器

        为每个ROI创建相应的检测算法实例：
        - yazhu类型：使用DirectionDetector（方向检测器）
        - pailiao类型：根据配置选择MotionDetector或FrameDifferencer
        """
        self.roi_processors = {}
        for roi in self.rois:
            roi_id = roi.id
            roi_type = roi.roi_type
            params = roi.params
            try:
                if roi_type == 'yazhu':
                    # 压铸机ROI使用方向检测器
                    self.roi_processors[roi_id] = DirectionDetector(params)
                elif roi_type == 'pailiao':
                    # 排料口ROI根据算法配置选择检测器
                    motion_params = params.get('运动检测', {})
                    algo = motion_params.get('algorithm', '帧差法')  # 默认使用帧差法
                    if algo == '背景减除法':
                        self.roi_processors[roi_id] = MotionDetector(motion_params)
                    else:  # 默认使用帧差法
                        self.roi_processors[roi_id] = FrameDifferencer(motion_params)
            except Exception as e:
                print(f"为ROI {roi_id} 创建处理器时出错: {e}")

    @Slot(str)
    def set_debug_roi(self, roi_id):
        """
        设置要调试的ROI

        启用特定ROI的调试模式，输出详细的检测信息。
        设置为None可停止调试。

        Args:
            roi_id (str): 要调试的ROI ID，None表示停止调试
        """
        self.debug_roi_id = roi_id
        if roi_id:
            self.debug_info.emit(f"--- 开始调试 ROI (ID: {roi_id}) ---")
        else:
            self.debug_info.emit("--- 停止调试 ---")

    @Slot(np.ndarray)
    def process_frame(self, frame):
        if self._stop_flag or frame is None:
            return

        # --- 1. Frame Skipping Logic ---
        self._frame_counter += 1
        is_process_frame = (self._frame_counter % (self._frame_skip_rate + 1) == 0)

        # --- 2. Processing Logic (only on non-skipped frames) ---
        if is_process_frame:
            original_h, original_w = frame.shape[:2]
            if self._processing_size:
                # Use GPU acceleration for image resizing
                scaled_frame = self.gpu_accelerator.resize_gpu(frame, self._processing_size)
                scale_x = original_w / self._processing_size[0]
                scale_y = original_h / self._processing_size[1]
            else:
                scaled_frame = frame
                scale_x, scale_y = 1.0, 1.0

            self._last_draw_data.clear() # Clear cache for new results

            overall_die_caster_state = 'STATIONARY'
            die_caster_states = []

            for roi in self.rois:
                roi_id = roi.id
                processor = self.roi_processors.get(roi_id)
                if not processor: continue

                scaled_roi = roi.get_scaled_roi(scale_x, scale_y)

                if roi.roi_type == 'yazhu':
                    state = processor.detect(scaled_frame, scaled_roi)
                    die_caster_states.append(state)
                    self._last_draw_data[roi_id] = {'type': 'die_caster', 'triggered': processor.is_triggered()}

                elif roi.roi_type == 'pailiao':
                    if self.state == 'MONITORING':
                        motion_detected, contours = processor.detect(scaled_frame, scaled_roi)
                        if motion_detected:
                            scaled_contours = [(c * [scale_x, scale_y]).astype(np.int32) for c in contours]
                            self._last_draw_data[roi_id] = {'type': 'discharge', 'contours': scaled_contours}
                            self.discharge_motion_detected[roi_id] = True
                    else:
                        processor.update_background(scaled_frame, scaled_roi)

            # Aggregate die-caster states
            if 'MOVING_DOWN' in die_caster_states:
                overall_die_caster_state = 'MOVING_DOWN'
            elif 'MOVING_UP' in die_caster_states:
                overall_die_caster_state = 'MOVING_UP'

            # --- FIX: Update the last motion time whenever the die caster is active ---
            current_time = time.time()
            if overall_die_caster_state != 'STATIONARY':
                self.last_die_caster_motion_time = current_time

            # Update the main state machine
            self.update_state_machine(scaled_frame, current_time, overall_die_caster_state)

        # --- 3. Drawing Logic (on EVERY frame) ---
        processed_frame = frame.copy() # Start with a fresh copy of the original frame

        for roi in self.rois:
            # Draw base ROI shape
            coords = roi.coordinates
            roi_type = roi.roi_type
            base_color = (0, 0, 255) if roi_type == 'yazhu' else (255, 255, 0)
            if roi.roi_type == 'rectangle':  # 🔥 修复：使用roi_type字段和小写
                cv2.rectangle(processed_frame, coords[0], coords[1], base_color, 2)
            elif roi.roi_type == 'polygon':  # 🔥 修复：使用roi_type字段和小写
                pts = np.array(coords, np.int32).reshape((-1, 1, 2))
                cv2.polylines(processed_frame, [pts], True, base_color, 2)
            cv2.putText(processed_frame, roi.name, (coords[0][0], coords[0][1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            # Draw cached detection results
            roi_id = getattr(roi, 'roi_id', getattr(roi, 'id', None))  # 🔥 修复：优先使用roi_id
            if roi_id in self._last_draw_data:
                draw_data = self._last_draw_data[roi_id]
                if draw_data.get('type') == 'die_caster' and draw_data.get('triggered'):
                    # Simple center calculation for drawing
                    x_coords = [p[0] for p in roi.coordinates]
                    y_coords = [p[1] for p in roi.coordinates]
                    center_x = int(sum(x_coords) / len(x_coords))
                    center_y = int(sum(y_coords) / len(y_coords))
                    start_p = (center_x, center_y - 20)
                    end_p = (center_x, center_y + 20)
                    cv2.arrowedLine(processed_frame, start_p, end_p, (0, 0, 255), 3, tipLength=0.3)
                elif draw_data.get('type') == 'discharge' and 'contours' in draw_data:
                    cv2.drawContours(processed_frame, draw_data['contours'], -1, (0, 255, 0), 2)

        # --- 4. Emit the final, fully drawn frame ---
        h, w, ch = processed_frame.shape
        bytes_per_line = ch * w
        qt_image = QImage(processed_frame.data, w, h, bytes_per_line, QImage.Format.Format_RGB888).rgbSwapped()
        pixmap = QPixmap.fromImage(qt_image)
        self.processed_frame.emit(pixmap, self.detection_group_id)

    def update_state_machine(self, frame, current_time, die_caster_state):
        if self.state == 'IDLE':
            self.status_update.emit("状态: 监测压铸机的下压动作...")

            # Find the die caster ROI and check if it's triggered
            triggered = False
            for roi in self.rois:
                if roi.roi_type == 'yazhu':
                    processor = self.roi_processors.get(roi.id)
                    if processor and processor.is_triggered():
                        triggered = True
                        break # Found a triggered die caster

            if die_caster_state == 'MOVING_DOWN' and triggered:
                self.state = 'MONITORING'
                self.state_end_time = current_time + self._delay_time_seconds
                self.monitoring_started.emit() # Emit signal that a new cycle has begun
                # Reset motion detection status for all discharge ports
                for roi in self.rois:
                    if roi.roi_type == 'pailiao':
                        self.discharge_motion_detected[roi.id] = False
            elif current_time - self.last_die_caster_motion_time > self._pause_threshold_seconds:
                self.state = 'MACHINE_PAUSED'

        elif self.state == 'MACHINE_PAUSED':
            self.status_update.emit(f"状态: 设备暂停中 (超过 {self._pause_threshold_seconds}s 无运动)")
            if die_caster_state != 'STATIONARY':
                self.state = 'IDLE' # Exit pause state on any motion

        elif self.state == 'MONITORING':
            remaining = self.state_end_time - current_time
            self.status_update.emit(f"状态: 废料监测中... 剩余 {remaining:.1f}s")
            if current_time >= self.state_end_time:
                for r in self.rois:
                    if r.roi_type == 'pailiao' and not self.discharge_motion_detected.get(r.id, False):
                        # Capture the frame at the moment of alarm
                        alarm_frame = frame.copy()
                        # Draw a red rectangle around the problem ROI for clarity
                        coords = r.coordinates
                        if r.shape == 'Rectangle':
                            if len(coords) >= 2:
                                cv2.rectangle(alarm_frame, tuple(coords[0]), tuple(coords[1]), (0, 0, 255), 3)
                        elif r.shape == 'Polygon':
                            pts = np.array(coords, np.int32).reshape((-1, 1, 2))
                            cv2.polylines(alarm_frame, [pts], True, (0, 0, 255), 3)
                        self._emit_detection_result(r.name, '卡料', alarm_frame)
                self.state = 'COOLDOWN'
                self.state_end_time = current_time + self._cooldown_time_seconds

        elif self.state == 'COOLDOWN':
            self.status_update.emit("状态: 监测结束, 冷却中...")
            if current_time >= self.state_end_time:
                self.state = 'IDLE'

    def stop(self):
        self._is_running = False
        if self.video_worker:
            self.video_worker.stop()

    @Slot(str, str, np.ndarray)
    def _emit_detection_result(self, roi_name, status, frame):
        """
        发送检测结果的辅助方法

        将检测结果通过信号发送给GUI或其他监听组件，
        包含检测组ID以便在仪表板模式下区分不同的检测组。

        Args:
            roi_name (str): ROI名称
            status (str): 检测状态（如"卡料"）
            frame (np.ndarray): 相关的视频帧
        """
        self.detection_result.emit(roi_name, status, frame, self.detection_group_id)

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { DetectionTemplate } from '@/types'

export interface RunningDetectionState {
  templateId: string
  templateName: string
  scheduleId?: string
  startTime: string
  status: 'running' | 'paused' | 'stopped'
  dashboardWindow?: Window | null
}

/**
 * 全局检测状态管理
 * 确保同时只能有一个检测模板在执行
 */
export const useDetectionStateStore = defineStore('detection-state', () => {
  // 当前运行的检测状态
  const currentRunningDetection = ref<RunningDetectionState | null>(null)
  
  // 是否有检测正在运行
  const isDetectionRunning = computed(() => {
    return currentRunningDetection.value?.status === 'running'
  })
  
  // 当前运行的模板ID
  const currentRunningTemplateId = computed(() => {
    return currentRunningDetection.value?.templateId || null
  })
  
  // 检测看板窗口引用
  const dashboardWindow = ref<Window | null>(null)
  
  /**
   * 启动检测模板
   */
  const startDetection = async (
    templateId: string, 
    templateName: string, 
    scheduleId?: string
  ): Promise<boolean> => {
    try {
      // 检查是否已有检测在运行
      if (isDetectionRunning.value) {
        const currentTemplate = currentRunningDetection.value?.templateName
        ElMessage.warning(`检测模板 "${currentTemplate}" 正在运行中，请先停止当前检测`)
        return false
      }
      
      // 设置运行状态
      currentRunningDetection.value = {
        templateId,
        templateName,
        scheduleId,
        startTime: new Date().toISOString(),
        status: 'running'
      }
      
      // 持久化到localStorage
      localStorage.setItem('currentRunningDetection', JSON.stringify(currentRunningDetection.value))
      
      // 打开检测看板弹窗
      await openDetectionDashboard(templateId, templateName)
      
      ElMessage.success(`检测模板 "${templateName}" 已启动`)
      return true
    } catch (error) {
      console.error('启动检测失败:', error)
      ElMessage.error('启动检测失败')
      return false
    }
  }
  
  /**
   * 停止检测模板
   */
  const stopDetection = async (): Promise<boolean> => {
    try {
      if (!currentRunningDetection.value) {
        ElMessage.warning('当前没有正在运行的检测')
        return false
      }
      
      const templateName = currentRunningDetection.value.templateName
      
      // 关闭检测看板窗口
      if (dashboardWindow.value && !dashboardWindow.value.closed) {
        dashboardWindow.value.close()
      }
      
      // 清除运行状态
      currentRunningDetection.value = null
      localStorage.removeItem('currentRunningDetection')
      dashboardWindow.value = null
      
      ElMessage.success(`检测模板 "${templateName}" 已停止`)
      return true
    } catch (error) {
      console.error('停止检测失败:', error)
      ElMessage.error('停止检测失败')
      return false
    }
  }
  
  /**
   * 暂停检测
   */
  const pauseDetection = async (): Promise<boolean> => {
    try {
      if (!currentRunningDetection.value || currentRunningDetection.value.status !== 'running') {
        ElMessage.warning('当前没有正在运行的检测')
        return false
      }
      
      currentRunningDetection.value.status = 'paused'
      localStorage.setItem('currentRunningDetection', JSON.stringify(currentRunningDetection.value))
      
      ElMessage.success('检测已暂停')
      return true
    } catch (error) {
      console.error('暂停检测失败:', error)
      ElMessage.error('暂停检测失败')
      return false
    }
  }
  
  /**
   * 恢复检测
   */
  const resumeDetection = async (): Promise<boolean> => {
    try {
      if (!currentRunningDetection.value || currentRunningDetection.value.status !== 'paused') {
        ElMessage.warning('当前没有暂停的检测')
        return false
      }
      
      currentRunningDetection.value.status = 'running'
      localStorage.setItem('currentRunningDetection', JSON.stringify(currentRunningDetection.value))
      
      ElMessage.success('检测已恢复')
      return true
    } catch (error) {
      console.error('恢复检测失败:', error)
      ElMessage.error('恢复检测失败')
      return false
    }
  }
  
  /**
   * 打开检测看板弹窗
   */
  const openDetectionDashboard = async (templateId: string, templateName: string): Promise<void> => {
    try {
      // 检查是否已有窗口打开
      if (dashboardWindow.value && !dashboardWindow.value.closed) {
        dashboardWindow.value.focus()
        return
      }
      
      // 准备自动启动数据
      const autoStartData = {
        templateId,
        templateName,
        autoStart: true,
        timestamp: Date.now()
      }
      
      // 存储到sessionStorage供检测看板使用
      sessionStorage.setItem('autoStartTemplate', JSON.stringify(autoStartData))
      
      // 打开新窗口
      const dashboardUrl = `${window.location.origin}/#/detection-dashboard`
      const windowFeatures = 'width=1400,height=900,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no'
      
      dashboardWindow.value = window.open(dashboardUrl, 'detectionDashboard', windowFeatures)
      
      if (!dashboardWindow.value) {
        throw new Error('无法打开检测看板窗口，请检查浏览器弹窗设置')
      }
      
      // 监听窗口关闭事件
      const checkClosed = setInterval(() => {
        if (dashboardWindow.value?.closed) {
          clearInterval(checkClosed)
          // 窗口关闭时停止检测
          stopDetection()
        }
      }, 1000)
      
    } catch (error) {
      console.error('打开检测看板失败:', error)
      throw error
    }
  }
  
  /**
   * 从localStorage恢复检测状态
   */
  const restoreDetectionState = (): void => {
    try {
      const savedState = localStorage.getItem('currentRunningDetection')
      if (savedState) {
        const state = JSON.parse(savedState) as RunningDetectionState
        
        // 检查状态是否有效（避免过期状态）
        const startTime = new Date(state.startTime)
        const now = new Date()
        const hoursDiff = (now.getTime() - startTime.getTime()) / (1000 * 60 * 60)
        
        // 如果超过24小时，认为状态过期
        if (hoursDiff > 24) {
          localStorage.removeItem('currentRunningDetection')
          return
        }
        
        currentRunningDetection.value = state
        
        // 如果是运行状态，提示用户是否继续
        if (state.status === 'running') {
          ElMessage.info(`检测到上次运行的模板 "${state.templateName}"，可在预设计划中继续执行`)
        }
      }
    } catch (error) {
      console.error('恢复检测状态失败:', error)
      localStorage.removeItem('currentRunningDetection')
    }
  }
  
  /**
   * 检查模板是否可以启动
   */
  const canStartTemplate = (templateId: string): boolean => {
    if (!isDetectionRunning.value) {
      return true
    }
    
    // 如果当前运行的就是这个模板，可以重新启动
    return currentRunningTemplateId.value === templateId
  }
  
  /**
   * 获取运行时长
   */
  const getRunningDuration = computed(() => {
    if (!currentRunningDetection.value) {
      return 0
    }
    
    const startTime = new Date(currentRunningDetection.value.startTime)
    const now = new Date()
    return Math.floor((now.getTime() - startTime.getTime()) / 1000)
  })
  
  // 初始化时恢复状态
  restoreDetectionState()
  
  return {
    // 状态
    currentRunningDetection,
    isDetectionRunning,
    currentRunningTemplateId,
    dashboardWindow,
    getRunningDuration,
    
    // 方法
    startDetection,
    stopDetection,
    pauseDetection,
    resumeDetection,
    openDetectionDashboard,
    restoreDetectionState,
    canStartTemplate
  }
})

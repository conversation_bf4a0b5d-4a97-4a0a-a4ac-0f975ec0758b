import cv2
import numpy as np
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Tu<PERSON>, Optional, Union
from dataclasses import dataclass
from enum import Enum
from threading import Lock
from .algorithms import MotionDetector, FrameDifferencer, DirectionDetector
from .gpu_accelerator import GPUAccelerator

logger = logging.getLogger(__name__)

class DetectionState(Enum):
    """检测状态枚举"""
    IDLE = "idle"                    # 空闲状态
    MONITORING = "monitoring"        # 监控状态
    MACHINE_PAUSED = "machine_paused" # 设备暂停状态
    COOLDOWN = "cooldown"            # 冷却状态

@dataclass
class DetectionResult:
    """检测结果数据类"""
    timestamp: datetime  # 改为datetime对象，使用服务器本地时间
    motion_detected: bool
    contours: List[Any]
    roi_results: List[Dict[str, Any]]
    direction_info: Optional[Dict[str, Any]] = None
    violation_detected: bool = False
    violation_type: Optional[str] = None
    confidence: float = 0.0

@dataclass
class ROIConfig:
    """ROI配置数据类"""
    roi_id: str
    roi_name: str
    roi_type: str  # 'rectangle' or 'polygon'
    coordinates: List[Tuple[int, int]]
    detector_type: str  # 'motion', 'direction', 'frame_difference'
    detector_params: Dict[str, Any]
    is_active: bool = True

class DetectionManager:
    """检测管理器 - 协调和管理整个检测流程"""
    
    def __init__(self, enable_gpu: bool = True):
        """初始化检测管理器
        
        Args:
            enable_gpu (bool): 是否启用GPU加速
        """
        self.enable_gpu = enable_gpu
        self.gpu_accelerator = GPUAccelerator() if enable_gpu else None
        
        # 检测器管理
        self.detectors: Dict[str, Union[MotionDetector, DirectionDetector, FrameDifferencer]] = {}
        self.roi_configs: Dict[str, ROIConfig] = {}
        
        # 状态管理
        self.current_state = DetectionState.IDLE
        self.state_start_time = datetime.now().timestamp()  # 保持为时间戳用于计算时间差
        self.last_detection_time = 0
        self.last_motion_time = 0
        
        # 全局设置
        self.global_settings = {
            'frame_skip': 2,           # 帧跳过数量
            'scale_factor': 0.5        # 图像缩放因子
        }
        
        # 性能统计
        self.performance_stats = {
            'total_frames': 0,
            'detection_count': 0,
            'avg_processing_time': 0.0,
            'last_fps': 0.0
        }
        
        # 线程安全
        self._lock = Lock()
        
        logger.info(f"检测管理器初始化完成，GPU加速: {enable_gpu}")
    
    def update_global_settings(self, settings: Dict[str, Any]) -> bool:
        """更新全局设置
        
        Args:
            settings (Dict[str, Any]): 新的设置参数
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with self._lock:
                self.global_settings.update(settings)
                logger.info(f"全局设置已更新: {settings}")
                return True
        except Exception as e:
            logger.error(f"更新全局设置失败: {e}")
            return False
    
    def get_global_settings(self) -> Dict[str, Any]:
        """获取全局设置
        
        Returns:
            Dict[str, Any]: 当前全局设置
        """
        with self._lock:
            return self.global_settings.copy()
    
    def add_roi(self, roi_config: ROIConfig) -> bool:
        """添加ROI配置
        
        Args:
            roi_config (ROIConfig): ROI配置
            
        Returns:
            bool: 是否添加成功
        """
        try:
            with self._lock:
                # 创建对应的检测器
                detector = self._create_detector(
                    roi_config.detector_type,
                    roi_config.detector_params
                )
                
                if detector:
                    self.roi_configs[roi_config.roi_id] = roi_config
                    self.detectors[roi_config.roi_id] = detector
                    logger.info(f"ROI {roi_config.roi_id} 添加成功")
                    return True
                else:
                    logger.error(f"创建检测器失败: {roi_config.detector_type}")
                    return False
                    
        except Exception as e:
            logger.error(f"添加ROI失败: {e}")
            return False
    
    def remove_roi(self, roi_id: str) -> bool:
        """移除ROI配置
        
        Args:
            roi_id (str): ROI ID
            
        Returns:
            bool: 是否移除成功
        """
        try:
            with self._lock:
                if roi_id in self.roi_configs:
                    del self.roi_configs[roi_id]
                    del self.detectors[roi_id]
                    logger.info(f"ROI {roi_id} 移除成功")
                    return True
                else:
                    logger.warning(f"ROI {roi_id} 不存在")
                    return False
                    
        except Exception as e:
            logger.error(f"移除ROI失败: {e}")
            return False
    
    def update_roi_config(self, roi_id: str, new_config: Dict[str, Any]) -> bool:
        """更新ROI配置
        
        Args:
            roi_id (str): ROI ID
            new_config (Dict[str, Any]): 新配置
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with self._lock:
                if roi_id not in self.roi_configs:
                    logger.warning(f"ROI {roi_id} 不存在")
                    return False
                
                roi_config = self.roi_configs[roi_id]
                
                # 更新检测器参数
                if 'detector_params' in new_config:
                    roi_config.detector_params.update(new_config['detector_params'])
                    
                    # 更新检测器
                    if hasattr(self.detectors[roi_id], 'update_params'):
                        self.detectors[roi_id].update_params(roi_config.detector_params)
                
                # 更新其他配置
                for key, value in new_config.items():
                    if hasattr(roi_config, key) and key != 'detector_params':
                        setattr(roi_config, key, value)
                
                logger.info(f"ROI {roi_id} 配置已更新")
                return True
                
        except Exception as e:
            logger.error(f"更新ROI配置失败: {e}")
            return False
    
    def _create_detector(self, detector_type: str, params: Dict[str, Any]) -> Optional[Any]:
        """创建检测器
        
        Args:
            detector_type (str): 检测器类型
            params (Dict[str, Any]): 检测器参数
            
        Returns:
            Optional[Any]: 检测器实例
        """
        try:
            if detector_type == 'motion':
                return MotionDetector(params, self.gpu_accelerator)
            elif detector_type == 'direction':
                return DirectionDetector(params, self.gpu_accelerator)
            elif detector_type == 'frame_difference':
                return FrameDifferencer(params, self.gpu_accelerator)
            else:
                logger.error(f"不支持的检测器类型: {detector_type}")
                return None
                
        except Exception as e:
            logger.error(f"创建检测器失败: {e}")
            return None
    
    def process_frame(self, frame: np.ndarray) -> DetectionResult:
        """处理视频帧
        
        Args:
            frame (np.ndarray): 输入视频帧
            
        Returns:
            DetectionResult: 检测结果
        """
        start_time = datetime.now()  # 使用服务器本地时间而不是UTC时间戳
        
        try:
            # 帧跳过逻辑
            self.performance_stats['total_frames'] += 1
            if self.performance_stats['total_frames'] % (self.global_settings['frame_skip'] + 1) != 0:
                return DetectionResult(
                    timestamp=start_time,
                    motion_detected=False,
                    contours=[],
                    roi_results=[]
                )
            
            # 图像预处理
            processed_frame = self._preprocess_frame(frame)
            
            # 执行检测
            roi_results = []
            overall_motion_detected = False
            overall_contours = []
            direction_info = None
            
            # 处理每个ROI
            for roi_id, roi_config in self.roi_configs.items():
                if not roi_config.is_active:
                    continue
                
                detector = self.detectors[roi_id]
                roi_mask = self._create_roi_mask(processed_frame, roi_config)
                
                # 执行检测
                if roi_config.detector_type == 'direction':
                    direction_state, contours, dir_info = detector.detect(processed_frame, roi_mask)
                    motion_detected = direction_state != 'STATIONARY'
                    if dir_info:
                        direction_info = dir_info
                else:
                    motion_detected, contours = detector.detect(processed_frame, roi_mask)
                
                # 收集结果
                roi_result = {
                    'roi_id': roi_id,
                    'roi_name': roi_config.roi_name,
                    'motion_detected': motion_detected,
                    'contours': self._format_contours(contours),
                    'detector_type': roi_config.detector_type
                }
                
                if direction_info:
                    roi_result['direction'] = direction_info
                
                roi_results.append(roi_result)
                
                # 更新整体结果
                if motion_detected:
                    overall_motion_detected = True
                    overall_contours.extend(contours)
            
            # 更新状态机
            violation_detected, violation_type = self._update_state_machine(
                overall_motion_detected, start_time.timestamp()  # 传递时间戳用于状态机计算
            )
            
            # 创建检测结果
            result = DetectionResult(
                timestamp=start_time,
                motion_detected=overall_motion_detected,
                contours=self._format_contours(overall_contours),
                roi_results=roi_results,
                direction_info=direction_info,
                violation_detected=violation_detected,
                violation_type=violation_type
            )
            
            # 更新性能统计
            self._update_performance_stats(start_time)
            
            return result
            
        except Exception as e:
            logger.error(f"处理帧时发生错误: {e}")
            return DetectionResult(
                timestamp=start_time,
                motion_detected=False,
                contours=[],
                roi_results=[]
            )
    
    def _preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """预处理视频帧
        
        Args:
            frame (np.ndarray): 原始帧
            
        Returns:
            np.ndarray: 处理后的帧
        """
        # 缩放图像
        scale_factor = self.global_settings['scale_factor']
        if scale_factor != 1.0:
            height, width = frame.shape[:2]
            new_height = int(height * scale_factor)
            new_width = int(width * scale_factor)
            
            if self.gpu_accelerator:
                frame = self.gpu_accelerator.resize_frame(frame, (new_width, new_height))
            else:
                frame = cv2.resize(frame, (new_width, new_height))
        
        return frame
    
    def _create_roi_mask(self, frame: np.ndarray, roi_config: ROIConfig) -> np.ndarray:
        """创建ROI掩码
        
        Args:
            frame (np.ndarray): 视频帧
            roi_config (ROIConfig): ROI配置
            
        Returns:
            np.ndarray: ROI掩码
        """
        height, width = frame.shape[:2]
        mask = np.zeros((height, width), dtype=np.uint8)
        
        if roi_config.roi_type == 'rectangle':
            if len(roi_config.coordinates) >= 2:
                x1, y1 = roi_config.coordinates[0]
                x2, y2 = roi_config.coordinates[1]
                cv2.rectangle(mask, (x1, y1), (x2, y2), 255, -1)
        
        elif roi_config.roi_type == 'polygon':
            if len(roi_config.coordinates) >= 3:
                points = np.array(roi_config.coordinates, np.int32)
                points = points.reshape((-1, 1, 2))
                cv2.fillPoly(mask, [points], 255)
        
        return mask
    
    def _format_contours(self, contours: List[Any]) -> List[Dict[str, Any]]:
        """格式化轮廓数据
        
        Args:
            contours (List[Any]): OpenCV轮廓列表
            
        Returns:
            List[Dict[str, Any]]: 格式化后的轮廓数据
        """
        formatted_contours = []
        
        for contour in contours:
            points = []
            for point in contour:
                if len(point.shape) == 2 and point.shape[0] == 1:
                    # OpenCV轮廓点格式 [[x, y]]
                    x, y = point[0]
                else:
                    # 其他格式
                    x, y = point
                
                points.append({'x': int(x), 'y': int(y)})
            
            formatted_contours.append({'points': points})
        
        return formatted_contours
    
    def _update_state_machine(self, motion_detected: bool, current_time: float) -> Tuple[bool, Optional[str]]:
        """更新状态机
        
        Args:
            motion_detected (bool): 是否检测到运动
            current_time (float): 当前时间
            
        Returns:
            Tuple[bool, Optional[str]]: (是否检测到违规, 违规类型)
        """
        violation_detected = False
        violation_type = None
        
        # 更新最后运动时间
        if motion_detected:
            self.last_motion_time = current_time
        
        # 计算状态持续时间
        state_duration = current_time - self.state_start_time
        time_since_last_motion = current_time - self.last_motion_time
        
        # 状态转换逻辑
        if self.current_state == DetectionState.IDLE:
            if motion_detected:
                self._change_state(DetectionState.MONITORING, current_time)
        
        elif self.current_state == DetectionState.MONITORING:
            # 检查是否设备暂停
            if time_since_last_motion > self.global_settings['pause_threshold']:
                self._change_state(DetectionState.MACHINE_PAUSED, current_time)
            
            # 卡料检测功能已移除
        
        elif self.current_state == DetectionState.MACHINE_PAUSED:
            if motion_detected:
                self._change_state(DetectionState.MONITORING, current_time)
        
        elif self.current_state == DetectionState.COOLDOWN:
            if state_duration > self.global_settings['cooldown_time']:
                if motion_detected:
                    self._change_state(DetectionState.MONITORING, current_time)
                else:
                    self._change_state(DetectionState.IDLE, current_time)
        
        return violation_detected, violation_type
    
    def _change_state(self, new_state: DetectionState, current_time: float):
        """改变检测状态
        
        Args:
            new_state (DetectionState): 新状态
            current_time (float): 当前时间
        """
        old_state = self.current_state
        self.current_state = new_state
        self.state_start_time = current_time
        
        logger.info(f"状态转换: {old_state.value} -> {new_state.value}")
    
    def _update_performance_stats(self, start_time: float):
        """更新性能统计
        
        Args:
            start_time (float): 处理开始时间
        """
        processing_time = (datetime.now() - start_time).total_seconds()  # 计算处理时间差
        
        # 更新平均处理时间
        self.performance_stats['detection_count'] += 1
        count = self.performance_stats['detection_count']
        avg_time = self.performance_stats['avg_processing_time']
        
        self.performance_stats['avg_processing_time'] = (
            (avg_time * (count - 1) + processing_time) / count
        )
        
        # 计算FPS
        if processing_time > 0:
            self.performance_stats['last_fps'] = 1.0 / processing_time
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计
        
        Returns:
            Dict[str, Any]: 性能统计数据
        """
        with self._lock:
            return self.performance_stats.copy()
    
    def get_current_state(self) -> Dict[str, Any]:
        """获取当前状态
        
        Returns:
            Dict[str, Any]: 当前状态信息
        """
        current_time = datetime.now().timestamp()  # 用于时间差计算
        
        return {
            'state': self.current_state.value,
            'state_duration': current_time - self.state_start_time,
            'time_since_last_motion': current_time - self.last_motion_time,
            'roi_count': len(self.roi_configs),
            'active_roi_count': sum(1 for roi in self.roi_configs.values() if roi.is_active)
        }
    
    def reset(self):
        """重置检测管理器状态"""
        with self._lock:
            self.current_state = DetectionState.IDLE
            self.state_start_time = datetime.now().timestamp()  # 重置状态开始时间
            self.last_detection_time = 0
            self.last_motion_time = 0
            
            # 重置所有检测器
            for detector in self.detectors.values():
                if hasattr(detector, 'reset'):
                    detector.reset()
            
            logger.info("检测管理器已重置")
2025-07-19 09:58:49,259 - app.api.system_logs - INFO - 日志文件处理器已设置，日志将保存到: logs\system.log
2025-07-19 09:58:49,259 - app.api.system_logs - INFO - 日志文件处理器已设置，日志将保存到: logs\system.log
2025-07-19 09:58:49,259 - app.api.system_logs - INFO - 系统日志API已启动，使用内存缓存模式
2025-07-19 09:58:49,259 - app.api.system_logs - INFO - 系统日志API已启动，使用内存缓存模式
2025-07-19 09:58:49,269 - app.api.system_logs - INFO - 🔧 [SYSTEM] 系统日志API已启动
2025-07-19 09:58:49,269 - app.api.system_logs - INFO - 🔧 [SYSTEM] 系统日志API已启动
2025-07-19 09:58:49,269 - app.api.system_logs - INFO - 🎯 [ROI-PARAM-VERIFY] ROI参数验证系统已就绪
2025-07-19 09:58:49,269 - app.api.system_logs - INFO - 🎯 [ROI-PARAM-VERIFY] ROI参数验证系统已就绪
2025-07-19 09:58:49,269 - app.api.system_logs - WARNING - ⚠️ [WEBSOCKET] WebSocket连接数量较多，注意性能
2025-07-19 09:58:49,269 - app.api.system_logs - WARNING - ⚠️ [WEBSOCKET] WebSocket连接数量较多，注意性能
2025-07-19 09:58:49,269 - app.api.system_logs - ERROR - ❌ [DATABASE] 数据库连接临时中断，正在重连...
2025-07-19 09:58:49,269 - app.api.system_logs - ERROR - ❌ [DATABASE] 数据库连接临时中断，正在重连...
2025-07-19 09:58:49,269 - app.api.system_logs - INFO - ✅ [DATABASE] 数据库连接已恢复
2025-07-19 09:58:49,269 - app.api.system_logs - INFO - ✅ [DATABASE] 数据库连接已恢复
2025-07-19 09:58:49,491 - app.main - INFO - 本机IP地址: ************
2025-07-19 09:58:49,491 - app.main - INFO - 本机IP地址: ************
2025-07-19 09:59:35,808 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://************:80/ISAPI/System/deviceInfo
2025-07-19 09:59:35,808 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://************:80/ISAPI/System/deviceInfo
2025-07-19 09:59:35,836 - httpx - INFO - HTTP Request: GET http://************/ISAPI/System/deviceInfo "HTTP/1.1 200 OK"
2025-07-19 09:59:35,836 - httpx - INFO - HTTP Request: GET http://************/ISAPI/System/deviceInfo "HTTP/1.1 200 OK"
2025-07-19 09:59:35,838 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-19 09:59:35,838 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-19 10:00:36,096 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://************:80/ISAPI/System/deviceInfo
2025-07-19 10:00:36,096 - app.api.endpoints.websdk_proxy - INFO - 代理请求: GET http://************:80/ISAPI/System/deviceInfo
2025-07-19 10:00:36,128 - httpx - INFO - HTTP Request: GET http://************/ISAPI/System/deviceInfo "HTTP/1.1 200 OK"
2025-07-19 10:00:36,128 - httpx - INFO - HTTP Request: GET http://************/ISAPI/System/deviceInfo "HTTP/1.1 200 OK"
2025-07-19 10:00:36,128 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200
2025-07-19 10:00:36,128 - app.api.endpoints.websdk_proxy - INFO - 设备响应: 200

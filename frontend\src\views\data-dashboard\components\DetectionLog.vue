<template>
  <div class="dashboard-card detection-log-card">
    <div class="card-header">
      <h3 class="card-title">
        <el-icon><Tickets /></el-icon>
        最近检测记录
      </h3>
      <el-button type="primary" link>查看更多记录</el-button>
    </div>
    <div class="card-content">
      <div class="log-list-header">
        <span class="log-time">时间</span>
        <span class="log-status">状态</span>
        <span class="log-group">检测组</span>
        <span class="log-duration">耗时</span>
      </div>
      <ul v-if="formattedLogs.length" class="log-list">
        <li v-for="log in formattedLogs" :key="log.id" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-status">
            <el-icon :class="log.isNormal ? 'status-success' : 'status-fail'"><component :is="log.isNormal ? CircleCheckFilled : CircleCloseFilled" /></el-icon>
            {{ log.isNormal ? '正常' : '卡料' }}
          </span>
          <span class="log-group">#{{ log.group }}</span>
          <span class="log-duration">{{ log.duration }}</span>
        </li>
      </ul>
      <el-empty v-else description="暂无记录" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PropType } from 'vue'
import { Tickets, CircleCheckFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import type { CardDetectionResultList } from '../types/dashboard.types'

const props = defineProps({
  logs: {
    type: Object as PropType<CardDetectionResultList | null>,
    required: true
  }
})

const formattedLogs = computed(() => {
  if (!props.logs?.items) return []
  return props.logs.items.slice(0, 10).map((log) => ({
    id: log.id,
    time: new Date(log.timestamp).toLocaleTimeString('zh-CN'),
    isNormal: log.is_normal,
    group: log.detection_group_id,
    duration: `${Math.round(log.detection_time)}ms`
  }))
})
</script>

<style scoped>
.detection-log-card {
  grid-column: 9 / -1;
  grid-row: 3;
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.log-list-header {
  display: flex;
  justify-content: space-between;
  padding: 0 4px 8px;
  font-size: 13px;
  color: var(--text-color-mute);
  font-weight: 600;
  flex-shrink: 0;
}

.log-list {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;
  overflow-y: auto;
}

.log-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 4px;
  border-bottom: 1px solid var(--border-color);
  font-size: 14px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: var(--text-color-mute);
  width: 70px;
}

.log-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  width: 80px;
}

.status-success {
  color: var(--success-color);
}

.status-fail {
  color: var(--danger-color);
}

.log-group {
  color: var(--text-color-soft);
  width: 60px;
}

.log-duration {
  font-weight: 500;
  color: var(--text-color);
  text-align: right;
  width: 60px;
}
</style> 
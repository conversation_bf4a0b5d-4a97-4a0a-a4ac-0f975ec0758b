<template>
  <div class="alarm-panel">
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon><warning /></el-icon>
        报警信息
        <el-badge v-if="unhandledCount > 0" :value="unhandledCount" class="alarm-badge" />
      </h3>
      <div class="panel-actions">
        <el-button size="small" type="primary" @click="handleAllAlarms" :disabled="unhandledCount === 0">
          全部处理
        </el-button>
      </div>
    </div>

    <div class="panel-content">
      <!-- 报警筛选 -->
      <div class="alarm-filters">
        <el-select v-model="filterLevel" size="small" placeholder="级别筛选" style="width: 100px">
          <el-option label="全部" value="all" />
          <el-option label="高级" value="high" />
          <el-option label="中级" value="medium" />
          <el-option label="低级" value="low" />
        </el-select>
        <el-select v-model="filterStatus" size="small" placeholder="状态筛选" style="width: 100px">
          <el-option label="全部" value="all" />
          <el-option label="待处理" value="unhandled" />
          <el-option label="已处理" value="handled" />
        </el-select>
      </div>

      <!-- 报警列表 -->
      <div class="alarm-list">
        <div 
          v-for="alarm in filteredAlarms" 
          :key="alarm.id"
          class="alarm-item"
          :class="[`level-${alarm.level}`, `status-${alarm.status}`]"
        >
          <!-- 报警级别指示器 -->
          <div class="alarm-indicator">
            <div class="level-dot" :class="alarm.level"></div>
          </div>

          <!-- 报警内容 -->
          <div class="alarm-content">
            <div class="alarm-header">
              <div class="alarm-type">
                <el-icon>
                  <warning v-if="alarm.type === 'jam'" />
                  <connection v-else-if="alarm.type === 'device_offline'" />
                  <cpu v-else-if="alarm.type === 'system_error'" />
                  <view v-else />
                </el-icon>
                <span>{{ getAlarmTypeText(alarm.type) }}</span>
              </div>
              <div class="alarm-time">{{ formatTime(alarm.timestamp) }}</div>
            </div>

            <div class="alarm-message">{{ alarm.message }}</div>

            <div class="alarm-details">
              <div class="detail-item" v-if="alarm.deviceName">
                <el-icon><monitor /></el-icon>
                <span>{{ alarm.deviceName }}</span>
              </div>
              <div class="detail-item" v-if="alarm.groupName">
                <el-icon><view /></el-icon>
                <span>{{ alarm.groupName }}</span>
              </div>
            </div>
          </div>

          <!-- 报警操作 -->
          <div class="alarm-actions">
            <el-button 
              v-if="alarm.status === 'unhandled'"
              size="small" 
              type="primary" 
              @click="handleAlarm(alarm, 'handle')"
            >
              处理
            </el-button>
            <el-button 
              v-if="alarm.status === 'unhandled'"
              size="small" 
              @click="handleAlarm(alarm, 'ignore')"
            >
              忽略
            </el-button>
            <el-tag v-else size="small" type="success">已处理</el-tag>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredAlarms.length === 0" class="empty-state">
          <el-icon><check /></el-icon>
          <p>暂无报警信息</p>
        </div>
      </div>

      <!-- 报警统计 -->
      <div class="alarm-statistics">
        <div class="stat-item">
          <div class="stat-label">总报警</div>
          <div class="stat-value">{{ alarms.length }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">待处理</div>
          <div class="stat-value unhandled">{{ unhandledCount }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">已处理</div>
          <div class="stat-value handled">{{ handledCount }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">今日新增</div>
          <div class="stat-value">{{ todayAlarmCount }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Warning, Connection, Cpu, View, Monitor, Check
} from '@element-plus/icons-vue'

// Props
interface Alarm {
  id: number
  type: 'jam' | 'device_offline' | 'system_error' | 'detection_error'
  level: 'high' | 'medium' | 'low'
  message: string
  timestamp: Date
  status: 'unhandled' | 'handled'
  deviceName?: string
  groupName?: string
}

const props = defineProps<{
  alarms: Alarm[]
}>()

// Emits
const emit = defineEmits<{
  alarmHandle: [alarm: Alarm, action: string]
}>()

// 响应式数据
const filterLevel = ref('all')
const filterStatus = ref('all')

// 计算属性
const filteredAlarms = computed(() => {
  let filtered = [...props.alarms]
  
  if (filterLevel.value !== 'all') {
    filtered = filtered.filter(alarm => alarm.level === filterLevel.value)
  }
  
  if (filterStatus.value !== 'all') {
    filtered = filtered.filter(alarm => alarm.status === filterStatus.value)
  }
  
  // 按时间倒序排列
  return filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
})

const unhandledCount = computed(() => 
  props.alarms.filter(alarm => alarm.status === 'unhandled').length
)

const handledCount = computed(() => 
  props.alarms.filter(alarm => alarm.status === 'handled').length
)

const todayAlarmCount = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return props.alarms.filter(alarm => alarm.timestamp >= today).length
})

// 方法
const getAlarmTypeText = (type: string) => {
  const typeMap = {
    jam: '卡料报警',
    device_offline: '设备离线',
    system_error: '系统错误',
    detection_error: '检测异常'
  }
  return typeMap[type] || '未知报警'
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`
  
  const days = Math.floor(hours / 24)
  return `${days}天前`
}

const handleAlarm = (alarm: Alarm, action: string) => {
  emit('alarmHandle', alarm, action)
}

const handleAllAlarms = () => {
  const unhandledAlarms = props.alarms.filter(alarm => alarm.status === 'unhandled')
  unhandledAlarms.forEach(alarm => {
    emit('alarmHandle', alarm, 'handle')
  })
  ElMessage.success(`已处理 ${unhandledAlarms.length} 条报警`)
}
</script>

<style scoped>
.alarm-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-color);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  position: relative;
}

.alarm-badge {
  margin-left: 8px;
}

.panel-content {
  flex: 1;
  padding: 16px 20px;
  overflow-y: auto;
}

.alarm-filters {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.alarm-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.alarm-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.alarm-item:hover {
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.1);
}

.alarm-item.level-high {
  border-left: 4px solid var(--danger-color);
}

.alarm-item.level-medium {
  border-left: 4px solid var(--warning-color);
}

.alarm-item.level-low {
  border-left: 4px solid var(--info-color);
}

.alarm-item.status-unhandled {
  background-color: rgba(var(--danger-color-rgb), 0.05);
}

.alarm-indicator {
  margin-right: 12px;
  margin-top: 4px;
}

.level-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.level-dot.high {
  background-color: var(--danger-color);
  animation: pulse 2s infinite;
}

.level-dot.medium {
  background-color: var(--warning-color);
}

.level-dot.low {
  background-color: var(--info-color);
}

.alarm-content {
  flex: 1;
  min-width: 0;
}

.alarm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.alarm-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
}

.alarm-time {
  font-size: 12px;
  color: var(--text-color-mute);
}

.alarm-message {
  font-size: 13px;
  color: var(--text-color-soft);
  margin-bottom: 8px;
  line-height: 1.4;
}

.alarm-details {
  display: flex;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-color-mute);
}

.alarm-actions {
  margin-left: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-color-mute);
}

.empty-state .el-icon {
  font-size: 48px;
  color: var(--success-color);
  margin-bottom: 16px;
}

.alarm-statistics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 16px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: var(--text-color-mute);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-color);
}

.stat-value.unhandled {
  color: var(--danger-color);
}

.stat-value.handled {
  color: var(--success-color);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>

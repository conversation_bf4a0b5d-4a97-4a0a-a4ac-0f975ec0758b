import { io, Socket } from 'socket.io-client'
import { ElMessage } from 'element-plus'

interface WebSocketOptions {
  url: string
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Error) => void
}

class WebSocketClient {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 2000
  private options: WebSocketOptions

  constructor(options: WebSocketOptions) {
    this.options = options
  }

  connect(): Socket {
    if (this.socket) {
      return this.socket
    }

    const baseURL = this.options.url || 'ws://localhost:8000'
    
    this.socket = io(baseURL, {
      transports: ['websocket'],
      reconnection: true,
      reconnectionDelay: this.reconnectInterval,
      reconnectionAttempts: this.maxReconnectAttempts
    })

    this.socket.on('connect', () => {
      console.log('WebSocket 连接成功')
      this.reconnectAttempts = 0
      if (this.options.onConnect) {
        this.options.onConnect()
      }
    })

    this.socket.on('disconnect', () => {
      console.log('WebSocket 断开连接')
      if (this.options.onDisconnect) {
        this.options.onDisconnect()
      }
    })

    this.socket.on('connect_error', (error) => {
      this.reconnectAttempts++
      console.error('WebSocket 连接错误:', error)
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        ElMessage.error('无法连接到服务器，请检查网络连接')
        if (this.options.onError) {
          this.options.onError(error)
        }
      }
    })

    return this.socket
  }

  // 注册事件监听
  on(event: string, callback: (...args: any[]) => void) {
    if (this.socket) {
      this.socket.on(event, callback)
    } else {
      console.error('WebSocket 未连接')
    }
  }

  // 发送事件
  emit(event: string, ...args: any[]) {
    if (this.socket) {
      this.socket.emit(event, ...args)
    } else {
      console.error('WebSocket 未连接')
    }
  }

  // 取消事件监听
  off(event: string, callback?: (...args: any[]) => void) {
    if (this.socket) {
      this.socket.off(event, callback)
    }
  }

  // 关闭连接
  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }
}

export default WebSocketClient 
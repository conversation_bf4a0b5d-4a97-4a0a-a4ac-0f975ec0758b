<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>WebSDK环境诊断工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .log { height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f8f9fa; font-family: monospace; font-size: 12px; }
        .video-container { width: 640px; height: 360px; border: 2px solid #ccc; background: #000; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebSDK环境诊断工具</h1>
        <p>根据WebSDK3.4编程指南进行全面的环境检查和问题诊断</p>
        
        <div class="section info">
            <h3>📋 环境检查结果</h3>
            <table id="envTable">
                <thead>
                    <tr>
                        <th>检查项目</th>
                        <th>状态</th>
                        <th>详细信息</th>
                        <th>建议</th>
                    </tr>
                </thead>
                <tbody id="envTableBody">
                    <!-- 动态填充 -->
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <h3>🚀 WebSDK功能测试</h3>
            <input type="text" id="deviceIp" placeholder="设备IP" value="************" style="margin: 5px; padding: 8px;">
            <input type="text" id="devicePort" placeholder="端口" value="80" style="margin: 5px; padding: 8px;">
            <input type="text" id="username" placeholder="用户名" value="admin" style="margin: 5px; padding: 8px;">
            <input type="password" id="password" placeholder="密码" value="admin123" style="margin: 5px; padding: 8px;">
            <br>
            <button class="btn btn-primary" onclick="testDirectMode()">测试直连模式</button>
            <button class="btn btn-warning" onclick="testProxyMode()">测试代理模式</button>
            <button class="btn btn-success" onclick="testAutoMode()">自动测试</button>
            <button class="btn btn-danger" onclick="stopTest()">停止测试</button>
        </div>
        
        <div id="videoContainer" class="video-container"></div>
        
        <div class="section">
            <h3>📝 诊断日志</h3>
            <button class="btn btn-warning" onclick="clearLog()">清空日志</button>
            <div class="log" id="logContainer"></div>
        </div>
    </div>

    <!-- WebSDK脚本 -->
    <script src="/websdk/codebase/jsPlugin/jquery.min.js"></script>
    <script src="/websdk/codebase/encryption/AES.js"></script>
    <script src="/websdk/codebase/encryption/cryptico.min.js"></script>
    <script src="/websdk/codebase/encryption/crypto-3.1.2.min.js"></script>
    <script>
        if (typeof CryptoJS !== 'undefined' && CryptoJS.MD5) {
            window.MD5 = function(message) { return CryptoJS.MD5(message).toString(); };
        }
    </script>
    <script src="/websdk/codebase/webVideoCtrl.js"></script>
    <script src="/websdk/codebase/jsPlugin/jsPlugin-3.0.0.min.js"></script>

    <script>
        let g_iWndIndex = 0;
        let currentDevice = null;
        let isInitialized = false;
        
        const envChecks = [
            { name: 'WebSDK加载', key: 'websdkLoaded', status: 'checking' },
            { name: 'jQuery加载', key: 'jqueryLoaded', status: 'checking' },
            { name: '浏览器支持', key: 'browserSupport', status: 'checking' },
            { name: '协议类型', key: 'protocol', status: 'checking' },
            { name: '跨域隔离头', key: 'corsHeaders', status: 'checking' },
            { name: '服务器环境', key: 'serverEnv', status: 'checking' },
            { name: 'WebSDK初始化', key: 'websdkInit', status: 'checking' }
        ];

        function addLog(message, type = 'info') {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const logItem = document.createElement('div');
            logItem.style.marginBottom = '3px';
            logItem.innerHTML = `<span style="color: #666;">[${timeStr}]</span> ${message}`;
            logContainer.appendChild(logItem);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }

        function updateEnvTable() {
            const tbody = document.getElementById('envTableBody');
            tbody.innerHTML = '';
            
            envChecks.forEach(check => {
                const row = tbody.insertRow();
                row.insertCell(0).textContent = check.name;
                
                const statusCell = row.insertCell(1);
                const detailCell = row.insertCell(2);
                const suggestionCell = row.insertCell(3);
                
                switch(check.status) {
                    case 'ok':
                        statusCell.innerHTML = '<span class="status-ok">✅ 正常</span>';
                        break;
                    case 'warning':
                        statusCell.innerHTML = '<span class="status-warning">⚠️ 警告</span>';
                        break;
                    case 'error':
                        statusCell.innerHTML = '<span class="status-error">❌ 错误</span>';
                        break;
                    default:
                        statusCell.innerHTML = '<span>⏳ 检查中...</span>';
                }
                
                detailCell.textContent = check.detail || '';
                suggestionCell.textContent = check.suggestion || '';
            });
        }

        function checkEnvironment() {
            addLog('🔍 开始环境诊断...');
            
            // 检查WebSDK
            const websdkCheck = envChecks.find(c => c.key === 'websdkLoaded');
            if (window.WebVideoCtrl) {
                websdkCheck.status = 'ok';
                websdkCheck.detail = 'WebSDK已正确加载';
                addLog('✅ WebSDK已加载');
            } else {
                websdkCheck.status = 'error';
                websdkCheck.detail = 'WebSDK未加载';
                websdkCheck.suggestion = '检查脚本引用路径';
                addLog('❌ WebSDK未加载');
            }

            // 检查jQuery
            const jqueryCheck = envChecks.find(c => c.key === 'jqueryLoaded');
            if (typeof $ !== 'undefined') {
                jqueryCheck.status = 'ok';
                jqueryCheck.detail = `jQuery ${$.fn.jquery} 已加载`;
                addLog('✅ jQuery已加载');
            } else {
                jqueryCheck.status = 'warning';
                jqueryCheck.detail = 'jQuery未加载，将使用原生DOM';
                jqueryCheck.suggestion = '建议加载jQuery以获得最佳兼容性';
                addLog('⚠️ jQuery未加载');
            }

            // 检查浏览器支持
            const browserCheck = envChecks.find(c => c.key === 'browserSupport');
            if (window.WebVideoCtrl && WebVideoCtrl.I_SupportNoPlugin()) {
                browserCheck.status = 'ok';
                browserCheck.detail = '浏览器支持无插件模式';
                addLog('✅ 浏览器支持无插件模式');
            } else {
                browserCheck.status = 'error';
                browserCheck.detail = '浏览器不支持无插件模式';
                browserCheck.suggestion = '升级到Chromium 91+内核浏览器';
                addLog('❌ 浏览器不支持无插件模式');
            }

            // 检查协议类型
            const protocolCheck = envChecks.find(c => c.key === 'protocol');
            const isHttps = window.location.protocol === 'https:';
            if (isHttps) {
                protocolCheck.status = 'ok';
                protocolCheck.detail = 'HTTPS协议（推荐）';
                protocolCheck.suggestion = '确保设置了正确的跨域隔离头';
                addLog('✅ 使用HTTPS协议');
            } else {
                protocolCheck.status = 'warning';
                protocolCheck.detail = 'HTTP协议';
                protocolCheck.suggestion = '建议使用HTTPS以获得最佳性能';
                addLog('⚠️ 使用HTTP协议');
            }

            // 检查跨域隔离头
            const corsCheck = envChecks.find(c => c.key === 'corsHeaders');
            if (isHttps) {
                // 在HTTPS下检查跨域隔离头
                corsCheck.status = 'warning';
                corsCheck.detail = '需要检查COEP和COOP头设置';
                corsCheck.suggestion = '确保服务器设置了正确的跨域隔离头';
                addLog('⚠️ HTTPS环境需要检查跨域隔离头');
            } else {
                corsCheck.status = 'ok';
                corsCheck.detail = 'HTTP环境无需跨域隔离头';
                addLog('✅ HTTP环境无需跨域隔离头');
            }

            // 检查服务器环境
            const serverCheck = envChecks.find(c => c.key === 'serverEnv');
            const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            if (isDev) {
                serverCheck.status = 'warning';
                serverCheck.detail = '开发环境，可能需要代理模式';
                serverCheck.suggestion = '某些设备在开发环境下需要bProxy=true';
                addLog('⚠️ 开发环境，建议测试代理模式');
            } else {
                serverCheck.status = 'ok';
                serverCheck.detail = '生产环境';
                addLog('✅ 生产环境');
            }

            updateEnvTable();
            
            // 初始化WebSDK
            if (window.WebVideoCtrl && WebVideoCtrl.I_SupportNoPlugin()) {
                initWebSDK();
            }
        }

        function initWebSDK() {
            addLog('🔧 初始化WebSDK...');
            
            WebVideoCtrl.I_InitPlugin("100%", "100%", {
                bWndFull: true,
                iPackageType: 2,
                iWndowType: 1,
                bNoPlugin: true,
                cbSelWnd: function(xmlDoc) {
                    g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
                    addLog(`🎯 选择窗口: ${g_iWndIndex}`);
                },
                cbInitPluginComplete: function() {
                    addLog('✅ WebSDK插件初始化完成');
                    WebVideoCtrl.I_InsertOBJECTPlugin("videoContainer");
                    addLog('✅ 插件嵌入完成');
                    isInitialized = true;
                    
                    const initCheck = envChecks.find(c => c.key === 'websdkInit');
                    initCheck.status = 'ok';
                    initCheck.detail = 'WebSDK初始化成功';
                    updateEnvTable();
                },
                cbPluginErrorHandler: function(iWndIndex, iErrorCode, oError) {
                    addLog(`❌ 插件错误: 窗口${iWndIndex}, 错误码${iErrorCode}`);
                }
            });
        }

        function testDirectMode() {
            addLog('🔗 测试直连模式 (bProxy: false)...');
            testPreview(false);
        }

        function testProxyMode() {
            addLog('🌐 测试代理模式 (bProxy: true)...');
            testPreview(true);
        }

        function testAutoMode() {
            addLog('🚀 自动测试模式...');
            // 先测试直连，失败后自动测试代理
            testPreview(false, true);
        }

        function testPreview(bProxy, autoFallback = false) {
            if (!isInitialized) {
                addLog('❌ WebSDK未初始化');
                return;
            }

            const ip = document.getElementById('deviceIp').value;
            const port = document.getElementById('devicePort').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!ip || !port) {
                addLog('❌ 请填写设备IP和端口');
                return;
            }

            const deviceIdentify = `${ip}_${port}`;
            
            // 先登录设备
            addLog(`🔑 登录设备: ${ip}:${port}`);
            WebVideoCtrl.I_Login(ip, 1, parseInt(port), username, password, {
                success: function(xmlDoc) {
                    addLog(`✅ 设备登录成功`);
                    currentDevice = { ip, port, username, password, deviceIdentify };
                    
                    // 开始预览测试
                    const modeText = bProxy ? '代理模式' : '直连模式';
                    addLog(`🎬 开始${modeText}预览测试...`);
                    
                    // 先获取设备端口信息
                    const oPort = WebVideoCtrl.I_GetDevicePort(deviceIdentify);
                    let iRtspPort = 554; // 默认端口
                    if (oPort != null) {
                        iRtspPort = oPort.iRtspPort || 554;
                        addLog(`✅ 获取设备端口: RTSP=${iRtspPort}, 设备=${oPort.iDevicePort}`);
                    } else {
                        addLog(`⚠️ 获取设备端口失败，使用默认端口: ${iRtspPort}`);
                    }

                    WebVideoCtrl.I_StartRealPlay(deviceIdentify, {
                        iWndIndex: g_iWndIndex,  // 明确指定播放窗口
                        iStreamType: 1,          // 主码流
                        iChannelID: 1,           // 通道1
                        bZeroChannel: false,     // 非零通道
                        iRtspPort: iRtspPort,   // 使用获取到的正确端口
                        bProxy: bProxy,
                        success: function() {
                            addLog(`✅ ${modeText}预览成功！`);
                            setTimeout(() => {
                                WebVideoCtrl.I_Stop();
                                addLog('⏹️ 测试预览已停止');
                            }, 3000);
                        },
                        error: function(status, xmlDoc) {
                            // 详细的错误信息解析
                            let errorDetail = `状态码: ${status || 'undefined'}`
                            if (xmlDoc) {
                                try {
                                    const parser = new DOMParser()
                                    const doc = parser.parseFromString(xmlDoc, 'text/xml')
                                    const statusString = doc.getElementsByTagName('statusString')[0]?.textContent
                                    const subStatusCode = doc.getElementsByTagName('subStatusCode')[0]?.textContent
                                    if (statusString) errorDetail += `, 错误: ${statusString}`
                                    if (subStatusCode) errorDetail += `, 子状态: ${subStatusCode}`
                                } catch (e) {
                                    console.warn('解析错误XML失败:', e)
                                }
                            }

                            addLog(`❌ ${modeText}预览失败: ${errorDetail}`);

                            // 如果是undefined状态码，可能是WebSocket连接问题
                            if (status === undefined || status === null) {
                                addLog('⚠️ 状态码为undefined，可能的原因：');
                                addLog('   1. 设备不支持WebSocket协议');
                                addLog('   2. 网络连接问题');
                                addLog('   3. 设备固件版本过低');
                                addLog('   4. 防火墙阻止WebSocket连接');
                            }

                            if (autoFallback && !bProxy && (status === 403 || status === undefined)) {
                                addLog('🔄 自动切换到代理模式...');
                                setTimeout(() => testPreview(true, false), 1000);
                            }
                        }
                    });
                },
                error: function(status, xmlDoc) {
                    addLog(`❌ 设备登录失败: ${status}`);
                }
            });
        }

        function stopTest() {
            if (window.WebVideoCtrl) {
                WebVideoCtrl.I_Stop();
                addLog('⏹️ 已停止所有测试');
            }
        }

        // 页面加载完成后开始诊断
        window.onload = function() {
            addLog('🌟 WebSDK环境诊断工具启动');
            updateEnvTable();
            setTimeout(checkEnvironment, 500);
        };
    </script>
</body>
</html>

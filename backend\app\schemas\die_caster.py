from datetime import datetime
from typing import Optional, List, Dict, Any, Generic, TypeVar

from pydantic import BaseModel


# 共享属性
class DieCasterBase(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    ip_address: Optional[str] = None
    port: Optional[int] = None
    status: Optional[str] = "offline"


# 创建时需要的属性
class DieCasterCreate(DieCasterBase):
    name: str = ""
    

# 更新时可以更新的属性
class DieCasterUpdate(DieCasterBase):
    pass


# API响应中包含的属性
class DieCasterInDBBase(DieCasterBase):
    id: int
    name: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


# 返回给API的压铸机信息
class DieCaster(DieCasterInDBBase):
    pass


 





# 检测组基础属性
class DetectionGroupBase(BaseModel):
    name: Optional[str] = None
    template_id: Optional[int] = None
    die_caster_id: Optional[int] = None
    video_source_id: Optional[int] = None
    status: Optional[str] = "inactive"
    config_json: Optional[Dict[str, Any]] = None


# 创建检测组
class DetectionGroupCreate(DetectionGroupBase):
    name: str = ""
    template_id: int = 0
    die_caster_id: int = 0
    video_source_id: int = 0


# 更新检测组
class DetectionGroupUpdate(DetectionGroupBase):
    pass


# 检测组数据库基础属性
class DetectionGroupInDBBase(DetectionGroupBase):
    id: int
    name: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


# 返回给API的检测组信息
class DetectionGroup(DetectionGroupInDBBase):
    pass


# 视频源基础属性
class VideoSourceBase(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    source_type: Optional[str] = None
    path: Optional[str] = None


# 创建视频源
class VideoSourceCreate(VideoSourceBase):
    name: str = ""
    source_type: str = ""
    path: str = ""


# 更新视频源
class VideoSourceUpdate(VideoSourceBase):
    pass


# 视频源数据库基础属性
class VideoSourceInDBBase(VideoSourceBase):
    id: int
    name: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


# 返回给API的视频源信息
class VideoSource(VideoSourceInDBBase):
    pass




# 通用分页响应模型
T = TypeVar('T')
class PaginatedResponse(BaseModel, Generic[T]):
    items: List[T]
    total: int
    page: int
    size: int
    pages: int
<template>
  <div class="roi-drawer">
    <!-- 透明绘制画布 - 只有画布，叠加在视频上方 -->
    <canvas
      ref="roiCanvas"
      @mousedown="onMouseDown"
      @mousemove="onMouseMove"
      @mouseup="onMouseUp"
      @dblclick="onDoubleClick"
      class="roi-overlay-canvas"
      :style="canvasStyle"
    ></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue'

interface Point {
  x: number
  y: number
}

interface ROI {
  id: string
  name: string
  type: 'rectangle' | 'polygon'
  points: Point[]
  color: string
}

const props = defineProps<{
  videoWidth: number
  videoHeight: number
  isDrawingEnabled: boolean
  drawMode: 'rectangle' | 'polygon' | null
}>()

const emit = defineEmits<{
  roisChanged: [rois: ROI[]]
}>()

// 响应式数据
const roiCanvas = ref<HTMLCanvasElement>()
const isDrawing = ref(false)
const currentROI = ref<ROI | null>(null)
const rois = ref<ROI[]>([])

// 绘制状态
const startPoint = ref<Point | null>(null)
const polygonPoints = ref<Point[]>([])

// 颜色列表
const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff']
let colorIndex = 0

// 计算画布样式，使其完全覆盖视频区域
const canvasStyle = computed(() => ({
  width: `${props.videoWidth}px`,
  height: `${props.videoHeight}px`,
  pointerEvents: props.isDrawingEnabled ? 'auto' : 'none',
  position: 'absolute',
  top: '0',
  left: '0',
  zIndex: '20'
}))

onMounted(() => {
  initCanvas()
})

const initCanvas = () => {
  nextTick(() => {
    if (roiCanvas.value) {
      const canvas = roiCanvas.value

      // 设置画布实际像素大小
      canvas.width = props.videoWidth || 640
      canvas.height = props.videoHeight || 360

      // 初始化画布上下文
      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.imageSmoothingEnabled = true
        ctx.lineCap = 'round'
        ctx.lineJoin = 'round'
      }

      console.log('ROI Canvas initialized:', canvas.width, 'x', canvas.height)
      redrawROIs()
    }
  })
}



const getMousePos = (event: MouseEvent): Point => {
  const canvas = roiCanvas.value
  if (!canvas) return { x: 0, y: 0 }

  const rect = canvas.getBoundingClientRect()
  const scaleX = canvas.width / rect.width
  const scaleY = canvas.height / rect.height

  const x = (event.clientX - rect.left) * scaleX
  const y = (event.clientY - rect.top) * scaleY

  console.log('Mouse position:', { x, y, rect, scale: { scaleX, scaleY } })

  return { x, y }
}

const onMouseDown = (event: MouseEvent) => {
  if (!props.drawMode || !props.isDrawingEnabled) {
    console.log('绘制被阻止:', { drawMode: props.drawMode, enabled: props.isDrawingEnabled })
    return
  }

  event.preventDefault()
  event.stopPropagation()

  const pos = getMousePos(event)
  console.log('开始绘制:', pos)
  
  if (props.drawMode === 'rectangle') {
    isDrawing.value = true
    startPoint.value = pos
    currentROI.value = {
      id: generateId(),
      name: `矩形ROI ${rois.value.length + 1}`,
      type: 'rectangle',
      points: [pos],
      color: colors[colorIndex % colors.length]
    }
    colorIndex++
  } else if (props.drawMode === 'polygon') {
    if (!isDrawing.value) {
      // 开始绘制多边形
      isDrawing.value = true
      polygonPoints.value = [pos]
      currentROI.value = {
        id: generateId(),
        name: `多边形ROI ${rois.value.length + 1}`,
        type: 'polygon',
        points: [pos],
        color: colors[colorIndex % colors.length]
      }
      colorIndex++
    } else {
      // 添加多边形点
      polygonPoints.value.push(pos)
      currentROI.value!.points = [...polygonPoints.value]
    }
  }
  
  redrawROIs()
}

const onMouseMove = (event: MouseEvent) => {
  if (!isDrawing.value || !currentROI.value || !props.isDrawingEnabled) return

  event.preventDefault()
  const pos = getMousePos(event)

  if (props.drawMode === 'rectangle' && startPoint.value) {
    // 更新矩形的第二个点
    currentROI.value.points = [startPoint.value, pos]
    redrawROIs()
  } else if (props.drawMode === 'polygon') {
    // 显示多边形预览线
    redrawROIs()
    drawPreviewLine(pos)
  }
}

const onMouseUp = (event: MouseEvent) => {
  if (props.drawMode === 'rectangle' && isDrawing.value && currentROI.value) {
    // 完成矩形绘制
    rois.value.push(currentROI.value)
    isDrawing.value = false
    currentROI.value = null
    startPoint.value = null
    emit('roisChanged', rois.value)
  }
}

const onDoubleClick = (event: MouseEvent) => {
  if (props.drawMode === 'polygon' && isDrawing.value && currentROI.value) {
    // 完成多边形绘制
    if (polygonPoints.value.length >= 3) {
      rois.value.push(currentROI.value)
      emit('roisChanged', rois.value)
    }

    // 重置状态
    isDrawing.value = false
    currentROI.value = null
    polygonPoints.value = []
  }
}

const redrawROIs = () => {
  const canvas = roiCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  try {
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 绘制已保存的ROI
    rois.value.forEach(roi => {
      drawROI(ctx, roi)
    })

    // 绘制当前正在绘制的ROI
    if (currentROI.value) {
      drawROI(ctx, currentROI.value, true)
    }
  } catch (error) {
    console.error('绘制ROI时出错:', error)
  }
}

const drawROI = (ctx: CanvasRenderingContext2D, roi: ROI, isPreview = false) => {
  if (!ctx || !roi || !roi.points || roi.points.length === 0) return

  try {
    ctx.strokeStyle = roi.color
    ctx.fillStyle = roi.color + '20' // 半透明填充
    ctx.lineWidth = 2
  
  // 🔥 优先使用coordinates字段，兼容points字段
  const roiPoints = roi.coordinates || roi.points

  if (roi.roi_type === 'rectangle' && roiPoints.length >= 2) {
    const [start, end] = roiPoints
    const width = end.x - start.x
    const height = end.y - start.y

    ctx.strokeRect(start.x, start.y, width, height)
    if (!isPreview) {
      ctx.fillRect(start.x, start.y, width, height)
    }
  } else if (roi.roi_type === 'polygon' && roiPoints.length >= 2) {
    ctx.beginPath()
    ctx.moveTo(roiPoints[0].x, roiPoints[0].y)

    for (let i = 1; i < roiPoints.length; i++) {
      ctx.lineTo(roiPoints[i].x, roiPoints[i].y)
    }

    if (!isPreview && roiPoints.length >= 3) {
      ctx.closePath()
      ctx.fill()
    }
    
    ctx.stroke()
  }
  
    // 绘制ROI标签
    if (!isPreview && roi.points.length > 0) {
      ctx.fillStyle = roi.color
      ctx.font = '12px Arial'
      ctx.fillText(roi.name, roi.points[0].x, roi.points[0].y - 5)
    }
  } catch (error) {
    console.error('绘制单个ROI时出错:', error)
  }
}

const drawPreviewLine = (currentPos: Point) => {
  if (!currentROI.value || polygonPoints.value.length === 0) return

  const canvas = roiCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  try {
    ctx.strokeStyle = currentROI.value.color
    ctx.lineWidth = 1
    ctx.setLineDash([5, 5])

    const lastPoint = polygonPoints.value[polygonPoints.value.length - 1]
    ctx.beginPath()
    ctx.moveTo(lastPoint.x, lastPoint.y)
    ctx.lineTo(currentPos.x, currentPos.y)
    ctx.stroke()

    ctx.setLineDash([])
  } catch (error) {
    console.error('绘制预览线时出错:', error)
  }
}

const clearROIs = () => {
  rois.value = []
  isDrawing.value = false
  currentROI.value = null
  polygonPoints.value = []
  redrawROIs()
  emit('roisChanged', rois.value)
}

const editROI = (index: number) => {
  const roi = rois.value[index]
  if (roi) {
    // 进入编辑模式
    isDrawingEnabled.value = true
    drawMode.value = roi.roi_type
    currentROI.value = { ...roi }

    // 如果是多边形，设置编辑点
    if (roi.roi_type === 'polygon') {
      polygonPoints.value = [...roi.points]
    }

    // 从列表中临时移除，编辑完成后重新添加
    rois.value.splice(index, 1)
    redrawROIs()
  }
}

const deleteROI = (index: number) => {
  rois.value.splice(index, 1)
  redrawROIs()
  emit('roisChanged', rois.value)
}

const saveROIs = () => {
  // 这里可以调用API保存ROI配置
  console.log('保存ROI配置:', rois.value)
  emit('roisChanged', rois.value)
}

const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 暴露方法给父组件
defineExpose({
  clearROIs,
  setROIs: (newROIs: ROI[]) => {
    try {
      if (Array.isArray(newROIs)) {
        rois.value = newROIs
        redrawROIs()
      }
    } catch (error) {
      console.error('设置ROI时出错:', error)
    }
  },
  getRois: () => rois.value,
  rois: rois.value
})
</script>

<style scoped>
.roi-drawer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 叠加画布样式 */
.roi-overlay-canvas {
  cursor: crosshair;
  pointer-events: none;
}

.roi-overlay-canvas[style*="pointer-events: auto"] {
  pointer-events: auto;
}
</style>

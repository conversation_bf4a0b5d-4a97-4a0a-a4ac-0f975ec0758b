"""add_material_jam_tables

Revision ID: bf57647ab1e8
Revises: 5470e460ad8a
Create Date: 2025-07-11 09:39:16.728958

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'bf57647ab1e8'
down_revision = '5470e460ad8a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_jam_detection_events_event_id', table_name='jam_detection_events')
    op.drop_index('ix_jam_detection_events_id', table_name='jam_detection_events')
    op.drop_index('ix_jam_detection_events_timestamp', table_name='jam_detection_events')
    op.drop_table('jam_detection_events')
    op.alter_column('detection_groups', 'id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
    op.alter_column('detection_groups', 'config_json',
               existing_type=sa.TEXT(),
               type_=sa.JSON(),
               nullable=False)
    op.alter_column('detection_groups', 'created_at',
               existing_type=sa.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_nullable=True,
               existing_server_default=sa.text('(CURRENT_TIMESTAMP)'))
    op.alter_column('detection_groups', 'updated_at',
               existing_type=sa.TIMESTAMP(),
               type_=sa.DateTime(),
               existing_nullable=True,
               existing_server_default=sa.text('(CURRENT_TIMESTAMP)'))
    op.create_index(op.f('ix_detection_groups_id'), 'detection_groups', ['id'], unique=False)
    op.drop_constraint(None, 'detection_groups', type_='foreignkey')
    op.drop_constraint(None, 'detection_groups', type_='foreignkey')
    op.create_foreign_key(None, 'detection_groups', 'die_casters', ['die_caster_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(None, 'detection_groups', 'video_sources', ['video_source_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(None, 'detection_groups', 'detection_templates', ['template_id'], ['id'], ondelete='CASCADE')
    op.create_unique_constraint(None, 'video_sources', ['name'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'video_sources', type_='unique')
    op.drop_constraint(None, 'detection_groups', type_='foreignkey')
    op.drop_constraint(None, 'detection_groups', type_='foreignkey')
    op.drop_constraint(None, 'detection_groups', type_='foreignkey')
    op.create_foreign_key(None, 'detection_groups', 'video_sources', ['video_source_id'], ['id'])
    op.create_foreign_key(None, 'detection_groups', 'die_casters', ['die_caster_id'], ['id'])
    op.drop_index(op.f('ix_detection_groups_id'), table_name='detection_groups')
    op.alter_column('detection_groups', 'updated_at',
               existing_type=sa.DateTime(),
               type_=sa.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('(CURRENT_TIMESTAMP)'))
    op.alter_column('detection_groups', 'created_at',
               existing_type=sa.DateTime(),
               type_=sa.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('(CURRENT_TIMESTAMP)'))
    op.alter_column('detection_groups', 'config_json',
               existing_type=sa.JSON(),
               type_=sa.TEXT(),
               nullable=True)
    op.alter_column('detection_groups', 'id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)
    op.create_table('jam_detection_events',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('event_id', sa.VARCHAR(length=36), nullable=True),
    sa.Column('timestamp', sa.DATETIME(), nullable=True),
    sa.Column('trigger_roi_id', sa.VARCHAR(length=50), nullable=True),
    sa.Column('stuck_roi_ids', sa.TEXT(), nullable=True),
    sa.Column('active_roi_ids', sa.TEXT(), nullable=True),
    sa.Column('total_discharge_rois', sa.INTEGER(), nullable=True),
    sa.Column('delay_time', sa.FLOAT(), nullable=True),
    sa.Column('pause_threshold', sa.FLOAT(), nullable=True),
    sa.Column('cooldown_time', sa.FLOAT(), nullable=True),
    sa.Column('is_acknowledged', sa.BOOLEAN(), nullable=True),
    sa.Column('acknowledged_at', sa.DATETIME(), nullable=True),
    sa.Column('acknowledged_by', sa.VARCHAR(length=50), nullable=True),
    sa.Column('notes', sa.TEXT(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_jam_detection_events_timestamp', 'jam_detection_events', ['timestamp'], unique=False)
    op.create_index('ix_jam_detection_events_id', 'jam_detection_events', ['id'], unique=False)
    op.create_index('ix_jam_detection_events_event_id', 'jam_detection_events', ['event_id'], unique=False)
    # ### end Alembic commands ### 
"""rebuild_video_sources_table_with_nullable_path

Revision ID: 5470e460ad8a
Revises: f1e2d9154f3e
Create Date: 2025-07-06 07:07:21.007678

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5470e460ad8a'
down_revision = 'f1e2d9154f3e'
branch_labels = None
depends_on = None


def upgrade():
    # 重建video_sources表，使path字段可空
    # 1. 创建新表
    op.create_table('video_sources_temp',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('source_type', sa.String(), nullable=False),
        sa.Column('path', sa.String(), nullable=True),  # 改为可空
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('device_ip', sa.String(), nullable=True),
        sa.Column('device_port', sa.Integer(), nullable=True),
        sa.Column('device_username', sa.String(), nullable=True),
        sa.Column('device_password', sa.String(), nullable=True),
        sa.Column('device_protocol', sa.Integer(), nullable=True),
        sa.Column('channel_id', sa.Integer(), nullable=True),
        sa.Column('stream_type', sa.Integer(), nullable=True),
        sa.Column('device_status', sa.String(), nullable=True),
        sa.Column('last_connected_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # 2. 复制数据
    op.execute("""
        INSERT INTO video_sources_temp
        SELECT id, name, description, source_type,
               CASE
                   WHEN source_type = 'websdk_device' THEN ''
                   ELSE path
               END as path,
               created_at, updated_at, device_ip, device_port, device_username,
               device_password, device_protocol, channel_id, stream_type,
               device_status, last_connected_at
        FROM video_sources
    """)

    # 3. 删除旧表
    op.drop_table('video_sources')

    # 4. 重命名新表
    op.rename_table('video_sources_temp', 'video_sources')

    # 5. 重新创建索引
    op.create_index(op.f('ix_video_sources_id'), 'video_sources', ['id'], unique=False)


def downgrade():
    # 回滚：重建表，path字段改回NOT NULL
    # 1. 创建旧表结构
    op.create_table('video_sources_temp',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('source_type', sa.String(), nullable=False),
        sa.Column('path', sa.String(), nullable=False),  # 改回NOT NULL
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('device_ip', sa.String(), nullable=True),
        sa.Column('device_port', sa.Integer(), nullable=True),
        sa.Column('device_username', sa.String(), nullable=True),
        sa.Column('device_password', sa.String(), nullable=True),
        sa.Column('device_protocol', sa.Integer(), nullable=True),
        sa.Column('channel_id', sa.Integer(), nullable=True),
        sa.Column('stream_type', sa.Integer(), nullable=True),
        sa.Column('device_status', sa.String(), nullable=True),
        sa.Column('last_connected_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # 2. 复制数据，确保path不为空
    op.execute("""
        INSERT INTO video_sources_temp
        SELECT id, name, description, source_type,
               CASE
                   WHEN path IS NULL OR path = '' THEN 'N/A'
                   ELSE path
               END as path,
               created_at, updated_at, device_ip, device_port, device_username,
               device_password, device_protocol, channel_id, stream_type,
               device_status, last_connected_at
        FROM video_sources
    """)

    # 3. 删除新表
    op.drop_table('video_sources')

    # 4. 重命名旧表
    op.rename_table('video_sources_temp', 'video_sources')

    # 5. 重新创建索引
    op.create_index(op.f('ix_video_sources_id'), 'video_sources', ['id'], unique=False)
import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def get_auth_token(username="admin", password="123456"):
    """获取认证token"""
    login_url = f"{API_BASE}/auth/login/json"
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data["access_token"]
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_api(method, url, headers=None, data=None):
    """测试API接口"""
    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers)
        
        print(f"{method} {url}")
        print(f"状态码: {response.status_code}")
        try:
            print(f"响应: {response.json()}")
        except:
            print(f"响应: {response.text}")
        print("-" * 50)
        
        return response
    except Exception as e:
        print(f"请求失败: {e}")
        print("-" * 50)
        return None

def main():
    print("获取认证token...")
    token = get_auth_token()
    
    if not token:
        print("无法获取认证token，退出测试")
        return
    
    print(f"获取到token: {token[:20]}...")
    print("\n开始测试预设检测计划API...\n")
    
    # 设置认证头
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试各个API
    
    # 1. 获取所有预设检测计划
    test_api("GET", f"{API_BASE}/preset-schedules/", headers)
    
    # 2. 获取特定预设检测计划
    test_api("GET", f"{API_BASE}/preset-schedules/1", headers)
    
    # 3. 检查时间冲突
    conflict_data = {
        "date": "2025-01-15",
        "startTime": "09:00",
        "endTime": "10:00",
        "excludeId": None
    }
    test_api("POST", f"{API_BASE}/preset-schedules/check-conflict", headers, conflict_data)
    
    # 4. 更新预设检测计划
    update_data = {
        "name": "测试更新计划",
        "description": "这是一个测试更新",
        "templateId": 1,
        "date": "2025-01-15",
        "startTime": "14:00",
        "endTime": "15:00",
        "isEnabled": True
    }
    test_api("PUT", f"{API_BASE}/preset-schedules/1", headers, update_data)
    
    # 5. 切换启用状态
    test_api("PUT", f"{API_BASE}/preset-schedules/1/toggle", headers)
    
    # 6. 执行预设检测计划
    test_api("POST", f"{API_BASE}/preset-schedules/1/execute", headers)
    
    # 7. 停止预设检测计划
    test_api("POST", f"{API_BASE}/preset-schedules/1/stop", headers)
    
    # 8. 获取执行状态
    test_api("GET", f"{API_BASE}/preset-schedules/1/status", headers)
    
    # 9. 按日期获取预设检测计划
    test_api("GET", f"{API_BASE}/preset-schedules/by-date/2025-01-15", headers)
    
    # 10. 创建新的预设检测计划
    create_data = {
        "name": "新建测试计划",
        "description": "这是一个新建的测试计划",
        "templateId": 1,
        "date": "2025-01-16",
        "startTime": "16:00",
        "endTime": "17:00",
        "isEnabled": True
    }
    test_api("POST", f"{API_BASE}/preset-schedules/", headers, create_data)
    
    print("\n所有API测试完成！")

if __name__ == "__main__":
    main()
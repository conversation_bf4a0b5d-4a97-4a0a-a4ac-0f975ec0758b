<template>
  <div class="realtime-detection-panel">
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon><view /></el-icon>
        实时检测状态
      </h3>
      <div class="panel-actions">
        <el-select v-model="filterStatus" size="small" placeholder="状态筛选" style="width: 120px">
          <el-option label="全部" value="all" />
          <el-option label="监测中" value="monitoring" />
          <el-option label="空闲" value="idle" />
          <el-option label="冷却" value="cooldown" />
          <el-option label="离线" value="offline" />
        </el-select>
      </div>
    </div>

    <div class="panel-content">
      <!-- 检测组网格 -->
      <div class="detection-grid">
        <div 
          v-for="group in filteredGroups" 
          :key="group.id"
          class="detection-group"
          :class="[`status-${group.status}`, { 'selected': selectedGroup?.id === group.id }]"
          @click="handleGroupSelect(group)"
        >
          <!-- 状态指示器 -->
          <div class="group-status-indicator">
            <div class="status-dot" :class="group.status"></div>
            <span class="status-text">{{ getStatusText(group.status) }}</span>
          </div>

          <!-- 组信息 -->
          <div class="group-info">
            <div class="group-name">{{ group.name }}</div>
            <div class="group-device">{{ getDeviceName(group.deviceId) }}</div>
          </div>

          <!-- ROI统计 -->
          <div class="roi-stats">
            <div class="stat-item">
              <el-icon><aim /></el-icon>
              <span>{{ group.roiCount }} ROI</span>
            </div>
            <div class="stat-item" v-if="group.alarmCount > 0">
              <el-icon><warning /></el-icon>
              <span class="alarm-count">{{ group.alarmCount }}</span>
            </div>
          </div>

          <!-- 检测算法状态 -->
          <div class="algorithm-indicators">
            <div class="algorithm-badge motion" :class="{ active: group.status === 'monitoring' }">
              <el-icon><view /></el-icon>
              <span>运动</span>
            </div>
            <div class="algorithm-badge direction" :class="{ active: group.status === 'monitoring' }">
              <el-icon><sort /></el-icon>
              <span>方向</span>
            </div>
            <div class="algorithm-badge jam" :class="{ active: group.status === 'monitoring' }">
              <el-icon><warning /></el-icon>
              <span>卡料</span>
            </div>
          </div>

          <!-- 性能指标 -->
          <div class="performance-metrics" v-if="group.status !== 'offline'">
            <div class="metric">
              <span class="metric-label">FPS</span>
              <span class="metric-value">{{ getGroupFPS(group.id) }}</span>
            </div>
            <div class="metric">
              <span class="metric-label">延迟</span>
              <span class="metric-value">{{ getGroupLatency(group.id) }}ms</span>
            </div>
          </div>

          <!-- 最后检测时间 -->
          <div class="last-detection">
            <el-icon><clock /></el-icon>
            <span>{{ formatLastDetection(group.id) }}</span>
          </div>
        </div>
      </div>

      <!-- 检测统计摘要 -->
      <div class="detection-summary">
        <div class="summary-card">
          <div class="summary-title">检测统计</div>
          <div class="summary-stats">
            <div class="stat">
              <span class="stat-label">总检测组</span>
              <span class="stat-value">{{ detectionGroups.length }}</span>
            </div>
            <div class="stat">
              <span class="stat-label">监测中</span>
              <span class="stat-value monitoring">{{ monitoringCount }}</span>
            </div>
            <div class="stat">
              <span class="stat-label">空闲</span>
              <span class="stat-value idle">{{ idleCount }}</span>
            </div>
            <div class="stat">
              <span class="stat-label">离线</span>
              <span class="stat-value offline">{{ offlineCount }}</span>
            </div>
          </div>
        </div>

        <div class="summary-card">
          <div class="summary-title">今日检测</div>
          <div class="summary-stats">
            <div class="stat">
              <span class="stat-label">检测次数</span>
              <span class="stat-value">{{ todayStats.detections }}</span>
            </div>
            <div class="stat">
              <span class="stat-label">报警次数</span>
              <span class="stat-value alarm">{{ todayStats.alarms }}</span>
            </div>
            <div class="stat">
              <span class="stat-label">卡料率</span>
              <span class="stat-value">{{ todayStats.jamRate }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { View, Aim, Warning, Sort, Clock } from '@element-plus/icons-vue'

// Props
interface DetectionGroup {
  id: number
  name: string
  deviceId: number
  status: 'monitoring' | 'idle' | 'cooldown' | 'offline'
  roiCount: number
  alarmCount: number
}

const props = defineProps<{
  detectionGroups: DetectionGroup[]
}>()

// Emits
const emit = defineEmits<{
  groupSelect: [group: DetectionGroup]
}>()

// 响应式数据
const filterStatus = ref('all')
const selectedGroup = ref<DetectionGroup | null>(null)

// 模拟数据
const deviceNames = {
  1: '压铸机1',
  2: '压铸机2', 
  3: '压铸机3',
  4: '压铸机4'
}

const todayStats = ref({
  detections: 1250,
  alarms: 8,
  jamRate: 0.64
})

// 计算属性
const filteredGroups = computed(() => {
  if (filterStatus.value === 'all') {
    return props.detectionGroups
  }
  return props.detectionGroups.filter(group => group.status === filterStatus.value)
})

const monitoringCount = computed(() => 
  props.detectionGroups.filter(g => g.status === 'monitoring').length
)

const idleCount = computed(() => 
  props.detectionGroups.filter(g => g.status === 'idle').length
)

const offlineCount = computed(() => 
  props.detectionGroups.filter(g => g.status === 'offline').length
)

// 方法
const getStatusText = (status: string) => {
  const statusMap = {
    monitoring: '监测中',
    idle: '空闲',
    cooldown: '冷却',
    offline: '离线'
  }
  return statusMap[status] || '未知'
}

const getDeviceName = (deviceId: number) => {
  return deviceNames[deviceId] || `设备${deviceId}`
}

const getGroupFPS = (groupId: number) => {
  // 模拟FPS数据
  const fps = [25, 24, 26, 23, 25, 24]
  return fps[groupId % fps.length]
}

const getGroupLatency = (groupId: number) => {
  // 模拟延迟数据
  const latencies = [120, 135, 110, 145, 125, 130]
  return latencies[groupId % latencies.length]
}

const formatLastDetection = (groupId: number) => {
  // 模拟最后检测时间
  const times = ['2分钟前', '刚刚', '5分钟前', '1小时前', '离线', '3分钟前']
  return times[groupId % times.length]
}

const handleGroupSelect = (group: DetectionGroup) => {
  selectedGroup.value = group
  emit('groupSelect', group)
}
</script>

<style scoped>
.realtime-detection-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-color);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.panel-content {
  flex: 1;
  padding: 16px 20px;
  overflow-y: auto;
}

.detection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.detection-group {
  padding: 16px;
  background-color: var(--bg-color);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.detection-group:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.1);
}

.detection-group.selected {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.detection-group.status-monitoring {
  border-left: 4px solid var(--success-color);
}

.detection-group.status-idle {
  border-left: 4px solid var(--info-color);
}

.detection-group.status-cooldown {
  border-left: 4px solid var(--warning-color);
}

.detection-group.status-offline {
  border-left: 4px solid var(--danger-color);
  opacity: 0.7;
}

.group-status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.monitoring {
  background-color: var(--success-color);
  animation: pulse 2s infinite;
}

.status-dot.idle {
  background-color: var(--info-color);
}

.status-dot.cooldown {
  background-color: var(--warning-color);
}

.status-dot.offline {
  background-color: var(--danger-color);
}

.status-text {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-color-soft);
}

.group-info {
  margin-bottom: 12px;
}

.group-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.group-device {
  font-size: 12px;
  color: var(--text-color-mute);
}

.roi-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-color-soft);
}

.alarm-count {
  color: var(--danger-color);
  font-weight: 600;
}

.algorithm-indicators {
  display: flex;
  gap: 6px;
  margin-bottom: 12px;
}

.algorithm-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  background-color: var(--bg-color-mute);
  color: var(--text-color-mute);
  transition: all 0.3s ease;
}

.algorithm-badge.active {
  color: white;
}

.algorithm-badge.motion.active {
  background-color: #3498db;
}

.algorithm-badge.direction.active {
  background-color: #e74c3c;
}

.algorithm-badge.jam.active {
  background-color: #f39c12;
}

.performance-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.metric {
  text-align: center;
}

.metric-label {
  display: block;
  font-size: 10px;
  color: var(--text-color-mute);
  margin-bottom: 2px;
}

.metric-value {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-color);
}

.last-detection {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: var(--text-color-mute);
}

.detection-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.summary-card {
  padding: 16px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.summary-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 12px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: var(--text-color-mute);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: var(--text-color);
}

.stat-value.monitoring {
  color: var(--success-color);
}

.stat-value.idle {
  color: var(--info-color);
}

.stat-value.offline {
  color: var(--danger-color);
}

.stat-value.alarm {
  color: var(--danger-color);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>

import os
import secrets
from typing import Any, Dict, List, Optional, Union

from pydantic import validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    API_V1_STR: str = "/api"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    # 60 minutes * 24 hours * 365 days = 1 year (实现长期登录状态)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 365
    SERVER_NAME: str = "压铸件智能检测系统"
    SERVER_HOST: str = "http://localhost"
    
    # CORS设置
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost", 
        "http://localhost:8080", 
        "http://localhost:3000",
        "http://localhost:5173",  # Vite 开发服务器
        "http://localhost:4173"   # Vite 预览服务器
    ]

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    PROJECT_NAME: str = "压铸件智能检测系统"
    
    # 数据库设置 - 使用 SQLite
    SQLITE_FILE: str = "die_casting_detection.db"
    DATABASE_URL: str = f"sqlite:///./{SQLITE_FILE}"
    
    # 视频存储路径
    UPLOAD_FOLDER: str = "uploads"
    DETECTION_IMAGES_FOLDER: str = "detection_images"
    
    class Config:
        case_sensitive = True
        env_file = ".env"


settings = Settings()
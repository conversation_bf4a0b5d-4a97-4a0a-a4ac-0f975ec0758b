<template>
  <div class="login-preview-test">
    <!-- 设备登录配置区域 -->
    <div class="config-section">
      <el-card class="config-card">
        <template #header>
          <div class="card-header">
            <span>设备登录配置</span>
            <el-button 
              type="primary" 
              size="small" 
              @click="loginDevice" 
              :loading="isConnecting"
              :disabled="!canLoginComputed"
            >
              {{ isConnecting ? '登录中...' : '登录设备' }}
            </el-button>
          </div>
        </template>
        
        <el-form :model="deviceConfig" label-width="100px" size="small">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备IP">
                <el-input v-model="deviceConfig.ip" placeholder="请输入设备IP地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口">
                <el-input-number 
                  v-model="deviceConfig.port" 
                  :min="1" 
                  :max="65535" 
                  placeholder="端口号"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名">
                <el-input v-model="deviceConfig.username" placeholder="请输入用户名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码">
                <el-input 
                  v-model="deviceConfig.password" 
                  type="password" 
                  placeholder="请输入密码" 
                  show-password
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="协议">
                <el-select v-model="deviceConfig.protocol" placeholder="请选择协议" style="width: 100%">
                  <el-option label="HTTP" :value="1" />
                  <el-option label="HTTPS" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="通道号">
                <el-input-number 
                  v-model="deviceConfig.channel" 
                  :min="1" 
                  :max="64" 
                  placeholder="通道号"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="码流类型">
                <el-select v-model="deviceConfig.streamType" style="width: 100%">
                  <el-option label="主码流" value="1" />
                  <el-option label="子码流" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>

    <!-- 视频预览和控制区域 -->
    <div class="preview-section">
      <el-row :gutter="20">
        <!-- 视频显示区域 -->
        <el-col :span="16">
          <el-card class="video-card">
            <template #header>
              <div class="card-header">
                <span>视频预览</span>
                <div class="video-controls">
                  <el-button 
                    type="success" 
                    size="small" 
                    @click="startPreview" 
                    :disabled="!isLoggedIn || isPreviewActive"
                  >
                    开始预览
                  </el-button>
                  <el-button 
                    type="warning" 
                    size="small" 
                    @click="stopPreview" 
                    :disabled="!isPreviewActive"
                  >
                    停止预览
                  </el-button>
                  <el-button 
                    type="info" 
                    size="small" 
                    @click="openSound" 
                    :disabled="!isPreviewActive || soundEnabled"
                  >
                    开启声音
                  </el-button>
                  <el-button 
                    type="info" 
                    size="small" 
                    @click="closeSound" 
                    :disabled="!isPreviewActive || !soundEnabled"
                  >
                    关闭声音
                  </el-button>
                </div>
              </div>
            </template>
            
            <div class="video-container" :style="videoContainerStyle">
              <div 
                id="divPlugin" 
                ref="divPlugin" 
                class="video-plugin"
                :style="{ width: '100%', height: '100%' }"
              >
                <div v-if="!isPreviewActive" class="video-placeholder">
                  <el-icon size="64" color="#ccc"><VideoCamera /></el-icon>
                  <p>{{ isLoggedIn ? '点击开始预览按钮开始视频预览' : '请先登录设备' }}</p>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 设备信息和状态区域 -->
        <el-col :span="8">
          <el-card class="info-card">
            <template #header>
              <span>设备状态</span>
            </template>
            
            <div class="status-info">
              <div class="status-item">
                <span class="label">连接状态:</span>
                <el-tag :type="isLoggedIn ? 'success' : 'danger'" size="small">
                  {{ isLoggedIn ? '已连接' : '未连接' }}
                </el-tag>
              </div>
              
              <div class="status-item">
                <span class="label">预览状态:</span>
                <el-tag :type="isPreviewActive ? 'success' : 'info'" size="small">
                  {{ isPreviewActive ? '预览中' : '未预览' }}
                </el-tag>
              </div>
              
              <div class="status-item">
                <span class="label">声音状态:</span>
                <el-tag :type="soundEnabled ? 'success' : 'info'" size="small">
                  {{ soundEnabled ? '已开启' : '已关闭' }}
                </el-tag>
              </div>
              
              <div v-if="deviceInfo" class="device-info">
                <h4>设备信息</h4>
                <div class="info-item">
                  <span class="label">设备名称:</span>
                  <span>{{ deviceInfo.deviceName || '未知' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">设备型号:</span>
                  <span>{{ deviceInfo.deviceModel || '未知' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">固件版本:</span>
                  <span>{{ deviceInfo.firmwareVersion || '未知' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">通道数量:</span>
                  <span>{{ deviceInfo.channelNum || '未知' }}</span>
                </div>
              </div>
            </div>
          </el-card>
          
          <!-- 操作日志 -->
          <el-card class="log-card" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>操作日志</span>
                <el-button size="small" @click="clearLogs">清空</el-button>
              </div>
            </template>
            
            <div class="log-container">
              <div 
                v-for="(log, index) in operationLogs" 
                :key="index" 
                class="log-item"
                :class="log.type"
              >
                <span class="log-time">{{ log.time }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
              <div v-if="operationLogs.length === 0" class="no-logs">
                暂无操作日志
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { VideoCamera } from '@element-plus/icons-vue'
import { useWebSDK } from '@/composables/useWebSDK'

// 设备配置
const deviceConfig = ref({
  ip: '************',
  port: 80,
  username: 'admin',
  password: 'Qq112233',
  channel: 1,
  streamType: '1',
  protocol: 1 // 1-HTTP, 2-HTTPS
})

// 使用WebSDK composable
const {
  // 状态
  isConnecting,
  isLoggedIn,
  isPreviewActive,
  soundEnabled,
  deviceInfo,
  operationLogs,
  canLogin,
  
  // 方法
  addLog,
  clearLogs,
  initWebSDK,
  loginDevice: webSDKLoginDevice,
  startPreview: webSDKStartPreview,
  stopPreview,
  openSound,
  closeSound
} = useWebSDK()

// WebSDK相关
const divPlugin = ref<HTMLElement>()

// 计算属性
const canLoginComputed = computed(() => {
  return deviceConfig.value.ip && 
         deviceConfig.value.port && 
         deviceConfig.value.username && 
         deviceConfig.value.password &&
         canLogin.value
})

const videoContainerStyle = computed(() => ({
  width: '100%',
  aspectRatio: '16/9', // 自动适配16:9视频比例
  minHeight: '300px',
  maxHeight: '600px',
  backgroundColor: '#000',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  position: 'relative'
}))

// 包装方法以传递设备配置
const loginDevice = async () => {
  await webSDKLoginDevice(deviceConfig.value)
}

const startPreview = () => {
  webSDKStartPreview(deviceConfig.value)
}



// 组件挂载时初始化WebSDK
onMounted(async () => {
  await initWebSDK()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (window.WebVideoCtrl) {
    window.WebVideoCtrl.I_Uninit()
  }
})

</script>

<style scoped>
@import './styles/video-login-preview.css';
</style>
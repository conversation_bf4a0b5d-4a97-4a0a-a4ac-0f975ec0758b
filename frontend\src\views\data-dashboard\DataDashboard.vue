<template>
  <div class="data-dashboard" ref="dashboardRef">
    <div class="dashboard-header">
      <h2 class="dashboard-title">数据看板</h2>
      <div class="header-actions">
        <el-button @click="refresh" :loading="loading" :icon="loading ? 'el-icon-loading' : 'el-icon-refresh'">
          刷新
        </el-button>
        <el-button @click="toggleFullScreen" :icon="isFullscreen ? Aim : FullScreen" circle />
      </div>
    </div>
    <div v-if="loading" class="dashboard-loading">
      <el-skeleton :rows="10" animated />
    </div>
    <div v-else-if="error" class="dashboard-error">
      <el-alert title="数据加载失败" type="error" :description="error.message" show-icon>
        <el-button @click="refresh">重试</el-button>
      </el-alert>
    </div>
    <div v-else class="dashboard-grid">
      <StatusBar :status="deviceStatus" :template="currentTemplate" />
      <MetricsPanel :overview="overview" />
      <TrendChart :trends="trends" />
      <DetectionLog :logs="detectionLogs" />
      <GroupMonitor :groups="detectionGroups" />
      <AlarmCenter :alarms="alarms" />
      <SystemResources :performance="systemPerformance" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useFullscreen } from '@vueuse/core'
import { FullScreen, Aim } from '@element-plus/icons-vue'

import './dashboard.styles.css'
import StatusBar from './components/StatusBar.vue'
import MetricsPanel from './components/MetricsPanel.vue'
import TrendChart from './components/TrendChart.vue'
import DetectionLog from './components/DetectionLog.vue'
import GroupMonitor from './components/GroupMonitor.vue'
import AlarmCenter from './components/AlarmCenter.vue'
import SystemResources from './components/SystemResources.vue'
import { useDataDashboard } from './composables/useDataDashboard'

const dashboardRef = ref(null)
const { isFullscreen, toggle: toggleFullScreen } = useFullscreen(dashboardRef)

const {
  loading,
  error,
  overview,
  trends,
  detectionLogs,
  detectionGroups,
  alarms,
  systemPerformance,
  currentTemplate,
  deviceStatus,
  refresh
} = useDataDashboard()
</script> 
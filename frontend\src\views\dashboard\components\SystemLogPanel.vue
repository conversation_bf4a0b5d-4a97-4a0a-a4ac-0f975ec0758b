<template>
  <div class="system-log-panel">
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon><document /></el-icon>
        系统日志
      </h3>
      <div class="panel-actions">
        <el-select v-model="filterLevel" size="small" style="width: 80px">
          <el-option label="全部" value="all" />
          <el-option label="错误" value="error" />
          <el-option label="警告" value="warning" />
          <el-option label="信息" value="info" />
        </el-select>
      </div>
    </div>

    <div class="panel-content">
      <div class="log-list">
        <div 
          v-for="log in filteredLogs" 
          :key="log.id"
          class="log-item"
          :class="`level-${log.level}`"
        >
          <div class="log-indicator">
            <div class="level-dot" :class="log.level"></div>
          </div>
          
          <div class="log-content">
            <div class="log-message">{{ log.message }}</div>
            <div class="log-time">{{ formatTime(log.timestamp) }}</div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredLogs.length === 0" class="empty-state">
          <el-icon><document /></el-icon>
          <p>暂无日志记录</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Document } from '@element-plus/icons-vue'

// Props
interface SystemLog {
  id: number
  level: 'info' | 'warning' | 'error'
  message: string
  timestamp: Date
}

const props = defineProps<{
  logs: SystemLog[]
}>()

// 响应式数据
const filterLevel = ref('all')

// 计算属性
const filteredLogs = computed(() => {
  if (filterLevel.value === 'all') {
    return props.logs
  }
  return props.logs.filter(log => log.level === filterLevel.value)
})

// 方法
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped>
.system-log-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-color);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.panel-content {
  flex: 1;
  padding: 16px 20px;
  overflow-y: auto;
}

.log-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.log-item:hover {
  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.1);
}

.log-item.level-error {
  border-left: 3px solid var(--danger-color);
  background-color: rgba(var(--danger-color-rgb), 0.05);
}

.log-item.level-warning {
  border-left: 3px solid var(--warning-color);
  background-color: rgba(var(--warning-color-rgb), 0.05);
}

.log-item.level-info {
  border-left: 3px solid var(--info-color);
}

.log-indicator {
  margin-right: 12px;
  margin-top: 2px;
}

.level-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.level-dot.error {
  background-color: var(--danger-color);
}

.level-dot.warning {
  background-color: var(--warning-color);
}

.level-dot.info {
  background-color: var(--info-color);
}

.log-content {
  flex: 1;
  min-width: 0;
}

.log-message {
  font-size: 13px;
  color: var(--text-color);
  margin-bottom: 4px;
  line-height: 1.4;
}

.log-time {
  font-size: 11px;
  color: var(--text-color-mute);
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-color-mute);
}

.empty-state .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}
</style>

<template>
  <div class="device-management">
    <h1>设备管理</h1>
    <div class="device-container">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="压铸机管理" name="die-casters">
          <el-card class="device-card">
            <template #header>
              <div class="card-header">
                <h3>压铸机列表</h3>
                <el-button type="primary" @click="addDieCaster">添加压铸机</el-button>
              </div>
            </template>
            <el-table :data="dieCasters" style="width: 100%" v-loading="loading.dieCasters">
              <el-table-column prop="name" label="名称" width="180" />
              <el-table-column prop="ip_address" label="IP地址" width="180" />
              <el-table-column prop="port" label="端口号" width="100" />
              <el-table-column prop="description" label="描述" />
              <el-table-column prop="status" label="状态">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'online' ? 'success' : 'info'">
                    {{ scope.row.status === 'online' ? '在线' : '离线' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button size="small" @click="editDieCaster(scope.row)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteDieCaster(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="视频源管理" name="video-sources">
          <el-card class="device-card">
            <template #header>
              <div class="card-header">
                <h3>视频源列表</h3>
                <el-button type="primary" @click="addVideoSource">添加视频源</el-button>
              </div>
            </template>
            <el-table :data="videoSources" style="width: 100%" v-loading="loading.videoSources">
              <el-table-column prop="name" label="名称" width="150" />
              <el-table-column prop="source_type" label="类型" width="120">
                <template #default="scope">
                  <el-tag :type="getSourceTypeTagType(scope.row.source_type)">
                    {{ getSourceTypeLabel(scope.row.source_type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="连接信息" width="300">
                <template #default="scope">
                  <div v-if="scope.row.source_type === 'websdk_device'">
                    <div>{{ scope.row.device_ip }}:{{ scope.row.device_port }}</div>
                    <div class="text-sm text-gray-500">通道{{ scope.row.channel_id }} | {{ scope.row.stream_type === 1 ? '主码流' : '子码流' }}</div>
                  </div>
                  <div v-else>
                    {{ scope.row.path }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="100">
                <template #default="scope">
                  <el-tag
                    v-if="scope.row.source_type === 'websdk_device'"
                    :type="scope.row.device_status === 'online' ? 'success' : 'info'"
                  >
                    {{ scope.row.device_status === 'online' ? '在线' : '离线' }}
                  </el-tag>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" />
              <el-table-column label="操作" width="200">
                <template #default="scope">
                  <el-button size="small" @click="editVideoSource(scope.row)">编辑</el-button>
                  <el-button size="small" type="success" @click="testVideoSource(scope.row)">测试</el-button>
                  <el-button
                    v-if="scope.row.source_type === 'websdk_device'"
                    size="small"
                    :type="scope.row.device_status === 'online' ? 'warning' : 'primary'"
                    @click="toggleDeviceConnection(scope.row)"
                  >
                    {{ scope.row.device_status === 'online' ? '断开' : '连接' }}
                  </el-button>
                  <el-button size="small" type="danger" @click="deleteVideoSource(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 添加/编辑压铸机对话框 -->
    <el-dialog
      v-model="dieCasterDialogVisible"
      :title="dialogType === 'add' ? '添加压铸机' : '编辑压铸机'"
      width="50%"
    >
      <el-form :model="dieCasterForm" label-width="120px">
        <el-form-item label="名称">
          <el-input v-model="dieCasterForm.name" />
        </el-form-item>
        <el-form-item label="IP地址">
          <el-input v-model="dieCasterForm.ip_address" />
        </el-form-item>
        <el-form-item label="端口号">
          <el-input-number v-model="dieCasterForm.port" :min="1" :max="65535" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="dieCasterForm.description" type="textarea" rows="3" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="dieCasterForm.status">
            <el-option label="在线" value="online" />
            <el-option label="离线" value="offline" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dieCasterDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDieCaster">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加/编辑视频源对话框 -->
    <el-dialog
      v-model="videoSourceDialogVisible"
      :title="dialogType === 'add' ? '添加视频源' : '编辑视频源'"
      width="60%"
    >
      <el-form :model="videoSourceForm" label-width="120px">
        <el-form-item label="类型" required>
          <el-select v-model="videoSourceForm.source_type" @change="onSourceTypeChange">
            <el-option label="本地文件" value="local_file" />
            <el-option label="RTSP流" value="rtsp_stream" />
            <el-option label="WebSDK设备" value="websdk_device" />
          </el-select>
        </el-form-item>

        <!-- 通用基本信息 -->
        <template v-if="videoSourceForm.source_type !== ''">
          <el-form-item label="名称" required>
            <el-input
              v-model="videoSourceForm.name"
              :placeholder="getNamePlaceholder(videoSourceForm.source_type)"
            />
          </el-form-item>

          <el-form-item label="描述">
            <el-input
              v-model="videoSourceForm.description"
              type="textarea"
              :rows="2"
              :placeholder="getDescriptionPlaceholder(videoSourceForm.source_type)"
            />
          </el-form-item>
        </template>

        <!-- 本地文件和RTSP流配置 -->
        <template v-if="videoSourceForm.source_type === 'local_file' || videoSourceForm.source_type === 'rtsp_stream'">
          <el-divider content-position="left">路径配置</el-divider>

          <el-form-item label="路径" required>
            <el-input
              v-model="videoSourceForm.path"
              :placeholder="videoSourceForm.source_type === 'local_file' ? '请输入本地文件路径，如：/path/to/video.mp4' : '请输入RTSP流地址，如：rtsp://*************:554/stream'"
            />
          </el-form-item>
        </template>

        <!-- WebSDK设备配置 -->
        <template v-if="videoSourceForm.source_type === 'websdk_device'">
          <!-- 设备连接信息 -->
          <el-divider content-position="left">设备连接配置</el-divider>

          <el-row :gutter="20">
            <el-col :span="16">
              <el-form-item label="设备IP" required>
                <el-input v-model="videoSourceForm.device_ip" placeholder="*************" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="端口" required>
                <el-input-number v-model="videoSourceForm.device_port" :min="1" :max="65535" placeholder="80" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名" required>
                <el-input v-model="videoSourceForm.device_username" placeholder="admin" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码" required>
                <el-input v-model="videoSourceForm.device_password" type="password" placeholder="请输入密码" show-password />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="协议">
                <el-select v-model="videoSourceForm.device_protocol">
                  <el-option label="HTTP" :value="1" />
                  <el-option label="HTTPS" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="通道号">
                <el-input-number v-model="videoSourceForm.channel_id" :min="1" :max="64" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="码流类型">
                <el-select v-model="videoSourceForm.stream_type">
                  <el-option label="主码流" :value="1" />
                  <el-option label="子码流" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <el-form-item label="描述">
          <el-input v-model="videoSourceForm.description" type="textarea" rows="3" placeholder="请输入描述信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="videoSourceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveVideoSource">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getDieCasters, createDieCaster, updateDieCaster, deleteDieCaster as apiDeleteDieCaster } from '@/api/die-casters'
import { getVideoSources, createVideoSource, updateVideoSource, deleteVideoSource as apiDeleteVideoSource, testVideoSource as apiTestVideoSource } from '@/api/video-sources'
import type { DieCaster, VideoSource } from '@/types'

// 标签页
const activeTab = ref('die-casters')

// 数据加载状态
const loading = reactive({
  dieCasters: false,
  videoSources: false
})

// 压铸机数据
const dieCasters = ref<DieCaster[]>([])

// 视频源数据
const videoSources = ref<VideoSource[]>([])

// 对话框相关
const dialogType = ref<'add' | 'edit'>('add')
const dieCasterDialogVisible = ref(false)
const videoSourceDialogVisible = ref(false)

// 表单数据
const dieCasterForm = reactive({
  id: 0,
  name: '',
  ip_address: '',
  port: 8080,
  description: '',
  status: 'offline'
})

const videoSourceForm = reactive({
  id: 0,
  name: '',
  source_type: 'local_file',
  description: '',

  // 通用字段
  path: '',

  // WebSDK设备字段
  device_ip: '',
  device_port: 80,
  device_username: 'admin',
  device_password: '',
  device_protocol: 1, // 1-HTTP, 2-HTTPS
  channel_id: 1,
  stream_type: 1, // 1-主码流, 2-子码流
  device_status: 'offline'
})

// 加载压铸机数据
const loadDieCasters = async () => {
  loading.dieCasters = true
  try {
    dieCasters.value = await getDieCasters()
  } catch (error) {
    console.error('加载压铸机数据失败:', error)
    ElMessage.error('加载压铸机数据失败')
  } finally {
    loading.dieCasters = false
  }
}

// 加载视频源数据
const loadVideoSources = async () => {
  loading.videoSources = true
  try {
    videoSources.value = await getVideoSources()
  } catch (error) {
    console.error('加载视频源数据失败:', error)
    ElMessage.error('加载视频源数据失败')
  } finally {
    loading.videoSources = false
  }
}

// 压铸机相关操作
const addDieCaster = () => {
  dialogType.value = 'add'
  resetDieCasterForm()
  dieCasterDialogVisible.value = true
}

const editDieCaster = (row: DieCaster) => {
  dialogType.value = 'edit'
  Object.assign(dieCasterForm, row)
  dieCasterDialogVisible.value = true
}

const deleteDieCaster = (row: DieCaster) => {
  ElMessageBox.confirm(`确认删除压铸机 "${row.name}"?`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await apiDeleteDieCaster(row.id)
      ElMessage.success('删除成功')
      loadDieCasters()
    } catch (error) {
      console.error('删除压铸机失败:', error)
      ElMessage.error('删除压铸机失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

const saveDieCaster = async () => {
  try {
    if (dialogType.value === 'add') {
      await createDieCaster(dieCasterForm)
      ElMessage.success('添加成功')
    } else {
      await updateDieCaster(dieCasterForm.id, dieCasterForm)
      ElMessage.success('更新成功')
    }
    dieCasterDialogVisible.value = false
    loadDieCasters()
  } catch (error) {
    console.error('保存压铸机失败:', error)
    ElMessage.error('保存压铸机失败')
  }
}

const resetDieCasterForm = () => {
  dieCasterForm.id = 0
  dieCasterForm.name = ''
  dieCasterForm.ip_address = ''
  dieCasterForm.port = 8080
  dieCasterForm.description = ''
  dieCasterForm.status = 'offline'
}

// 视频源相关操作
const addVideoSource = () => {
  dialogType.value = 'add'
  resetVideoSourceForm()
  videoSourceDialogVisible.value = true
}

const editVideoSource = (row: VideoSource) => {
  dialogType.value = 'edit'
  Object.assign(videoSourceForm, row)
  videoSourceDialogVisible.value = true
}

const testVideoSource = async (row: VideoSource) => {
  ElMessage.info(`正在测试视频源 "${row.name}" 的连接...`)
  try {
    const result = await apiTestVideoSource(row.id)
    if (result.success) {
      ElMessage.success(result.message || '连接测试成功')
    } else {
      ElMessage.error(result.message || '连接测试失败')
    }
  } catch (error) {
    console.error('测试视频源失败:', error)
    ElMessage.error('测试视频源失败')
  }
}

const deleteVideoSource = (row: VideoSource) => {
  ElMessageBox.confirm(`确认删除视频源 "${row.name}"?`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await apiDeleteVideoSource(row.id)
      ElMessage.success('删除成功')
      loadVideoSources()
    } catch (error) {
      console.error('删除视频源失败:', error)
      ElMessage.error('删除视频源失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

const saveVideoSource = async () => {
  try {
    if (dialogType.value === 'add') {
      await createVideoSource(videoSourceForm)
      ElMessage.success('添加成功')
    } else {
      await updateVideoSource(videoSourceForm.id, videoSourceForm)
      ElMessage.success('更新成功')
    }
    videoSourceDialogVisible.value = false
    loadVideoSources()
  } catch (error) {
    console.error('保存视频源失败:', error)
    ElMessage.error('保存视频源失败')
  }
}

const resetVideoSourceForm = () => {
  videoSourceForm.id = 0
  videoSourceForm.name = ''
  videoSourceForm.source_type = 'local_file'
  videoSourceForm.description = ''
  videoSourceForm.path = ''
  videoSourceForm.device_ip = ''
  videoSourceForm.device_port = 80
  videoSourceForm.device_username = 'admin'
  videoSourceForm.device_password = ''
  videoSourceForm.device_protocol = 1
  videoSourceForm.channel_id = 1
  videoSourceForm.stream_type = 1
  videoSourceForm.device_status = 'offline'
}

// 辅助方法
const getSourceTypeLabel = (sourceType: string) => {
  const labels: Record<string, string> = {
    'local_file': '本地文件',
    'rtsp_stream': 'RTSP流',
    'websdk_device': 'WebSDK设备'
  }
  return labels[sourceType] || sourceType
}

const getSourceTypeTagType = (sourceType: string) => {
  const types: Record<string, string> = {
    'local_file': 'info',
    'rtsp_stream': 'success',
    'websdk_device': 'warning'
  }
  return types[sourceType] || 'info'
}

const getNamePlaceholder = (sourceType: string) => {
  const placeholders: Record<string, string> = {
    'local_file': '请输入文件名称，如：产品宣传视频',
    'rtsp_stream': '请输入流名称，如：车间监控摄像头',
    'websdk_device': '请输入设备名称，如：前门监控摄像头'
  }
  return placeholders[sourceType] || '请输入名称'
}

const getDescriptionPlaceholder = (sourceType: string) => {
  const placeholders: Record<string, string> = {
    'local_file': '请输入文件描述，如：用于展示的产品宣传视频文件',
    'rtsp_stream': '请输入流描述，如：车间生产线实时监控视频流',
    'websdk_device': '请输入设备描述，如：用于监控前门区域的高清摄像头设备'
  }
  return placeholders[sourceType] || '请输入描述'
}

const onSourceTypeChange = () => {
  // 清空相关字段
  videoSourceForm.path = ''
  videoSourceForm.device_ip = ''
  videoSourceForm.device_port = 80
  videoSourceForm.device_username = 'admin'
  videoSourceForm.device_password = ''
}

const toggleDeviceConnection = async (videoSource: VideoSource) => {
  try {
    const newStatus = videoSource.device_status === 'online' ? 'offline' : 'online'
    // 这里应该调用API更新设备状态
    ElMessage.success(`设备${newStatus === 'online' ? '连接' : '断开'}成功`)
    loadVideoSources()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

onMounted(() => {
  loadDieCasters()
  loadVideoSources()
})
</script>

<style scoped>
.device-management {
  padding: 20px;
}

.device-container {
  margin-top: 20px;
}

.device-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 
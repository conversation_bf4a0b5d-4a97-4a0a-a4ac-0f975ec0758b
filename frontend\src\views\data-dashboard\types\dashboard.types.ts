/**
 * @file dashboard.types.ts
 * @description
 * This file contains all the TypeScript type definitions for the data dashboard.
 * These types are based on the API responses and are used throughout the
 * dashboard components, services, and composables to ensure type safety.
 */

// From: 1.1 获取卡料检测结果列表
// Note: This is a nested type within CardDetectionResultList
export interface CardDetectionResult {
  id: number;
  detection_group_id: number;
  timestamp: string;
  created_at: string;
  is_normal: boolean;
  detection_time: number;
  undetected_rois: string[];
  detected_rois: string[];
  trigger_roi_id: string | null;
  result_details: {
    algorithm_version?: string;
    confidence?: number;
    detection_method?: string;
    frame_count?: number;
    total_pailiao_rois?: number;
    detected_pailiao_count?: number;
    notes?: string;
  };
}

export interface CardDetectionResultList {
  total: number;
  page: number;
  page_size: number;
  items: CardDetectionResult[];
}

// From: 1.2 获取看板概览数据
export interface DashboardOverview {
  total_detections_today: number;
  normal_detections_today: number;
  jam_detections_today: number;
  success_rate_today: number;
  total_detections_week: number;
  normal_detections_week: number;
  jam_detections_week: number;
  success_rate_week: number;
  avg_detection_time: number;
  recent_results: CardDetectionResult[];
}

// From: 1.3 获取趋势数据
export interface TrendData {
  date: string;
  total_count: number;
  normal_count: number;
  jam_count: number;
  success_rate: number;
  avg_detection_time: number;
}

// From: 1.4 获取检测组统计信息
export interface DetectionGroupStatistics {
  detection_group_id: number;
  total_detections: number;
  normal_count: number;
  abnormal_count: number;
  success_rate: number;
  avg_detection_time: number;
  recent_results: CardDetectionResult[];
}

// From: 2.1 获取所有检测组
export interface DetectionGroup {
  id: number;
  name: string;
  template_id: number | null;
  die_caster_id: number;
  video_source_id: number;
  status: 'active' | 'inactive';
  config_json: {
    rois?: any[];
    roiIds?: string[];
    globalSettings?: {
      delayTime?: number;
      pauseThreshold?: number;
      cooldownTime?: number;
      cardDelayTime?: number;
    };
    global_parameters?: {
      frame_skip?: number;
      cool_down_time?: number;
      detection_interval?: number;
    };
    roi_config?: {
      rois: any[];
      global_settings: {
        detection_mode: string;
        save_results: boolean;
        image_preprocessing: {
          brightness: number;
          contrast: number;
          gamma: number;
        };
      };
    };
    last_updated?: string;
  } | null;
  created_at: string;
  updated_at: string;
}

// From: 3.1 获取所有检测模板
export interface DetectionTemplate {
  id: number;
  name: string;
  description: string;
  status: 'enabled' | 'disabled';
  created_at: string;
  updated_at: string;
}

// From: 3.2 获取特定模板详情
export interface DetectionTemplateDetail extends DetectionTemplate {
  die_caster_ids: number[];
}

// From: 4.1 获取系统信息
export interface SystemInfo {
  version: string;
  python_version: string;
  platform: string;
  architecture: string;
  uptime: string;
  cpu_usage: number;
  cpu_count: number;
  memory_usage: number;
  memory_total: string;
  memory_available: string;
  disk_usage: number;
  disk_total: string;
  disk_free: string;
  network_info: {
    bytes_sent: string;
    bytes_recv: string;
    packets_sent: number;
    packets_recv: number;
  };
  process_count: number;
  boot_time: string;
}

// From: 4.2 获取系统日志
// Note: This is a nested type within SystemLogs
export interface LogEntry {
  id: number;
  timestamp: string;
  level: 'info' | 'warning' | 'error';
  module: string;
  message: string;
  filename: string;
  line_number: number;
  function_name: string;
}

export interface SystemLogs {
  success: boolean;
  data: {
    logs: LogEntry[];
    total: number;
    limit: number;
    offset: number;
    source: string;
  };
}

// From: 4.3 获取日志统计
export interface LogStats {
  success: boolean;
  data: {
    total_logs: number;
    recent_24h: number;
    level_distribution: Record<string, number>;
    hourly_distribution: Record<string, number>;
    cache_size: number;
    max_cache_size: number;
  };
}

// From: 4.4 获取日志级别统计
export interface LogLevelStats {
  success: boolean;
  data: Record<string, number>;
}

// From: 4.5 获取日志模块列表
export interface LogModules {
  success: boolean;
  data: string[];
}

// From: 4.6 获取系统性能数据
export interface SystemPerformance {
  timestamp: string;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_io: {
    bytes_sent_mb: number;
    bytes_recv_mb: number;
  };
  active_processes: number;
  load_average: number[];
}

// From: 5.1 获取设备状态
// Note: This is a nested type within DeviceStatus
export interface Device {
  device_id: number;
  device_name: string;
  device_type: 'die_caster' | 'video_source';
  status: 'online' | 'offline' | 'active';
  last_update: string;
  detection_groups: Array<{
    id: number;
    name: string;
    status: 'active' | 'inactive';
  }>;
}

export interface DeviceStatus {
  total_devices: number;
  online_devices: number;
  offline_devices: number;
  active_detection_groups: number;
  devices: Device[];
}

// From: 5.2 获取报警列表
// Note: This is a nested type within AlarmList
export interface Alarm {
  id: string;
  type: string;
  level: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  source: string;
  timestamp: string;
  is_resolved: boolean;
  resolved_at: string | null;
  detection_group_id: number;
  metadata: {
    undetected_rois: string[];
    detected_rois: string[];
    trigger_roi_id: string | null;
  };
}

// Note: This is a nested type within AlarmList
export interface AlarmStatistics {
  total_alarms: number;
  unresolved_alarms: number;
  critical_alarms: number;
  high_alarms: number;
  medium_alarms: number;
  low_alarms: number;
  recent_24h: number;
  recent_7d: number;
}

export interface AlarmList {
  items: Alarm[];
  statistics: AlarmStatistics;
  total: number;
  page: number;
  limit: number;
}


// From: 5.3 获取检测效率分析
export interface DetectionEfficiency {
  total_detections: number;
  overall_efficiency_rate: number;
  active_detection_groups: number;
  avg_detections_per_hour: number;
  top_performing_groups: Array<{
    id: number;
    name: string;
    efficiency_rate: number;
    total_detections: number;
  }>;
  efficiency_trends: Array<{
    date: string;
    rate: number;
  }>;
} 
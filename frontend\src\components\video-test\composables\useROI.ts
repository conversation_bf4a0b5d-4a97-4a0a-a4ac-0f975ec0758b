import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { v4 as uuidv4 } from 'uuid'

// ROI数据类型定义 - 与数据库模型一致
export interface ROIData {
  roi_id: string // 唯一标识符（UUID）- 🔥 使用数据库字段名
  name: string // ROI名称
  attribute: 'yazhu' | 'pailiao' // ROI属性：yazhu/pailiao
  roi_type: 'rectangle' | 'polygon' // ROI类型：rectangle/polygon
  coordinates: number[][] // 坐标点列表
  params: ROIParams // 检测参数配置
}

// ROI检测参数
export interface ROIParams {
  threshold?: number // 检测阈值
  sensitivity?: number // 敏感度
  [key: string]: any // 其他参数
}

// 绘制状态
export interface DrawingState {
  isDrawing: boolean
  currentShape: 'rectangle' | 'polygon' | null
  currentROIType: 'yazhu' | 'pailiao'
  tempPoints: number[][]
  isCompleted: boolean
}

// ROI颜色配置
export const ROI_COLORS = {
  yazhu: '#ff4444', // 压铸机 - 红色
  pailiao: '#00cccc' // 排料口 - 青色
} as const

export function useROI() {
  // 状态管理
  const roiList = ref<ROIData[]>([])
  const selectedROI = ref<ROIData | null>(null)
  const drawingState = ref<DrawingState>({
    isDrawing: false,
    currentShape: null,
    currentROIType: 'yazhu',
    tempPoints: [],
    isCompleted: false
  })

  // 计算属性
  const yazhuROIs = computed(() => roiList.value.filter(roi => roi.attribute === 'yazhu'))
  const pailiaoROIs = computed(() => roiList.value.filter(roi => roi.attribute === 'pailiao'))
  const rectangleROIs = computed(() => roiList.value.filter(roi => roi.roi_type === 'rectangle'))
  const polygonROIs = computed(() => roiList.value.filter(roi => roi.roi_type === 'polygon'))
  const canStartDrawing = computed(() => !drawingState.value.isDrawing)

  // ROI统计信息
  const roiStats = computed(() => ({
    total: roiList.value.length,
    yazhu: yazhuROIs.value.length,
    pailiao: pailiaoROIs.value.length,
    rectangle: rectangleROIs.value.length,
    polygon: polygonROIs.value.length
  }))

  // 生成ROI名称
  const generateROIName = (type: 'yazhu' | 'pailiao'): string => {
    const id = uuidv4()
    const shortId = id.substring(0, 4).toUpperCase()
    const typeName = type === 'yazhu' ? '压铸' : '排料'
    return `${typeName}_${shortId}`
  }

  // 创建新ROI
  const createROI = (
    shape: 'rectangle' | 'polygon',
    coordinates: number[][],
    attribute: 'yazhu' | 'pailiao' = 'yazhu',
    params: ROIParams = {}
  ): ROIData => {
    const roi_id = uuidv4() // 🔥 使用roi_id字段名
    const name = generateROIName(attribute)

    return {
      roi_id, // 🔥 使用roi_id字段名
      name,
      attribute,
      roi_type: shape,
      coordinates,
      params: {
        threshold: 0.5,
        sensitivity: 0.8,
        ...params
      }
    }
  }

  // 添加ROI
  const addROI = (roi: ROIData) => {
    roiList.value.push(roi)
    ElMessage.success(`ROI "${roi.name}" 添加成功`)
  }

  // 删除ROI
  const deleteROI = (roi_id: string) => {
    const index = roiList.value.findIndex(roi => roi.roi_id === roi_id)
    if (index !== -1) {
      const deletedROI = roiList.value.splice(index, 1)[0]
      if (selectedROI.value?.roi_id === roi_id) {
        selectedROI.value = null
      }
      ElMessage.success(`ROI "${deletedROI.name}" 删除成功`)
    }
  }

  // 更新ROI
  const updateROI = (roi_id: string, updates: Partial<ROIData>) => {
    const index = roiList.value.findIndex(roi => roi.roi_id === roi_id)
    if (index !== -1) {
      // 验证更新数据
      const updatedROI = { ...roiList.value[index], ...updates }

      // 验证必要字段
      if (!updatedROI.name || !updatedROI.name.trim()) {
        ElMessage.error('ROI名称不能为空')
        return false
      }

      // 验证坐标
      if (!updatedROI.coordinates || updatedROI.coordinates.length < 2) {
        ElMessage.error('ROI坐标点不能少于2个')
        return false
      }

      roiList.value[index] = updatedROI
      if (selectedROI.value?.roi_id === roi_id) {
        selectedROI.value = roiList.value[index]
      }
      ElMessage.success('ROI更新成功')
      return true
    }
    return false
  }

  // 选择ROI
  const selectROI = (roi: ROIData | null) => {
    selectedROI.value = roi
  }

  // 开始绘制
  const startDrawing = (shape: 'rectangle' | 'polygon', type: 'yazhu' | 'pailiao' = 'yazhu') => {
    if (drawingState.value.isDrawing) {
      ElMessage.warning('请先完成当前绘制')
      return false
    }

    drawingState.value = {
      isDrawing: true,
      currentShape: shape,
      currentROIType: type,
      tempPoints: [],
      isCompleted: false
    }
    
    ElMessage.info(`开始绘制${shape === 'rectangle' ? '矩形' : '多边形'}ROI`)
    return true
  }

  // 添加绘制点
  const addDrawingPoint = (x: number, y: number) => {
    if (!drawingState.value.isDrawing) return

    // 确保坐标在有效范围内
    const clampedX = Math.max(0, Math.min(x, 1920)) // 假设最大宽度1920
    const clampedY = Math.max(0, Math.min(y, 1080)) // 假设最大高度1080

    drawingState.value.tempPoints.push([clampedX, clampedY])

    // 矩形需要2个点
    if (drawingState.value.currentShape === 'rectangle' && drawingState.value.tempPoints.length === 2) {
      completeDrawing()
    }
  }

  // 移除最后一个绘制点（用于撤销）
  const removeLastPoint = () => {
    if (drawingState.value.isDrawing && drawingState.value.tempPoints.length > 0) {
      drawingState.value.tempPoints.pop()
    }
  }

  // 完成绘制
  const completeDrawing = () => {
    if (!drawingState.value.isDrawing || drawingState.value.tempPoints.length < 2) {
      ElMessage.warning('至少需要2个点才能完成绘制')
      return
    }

    const { currentShape, currentROIType, tempPoints } = drawingState.value
    
    let coordinates: number[][]
    
    if (currentShape === 'rectangle') {
      // 矩形：转换为4个角点
      const [point1, point2] = tempPoints
      coordinates = [
        [point1[0], point1[1]], // 左上
        [point2[0], point1[1]], // 右上
        [point2[0], point2[1]], // 右下
        [point1[0], point2[1]]  // 左下
      ]
    } else {
      // 多边形：直接使用点列表
      coordinates = [...tempPoints]
    }

    const newROI = createROI(currentShape!, coordinates, currentROIType)
    addROI(newROI)
    
    // 重置绘制状态
    resetDrawing()
  }

  // 取消绘制
  const cancelDrawing = () => {
    resetDrawing()
    ElMessage.info('取消绘制')
  }

  // 重置绘制状态
  const resetDrawing = () => {
    drawingState.value = {
      isDrawing: false,
      currentShape: null,
      currentROIType: 'yazhu',
      tempPoints: [],
      isCompleted: false
    }
  }

  // 清空所有ROI
  const clearAllROI = () => {
    roiList.value = []
    selectedROI.value = null
    resetDrawing()
    ElMessage.success('已清空所有ROI')
  }

  // 导出ROI数据
  const exportROIData = () => {
    return JSON.stringify(roiList.value, null, 2)
  }

  // 导入ROI数据
  const importROIData = (jsonData: string) => {
    try {
      const data = JSON.parse(jsonData) as ROIData[]
      if (Array.isArray(data)) {
        roiList.value = data
        ElMessage.success(`成功导入${data.length}个ROI`)
      } else {
        throw new Error('数据格式不正确')
      }
    } catch (error) {
      ElMessage.error('导入失败：数据格式不正确')
    }
  }

  // 根据ID查找ROI
  const findROIById = (roi_id: string): ROIData | undefined => {
    return roiList.value.find(roi => roi.roi_id === roi_id)
  }

  // 根据名称查找ROI
  const findROIByName = (name: string): ROIData | undefined => {
    return roiList.value.find(roi => roi.name === name)
  }

  // 验证ROI数据
  const validateROI = (roi: Partial<ROIData>): boolean => {
    if (!roi.name || !roi.name.trim()) {
      ElMessage.error('ROI名称不能为空')
      return false
    }

    if (!roi.attribute || !['yazhu', 'pailiao'].includes(roi.attribute)) {
      ElMessage.error('ROI属性必须是yazhu或pailiao')
      return false
    }

    if (!roi.roi_type || !['rectangle', 'polygon'].includes(roi.roi_type)) {
      ElMessage.error('ROI形状必须是rectangle或polygon')
      return false
    }

    if (!roi.coordinates || roi.coordinates.length < 2) {
      ElMessage.error('ROI坐标点不能少于2个')
      return false
    }

    return true
  }

  return {
    // 状态
    roiList,
    selectedROI,
    drawingState,
    yazhuROIs,
    pailiaoROIs,
    rectangleROIs,
    polygonROIs,
    canStartDrawing,
    roiStats,

    // 方法
    createROI,
    addROI,
    deleteROI,
    updateROI,
    selectROI,
    startDrawing,
    addDrawingPoint,
    removeLastPoint,
    completeDrawing,
    cancelDrawing,
    resetDrawing,
    clearAllROI,
    exportROIData,
    importROIData,
    findROIById,
    findROIByName,
    validateROI,

    // 常量
    ROI_COLORS
  }
}

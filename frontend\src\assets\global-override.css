/* 全局样式覆盖 */

/* 修复Element Plus组件在暗色模式下的颜色问题 */
.dark-theme body {
  color-scheme: dark;
}

/* 覆盖默认链接颜色 */
a {
  color: var(--primary-color);
}

a:hover {
  color: var(--primary-color-light);
}

/* 视频卡片组件样式覆盖 */
.video-card {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--bg-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.dark-theme .video-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.video-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}

.video-header {
  padding: 12px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-color-soft);
}

.video-header h3 {
  margin: 0;
  font-size: 16px;
  color: var(--text-color);
}

.video-placeholder {
  position: relative;
  width: 100%;
  height: 240px;
  background-color: var(--bg-color-mute);
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-stream {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.05);
}

.dark-theme .video-stream {
  background-color: rgba(0, 0, 0, 0.3);
}

.loading-placeholder {
  color: var(--text-color-soft);
}

.video-footer {
  padding: 12px 15px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-color-soft);
}

.status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status.active {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.dark-theme .status.active {
  background-color: rgba(103, 194, 58, 0.2);
}

.status.offline {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.status.error {
  background-color: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

/* 按钮样式覆盖 */
.el-button--info {
  --el-button-bg-color: #909399;
  --el-button-border-color: #909399;
}

.dark-theme .el-button--info {
  --el-button-bg-color: #5a5b5e;
  --el-button-border-color: #5a5b5e;
}

/* 覆盖默认滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color-soft);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* 暗色模式下的滚动条 */
.dark-theme ::-webkit-scrollbar-track {
  background: var(--bg-color-mute);
}

.dark-theme ::-webkit-scrollbar-thumb {
  background: var(--primary-color-dark);
}

/* 选中文本的颜色 */
::selection {
  background-color: var(--primary-color);
  color: white;
}

/* 确保所有弹出元素都使用正确的主题颜色 */
body .el-popper.is-light {
  border-color: var(--border-color);
}

.dark-theme body .el-popper.is-light {
  background-color: var(--bg-color-soft);
  color: var(--text-color);
  border-color: var(--border-color);
}

/* 确保所有SVG图标使用正确的颜色 */
.dark-theme svg {
  fill: currentColor;
}

/* 覆盖Element Plus的主色调 */
.el-button--primary {
  --el-button-bg-color: var(--primary-color);
  --el-button-border-color: var(--primary-color);
}

.el-button--primary:hover {
  --el-button-hover-bg-color: var(--primary-color-light);
  --el-button-hover-border-color: var(--primary-color-light);
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.el-radio__input.is-checked .el-radio__inner {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.el-switch.is-checked .el-switch__core {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: var(--primary-color);
}

/* 覆盖Element Plus的链接颜色 */
.el-link.el-link--primary {
  color: var(--primary-color);
}

.el-link.el-link--primary:hover {
  color: var(--primary-color-light);
}

/* 覆盖进度条颜色 */
.el-progress-bar__inner {
  background-color: var(--primary-color);
}

/* 覆盖步骤条颜色 */
.el-step__head.is-process {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.el-step__title.is-process {
  color: var(--primary-color);
}

/* 覆盖标签颜色 */
.el-tag--primary {
  --el-tag-bg-color: rgba(191, 46, 41, 0.1);
  --el-tag-border-color: rgba(191, 46, 41, 0.2);
  --el-tag-text-color: var(--primary-color);
}

/* 覆盖通知颜色 */
.el-notification {
  border-color: var(--border-color);
}

/* 覆盖日期选择器的选中颜色 */
.el-picker-panel__icon-btn {
  color: var(--primary-color);
}

.el-date-table td.current:not(.disabled) span {
  background-color: var(--primary-color);
}

.el-date-table td.today span {
  color: var(--primary-color);
}

/* 覆盖时间选择器的选中颜色 */
.el-time-panel__btn.confirm {
  color: var(--primary-color);
}

/* ===== 检测配置页面主题适配 ===== */

/* 检测配置容器 */
.detection-config-container {
  background-color: var(--bg-color) !important;
  transition: background-color 0.3s ease;
}

/* 页面标题和副标题 */
.detection-config-container .header h1 {
  color: var(--text-color) !important;
  transition: color 0.3s ease;
}

.detection-config-container .subtitle {
  color: var(--text-color-soft) !important;
  transition: color 0.3s ease;
}

/* 主要区域背景 */
.detection-config-container .template-section,
.detection-config-container .config-tree-section,
.detection-config-container .config-preview-section {
  background: var(--bg-color-soft) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 2px 8px var(--shadow-color) !important;
  transition: all 0.3s ease;
}

/* 区域标题 */
.detection-config-container .section-header {
  border-bottom: 1px solid var(--border-color) !important;
}

.detection-config-container .section-header h2 {
  color: var(--text-color) !important;
  transition: color 0.3s ease;
}

/* 模板卡片 */
.detection-config-container .template-card {
  background: var(--bg-color) !important;
  border: 2px solid var(--border-color) !important;
  transition: all 0.3s ease;
}

.detection-config-container .template-card:hover {
  box-shadow: 0 4px 12px var(--shadow-color-hover) !important;
}

.detection-config-container .template-card.active {
  border-color: var(--primary-color) !important;
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3) !important;
}

/* 模板信息文字 */
.detection-config-container .template-info h3 {
  color: var(--text-color) !important;
  transition: color 0.3s ease;
}

.detection-config-container .template-desc {
  color: var(--text-color-soft) !important;
  transition: color 0.3s ease;
}

.detection-config-container .create-time {
  color: var(--text-color-mute) !important;
  transition: color 0.3s ease;
}

/* 树节点颜色 */
.detection-config-container .tree-node-template .node-content {
  color: var(--primary-color) !important;
}

.detection-config-container .tree-node-die-caster .node-content {
  color: var(--success-color) !important;
}

.detection-config-container .tree-node-detection-group .node-content {
  color: var(--warning-color) !important;
}

/* 关联区域 */
.detection-config-container .associate-section h4,
.detection-config-container .context-info h4,
.detection-config-container .roi-header h4 {
  color: var(--text-color) !important;
  transition: color 0.3s ease;
}

.detection-config-container .associated-list,
.detection-config-container .context-info {
  background: var(--bg-color-mute) !important;
  transition: background-color 0.3s ease;
}

/* 压铸机信息 */
.detection-config-container .die-caster-info .name {
  color: var(--text-color) !important;
  transition: color 0.3s ease;
}

.detection-config-container .die-caster-info .desc {
  color: var(--text-color-soft) !important;
  transition: color 0.3s ease;
}

/* 上下文信息 */
.detection-config-container .context-item .label {
  color: var(--text-color-soft) !important;
  transition: color 0.3s ease;
}

.detection-config-container .context-item .value {
  color: var(--text-color) !important;
  transition: color 0.3s ease;
}

/* 空状态文字 */
.detection-config-container .empty-text {
  color: var(--text-color-mute) !important;
  transition: color 0.3s ease;
}

/* 预览区域 */
.detection-config-container .preview-header {
  background: var(--bg-color-mute) !important;
  transition: background-color 0.3s ease;
}

.detection-config-container .preview-title {
  color: var(--text-color) !important;
  transition: color 0.3s ease;
}

/* JSON查看器 */
.detection-config-container .json-viewer {
  border: 1px solid var(--border-color) !important;
  transition: border-color 0.3s ease;
}

.detection-config-container .json-content {
  background: var(--bg-color-mute) !important;
  color: var(--text-color) !important;
  transition: all 0.3s ease;
}

/* Element Tree组件覆盖 - 强制移除白色背景 */
.detection-config-container :deep(.el-tree-node__content) {
  background-color: transparent !important;
}

.detection-config-container :deep(.el-tree-node__content:hover) {
  background-color: var(--bg-color-hover) !important;
  color: var(--text-color) !important;
}

.detection-config-container :deep(.config-tree-component .el-tree-node__content) {
  background-color: transparent !important;
}

.detection-config-container :deep(.config-tree-component .el-tree-node__content:hover) {
  background-color: var(--bg-color-hover) !important;
  color: var(--text-color) !important;
}

/* 全局树形组件强制覆盖 */
.el-tree-node__content {
  background-color: transparent !important;
}

.el-tree-node__content:hover {
  background-color: var(--bg-color-hover) !important;
  color: var(--text-color) !important;
}

.el-tree-node__content:focus {
  background-color: var(--bg-color-hover) !important;
  color: var(--text-color) !important;
}

/* ===== 检测配置页面弹窗主题适配 ===== */

/* Element Plus 对话框 - 全局强制覆盖 */
.el-dialog {
  background-color: var(--bg-color-soft) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 8px 32px var(--shadow-color) !important;
}

.el-dialog__header {
  background-color: var(--bg-color-mute) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

.el-dialog__title {
  color: var(--text-color) !important;
}

.el-dialog__body {
  background-color: var(--bg-color-soft) !important;
  color: var(--text-color) !important;
}

.el-dialog__footer {
  background-color: var(--bg-color-mute) !important;
  border-top: 1px solid var(--border-color) !important;
}

/* 对话框内的表单组件强制覆盖 */
.el-dialog .el-form {
  background-color: transparent !important;
}

.el-dialog .el-form-item {
  background-color: transparent !important;
}

.el-dialog .el-form-item__label {
  color: var(--text-color) !important;
}

.el-dialog .el-form-item__content {
  background-color: transparent !important;
}

.el-dialog .el-input__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.el-dialog .el-textarea__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.el-dialog .el-select .el-input__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

/* Element Plus 抽屉 */
.el-drawer {
  background-color: var(--bg-color-soft) !important;
  border-left: 1px solid var(--border-color) !important;
}

.el-drawer__header {
  background-color: var(--bg-color-mute) !important;
  border-bottom: 1px solid var(--border-color) !important;
  color: var(--text-color) !important;
}

.el-drawer__body {
  background-color: var(--bg-color-soft) !important;
  color: var(--text-color) !important;
}

/* Element Plus 消息框 */
.el-message-box {
  background-color: var(--bg-color-soft) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 8px 32px var(--shadow-color) !important;
}

.el-message-box__header {
  background-color: var(--bg-color-mute) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

.el-message-box__title {
  color: var(--text-color) !important;
}

.el-message-box__content {
  color: var(--text-color) !important;
}

/* Element Plus 下拉菜单 */
.el-dropdown-menu {
  background-color: var(--bg-color-soft) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 4px 16px var(--shadow-color) !important;
}

.el-dropdown-menu__item {
  color: var(--text-color) !important;
}

.el-dropdown-menu__item:hover {
  background-color: var(--bg-color-hover) !important;
  color: var(--text-color) !important;
}

/* Element Plus 工具提示 */
.el-tooltip__popper {
  background-color: var(--bg-color-soft) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-color) !important;
}

.el-tooltip__popper .el-tooltip__arrow::before {
  background-color: var(--bg-color-soft) !important;
  border: 1px solid var(--border-color) !important;
}

/* Element Plus 气泡确认框 */
.el-popconfirm {
  background-color: var(--bg-color-soft) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 4px 16px var(--shadow-color) !important;
}

.el-popconfirm__main {
  color: var(--text-color) !important;
}

/* Element Plus 表单组件在弹窗中的适配 */
.el-dialog .el-form-item__label,
.el-drawer .el-form-item__label {
  color: var(--text-color) !important;
}

.el-dialog .el-input__inner,
.el-drawer .el-input__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.el-dialog .el-textarea__inner,
.el-drawer .el-textarea__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.el-dialog .el-select .el-input__inner,
.el-drawer .el-select .el-input__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

/* Element Plus 选择器下拉面板 */
.el-select-dropdown {
  background-color: var(--bg-color-soft) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 4px 16px var(--shadow-color) !important;
}

.el-select-dropdown__item {
  color: var(--text-color) !important;
}

.el-select-dropdown__item:hover {
  background-color: var(--bg-color-hover) !important;
}

.el-select-dropdown__item.selected {
  background-color: rgba(var(--primary-color-rgb), 0.1) !important;
  color: var(--primary-color) !important;
}

/* ===== 检测配置页面特定弹窗样式 ===== */

/* 创建/编辑模板对话框 */
.detection-config-container .el-dialog .template-form .el-form-item__label {
  color: var(--text-color) !important;
}

.detection-config-container .el-dialog .template-form .el-input__inner,
.detection-config-container .el-dialog .template-form .el-textarea__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

/* 创建检测组对话框特殊适配 */
.detection-config-container .el-dialog[aria-label*="创建检测组"] {
  background-color: var(--bg-color-soft) !important;
}

.detection-config-container .el-dialog[aria-label*="创建检测组"] .el-dialog__body {
  background-color: var(--bg-color-soft) !important;
}

.detection-config-container .el-dialog[aria-label*="创建检测组"] .el-form {
  background-color: transparent !important;
}

.detection-config-container .el-dialog[aria-label*="创建检测组"] .el-form-item {
  background-color: transparent !important;
}

.detection-config-container .el-dialog[aria-label*="创建检测组"] .el-form-item__content {
  background-color: transparent !important;
}

/* 创建检测组表单内容 */
.detection-config-container .create-detection-group-form {
  background-color: transparent !important;
}

.detection-config-container .create-detection-group-form .el-form-item__label {
  color: var(--text-color) !important;
}

.detection-config-container .create-detection-group-form .el-input__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.detection-config-container .create-detection-group-form .el-textarea__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.detection-config-container .create-detection-group-form .el-select .el-input__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

/* 关联压铸机对话框 */
.detection-config-container .associate-section .associated-list {
  background: var(--bg-color-mute) !important;
  border: 1px solid var(--border-color) !important;
}

.detection-config-container .associate-section .die-caster-item {
  border-bottom: 1px solid var(--border-color) !important;
}

.detection-config-container .associate-section .die-caster-item .name {
  color: var(--text-color) !important;
}

.detection-config-container .associate-section .die-caster-item .desc {
  color: var(--text-color-soft) !important;
}

/* 可选压铸机列表 */
.detection-config-container .available-die-casters .die-caster-card {
  background: var(--bg-color) !important;
  border: 1px solid var(--border-color) !important;
}

.detection-config-container .available-die-casters .die-caster-card:hover {
  box-shadow: 0 2px 8px var(--shadow-color-hover) !important;
}

.detection-config-container .available-die-casters .die-caster-card.selected {
  border-color: var(--primary-color) !important;
  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3) !important;
}

/* ROI配置对话框 */
.detection-config-container .roi-config-container .context-info {
  background: var(--bg-color-mute) !important;
  border: 1px solid var(--border-color) !important;
}

.detection-config-container .roi-config-container .context-item .label {
  color: var(--text-color-soft) !important;
}

.detection-config-container .roi-config-container .context-item .value {
  color: var(--text-color) !important;
}

.detection-config-container .roi-config-container .roi-header h4 {
  color: var(--text-color) !important;
}

.detection-config-container .roi-config-container .roi-list {
  background: var(--bg-color) !important;
  border: 1px solid var(--border-color) !important;
}

.detection-config-container .roi-config-container .roi-item {
  border-bottom: 1px solid var(--border-color) !important;
}

.detection-config-container .roi-config-container .roi-item .roi-name {
  color: var(--text-color) !important;
}

.detection-config-container .roi-config-container .roi-item .roi-desc {
  color: var(--text-color-soft) !important;
}

/* 算法配置区域 */
.detection-config-container .algorithm-config .config-item {
  background: var(--bg-color) !important;
  border: 1px solid var(--border-color) !important;
}

.detection-config-container .algorithm-config .config-item .label {
  color: var(--text-color) !important;
}

.detection-config-container .algorithm-config .config-item .value {
  color: var(--text-color-soft) !important;
}

/* 检测组创建对话框 */
.detection-config-container .current-die-caster-info {
  background: var(--bg-color-mute) !important;
  border: 1px solid var(--border-color) !important;
}

.detection-config-container .current-die-caster-info .die-caster-name {
  color: var(--text-color) !important;
}

.detection-config-container .current-die-caster-info .die-caster-desc {
  color: var(--text-color-soft) !important;
}

/* 表格组件在弹窗中的适配 */
.detection-config-container .el-dialog .el-table {
  background-color: var(--bg-color) !important;
}

.detection-config-container .el-dialog .el-table th {
  background-color: var(--bg-color-mute) !important;
  color: var(--text-color) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

.detection-config-container .el-dialog .el-table td {
  border-bottom: 1px solid var(--border-color) !important;
  color: var(--text-color) !important;
}

.detection-config-container .el-dialog .el-table tr:hover {
  background-color: var(--bg-color-hover) !important;
}

/* 分页组件在弹窗中的适配 */
.detection-config-container .el-dialog .el-pagination {
  background-color: transparent !important;
}

.detection-config-container .el-dialog .el-pagination .el-pager li {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
  border: 1px solid var(--border-color) !important;
}

.detection-config-container .el-dialog .el-pagination .el-pager li:hover {
  background-color: var(--bg-color-hover) !important;
}

.detection-config-container .el-dialog .el-pagination .el-pager li.active {
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* ===== 检测配置页面表单组件主题适配 ===== */

/* 树形组件 */
.detection-config-container :deep(.el-tree) {
  background-color: transparent !important;
}

.detection-config-container :deep(.el-tree-node__content) {
  color: var(--text-color) !important;
  background-color: transparent !important;
  transition: all 0.3s ease;
}

.detection-config-container :deep(.el-tree-node__content:hover) {
  background-color: var(--bg-color-hover) !important;
  color: var(--text-color) !important;
}

.detection-config-container :deep(.el-tree-node__content:focus) {
  background-color: var(--bg-color-hover) !important;
  color: var(--text-color) !important;
}

.detection-config-container :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(var(--primary-color-rgb), 0.1) !important;
  color: var(--primary-color) !important;
}

.detection-config-container :deep(.el-tree-node__expand-icon) {
  color: var(--text-color-soft) !important;
}

.detection-config-container :deep(.el-tree-node__label) {
  color: inherit !important;
}

/* 配置树组件特殊适配 */
.detection-config-container .config-tree-section :deep(.el-tree-node__content) {
  background-color: transparent !important;
}

.detection-config-container .config-tree-section :deep(.el-tree-node__content:hover) {
  background-color: var(--bg-color-hover) !important;
  color: var(--text-color) !important;
}

.detection-config-container .config-tree-section :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(var(--primary-color-rgb), 0.1) !important;
  color: var(--primary-color) !important;
}

/* 树节点图标颜色 */
.detection-config-container .config-tree-section :deep(.tree-node-template .node-content) {
  color: var(--primary-color) !important;
}

.detection-config-container .config-tree-section :deep(.tree-node-die-caster .node-content) {
  color: var(--success-color) !important;
}

.detection-config-container .config-tree-section :deep(.tree-node-detection-group .node-content) {
  color: var(--warning-color) !important;
}

/* 单选框组件 */
.detection-config-container .el-radio {
  color: var(--text-color) !important;
}

.detection-config-container .el-radio__label {
  color: var(--text-color) !important;
}

.detection-config-container .el-radio__input.is-checked .el-radio__inner {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.detection-config-container .el-radio__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
}

.detection-config-container .el-radio__inner:hover {
  border-color: var(--primary-color) !important;
}

/* 复选框组件 */
.detection-config-container .el-checkbox {
  color: var(--text-color) !important;
}

.detection-config-container .el-checkbox__label {
  color: var(--text-color) !important;
}

.detection-config-container .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.detection-config-container .el-checkbox__inner {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
}

.detection-config-container .el-checkbox__inner:hover {
  border-color: var(--primary-color) !important;
}

/* 复选框组中的压铸机卡片 */
.detection-config-container .el-checkbox .die-caster-card {
  background: var(--bg-color) !important;
  border: 1px solid var(--border-color) !important;
  transition: all 0.3s ease;
}

.detection-config-container .el-checkbox .die-caster-card:hover {
  box-shadow: 0 2px 8px var(--shadow-color-hover) !important;
}

.detection-config-container .el-checkbox.is-checked .die-caster-card {
  border-color: var(--primary-color) !important;
  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3) !important;
}

.detection-config-container .el-checkbox .die-caster-card .name {
  color: var(--text-color) !important;
}

.detection-config-container .el-checkbox .die-caster-card .desc {
  color: var(--text-color-soft) !important;
}

/* 按钮组件在检测配置页面中的适配 */
.detection-config-container .el-button {
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
  background-color: var(--bg-color) !important;
}

.detection-config-container .el-button:hover {
  background-color: var(--bg-color-hover) !important;
  border-color: var(--border-color-hover) !important;
}

.detection-config-container .el-button--primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

.detection-config-container .el-button--primary:hover {
  background-color: var(--primary-color-light) !important;
  border-color: var(--primary-color-light) !important;
}

.detection-config-container .el-button--danger {
  background-color: var(--danger-color) !important;
  border-color: var(--danger-color) !important;
  color: white !important;
}

.detection-config-container .el-button--danger:hover {
  background-color: #f78989 !important;
  border-color: #f78989 !important;
}

/* 标签组件 */
.detection-config-container .el-tag {
  background-color: var(--bg-color-mute) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.detection-config-container .el-tag--success {
  background-color: rgba(103, 194, 58, 0.1) !important;
  border-color: var(--success-color) !important;
  color: var(--success-color) !important;
}

.detection-config-container .el-tag--warning {
  background-color: rgba(230, 162, 60, 0.1) !important;
  border-color: var(--warning-color) !important;
  color: var(--warning-color) !important;
}

.detection-config-container .el-tag--danger {
  background-color: rgba(245, 108, 108, 0.1) !important;
  border-color: var(--danger-color) !important;
  color: var(--danger-color) !important;
}

/* ===== 检测配置页面状态组件主题适配 ===== */

/* 空状态组件 */
.detection-config-container .el-empty {
  background-color: transparent !important;
}

.detection-config-container .el-empty__description {
  color: var(--text-color-mute) !important;
}

.detection-config-container .el-empty__image svg {
  fill: var(--text-color-mute) !important;
}

.detection-config-container .empty-state {
  background-color: var(--bg-color-mute) !important;
  border: 1px solid var(--border-color) !important;
  transition: all 0.3s ease;
}

.detection-config-container .empty-text {
  color: var(--text-color-mute) !important;
  transition: color 0.3s ease;
}

.detection-config-container .empty-roi {
  background-color: var(--bg-color-mute) !important;
  border: 1px solid var(--border-color) !important;
  transition: all 0.3s ease;
}

/* 加载状态 */
.detection-config-container .el-loading-mask {
  background-color: rgba(var(--bg-color-rgb), 0.8) !important;
}

.detection-config-container .el-loading-spinner {
  color: var(--primary-color) !important;
}

.detection-config-container .el-loading-text {
  color: var(--text-color) !important;
}

/* 按钮加载状态 */
.detection-config-container .el-button.is-loading {
  color: var(--text-color-mute) !important;
}

.detection-config-container .el-button--primary.is-loading {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 分割线 */
.detection-config-container .el-divider {
  border-color: var(--border-color) !important;
}

.detection-config-container .el-divider__text {
  background-color: var(--bg-color-soft) !important;
  color: var(--text-color-soft) !important;
}

/* 卡片组件 */
.detection-config-container .el-card {
  background-color: var(--bg-color-soft) !important;
  border-color: var(--border-color) !important;
}

.detection-config-container .el-card__header {
  background-color: var(--bg-color-mute) !important;
  border-bottom: 1px solid var(--border-color) !important;
  color: var(--text-color) !important;
}

.detection-config-container .el-card__body {
  color: var(--text-color) !important;
}

/* 折叠面板 */
.detection-config-container .el-collapse {
  border-color: var(--border-color) !important;
}

.detection-config-container .el-collapse-item__header {
  background-color: var(--bg-color-mute) !important;
  color: var(--text-color) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

.detection-config-container .el-collapse-item__content {
  background-color: var(--bg-color-soft) !important;
  color: var(--text-color) !important;
}

.detection-config-container .el-collapse-item__arrow {
  color: var(--text-color-soft) !important;
}

/* 步骤条 */
.detection-config-container .el-steps {
  background-color: transparent !important;
}

.detection-config-container .el-step__title {
  color: var(--text-color) !important;
}

.detection-config-container .el-step__description {
  color: var(--text-color-soft) !important;
}

.detection-config-container .el-step__icon {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color-soft) !important;
}

.detection-config-container .el-step.is-process .el-step__icon {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

.detection-config-container .el-step.is-finish .el-step__icon {
  background-color: var(--success-color) !important;
  border-color: var(--success-color) !important;
  color: white !important;
}

/* ===== 强制覆盖所有白色背景 ===== */

/* 暗色模式下强制移除所有白色背景 */
.dark-theme .detection-config-container * {
  background-color: inherit !important;
}

.dark-theme .detection-config-container *[style*="background-color: white"],
.dark-theme .detection-config-container *[style*="background-color: #fff"],
.dark-theme .detection-config-container *[style*="background-color: #ffffff"],
.dark-theme .detection-config-container *[style*="background: white"],
.dark-theme .detection-config-container *[style*="background: #fff"],
.dark-theme .detection-config-container *[style*="background: #ffffff"] {
  background-color: var(--bg-color-soft) !important;
}

/* 特殊元素背景覆盖 */
.dark-theme .detection-config-container .el-dialog,
.dark-theme .detection-config-container .el-dialog__body,
.dark-theme .detection-config-container .el-dialog__header,
.dark-theme .detection-config-container .el-dialog__footer,
.dark-theme .detection-config-container .el-form,
.dark-theme .detection-config-container .el-form-item,
.dark-theme .detection-config-container .el-form-item__content {
  background-color: var(--bg-color-soft) !important;
}

.dark-theme .detection-config-container .el-tree-node__content {
  background-color: transparent !important;
}

.dark-theme .detection-config-container .el-tree-node__content:hover {
  background-color: var(--bg-color-hover) !important;
}

/* 确保文字颜色正确 */
.dark-theme .detection-config-container * {
  color: var(--text-color) !important;
}

.dark-theme .detection-config-container .el-form-item__label {
  color: var(--text-color) !important;
}

.dark-theme .detection-config-container .el-dialog__title {
  color: var(--text-color) !important;
}

/* 输入框特殊处理 */
.dark-theme .detection-config-container .el-input__inner,
.dark-theme .detection-config-container .el-textarea__inner {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

/* ===== 最终强制覆盖 - 确保没有白色背景 ===== */

/* 全局强制覆盖白色背景 */
.dark-theme .detection-config-container,
.dark-theme .detection-config-container *,
.dark-theme .detection-config-container *::before,
.dark-theme .detection-config-container *::after {
  background-color: inherit !important;
}

/* 重新设置主要容器背景 */
.dark-theme .detection-config-container {
  background-color: var(--bg-color) !important;
}

.dark-theme .detection-config-container .template-section,
.dark-theme .detection-config-container .config-tree-section,
.dark-theme .detection-config-container .config-preview-section {
  background-color: var(--bg-color-soft) !important;
}

/* 对话框强制覆盖 */
.dark-theme .el-dialog,
.dark-theme .el-dialog *,
.dark-theme .el-dialog *::before,
.dark-theme .el-dialog *::after {
  background-color: inherit !important;
}

.dark-theme .el-dialog {
  background-color: var(--bg-color-soft) !important;
}

.dark-theme .el-dialog__header {
  background-color: var(--bg-color-mute) !important;
}

.dark-theme .el-dialog__body {
  background-color: var(--bg-color-soft) !important;
}

.dark-theme .el-dialog__footer {
  background-color: var(--bg-color-mute) !important;
}

/* 树形组件最终覆盖 */
.dark-theme .el-tree-node__content,
.dark-theme .detection-config-container .el-tree-node__content {
  background-color: transparent !important;
}

.dark-theme .el-tree-node__content:hover,
.dark-theme .detection-config-container .el-tree-node__content:hover {
  background-color: var(--bg-color-hover) !important;
}

/* 输入框最终覆盖 */
.dark-theme .el-input__inner,
.dark-theme .el-textarea__inner,
.dark-theme .el-select .el-input__inner {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

/* 表单项最终覆盖 */
.dark-theme .el-form-item,
.dark-theme .el-form-item__content,
.dark-theme .el-form-item__label {
  background-color: transparent !important;
  color: var(--text-color) !important;
}

/* 确保所有Element Plus组件都使用正确的背景色 */
.dark-theme .el-card,
.dark-theme .el-table,
.dark-theme .el-pagination,
.dark-theme .el-tabs,
.dark-theme .el-collapse {
  background-color: var(--bg-color-soft) !important;
}

.dark-theme .el-card__body,
.dark-theme .el-table__body,
.dark-theme .el-tabs__content,
.dark-theme .el-collapse-item__content {
  background-color: var(--bg-color-soft) !important;
}
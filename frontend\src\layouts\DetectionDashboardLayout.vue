<template>
  <div class="detection-dashboard-layout">
    <!-- 纯看板内容，不包含导航栏和状态栏 -->
    <router-view />
  </div>
</template>

<script setup lang="ts">
// 独立的检测看板布局，不包含任何导航元素
</script>

<style scoped>
.detection-dashboard-layout {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background-color: var(--bg-color, #f5f5f5);
  box-sizing: border-box;
}

/* 暗色模式支持 */
.dark-theme .detection-dashboard-layout {
  background-color: var(--bg-color);
}
</style>
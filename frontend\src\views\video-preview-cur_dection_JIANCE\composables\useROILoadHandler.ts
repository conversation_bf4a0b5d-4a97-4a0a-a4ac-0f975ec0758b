import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { ROIData } from './useROILoader'

/**
 * ROI加载处理器的Composable
 * 负责将数据库中的ROI数据加载到前端绘制器中
 */
export function useROILoadHandler() {
  const isLoading = ref(false)

  /**
   * 检查ROI绘制的前提条件
   */
  const checkROIDrawingPrerequisites = (): { 
    canDraw: boolean; 
    message?: string; 
    videoElement?: HTMLVideoElement;
    canvas?: HTMLCanvasElement;
  } => {
    // 检查视频是否已加载
    const videoElement = document.querySelector('video') as HTMLVideoElement
    if (!videoElement) {
      return { 
        canDraw: false, 
        message: '未找到视频元素，请先加载视频' 
      }
    }

    if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
      return { 
        canDraw: false, 
        message: '视频尚未加载完成，请等待视频加载后再试' 
      }
    }

    // 检查canvas是否已初始化
    const canvas = document.querySelector('.roi-display-canvas') as HTMLCanvasElement
    if (!canvas) {
      return { 
        canDraw: false, 
        message: '未找到ROI绘制画布，请检查页面状态' 
      }
    }

    const context = canvas.getContext('2d')
    if (!context) {
      return { 
        canDraw: false, 
        message: 'ROI绘制画布上下文获取失败' 
      }
    }

    return { 
      canDraw: true, 
      videoElement, 
      canvas 
    }
  }

  /**
   * 转换数据库ROI数据为前端绘制格式
   */
  const convertDatabaseROIToDrawFormat = (roiData: ROIData) => {
    // 🔥 修复：添加数据验证，提供详细的调试信息
    if (!roiData) {
      throw new Error('ROI数据为空或undefined')
    }
    
    if (!roiData.roi_id) {
      console.error('ROI数据缺少roi_id字段:', roiData)
      throw new Error(`ROI数据缺少roi_id字段，完整数据: ${JSON.stringify(roiData)}`)
    }
    
    // 确保坐标数据格式正确
    const coordinates = Array.isArray(roiData.coordinates) 
      ? roiData.coordinates 
      : []

    // 验证坐标数据
    const validCoordinates = coordinates.filter(coord => 
      coord && 
      typeof coord.x === 'number' && 
      typeof coord.y === 'number' &&
      !isNaN(coord.x) && 
      !isNaN(coord.y)
    )

    if (validCoordinates.length < 3) {
      throw new Error(`ROI ${roiData.roi_id} 的坐标数据无效，至少需要3个有效坐标点。当前坐标: ${JSON.stringify(coordinates)}`)
    }

    return {
      roi_id: roiData.roi_id, // 🔥 使用roi_id字段保持一致性
      name: roiData.name || `ROI_${roiData.roi_id.slice(-8)}`,
      attribute: roiData.attribute || 'pailiao',
      roi_type: roiData.roi_type || 'polygon',
      coordinates: validCoordinates,
      points: validCoordinates, // 兼容性字段，绘制器需要
      color: roiData.color || (roiData.attribute === 'yazhu' ? '#ff0000' : '#00ffff'),
      isActive: roiData.is_active !== false,
      isCompleted: true, // 从数据库加载的ROI都是已完成的
      // 保留原始数据
      _originalData: roiData,
      _loadedFromDatabase: true
    }
  }

  /**
   * 恢复ROI的算法参数
   */
  const restoreROIAlgorithmParams = (roiData: ROIData, roiDetectors: any, setRoiDetector: Function) => {
    if (!roiData.algorithm_params) {
      return
    }

    try {
      // 将数据库中的算法参数转换为新格式
      const algorithmParams = roiData.algorithm_params
      let normalizedParams: any = {}

      if (algorithmParams.type === 'direction') {
        // 方向检测参数
        normalizedParams = {
          type: 'direction',
          motion_detection: {
            enabled: algorithmParams['前置背景检测']?.enabled ?? true,
            backgroundUpdateRate: algorithmParams['前置背景检测']?.backgroundUpdateRate ?? 0.01,
            motionThreshold: algorithmParams['前置背景检测']?.motionThreshold ?? 50,
            minArea: algorithmParams['前置背景检测']?.minArea ?? 500
          },
          direction_detection: {
            consecutiveDetectionThreshold: algorithmParams['后置方向检测']?.consecutiveDetectionThreshold ?? 3,
            minDisplacement: algorithmParams['后置方向检测']?.minDisplacement ?? 2,
            maxPatience: algorithmParams['后置方向检测']?.maxPatience ?? 3
          }
        }
      } else if (algorithmParams.type === 'motion') {
        // 运动检测参数
        const motionParams = algorithmParams['运动检测'] || {}
        normalizedParams = {
          type: 'motion',
          motion_detection: {
            algorithm: motionParams.algorithm || 'frame_difference',
            learningRate: motionParams.learningRate ?? 0.01,
            detectionThreshold: motionParams.detectionThreshold ?? 50,
            shadowRemoval: motionParams.shadowRemoval ?? 0.5,
            threshold: motionParams.threshold ?? 30,
            frameInterval: motionParams.frameInterval ?? 2,
            minArea: motionParams.minArea ?? 300
          }
        }
      } else {
        // 其他格式，直接使用
        normalizedParams = algorithmParams
      }

      // 保存到roiDetectors
      roiDetectors[roiData.roi_id] = normalizedParams

      // 调用setRoiDetector配置后端
      const algorithmType = normalizedParams.type
      if (roiData.attribute === 'pailiao' && algorithmType === 'motion') {
        // 对于pailiao类型的motion，需要根据algorithm确定具体类型
        const algorithm = normalizedParams.motion_detection?.algorithm
        if (algorithm === 'background_subtraction') {
          setRoiDetector(roiData.roi_id, 'background_subtraction', normalizedParams)
        } else {
          setRoiDetector(roiData.roi_id, 'frame_difference', normalizedParams)
        }
      } else {
        setRoiDetector(roiData.roi_id, algorithmType, normalizedParams)
      }

    } catch (error) {
      console.warn(`恢复ROI ${roiData.roi_id} 算法参数失败:`, error)
    }
  }

  /**
   * 加载ROI到绘制器
   */
  const loadROIsToDrawer = async (
    selectedROIs: ROIData[],
    roiDrawer: any,
    roiList: any,
    roiDetectors: any,
    setRoiDetector: Function,
    addOperationInfo: Function
  ): Promise<boolean> => {
    if (!selectedROIs || selectedROIs.length === 0) {
      ElMessage.warning('没有选择要加载的ROI')
      return false
    }

    // 检查前提条件
    const prerequisites = checkROIDrawingPrerequisites()
    if (!prerequisites.canDraw) {
      ElMessage.error(prerequisites.message)
      return false
    }

    isLoading.value = true
    
    try {
      addOperationInfo(`[ROI-LOAD] 开始加载 ${selectedROIs.length} 个ROI到绘制器`)
      addOperationInfo(`[ROI-LOAD] 选中的ROI ID列表: ${selectedROIs.map(roi => roi.roi_id).join(', ')}`)

      // 清空当前ROI
      if (roiDrawer && typeof roiDrawer.clearAll === 'function') {
        roiDrawer.clearAll()
        addOperationInfo('[ROI-LOAD] 已清空当前ROI配置')
      }

      // 清空roiList和roiDetectors
      const beforeClearCount = roiList.length
      roiList.splice(0, roiList.length)
      Object.keys(roiDetectors).forEach(key => delete roiDetectors[key])
      addOperationInfo(`[ROI-LOAD] 清空前ROI数量: ${beforeClearCount}, 清空后ROI数量: ${roiList.length}`)

      let successCount = 0
      let failCount = 0

      // 逐个加载ROI
      for (let i = 0; i < selectedROIs.length; i++) {
        const roiData = selectedROIs[i]
        try {
          // 🔥 修复：添加详细的ROI数据调试信息
          addOperationInfo(`[ROI-LOAD] 处理第${i + 1}个ROI，原始数据: ${JSON.stringify({
            roi_id: roiData?.roi_id,
            name: roiData?.name,
            attribute: roiData?.attribute,
            coordinates_length: roiData?.coordinates?.length,
            has_algorithm_params: !!roiData?.algorithm_params
          })}`)

          // 转换为绘制格式
          const drawROI = convertDatabaseROIToDrawFormat(roiData)
          addOperationInfo(`[ROI-LOAD] 转换后的ROI数据: ${JSON.stringify({
            roi_id: drawROI.roi_id,
            name: drawROI.name,
            attribute: drawROI.attribute,
            coordinatesCount: drawROI.coordinates.length
          })}`)

          // 恢复算法参数（在添加到列表之前）
          restoreROIAlgorithmParams(roiData, roiDetectors, setRoiDetector)

          // 🔥 关键修复：使用addROIToVideo方法，避免触发onROIAdded回调
          if (roiDrawer && typeof roiDrawer.addROIToVideo === 'function') {
            const success = roiDrawer.addROIToVideo(drawROI)
            if (success) {
              // 手动添加到roiList，因为addROIToVideo不会触发回调
              roiList.push(drawROI)
              addOperationInfo(`[ROI-LOAD] 已添加到绘制器和roiList，当前数量: ${roiList.length}`)
            } else {
              throw new Error('绘制器添加ROI失败')
            }
          } else {
            // 如果没有绘制器或方法，手动添加到roiList
            roiList.push(drawROI)
            addOperationInfo(`[ROI-LOAD] 手动添加到roiList，当前数量: ${roiList.length}`)
          }

          successCount++
          addOperationInfo(`[ROI-LOAD] ✅ 成功加载ROI: ${roiData.roi_id} (${roiData.name || '未命名'})`)

        } catch (error) {
          failCount++
          addOperationInfo(`[ROI-LOAD] ❌ 加载ROI失败: ${roiData.roi_id} - ${error}`)
          console.error('加载ROI失败:', roiData, error)
        }
      }

      addOperationInfo(`[ROI-LOAD] 加载完成，最终roiList数量: ${roiList.length}`)

      // 刷新绘制器显示
      if (roiDrawer && typeof roiDrawer.redraw === 'function') {
        roiDrawer.redraw()
      }

      // 显示结果
      if (successCount > 0) {
        ElMessage.success(`成功加载 ${successCount} 个ROI${failCount > 0 ? `，失败 ${failCount} 个` : ''}`)
        addOperationInfo(`[ROI-LOAD] 🎉 ROI加载完成: 成功 ${successCount} 个，失败 ${failCount} 个`)
        return true
      } else {
        ElMessage.error('所有ROI加载失败')
        addOperationInfo('[ROI-LOAD] ❌ 所有ROI加载失败')
        return false
      }

    } catch (error) {
      ElMessage.error(`ROI加载过程中发生异常: ${error}`)
      addOperationInfo(`[ROI-LOAD] ❌ ROI加载异常: ${error}`)
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 验证ROI数据完整性
   */
  const validateROIData = (roiData: ROIData): { valid: boolean; message?: string } => {
    if (!roiData.roi_id) {
      return { valid: false, message: 'ROI ID不能为空' }
    }

    // attribute字段可以为空，如果为空会使用默认值
    if (roiData.attribute && !['yazhu', 'pailiao'].includes(roiData.attribute)) {
      return { valid: false, message: 'ROI属性必须是yazhu或pailiao' }
    }

    if (!Array.isArray(roiData.coordinates) || roiData.coordinates.length < 3) {
      return { valid: false, message: 'ROI坐标至少需要3个点' }
    }

    const invalidCoords = roiData.coordinates.some(coord => 
      !coord || 
      typeof coord.x !== 'number' || 
      typeof coord.y !== 'number' ||
      isNaN(coord.x) || 
      isNaN(coord.y)
    )

    if (invalidCoords) {
      return { valid: false, message: 'ROI坐标数据格式无效' }
    }

    return { valid: true }
  }

  /**
   * 批量验证ROI数据
   */
  const validateROIList = (roiList: ROIData[]): { 
    validROIs: ROIData[]; 
    invalidROIs: Array<{ roi: ROIData; message: string }> 
  } => {
    const validROIs: ROIData[] = []
    const invalidROIs: Array<{ roi: ROIData; message: string }> = []

    roiList.forEach(roi => {
      const validation = validateROIData(roi)
      if (validation.valid) {
        validROIs.push(roi)
      } else {
        invalidROIs.push({ roi, message: validation.message || '未知错误' })
      }
    })

    return { validROIs, invalidROIs }
  }

  return {
    // 状态
    isLoading,
    
    // 方法
    checkROIDrawingPrerequisites,
    convertDatabaseROIToDrawFormat,
    restoreROIAlgorithmParams,
    loadROIsToDrawer,
    validateROIData,
    validateROIList
  }
}

<template>
  <div class="template-status-panel">
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon><document /></el-icon>
        检测模板状态
      </h3>
    </div>

    <div class="panel-content">
      <div class="template-list">
        <div 
          v-for="template in templates" 
          :key="template.id"
          class="template-item"
          :class="{ 'template-active': template.status === 'active' }"
        >
          <div class="template-info">
            <div class="template-name">{{ template.name }}</div>
            <div class="template-stats">
              <span class="stat">{{ template.deviceCount }} 设备</span>
              <span class="stat">{{ template.groupCount }} 检测组</span>
            </div>
          </div>
          
          <div class="template-status">
            <el-tag 
              :type="template.status === 'active' ? 'success' : 'info'" 
              size="small"
            >
              {{ template.status === 'active' ? '活跃' : '未使用' }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Document } from '@element-plus/icons-vue'

interface Template {
  id: number
  name: string
  status: 'active' | 'inactive'
  deviceCount: number
  groupCount: number
}

const props = defineProps<{
  templates: Template[]
}>()
</script>

<style scoped>
.template-status-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-color);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.panel-content {
  flex: 1;
  padding: 16px 20px;
}

.template-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.template-item:hover {
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.1);
}

.template-item.template-active {
  border-color: var(--success-color);
  background-color: rgba(var(--success-color-rgb), 0.05);
}

.template-info {
  flex: 1;
}

.template-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.template-stats {
  display: flex;
  gap: 12px;
}

.stat {
  font-size: 12px;
  color: var(--text-color-mute);
}
</style>

<template>
  <div class="dashboard-card trend-chart-card">
    <div class="card-header">
      <h3 class="card-title">
        <el-icon><DataLine /></el-icon>
        检测趋势
      </h3>
      <el-button type="primary" link>查看详细趋势</el-button>
    </div>
    <div class="card-content">
      <div ref="chart" style="width: 100%; height: 100%;"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, defineProps, watch, computed } from 'vue'
import * as echarts from 'echarts'
import { DataLine } from '@element-plus/icons-vue'
import type { PropType } from 'vue'
import type { TrendData } from '../types/dashboard.types'

const props = defineProps({
  trends: {
    type: Array as PropType<TrendData[]>,
    required: true
  }
})

const chart = ref<HTMLElement | null>(null)
let myChart: echarts.ECharts | null = null
const isDark = ref(document.documentElement.classList.contains('dark'))
let resizeObserver: ResizeObserver | null = null

const chartOption = computed(() => {
  const textColor = isDark.value ? 'hsl(210 14% 89%)' : 'hsl(210 4% 28%)'
  const axisLineColor = isDark.value ? 'hsl(210 11% 35%)' : 'hsl(210 11% 85%)'
  const splitLineColor = isDark.value ? 'hsl(210 11% 25%)' : 'hsl(210 11% 91%)'
  const dates = props.trends.map(t => new Date(t.date).toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))
  const totals = props.trends.map(t => t.total_count)
  const successRates = props.trends.map(t => (t.success_rate * 100).toFixed(1))
  const jamCounts = props.trends.map(t => t.jam_count)

  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['总检测量', '成功率', '卡料次数'],
      textStyle: {
        color: textColor
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates,
      axisLine: {
        lineStyle: {
          color: axisLineColor
        }
      },
      axisLabel: {
        color: textColor
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '次数',
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: axisLineColor
          }
        },
        axisLabel: {
          color: textColor
        },
        splitLine: {
          lineStyle: {
            color: splitLineColor
          }
        }
      },
      {
        type: 'value',
        name: '成功率',
        position: 'right',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%',
          color: textColor
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: axisLineColor
          }
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '总检测量',
        type: 'line',
        yAxisIndex: 0,
        data: totals,
        smooth: true
      },
      {
        name: '卡料次数',
        type: 'line',
        yAxisIndex: 0,
        data: jamCounts,
        smooth: true
      },
      {
        name: '成功率',
        type: 'line',
        yAxisIndex: 1,
        data: successRates,
        smooth: true
      }
    ]
  }
})

const initChart = () => {
  if (chart.value) {
    myChart = echarts.init(chart.value)
    myChart.setOption(chartOption.value)
  }
}

watch(
  () => props.trends,
  () => {
    if (myChart) {
      myChart.setOption(chartOption.value)
    }
  },
  { deep: true }
)

const handleThemeChange = (event: Event) => {
  isDark.value = (event as CustomEvent).detail.isDark
  if (myChart) {
    myChart.setOption(chartOption.value)
  }
}

onMounted(() => {
  initChart()
  window.addEventListener('themeChange', handleThemeChange)

  if (chart.value) {
    resizeObserver = new ResizeObserver(() => {
      myChart?.resize()
    })
    resizeObserver.observe(chart.value)
  }
})

onUnmounted(() => {
  window.removeEventListener('themeChange', handleThemeChange)
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  myChart?.dispose()
})
</script>

<style scoped>
.trend-chart-card {
  grid-column: 1 / span 8;
  grid-row: 3;
}
.card-content {
  height: calc(100% - 50px); /* Adjust based on header height */
}
</style> 
<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1 class="dashboard-title">监控看板调试版本</h1>
      <div class="header-actions">
        <el-button type="primary" @click="refreshAll">
          <el-icon><refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 测试系统概览组件 -->
    <div class="overview-section">
      <h2>系统概览测试</h2>
      <SystemOverview :overview-data="overviewData" />
    </div>

    <!-- 测试设备状态组件 -->
    <div class="device-section">
      <h2>设备状态测试</h2>
      <DeviceStatusPanel :devices="deviceList" @device-click="handleDeviceClick" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'

// 先只导入两个组件进行测试
import SystemOverview from './components/SystemOverview.vue'
import DeviceStatusPanel from './components/DeviceStatusPanel.vue'

// 简化的测试数据
const overviewData = ref({
  totalDevices: 4,
  activeDevices: 3,
  totalDetectionGroups: 9,
  activeDetectionGroups: 6,
  totalAlarms: 12,
  unhandledAlarms: 3,
  systemStatus: 'normal'
})

const deviceList = ref([
  { id: 1, name: '压铸机1', status: 'online', ip: '*************', lastUpdate: new Date() },
  { id: 2, name: '压铸机2', status: 'online', ip: '*************', lastUpdate: new Date() }
])

const refreshAll = () => {
  ElMessage.success('数据刷新完成')
}

const handleDeviceClick = (device: any) => {
  console.log('设备点击:', device)
  ElMessage.info(`点击了设备: ${device.name}`)
}
</script>

<style scoped>
.dashboard-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
  color: var(--text-color);
  overflow-y: auto;
  margin: -20px;
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: var(--bg-color-soft);
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
  border-radius: 8px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.overview-section,
.device-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: var(--bg-color-soft);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.overview-section h2,
.device-section h2 {
  margin: 0 0 20px 0;
  color: var(--text-color);
  font-size: 18px;
}
</style>

"""
系统日志API接口
提供系统日志的查询、过滤、导出等功能
"""

from fastapi import APIRouter, Query, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import Optional, List
import logging
import json
from datetime import datetime, timedelta
import asyncio
import threading
import os
from pathlib import Path

from app.db.session import get_db
from app.services.system_log_service import SystemLogService

router = APIRouter()

# 配置日志记录器
logger = logging.getLogger(__name__)

# 内存中的日志缓存（用于实时日志）
log_cache = []
MAX_CACHE_SIZE = 1000

# 日志文件配置
LOG_DIR = Path("logs")
if not LOG_DIR.exists():
    LOG_DIR.mkdir(parents=True, exist_ok=True)

LOG_FILE = LOG_DIR / "system.log"
LOG_ENABLED = True  # 日志开关状态

# 创建日志文件处理器
file_handler = None

def setup_file_handler():
    global file_handler
    if file_handler is None:
        file_handler = logging.FileHandler(LOG_FILE, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        file_handler.setLevel(logging.INFO)
        
        # 添加到根日志记录器
        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)
        
        logger.info(f"日志文件处理器已设置，日志将保存到: {LOG_FILE}")

# 初始化日志文件处理器
setup_file_handler()

def enable_disable_logging(enable: bool):
    """启用或禁用日志记录"""
    global LOG_ENABLED, file_handler
    
    # 获取根日志记录器
    root_logger = logging.getLogger()
    
    if enable:
        # 启用日志
        if file_handler is not None and file_handler not in root_logger.handlers:
            # 如果处理器不在，重新添加
            root_logger.addHandler(file_handler)
        # 设置日志级别
        root_logger.setLevel(logging.INFO)
        if file_handler is not None:
            file_handler.setLevel(logging.INFO)
        LOG_ENABLED = True
        return "系统日志记录已启用"
    else:
        # 禁用日志
        if file_handler is not None and file_handler in root_logger.handlers:
            # 移除文件处理器
            root_logger.removeHandler(file_handler)
        # 设置控制台日志级别为ERROR（只显示错误）
        for handler in root_logger.handlers:
            if isinstance(handler, logging.StreamHandler):
                handler.setLevel(logging.ERROR)
        LOG_ENABLED = False
        return "系统日志记录已禁用"

class DatabaseLogHandler(logging.Handler):
    """数据库日志处理器，将日志同时存储到数据库和内存缓存"""

    def __init__(self):
        super().__init__()
        # 不在初始化时创建会话，而是每次使用时创建新会话

    def emit(self, record):
        try:
            # 创建日志条目
            log_entry = {
                'id': len(log_cache) + 1,
                'timestamp': datetime.now().isoformat(),  # 使用服务器本地时间而不是record.created
                'level': record.levelname.lower(),
                'module': record.name,
                'message': record.getMessage(),
                'filename': record.filename,
                'line_number': record.lineno,
                'function_name': record.funcName
            }

            # 添加到内存缓存
            log_cache.append(log_entry)

            # 限制缓存大小
            if len(log_cache) > MAX_CACHE_SIZE:
                log_cache.pop(0)

            # 异步保存到数据库
            if LOG_ENABLED:
                self._save_to_database_async(record)

        except Exception as e:
            # 避免日志处理器本身的错误影响应用
            pass

    def _save_to_database_async(self, record):
        """异步保存到数据库"""
        def save_to_db():
            db = None
            try:
                # 每次创建新的数据库会话
                from app.db.session import SessionLocal
                db = SessionLocal()

                # 创建SystemLog实例
                from app.models.models import SystemLog
                log_entry = SystemLog.from_log_record(record)

                # 保存到数据库
                db.add(log_entry)
                db.commit()

            except Exception as e:
                # 静默处理数据库错误，避免影响主应用
                if db:
                    try:
                        db.rollback()
                    except:
                        pass
            finally:
                # 确保会话被正确关闭
                if db:
                    try:
                        db.close()
                    except:
                        pass

        # 在新线程中执行数据库操作
        threading.Thread(target=save_to_db, daemon=True).start()

# 暂时禁用自动数据库日志处理器，避免会话问题
# 用户可以通过API手动将内存日志保存到数据库
logger.info("系统日志API已启动，使用内存缓存模式")

@router.get("/logs")
async def get_system_logs(
    level: Optional[str] = Query(None, description="日志级别过滤 (debug, info, warning, error)"),
    module: Optional[str] = Query(None, description="模块名过滤"),
    limit: int = Query(100, description="返回日志数量限制"),
    offset: int = Query(0, description="偏移量"),
    start_time: Optional[str] = Query(None, description="开始时间 (ISO格式)"),
    end_time: Optional[str] = Query(None, description="结束时间 (ISO格式)"),
    source: str = Query("file", description="数据源 (database, cache, file)"),
    db: Session = Depends(get_db)
):
    """
    获取系统日志
    """
    try:
        if source == "database":
            # 从数据库获取日志
            start_dt = None
            end_dt = None

            if start_time:
                start_dt = datetime.fromisoformat(start_time.replace('Z', ''))
            if end_time:
                end_dt = datetime.fromisoformat(end_time.replace('Z', ''))

            logs, total = SystemLogService.get_logs(
                db=db,
                level=level,
                module=module,
                start_time=start_dt,
                end_time=end_dt,
                limit=limit,
                offset=offset
            )

            # 转换为字典格式
            log_dicts = [log.to_dict() for log in logs]
        
        elif source == "file":
            # 从日志文件获取日志
            log_dicts = []
            total = 0
            
            if LOG_FILE.exists():
                with open(LOG_FILE, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                filtered_logs = []
                
                for line in lines:
                    try:
                        # 解析日志行
                        # 格式: 2023-06-07 12:34:56,789 - module_name - LEVEL - message
                        parts = line.strip().split(' - ', 3)
                        if len(parts) < 4:
                            continue
                            
                        timestamp_str, module_name, level_name, message = parts
                        
                        # 转换时间戳
                        try:
                            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
                        except:
                            timestamp = datetime.now()
                            
                        log_level = level_name.lower()
                        
                        # 应用过滤
                        if level and log_level != level.lower():
                            continue
                            
                        if module and module.lower() not in module_name.lower():
                            continue
                            
                        if start_time:
                            start_dt = datetime.fromisoformat(start_time.replace('Z', ''))
                            if timestamp < start_dt:
                                continue
                                
                        if end_time:
                            end_dt = datetime.fromisoformat(end_time.replace('Z', ''))
                            if timestamp > end_dt:
                                continue
                        
                        # 创建日志条目
                        log_entry = {
                            'id': len(filtered_logs) + 1,
                            'timestamp': timestamp.isoformat(),
                            'level': log_level,
                            'module': module_name,
                            'message': message,
                            'filename': '',
                            'line_number': 0,
                            'function_name': ''
                        }
                        
                        filtered_logs.append(log_entry)
                    except Exception as e:
                        # 忽略解析错误
                        continue
                
                # 按时间倒序排序
                filtered_logs.sort(key=lambda x: x['timestamp'], reverse=True)
                
                # 分页
                total = len(filtered_logs)
                log_dicts = filtered_logs[offset:offset + limit]
        else:
            # 从内存缓存获取日志（向后兼容）
            filtered_logs = log_cache.copy()

            # 按级别过滤
            if level:
                filtered_logs = [log for log in filtered_logs if log['level'] == level.lower()]

            # 按模块过滤
            if module:
                filtered_logs = [log for log in filtered_logs if module.lower() in log['module'].lower()]

            # 按时间范围过滤
            if start_time:
                start_dt = datetime.fromisoformat(start_time.replace('Z', ''))
                filtered_logs = [log for log in filtered_logs
                               if datetime.fromisoformat(log['timestamp']) >= start_dt]

            if end_time:
                end_dt = datetime.fromisoformat(end_time.replace('Z', ''))
                filtered_logs = [log for log in filtered_logs
                               if datetime.fromisoformat(log['timestamp']) <= end_dt]

            # 按时间倒序排列（最新的在前）
            filtered_logs.sort(key=lambda x: x['timestamp'], reverse=True)

            # 分页
            total = len(filtered_logs)
            log_dicts = filtered_logs[offset:offset + limit]

        return {
            "success": True,
            "data": {
                "logs": log_dicts,
                "total": total,
                "limit": limit,
                "offset": offset,
                "source": source
            }
        }

    except Exception as e:
        logger.error(f"获取系统日志失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统日志失败: {str(e)}")

# 添加日志启停控制接口
@router.post("/logs/toggle")
async def toggle_logging(enable: bool = Query(..., description="是否启用日志")):
    """
    启用或禁用日志记录
    """
    try:
        message = enable_disable_logging(enable)
        logger.info(message)
        return {
            "success": True,
            "message": message,
            "status": "enabled" if enable else "disabled"
        }
    except Exception as e:
        logger.error(f"切换日志状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"切换日志状态失败: {str(e)}")

@router.get("/logs/status")
async def get_log_status():
    """
    获取日志状态和文件信息
    """
    try:
        # 获取日志文件大小
        file_size = 0
        if LOG_FILE.exists():
            file_size = LOG_FILE.stat().st_size
            
        # 格式化文件大小
        if file_size < 1024:
            size_str = f"{file_size} B"
        elif file_size < 1024 * 1024:
            size_str = f"{file_size / 1024:.2f} KB"
        else:
            size_str = f"{file_size / (1024 * 1024):.2f} MB"
            
        # 获取最后修改时间
        last_modified = None
        if LOG_FILE.exists():
            last_modified = datetime.fromtimestamp(LOG_FILE.stat().st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            
        return {
            "enabled": LOG_ENABLED,
            "file_path": str(LOG_FILE),
            "file_size": size_str,
            "raw_size": file_size,
            "last_modified": last_modified
        }
    except Exception as e:
        logger.error(f"获取日志状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取日志状态失败: {str(e)}")

@router.get("/logs/file")
async def get_log_file_content(
    lines: int = Query(100, description="返回的最大行数", ge=1, le=1000)
):
    """
    获取日志文件内容（最后N行）
    """
    try:
        if not LOG_FILE.exists():
            return {"content": "", "lines": 0}
            
        # 读取日志文件最后N行
        with open(LOG_FILE, "r", encoding="utf-8") as f:
            content = f.readlines()
            
        # 只返回最后N行
        if len(content) > lines:
            content = content[-lines:]
            
        return {
            "content": "".join(content),
            "lines": len(content)
        }
    except Exception as e:
        logger.error(f"读取日志文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"读取日志文件失败: {str(e)}")

@router.get("/logs/levels")
async def get_log_levels(db: Session = Depends(get_db)):
    """
    获取可用的日志级别统计
    """
    try:
        level_counts = SystemLogService.get_log_levels(db)

        return {
            "success": True,
            "data": level_counts
        }

    except Exception as e:
        logger.error(f"获取日志级别统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取日志级别统计失败: {str(e)}")

@router.get("/logs/modules")
async def get_log_modules(db: Session = Depends(get_db)):
    """
    获取可用的模块列表
    """
    try:
        modules = SystemLogService.get_log_modules(db)

        return {
            "success": True,
            "data": modules
        }

    except Exception as e:
        logger.error(f"获取模块列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模块列表失败: {str(e)}")

@router.post("/logs/clear")
async def clear_logs(
    clear_database: bool = Query(True, description="是否清空数据库日志"),
    clear_cache: bool = Query(True, description="是否清空缓存日志"),
    db: Session = Depends(get_db)
):
    """
    清空日志
    """
    try:
        cleared_count = 0

        if clear_database:
            cleared_count = SystemLogService.clear_logs(db)
            logger.info(f"数据库日志已清空，共 {cleared_count} 条")

        if clear_cache:
            global log_cache
            cache_count = len(log_cache)
            log_cache.clear()
            logger.info(f"内存缓存日志已清空，共 {cache_count} 条")

        return {
            "success": True,
            "message": f"日志已清空，数据库: {cleared_count} 条，缓存: {len(log_cache) if not clear_cache else 0} 条"
        }

    except Exception as e:
        logger.error(f"清空日志失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空日志失败: {str(e)}")

@router.get("/logs/export")
async def export_logs(
    level: Optional[str] = Query(None, description="日志级别过滤"),
    module: Optional[str] = Query(None, description="模块名过滤"),
    start_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    format: str = Query("json", description="导出格式 (json, txt, csv)")
):
    """
    导出系统日志
    """
    try:
        # 获取过滤后的日志
        filtered_logs = log_cache.copy()
        
        if level:
            filtered_logs = [log for log in filtered_logs if log['level'] == level.lower()]
        
        if module:
            filtered_logs = [log for log in filtered_logs if module.lower() in log['module'].lower()]
        
        if start_time:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            filtered_logs = [log for log in filtered_logs 
                           if datetime.fromisoformat(log['timestamp']) >= start_dt]
        
        if end_time:
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            filtered_logs = [log for log in filtered_logs 
                           if datetime.fromisoformat(log['timestamp']) <= end_dt]
        
        # 按时间排序
        filtered_logs.sort(key=lambda x: x['timestamp'])
        
        if format.lower() == "txt":
            # 文本格式导出
            content = []
            for log in filtered_logs:
                line = f"[{log['timestamp']}] [{log['level'].upper()}] [{log['module']}] {log['message']}"
                if log.get('filename'):
                    line += f"\n位置: {log['filename']}:{log['line_number']}"
                    if log.get('function_name'):
                        line += f" {log['function_name']}()"
                content.append(line)

            return {
                "success": True,
                "data": {
                    "content": "\n\n".join(content),
                    "filename": f"system_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                    "format": "text/plain"
                }
            }
        elif format.lower() == "csv":
            # CSV格式导出
            import csv
            import io

            output = io.StringIO()
            writer = csv.writer(output)

            # 写入表头
            writer.writerow(['时间戳', '级别', '模块', '消息', '文件名', '行号', '函数名'])

            # 写入数据
            for log in filtered_logs:
                writer.writerow([
                    log['timestamp'],
                    log['level'].upper(),
                    log['module'],
                    log['message'],
                    log.get('filename', ''),
                    log.get('line_number', ''),
                    log.get('function_name', '')
                ])

            return {
                "success": True,
                "data": {
                    "content": output.getvalue(),
                    "filename": f"system_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    "format": "text/csv"
                }
            }
        else:
            # JSON格式导出
            return {
                "success": True,
                "data": {
                    "content": json.dumps(filtered_logs, indent=2, ensure_ascii=False),
                    "filename": f"system_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    "format": "application/json"
                }
            }
        
    except Exception as e:
        logger.error(f"导出日志失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出日志失败: {str(e)}")

@router.get("/logs/stats")
async def get_log_statistics(db: Session = Depends(get_db)):
    """
    获取日志统计信息
    """
    try:
        # 从数据库获取统计信息
        db_stats = SystemLogService.get_log_statistics(db)

        # 添加缓存信息
        db_stats.update({
            "cache_size": len(log_cache),
            "max_cache_size": MAX_CACHE_SIZE
        })

        return {
            "success": True,
            "data": db_stats
        }

    except Exception as e:
        logger.error(f"获取日志统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取日志统计失败: {str(e)}")

@router.post("/logs/sync-to-database")
async def sync_logs_to_database(db: Session = Depends(get_db)):
    """手动将内存缓存中的日志同步到数据库"""
    try:
        if not log_cache:
            return {
                "success": True,
                "message": "没有日志需要同步",
                "synced_count": 0
            }

        synced_count = 0
        failed_count = 0

        for log_entry in log_cache:
            try:
                # 创建模拟的日志记录对象
                class MockLogRecord:
                    def __init__(self, log_data):
                        self.created = datetime.fromisoformat(log_data['timestamp']).timestamp()
                        self.levelname = log_data['level'].upper()
                        self.name = log_data['module']
                        self.filename = log_data['filename']
                        self.lineno = log_data['line_number']
                        self.funcName = log_data['function_name']
                        self.process = 0
                        self.thread = 0
                        self.threadName = "MainThread"
                        self._message = log_data['message']

                    def getMessage(self):
                        return self._message

                mock_record = MockLogRecord(log_entry)
                SystemLogService.create_log(db, mock_record)
                synced_count += 1

            except Exception as e:
                failed_count += 1
                logger.error(f"同步日志失败: {e}")

        return {
            "success": True,
            "message": f"日志同步完成",
            "synced_count": synced_count,
            "failed_count": failed_count,
            "total_cache_logs": len(log_cache)
        }

    except Exception as e:
        logger.error(f"同步日志到数据库失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步日志失败: {str(e)}")

# 添加一些测试日志
def add_test_logs():
    """添加一些测试日志"""
    logger.info("🔧 [SYSTEM] 系统日志API已启动")
    logger.info("🎯 [ROI-PARAM-VERIFY] ROI参数验证系统已就绪")
    logger.warning("⚠️ [WEBSOCKET] WebSocket连接数量较多，注意性能")
    logger.error("❌ [DATABASE] 数据库连接临时中断，正在重连...")
    logger.info("✅ [DATABASE] 数据库连接已恢复")

# 启动时添加测试日志
add_test_logs()

# 系统日志持久化存储说明

## 🎯 **功能概述**

为系统日志管理器添加了数据库持久化存储功能，日志将同时保存到SQLite数据库和内存缓存中，确保日志数据的持久性和可靠性。

## 🗄️ **数据库设计**

### **系统日志表结构 (system_logs)**

```sql
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp DATETIME NOT NULL,              -- 日志时间戳
    level VARCHAR(20) NOT NULL,               -- 日志级别 (debug, info, warning, error)
    module VARCHAR(100) NOT NULL,             -- 模块名
    message TEXT NOT NULL,                    -- 日志消息
    filename VARCHAR(200),                    -- 源文件名
    line_number INTEGER,                      -- 行号
    function_name VARCHAR(100),               -- 函数名
    extra_data TEXT,                          -- 额外数据 (JSON格式)
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### **索引设计**

```sql
-- 复合索引，优化常用查询
CREATE INDEX idx_timestamp_level ON system_logs(timestamp, level);
CREATE INDEX idx_module_level ON system_logs(module, level);
CREATE INDEX idx_created_at ON system_logs(created_at);

-- 单列索引，优化基础查询
CREATE INDEX idx_timestamp ON system_logs(timestamp);
CREATE INDEX idx_level ON system_logs(level);
CREATE INDEX idx_module ON system_logs(module);
```

## 🛠️ **实现组件**

### **1. 数据库模型 (`app/models/models.py`)**

#### **SystemLog 类**
- ✅ **ORM映射**：SQLAlchemy模型定义
- ✅ **数据转换**：`to_dict()` 方法转换为API格式
- ✅ **工厂方法**：`from_log_record()` 从日志记录创建实例
- ✅ **JSON处理**：安全的JSON序列化和反序列化

#### **关键方法**
```python
def to_dict(self):
    """转换为字典格式，供API返回"""
    
@classmethod
def from_log_record(cls, record):
    """从Python日志记录创建数据库实例"""
```

### **2. 数据库服务 (`app/services/system_log_service.py`)**

#### **SystemLogService 类**
- ✅ **CRUD操作**：创建、查询、更新、删除日志
- ✅ **高级查询**：按级别、模块、时间范围过滤
- ✅ **统计分析**：级别分布、时间分布统计
- ✅ **数据清理**：清空日志、清理旧日志
- ✅ **搜索功能**：全文搜索日志内容

#### **主要方法**
```python
@staticmethod
def create_log(db: Session, log_record) -> SystemLog
    """创建日志记录"""

@staticmethod
def get_logs(db: Session, level=None, module=None, ...) -> tuple[List[SystemLog], int]
    """获取日志列表，支持过滤和分页"""

@staticmethod
def get_log_statistics(db: Session) -> Dict[str, Any]
    """获取日志统计信息"""

@staticmethod
def clear_logs(db: Session, before_date=None) -> int
    """清空日志"""
```

### **3. 数据库日志处理器 (`app/api/system_logs.py`)**

#### **DatabaseLogHandler 类**
- ✅ **双重存储**：同时写入数据库和内存缓存
- ✅ **异步处理**：数据库操作在后台线程执行
- ✅ **错误隔离**：数据库错误不影响主应用
- ✅ **性能优化**：避免阻塞主线程

#### **工作流程**
```
日志产生 → LogHandler.emit() → 内存缓存 + 异步数据库存储
```

### **4. 更新的API接口**

#### **增强的端点**
```python
GET  /api/admin/logs?source=database     # 从数据库获取日志
GET  /api/admin/logs?source=cache        # 从缓存获取日志（向后兼容）
GET  /api/admin/logs/levels               # 数据库级别统计
GET  /api/admin/logs/modules              # 数据库模块列表
GET  /api/admin/logs/stats                # 数据库统计信息
POST /api/admin/logs/clear                # 清空数据库和缓存
```

#### **新增参数**
- `source`: 数据源选择 (database/cache)
- `clear_database`: 是否清空数据库日志
- `clear_cache`: 是否清空缓存日志

## 🚀 **部署步骤**

### **步骤1：创建数据库表**

```bash
cd backend
python create_system_log_table.py
```

**预期输出**：
```
🚀 开始创建系统日志表...
📁 数据库路径: C:\...\die_casting_detection.db
✅ system_logs 表创建成功
✅ 相关索引创建成功
✅ 测试日志数据插入成功

📋 表结构:
  - id (INTEGER)
  - timestamp (DATETIME)
  - level (VARCHAR(20))
  - module (VARCHAR(100))
  - message (TEXT)
  - filename (VARCHAR(200))
  - line_number (INTEGER)
  - function_name (VARCHAR(100))
  - extra_data (TEXT)
  - created_at (DATETIME)

📋 索引列表:
  - idx_timestamp_level
  - idx_module_level
  - idx_created_at
  - idx_timestamp
  - idx_level
  - idx_module

📊 当前日志记录数: 2

🎉 系统日志表创建完成！
```

### **步骤2：重启后端服务器**

```bash
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### **步骤3：验证功能**

1. **访问管理界面**
2. **点击"系统日志"卡片**
3. **验证日志显示**：应该看到数据库中的日志
4. **测试过滤功能**：按级别、模块过滤
5. **测试导出功能**：导出日志文件
6. **测试清空功能**：清空日志记录

## 📊 **功能特性**

### **1. 双重存储**
- ✅ **数据库存储**：持久化，重启后不丢失
- ✅ **内存缓存**：快速访问，实时更新
- ✅ **自动同步**：新日志同时写入两处

### **2. 高性能查询**
- ✅ **索引优化**：复合索引提升查询速度
- ✅ **分页查询**：支持大量日志的高效分页
- ✅ **过滤查询**：按多个维度快速过滤

### **3. 数据管理**
- ✅ **自动清理**：支持按时间清理旧日志
- ✅ **统计分析**：实时统计各种维度数据
- ✅ **搜索功能**：全文搜索日志内容

### **4. 可靠性**
- ✅ **错误隔离**：数据库错误不影响应用运行
- ✅ **异步处理**：数据库操作不阻塞主线程
- ✅ **事务安全**：数据库操作支持事务回滚

## 🔧 **配置选项**

### **环境变量**
```bash
# 数据库路径
DATABASE_URL=sqlite:///die_casting_detection.db

# 日志级别
LOG_LEVEL=INFO

# 缓存大小
LOG_CACHE_SIZE=1000
```

### **清理策略**
```python
# 自动清理30天前的日志
SystemLogService.cleanup_old_logs(db, days=30)

# 清空所有日志
SystemLogService.clear_logs(db)

# 清空指定时间前的日志
SystemLogService.clear_logs(db, before_date=datetime(2024, 1, 1))
```

## 📈 **性能优化**

### **查询优化**
- **复合索引**：`(timestamp, level)` 优化时间范围+级别查询
- **单列索引**：`timestamp`, `level`, `module` 优化基础查询
- **分页查询**：避免一次性加载大量数据

### **写入优化**
- **异步写入**：数据库操作在后台线程执行
- **批量插入**：支持批量写入提升性能
- **连接池**：复用数据库连接

### **存储优化**
- **JSON压缩**：额外数据使用JSON格式存储
- **索引选择**：只为常用查询字段创建索引
- **定期清理**：自动清理旧日志释放空间

## 🔍 **监控和维护**

### **日志统计**
```python
# 获取统计信息
stats = SystemLogService.get_log_statistics(db)
print(f"总日志数: {stats['total_logs']}")
print(f"24小时内: {stats['recent_24h']}")
print(f"错误日志: {stats['level_distribution']['error']}")
```

### **数据库维护**
```sql
-- 查看表大小
SELECT COUNT(*) FROM system_logs;

-- 查看最新日志
SELECT * FROM system_logs ORDER BY timestamp DESC LIMIT 10;

-- 清理旧日志
DELETE FROM system_logs WHERE timestamp < datetime('now', '-30 days');

-- 重建索引
REINDEX;

-- 优化数据库
VACUUM;
```

## 🎉 **升级完成**

现在系统日志管理器具有：

- ✅ **持久化存储**：日志保存到SQLite数据库
- ✅ **高性能查询**：索引优化的快速查询
- ✅ **双重保障**：数据库+缓存双重存储
- ✅ **自动清理**：支持定期清理旧日志
- ✅ **统计分析**：丰富的统计和分析功能
- ✅ **向后兼容**：保持原有API接口兼容

**🚀 请运行迁移脚本创建数据库表，然后重启服务器享受持久化的系统日志功能！**

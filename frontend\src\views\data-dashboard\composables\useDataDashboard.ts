/**
 * @file useDataDashboard.ts
 * @description A comprehensive composable for fetching and managing all data
 * required by the data dashboard. This centralizes the data logic, making
 * components cleaner and data management more predictable.
 */
import { ref, onMounted, onUnmounted, readonly } from 'vue'
import * as api from '../api/dashboard.api'
import type {
  DashboardOverview,
  TrendData,
  CardDetectionResultList,
  DetectionGroup,
  SystemPerformance,
  DeviceStatus,
  AlarmList,
  DetectionTemplate
} from '../types/dashboard.types'

export function useDataDashboard() {
  // --- Refs for State Management ---
  const loading = ref(true)
  const error = ref<Error | null>(null)

  // Data for StatusBar
  const currentTemplate = ref<DetectionTemplate[] | null>(null)
  const deviceStatus = ref<DeviceStatus | null>(null)

  // Data for MetricsPanel
  const overview = ref<DashboardOverview | null>(null)

  // Data for TrendChart
  const trends = ref<TrendData[]>([])

  // Data for DetectionLog
  const detectionLogs = ref<CardDetectionResultList | null>(null)

  // Data for GroupMonitor
  const detectionGroups = ref<DetectionGroup[]>([])

  // Data for AlarmCenter
  const alarms = ref<AlarmList | null>(null)

  // Data for SystemResources
  const systemPerformance = ref<SystemPerformance | null>(null)

  // --- Data Fetching Logic ---
  const fetchAllData = async (isBackgroundRefresh = false) => {
    if (!isBackgroundRefresh) {
      loading.value = true
    }
    error.value = null
    try {
      // Fetch all data in parallel
      const [
        overviewRes,
        trendsRes,
        logsRes,
        groupsRes,
        alarmsRes,
        performanceRes,
        templateRes,
        deviceStatusRes
      ] = await Promise.all([
        api.getDashboardOverview(),
        api.getTrends(),
        api.getDetectionLogs(),
        api.getDetectionGroups(),
        api.getAlarms(),
        api.getSystemPerformance(),
        api.getCurrentRunningTemplate(),
        api.getDeviceStatus()
      ])

      // Update refs with fetched data
      overview.value = overviewRes
      trends.value = trendsRes
      detectionLogs.value = logsRes
      detectionGroups.value = groupsRes
      alarms.value = alarmsRes
      systemPerformance.value = performanceRes
      currentTemplate.value = templateRes
      deviceStatus.value = deviceStatusRes

    } catch (e) {
      error.value = e instanceof Error ? e : new Error('Failed to fetch dashboard data')
      console.error(e)
    } finally {
      if (!isBackgroundRefresh) {
        loading.value = false
      }
    }
  }

  let pollingInterval: number | undefined;

  // Fetch data when the composable is first used and set up polling
  onMounted(() => {
    fetchAllData();
    pollingInterval = window.setInterval(() => {
      fetchAllData(true)
    }, 30000); // Poll every 30 seconds
  })

  onUnmounted(() => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
    }
  })

  // --- Return Readonly State and Control Functions ---
  return {
    loading: readonly(loading),
    error: readonly(error),
    
    // State
    overview: readonly(overview),
    trends: readonly(trends),
    detectionLogs: readonly(detectionLogs),
    detectionGroups: readonly(detectionGroups),
    alarms: readonly(alarms),
    systemPerformance: readonly(systemPerformance),
    currentTemplate: readonly(currentTemplate),
    deviceStatus: readonly(deviceStatus),

    // Functions
    refresh: () => fetchAllData(false)
  }
} 
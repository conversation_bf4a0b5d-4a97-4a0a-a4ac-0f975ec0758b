<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROI加载功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-area {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .video-container {
            position: relative;
            background: #000;
            border-radius: 4px;
            overflow: hidden;
        }
        
        #testCanvas {
            width: 100%;
            height: 400px;
            background: #333;
            cursor: crosshair;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 4px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .roi-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .roi-item {
            padding: 8px;
            margin: 4px 0;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .roi-item:hover {
            background: #e9ecef;
        }
        
        .roi-item.highlighted {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .roi-name {
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .roi-info {
            font-size: 12px;
            color: #6c757d;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-info {
            color: #007bff;
        }
        
        .log-success {
            color: #28a745;
        }
        
        .log-warning {
            color: #ffc107;
        }
        
        .log-error {
            color: #dc3545;
        }
        
        .sample-data {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ROI加载功能测试</h1>
            <p>测试ROI绘制器的加载、同步和高亮功能</p>
        </div>
        
        <div class="test-area">
            <div class="video-container">
                <canvas id="testCanvas" width="640" height="400"></canvas>
            </div>
            
            <div class="controls">
                <h3>控制面板</h3>
                
                <div style="margin-bottom: 15px;">
                    <button id="loadSampleBtn" class="btn btn-primary">加载示例ROI</button>
                    <button id="clearAllBtn" class="btn btn-danger">清空所有</button>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <button id="addPailiaoBtn" class="btn btn-success">添加排料口</button>
                    <button id="addYazhuBtn" class="btn btn-warning">添加压铸机</button>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <button id="syncBtn" class="btn btn-primary">同步ROI</button>
                    <button id="exportBtn" class="btn btn-success">导出数据</button>
                </div>
                
                <h4>ROI列表</h4>
                <div id="roiList" class="roi-list"></div>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>示例数据</h3>
            <div id="sampleData" class="sample-data"></div>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>操作日志</h3>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script src="roiDrawer.js"></script>
    <script>
        // 初始化
        const canvas = document.getElementById('testCanvas')
        const roiDrawer = new ROIDrawer(canvas, {
            videoWidth: 640,
            videoHeight: 400,
            debugMode: true
        })
        
        let currentROIList = []
        let highlightedROIId = null
        
        // 示例ROI数据
        const sampleROIData = [
            {
                id: 'pailiao_001',
                name: '排料口1',
                type: 'polygon',
                attribute: 'pailiao',
                color: '#00ffff',
                points: [
                    { x: 100, y: 100 },
                    { x: 200, y: 100 },
                    { x: 200, y: 150 },
                    { x: 100, y: 150 }
                ]
            },
            {
                id: 'pailiao_002',
                name: '排料口2',
                type: 'polygon',
                attribute: 'pailiao',
                color: '#00ffff',
                points: [
                    { x: 300, y: 120 },
                    { x: 380, y: 120 },
                    { x: 380, y: 180 },
                    { x: 300, y: 180 }
                ]
            },
            {
                id: 'yazhu_001',
                name: '压铸机1',
                type: 'polygon',
                attribute: 'yazhu',
                color: '#ff0000',
                points: [
                    { x: 450, y: 200 },
                    { x: 550, y: 200 },
                    { x: 550, y: 280 },
                    { x: 450, y: 280 }
                ]
            }
        ]
        
        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea')
            const entry = document.createElement('div')
            entry.className = `log-entry log-${type}`
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`
            logArea.appendChild(entry)
            logArea.scrollTop = logArea.scrollHeight
        }
        
        // 更新ROI列表显示
        function updateROIListDisplay() {
            const listContainer = document.getElementById('roiList')
            listContainer.innerHTML = ''
            
            currentROIList.forEach(roi => {
                const item = document.createElement('div')
                item.className = `roi-item ${highlightedROIId === roi.id ? 'highlighted' : ''}`
                item.innerHTML = `
                    <div class="roi-name">${roi.name}</div>
                    <div class="roi-info">${roi.attribute} | ${roi.points.length} 个点</div>
                `
                item.addEventListener('click', () => toggleHighlight(roi.id))
                listContainer.appendChild(item)
            })
        }
        
        // 切换高亮
        function toggleHighlight(roiId) {
            if (highlightedROIId === roiId) {
                highlightedROIId = null
                roiDrawer.clearHighlight()
                log('清除高亮显示')
            } else {
                highlightedROIId = roiId
                roiDrawer.highlightROI(roiId)
                const roi = currentROIList.find(r => r.roi_id === roiId)
                log(`高亮显示: ${roi.name}`, 'success')
            }
            updateROIListDisplay()
        }
        
        // 显示示例数据
        document.getElementById('sampleData').textContent = JSON.stringify(sampleROIData, null, 2)
        
        // 事件监听器
        document.getElementById('loadSampleBtn').addEventListener('click', () => {
            const success = roiDrawer.loadROIList(sampleROIData)
            if (success) {
                currentROIList = [...sampleROIData]
                updateROIListDisplay()
                log(`成功加载 ${sampleROIData.length} 个示例ROI`, 'success')
            } else {
                log('加载示例ROI失败', 'error')
            }
        })
        
        document.getElementById('clearAllBtn').addEventListener('click', () => {
            roiDrawer.clearROIs()
            currentROIList = []
            highlightedROIId = null
            updateROIListDisplay()
            log('清空所有ROI', 'warning')
        })
        
        document.getElementById('addPailiaoBtn').addEventListener('click', () => {
            const newROI = {
                id: `pailiao_${Date.now()}`,
                name: `排料口${currentROIList.filter(r => r.attribute === 'pailiao').length + 1}`,
                type: 'polygon',
                attribute: 'pailiao',
                color: '#00ffff',
                points: [
                    { x: Math.random() * 400 + 50, y: Math.random() * 200 + 50 },
                    { x: Math.random() * 400 + 50, y: Math.random() * 200 + 50 },
                    { x: Math.random() * 400 + 50, y: Math.random() * 200 + 50 },
                    { x: Math.random() * 400 + 50, y: Math.random() * 200 + 50 }
                ]
            }
            
            const success = roiDrawer.addROIToVideo(newROI)
            if (success) {
                currentROIList.push(newROI)
                updateROIListDisplay()
                log(`添加排料口ROI: ${newROI.name}`, 'success')
            }
        })
        
        document.getElementById('addYazhuBtn').addEventListener('click', () => {
            const newROI = {
                id: `yazhu_${Date.now()}`,
                name: `压铸机${currentROIList.filter(r => r.attribute === 'yazhu').length + 1}`,
                type: 'polygon',
                attribute: 'yazhu',
                color: '#ff0000',
                points: [
                    { x: Math.random() * 400 + 50, y: Math.random() * 200 + 200 },
                    { x: Math.random() * 400 + 50, y: Math.random() * 200 + 200 },
                    { x: Math.random() * 400 + 50, y: Math.random() * 200 + 200 },
                    { x: Math.random() * 400 + 50, y: Math.random() * 200 + 200 }
                ]
            }
            
            const success = roiDrawer.addROIToVideo(newROI)
            if (success) {
                currentROIList.push(newROI)
                updateROIListDisplay()
                log(`添加压铸机ROI: ${newROI.name}`, 'success')
            }
        })
        
        document.getElementById('syncBtn').addEventListener('click', () => {
            const success = roiDrawer.syncROIList(currentROIList)
            if (success) {
                log('ROI同步成功', 'success')
            } else {
                log('ROI同步失败', 'error')
            }
        })
        
        document.getElementById('exportBtn').addEventListener('click', () => {
            const exportData = {
                version: '2.0',
                timestamp: new Date().toISOString(),
                rois: currentROIList
            }
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `roi_test_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)
            
            log('导出ROI数据完成', 'success')
        })
        
        // 初始化日志
        log('ROI加载功能测试页面已初始化')
        log('点击"加载示例ROI"开始测试')
    </script>
</body>
</html>

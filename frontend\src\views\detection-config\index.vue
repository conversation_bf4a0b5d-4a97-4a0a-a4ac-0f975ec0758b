<template>
  <div class="detection-config-container">
    <div class="header">
      <h1>检测配置管理</h1>
      <p class="subtitle">智能视频检测的四层级配置管理</p>
    </div>

    <div class="config-layout">
      <!-- 左侧：检测模板管理 -->
      <div class="template-section">
        <div class="section-header">
          <h2>检测模板管理</h2>
          <el-button type="primary" @click="showCreateTemplateDialog = true">
            <el-icon><Plus /></el-icon>
            新建模板
          </el-button>
        </div>

        <div class="template-list">
          <el-card 
            v-for="template in templates" 
            :key="template.id" 
            class="template-card"
            :class="{ active: selectedTemplate?.id === template.id }"
            @click="selectTemplate(template)"
          >
            <div class="template-header">
              <div class="template-info">
                <h3>{{ template.name }}</h3>
                <p class="template-desc">{{ template.description || '暂无描述' }}</p>
                <div class="template-meta">
                  <el-tag :type="template.status === 'enabled' ? 'success' : 'danger'" size="small">
                    {{ template.status === 'enabled' ? '启用' : '禁用' }}
                  </el-tag>
                  <span class="create-time">{{ formatDate(template.created_at) }}</span>
                </div>
              </div>
              <div class="template-actions">
                <el-dropdown @command="handleTemplateAction">
                  <el-button type="text" size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{ action: 'edit', template }">
                        <el-icon><Edit /></el-icon> 编辑
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'copy', template }">
                        <el-icon><CopyDocument /></el-icon> 复制
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'delete', template }" divided>
                        <el-icon><Delete /></el-icon> 删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 中间：配置树状结构 -->
      <div class="config-tree-section">
        <div class="section-header">
          <h2>配置层级结构</h2>
          <div class="tree-actions" v-if="selectedTemplate">
            <el-button size="small" @click="showAssociateDiecastersDialog = true">
              <el-icon><Link /></el-icon>
              关联压铸机
            </el-button>
          </div>
        </div>

        <div class="config-tree" v-if="selectedTemplate">
          <el-tree
            :data="treeData"
            :props="treeProps"
            node-key="id"
            :expand-on-click-node="false"
            :default-expand-all="true"
            class="config-tree-component"
          >
            <template #default="{ node, data }">
              <div class="tree-node" :class="`tree-node-${data.type}`">
                <div class="node-content">
                  <el-icon class="node-icon">
                    <component :is="getNodeIcon(data.type)" />
                  </el-icon>
                  <span class="node-label">{{ data.label }}</span>
                  <el-tag v-if="data.status" :type="getStatusType(data.status)" size="small">
                    {{ getStatusText(data.status) }}
                  </el-tag>
                </div>
                <div class="node-actions">
                  <el-button 
                    v-if="data.type === 'detection-group'" 
                    type="text" 
                    size="small"
                    @click="openROIConfig(data)"
                  >
                    <el-icon><Setting /></el-icon>
                    ROI配置
                  </el-button>

                  <el-button 
                    v-if="data.type === 'die-caster'" 
                    type="text" 
                    size="small"
                    @click="openCreateDetectionGroupDialog(data)"
                  >
                    <el-icon><Plus /></el-icon>
                    添加检测组
                  </el-button>
                  <el-button 
                    v-if="data.type === 'die-caster'" 
                    type="text" 
                    size="small"
                    @click="removeDieCasterAssociation(data.id)"
                  >
                    <el-icon><Close /></el-icon>
                    移除关联
                  </el-button>
                  <el-button 
                    v-if="data.type === 'detection-group'" 
                    type="text" 
                    size="small"
                    @click="deleteDetectionGroup(data.id)"
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </div>
            </template>
          </el-tree>
        </div>

        <div v-else class="empty-state">
          <el-empty description="请选择一个检测模板查看配置结构" />
        </div>
      </div>

      <!-- 右侧：配置预览 -->
      <div class="config-preview-section">
        <div class="section-header">
          <h2>配置预览</h2>
          <div class="preview-actions" v-if="selectedTemplate">
            <el-button size="small" @click="refreshPreview">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button size="small" @click="copyConfigToClipboard">
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
          </div>
        </div>

        <div class="config-preview" v-if="selectedTemplate">
          <div class="preview-header">
            <span class="preview-title">{{ selectedTemplate.name }} - 完整配置</span>
            <el-tag :type="getStatusType(selectedTemplate.status)" size="small">
              {{ getStatusText(selectedTemplate.status) }}
            </el-tag>
          </div>
          <div class="json-viewer">
            <pre class="json-content">{{ formattedConfigJson }}</pre>
          </div>
        </div>

        <div v-else class="empty-state">
          <el-empty description="请选择一个检测模板查看配置预览" />
        </div>
      </div>
    </div>

    <!-- 创建/编辑模板对话框 -->
    <el-dialog 
      v-model="showCreateTemplateDialog" 
      :title="editingTemplate ? '编辑模板' : '创建检测模板'"
      width="500px"
    >
      <el-form :model="templateForm" :rules="templateRules" ref="templateFormRef" label-width="100px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="templateForm.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="用途描述" prop="description">
          <el-input 
            v-model="templateForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入模板用途描述（可选）" 
          />
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="editingTemplate">
          <el-radio-group v-model="templateForm.status">
            <el-radio label="enabled">启用</el-radio>
            <el-radio label="disabled">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleCancelTemplate">取消</el-button>
        <el-button type="primary" @click="handleTemplateSubmit" :loading="submitting">
          {{ editingTemplate ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 关联压铸机对话框 -->
    <el-dialog v-model="showAssociateDiecastersDialog" title="关联压铸机" width="600px">
      <div class="associate-section">
        <h4>当前已关联的压铸机</h4>
        <div class="associated-list">
          <el-tag 
            v-for="dieCaster in associatedDieCasters" 
            :key="dieCaster.id"
            closable
            @close="removeDieCasterAssociation(dieCaster.id)"
            class="associated-tag"
          >
            {{ dieCaster.name }}
          </el-tag>
          <span v-if="associatedDieCasters.length === 0" class="empty-text">暂无关联的压铸机</span>
        </div>
      </div>
      
      <div class="associate-section">
        <h4>可关联的压铸机</h4>
        <el-checkbox-group v-model="selectedDieCasterIds">
          <el-checkbox 
            v-for="dieCaster in availableDieCasters" 
            :key="dieCaster.id" 
            :label="dieCaster.id"
            class="die-caster-checkbox"
          >
            <div class="die-caster-info">
              <span class="name">{{ dieCaster.name }}</span>
              <span class="desc">{{ dieCaster.description || '暂无描述' }}</span>
            </div>
          </el-checkbox>
        </el-checkbox-group>
        <div v-if="availableDieCasters.length === 0" class="empty-text">
          暂无可关联的压铸机
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showAssociateDiecastersDialog = false">取消</el-button>
        <el-button type="primary" @click="handleAssociateDieCasters" :loading="submitting">
          确认关联
        </el-button>
      </template>
    </el-dialog>

    <!-- 创建检测组对话框 -->
    <el-dialog v-model="showCreateDetectionGroupDialog" title="创建检测组" width="500px">
      <!-- 当前压铸机信息提醒 -->
      <div v-if="currentDieCaster" class="current-die-caster-info">
        <el-alert 
          :title="`当前压铸机: ${currentDieCaster.name}`" 
          type="info" 
          :description="`描述: ${currentDieCaster.description || '暂无描述'}`"
          show-icon 
          :closable="false"
          style="margin-bottom: 20px"
        />
      </div>
      
      <el-form :model="detectionGroupForm" :rules="detectionGroupRules" ref="detectionGroupFormRef" label-width="100px">
        <el-form-item label="压铸机" prop="die_caster_id">
          <el-input 
            v-if="currentDieCaster"
            :value="currentDieCaster.name" 
            disabled 
            style="width: 100%"
          />
          <el-select 
            v-else
            v-model="detectionGroupForm.die_caster_id" 
            placeholder="请选择压铸机" 
            style="width: 100%"
          >
            <el-option 
              v-for="dieCaster in associatedDieCasters" 
              :key="dieCaster.id" 
              :label="dieCaster.name" 
              :value="dieCaster.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检测组名称" prop="name">
          <el-input v-model="detectionGroupForm.name" placeholder="请输入检测组名称" />
        </el-form-item>
        <el-form-item label="视频源" prop="video_source_id">
          <el-select 
            v-model="detectionGroupForm.video_source_id" 
            placeholder="请选择视频源" 
            filterable
            style="width: 100%"
          >
            <el-option 
              v-for="videoSource in availableVideoSources" 
              :key="videoSource.id" 
              :label="videoSource.name" 
              :value="videoSource.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDetectionGroupDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateDetectionGroup" :loading="submitting">
          创建
        </el-button>
      </template>
    </el-dialog>

    <!-- ROI配置对话框 -->
    <el-dialog
      v-model="showROIConfigDialog"
      title=""
      width="95%"
      :style="{ minWidth: '1200px', maxWidth: '1800px' }"
      :show-close="true"
      :close-on-click-modal="false"
    >



      <template #footer>
        <el-button @click="showROIConfigDialog = false">关闭</el-button>
        <el-button type="primary" @click="saveROIConfig">保存配置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useDetectionConfig } from './composables/useDetectionConfig';
import './styles/detection-config.css';

const {
  templates,
  selectedTemplate,
  associatedDieCasters,
  availableDieCasters,
  submitting,
  showCreateTemplateDialog,
  showAssociateDiecastersDialog,
  showCreateDetectionGroupDialog,
  showROIConfigDialog,
  templateForm,
  detectionGroupForm,
  selectedDieCasterIds,
  editingTemplate,
  currentDieCaster,
  templateFormRef,
  detectionGroupFormRef,
  templateRules,
  detectionGroupRules,
  treeProps,
  formattedConfigJson,
  treeData,
  availableVideoSources,
  selectTemplate,
  handleTemplateSubmit,
  handleTemplateAction,
  handleCancelTemplate,
  handleAssociateDieCasters,
  removeDieCasterAssociation,
  handleCreateDetectionGroup,
  deleteDetectionGroup,
  openROIConfig,
  openCreateDetectionGroupDialog,
  saveROIConfig,
  formatDate,
  getNodeIcon,
  getStatusType,
  getStatusText,
  refreshPreview,
  copyConfigToClipboard,
  Plus,
  Edit,
  Delete,
  CopyDocument,
  MoreFilled,
  Link,
  Setting,
  Close,
} = useDetectionConfig();

</script>
<template>
  <div class="login-container">
    <div class="login-content">
      <div class="login-header">
        <img :src="isDarkMode ? logoDark : logoLight" alt="Logo" class="logo" />
        <h1 class="system-title">压铸件智能检测系统</h1>
      </div>

      <div class="login-box">
        <div class="login-form-wrapper">
          <h2 class="login-subtitle">用户登录</h2>
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            label-width="0"
            class="login-form"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                :prefix-icon="User"
                placeholder="用户名"
                clearable
              />
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                :prefix-icon="Lock"
                type="password"
                placeholder="密码"
                show-password
                @keyup.enter="handleLogin"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                :loading="userStore.loading"
                type="primary"
                class="login-button"
                @click="handleLogin"
              >
                {{ userStore.loading ? '登录中...' : '登 录' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="system-info">
          <h3>系统功能</h3>
          <ul>
            <li><el-icon><VideoCamera /></el-icon> 视频实时监控</li>
            <li><el-icon><Aim /></el-icon> ROI区域智能绘制</li>
            <li><el-icon><Warning /></el-icon> 压铸件卡料智能检测</li>
            <li><el-icon><Bell /></el-icon> 实时报警与通知</li>
            <li><el-icon><DataAnalysis /></el-icon> 数据分析与管理</li>
          </ul>
        </div>
      </div>

      <div class="login-footer">
        <p>© {{ new Date().getFullYear() }} 压铸件智能检测系统 - 智能工业解决方案</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted } from 'vue'
import { User, Lock, VideoCamera, Aim, Warning, Bell, DataAnalysis } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { useRoute, useRouter } from 'vue-router'
import logoLight from '@/assets/logo_light.png'
import logoDark from '@/assets/logo_dark.png'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 主题状态
const isDarkMode = ref(false)

// 初始化主题状态
onMounted(() => {
  const savedTheme = localStorage.getItem('theme')
  isDarkMode.value = savedTheme === 'dark'
  
  // 监听主题变化
  window.addEventListener('themeChange', () => {
    isDarkMode.value = localStorage.getItem('theme') === 'dark'
  })
})

const loginFormRef = ref<FormInstance>()
const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules: FormRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

const handleLogin = () => {
  loginFormRef.value?.validate(async (valid) => {
    if (!valid) return
    
    const success = await userStore.login(loginForm.username, loginForm.password)
    
    if (success) {
      // 如果有重定向参数，跳转到对应页面
      const redirectPath = route.query.redirect as string
      router.replace(redirectPath || '/')
    }
  })
}
</script>

<style scoped>
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--el-color-primary-light-8) 0%, var(--el-bg-color) 100%);
  padding: 20px;
}

.login-content {
  width: 100%;
  max-width: 1000px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.logo {
  height: 60px;
  margin-right: 15px;
}

.system-title {
  font-size: 28px;
  color: var(--el-color-primary);
  margin: 0;
}

.login-box {
  width: 100%;
  display: flex;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow);
  overflow: hidden;
}

.login-form-wrapper {
  flex: 1;
  padding: 40px;
  border-right: 1px solid var(--el-border-color-lighter);
}

.login-subtitle {
  margin-top: 0;
  margin-bottom: 30px;
  font-size: 22px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  text-align: center;
}

.login-form {
  width: 100%;
}

.login-button {
  width: 100%;
  padding: 12px 0;
  font-size: 16px;
}

.system-info {
  flex: 1;
  padding: 40px;
  background-color: var(--el-bg-color);
}

.system-info h3 {
  font-size: 18px;
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--el-color-primary);
  text-align: center;
}

.system-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.system-info li {
  display: flex;
  align-items: center;
  padding: 12px 0;
  font-size: 16px;
  border-bottom: 1px dashed var(--el-border-color-lighter);
}

.system-info li:last-child {
  border-bottom: none;
}

.system-info .el-icon {
  font-size: 18px;
  color: var(--el-color-primary);
  margin-right: 10px;
}

.login-footer {
  margin-top: 20px;
  text-align: center;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-box {
    flex-direction: column;
  }
  
  .login-form-wrapper {
    border-right: none;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
}
</style> 
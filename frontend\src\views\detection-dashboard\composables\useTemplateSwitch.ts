import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { getDetectionTemplates } from '@/api/detection-templates'
import { getDetectionGroupsByTemplate } from '@/api/detection-groups'
import type { DetectionTemplate, DetectionGroup } from '@/types'

/**
 * 模板切换组合式函数
 */
export function useTemplateSwitch() {
  // 响应式数据
  const templates = ref<DetectionTemplate[]>([])
  const loading = ref(false)
  const searchKeyword = ref('')
  const selectedTemplate = ref<DetectionTemplate | null>(null)
  const startingTemplateId = ref<number | null>(null)

  /**
   * 加载检测模板列表
   */
  const loadTemplates = async () => {
    try {
      loading.value = true
      const response = await getDetectionTemplates()
      templates.value = response || []
    } catch (error) {
      console.error('加载检测模板失败:', error)
      ElMessage.error('加载检测模板失败')
      templates.value = []
    } finally {
      loading.value = false
    }
  }

  /**
   * 选择模板
   */
  const selectTemplate = (template: DetectionTemplate) => {
    selectedTemplate.value = template
  }

  /**
   * 启动模板
   */
  const startTemplate = async (template: DetectionTemplate) => {
    try {
      startingTemplateId.value = template.id
      
      // 获取模板关联的检测组
      const detectionGroups = await getDetectionGroupsByTemplate(template.id)
      
      if (!detectionGroups || detectionGroups.length === 0) {
        ElMessage.warning('该模板没有关联的检测组')
        return null
      }

      ElMessage.success(`成功获取到 ${detectionGroups.length} 个检测组，开始加载...`)
      
      return {
        templateId: template.id,
        templateName: template.name,
        detectionGroups: detectionGroups
      }
    } catch (error) {
      console.error('启动模板失败:', error)
      ElMessage.error('启动模板失败')
      return null
    } finally {
      startingTemplateId.value = null
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    selectedTemplate.value = null
    searchKeyword.value = ''
    startingTemplateId.value = null
  }

  return {
    // 响应式数据
    templates,
    loading,
    searchKeyword,
    selectedTemplate,
    startingTemplateId,
    
    // 方法
    loadTemplates,
    selectTemplate,
    startTemplate,
    resetState
  }
}

/**
 * 模板切换状态类型
 */
export interface TemplateSelectedData {
  templateId: number
  templateName: string
  detectionGroups: DetectionGroup[]
}
17:09:09 [设置] 手动应用全局设置: 延时=5秒, 暂停阈值=15秒, 冷却时间=3秒
17:09:09 [CONFIG] 准备发送配置更新:
17:09:09 [CONFIG] - 全局设置: {"delay_time":5,"pause_threshold":15,"cooldown_time":3}
17:09:09 [CONFIG] ⚠️ WebSocket未连接，配置将在连接后发送
17:09:09 [系统] 视频预览组件开始初始化
17:09:09 [INFO] jQuery已加载，将使用jQuery解析XML
17:09:09 浏览器支持检查通过
17:09:09 [API] 开始获取视频源列表...
17:09:09 [API] 成功加载 2 个WebSDK设备
17:09:09 [设置] 初始化全局检测参数
17:09:10 [PLUGIN] WebSDK插件初始化完成
17:09:10 [PLUGIN] WebSDK插件嵌入完成
17:09:10 [INFO] 初始组件挂载完成，预检查ROI画布状态
17:09:10 [INFO] 找到ROI画布: 640x360
17:09:11 [DB] 恢复ROI配置: mcvqi79z332z1t58x1w, 坐标: 4个点
17:09:11 [ROI-CONFIG] 设置ROI mcvqi79z332z1t58x1w 检测器:
17:09:11 [ROI-CONFIG] - 算法类型: motion
17:09:11 [ROI-CONFIG] - 原始参数: {
  "type": "motion",
  "motion_detection": {
    "algorithm": "frame_difference",
    "learningRate": 0.01,
    "detectionThreshold": 50,
    "shadowRemoval": 0.5,
    "threshold": 30,
    "frameInterval": 2,
    "minArea": 300
  }
}
17:09:11 [ROI-CONFIG] - 标准化参数: {
  "type": "motion"
}
17:09:11 [ROI-CONFIG] ⚠️ WebSocket未连接，ROI mcvqi79z332z1t58x1w 配置将在连接后发送
17:09:11 [DB] 恢复ROI配置: mcvqg67fdcngju35cp4, 坐标: 4个点
17:09:11 [ROI-CONFIG] 设置ROI mcvqg67fdcngju35cp4 检测器:
17:09:11 [ROI-CONFIG] - 算法类型: motion
17:09:11 [ROI-CONFIG] - 原始参数: {
  "type": "motion",
  "motion_detection": {
    "algorithm": "frame_difference",
    "learningRate": 0.01,
    "detectionThreshold": 50,
    "shadowRemoval": 0.5,
    "threshold": 30,
    "frameInterval": 2,
    "minArea": 300
  }
}
17:09:11 [ROI-CONFIG] - 标准化参数: {
  "type": "motion"
}
17:09:11 [ROI-CONFIG] ⚠️ WebSocket未连接，ROI mcvqg67fdcngju35cp4 配置将在连接后发送
17:09:11 [DB] 恢复ROI配置: mcvqeh89y83825pbtn8, 坐标: 4个点
17:09:11 [ROI-CONFIG] 设置ROI mcvqeh89y83825pbtn8 检测器:
17:09:11 [ROI-CONFIG] - 算法类型: motion
17:09:11 [ROI-CONFIG] - 原始参数: {
  "type": "motion",
  "motion_detection": {
    "algorithm": "frame_difference",
    "learningRate": 0.01,
    "detectionThreshold": 50,
    "shadowRemoval": 0.5,
    "threshold": 30,
    "frameInterval": 2,
    "minArea": 300
  }
}
17:09:11 [ROI-CONFIG] - 标准化参数: {
  "type": "motion"
}
17:09:11 [ROI-CONFIG] ⚠️ WebSocket未连接，ROI mcvqeh89y83825pbtn8 配置将在连接后发送
17:09:11 [DB] ✅ 已加载3个ROI配置
17:09:13 [SOURCE] 选择视频源: 234 (192.168.1.64:80)
17:09:13 [AUTO] 开始自动化流程: 登录设备 → 获取通道 → 开始预览
17:09:13 [PREVIEW] 开始连接设备...
17:09:13 [PREVIEW] 正在登录设备...
17:09:13 [LOGIN] 正在连接设备: 192.168.1.64:80 (1)
17:09:14 [LOGIN] 设备登录成功: 192.168.1.64
17:09:14 [LOGIN] 登录成功，正在获取通道信息...
17:09:14 [LOGIN] 设备连接完成，可以开始预览
17:09:14 [CHANNEL] 正在获取通道信息（优先选择模拟通道）...
17:09:14 [CHANNEL] 成功获取模拟通道信息
17:09:14 [CHANNEL] 找到 1 个模拟通道
17:09:14 [CHANNEL] 自动选择模拟通道: Camera 01 (ID: 1)
17:09:14 [WARNING] 获取数字通道信息失败
17:09:14 [CHANNEL] 通道配置完成，当前选择通道: Camera 01
17:09:15 [PREVIEW] 正在获取设备端口信息...
17:09:15 [PREVIEW] 获取设备端口成功: RTSP端口=554, 设备端口=8000
17:09:15 [PREVIEW] 开始播放通道 1 (Camera 01) - 主码流
17:09:15 [PREVIEW] 尝试直连模式 (bProxy: false)...
17:09:15 [PREVIEW] ✅ 直连模式预览成功
17:09:15 [VIDEO] 视频实际尺寸: 1280x720
17:09:15 [VIDEO] ℹ️ 注意：检测到非标准尺寸: 1280x720
17:09:15 [VIDEO] 视频实际尺寸: 1280x720
17:09:15 [VIDEO] ℹ️ 注意：检测到非标准尺寸: 1280x720
17:09:16 [VIDEO] 视频实际尺寸: 1280x720
17:09:16 [VIDEO] ℹ️ 注意：检测到非标准尺寸: 1280x720
17:09:16 [VIDEO] 视频实际尺寸: 1280x720
17:09:16 [VIDEO] ℹ️ 注意：检测到非标准尺寸: 1280x720
17:09:16 [VIDEO] 视频实际尺寸: 1280x720
17:09:16 [VIDEO] ℹ️ 注意：检测到非标准尺寸: 1280x720
17:09:16 [ROI] 画布准备完成，尺寸: 1280x720，开始初始化绘制器
17:09:16 [ROI] 初始化绘制器，Canvas尺寸: 640x360
17:09:16 [ROI] Canvas尺寸检查: 640x360
17:09:16 [ROI] 准备实例化ROIDrawer, canvas=true, context=true
17:09:16 [ROI] 初始化绘制器 - 未选择属性，禁用绘制
17:09:16 [ROI] 绘制器初始化成功
17:09:17 [VIDEO] 视频实际尺寸: 1280x720
17:09:17 [VIDEO] ℹ️ 注意：检测到非标准尺寸: 1280x720
17:09:17 [ROI] 初始化绘制器，Canvas尺寸: 1280x720
17:09:17 [ROI] Canvas尺寸检查: 1280x720
17:09:17 [ROI] 准备实例化ROIDrawer, canvas=true, context=true
17:09:17 [ROI] 初始化绘制器 - 未选择属性，禁用绘制
17:09:17 [ROI] 绘制器初始化成功
17:09:17 [ROI] 成功将ROI绘制器尺寸更新为: 1280x720
17:09:17 [VIDEO] 视频实际尺寸: 1280x720
17:09:17 [VIDEO] ℹ️ 注意：检测到非标准尺寸: 1280x720
17:09:17 [ROI] 初始化绘制器，Canvas尺寸: 1280x720
17:09:17 [ROI] Canvas尺寸检查: 1280x720
17:09:17 [ROI] 准备实例化ROIDrawer, canvas=true, context=true
17:09:17 [ROI] 初始化绘制器 - 未选择属性，禁用绘制
17:09:17 [ROI] 绘制器初始化成功
17:09:17 [ROI] 成功将ROI绘制器尺寸更新为: 1280x720
17:09:17 [ROI] 尺寸更新后重新加载 3 个ROI
17:09:17 [ROI] 尺寸更新后重新加载 3 个ROI
17:09:20 [VIDEO] 视频实际尺寸: 1280x720
17:09:20 [VIDEO] ℹ️ 注意：检测到非标准尺寸: 1280x720
17:09:20 [ROI] 初始化绘制器，Canvas尺寸: 1280x720
17:09:20 [ROI] Canvas尺寸检查: 1280x720
17:09:20 [ROI] 准备实例化ROIDrawer, canvas=true, context=true
17:09:20 [ROI] 初始化绘制器 - 未选择属性，禁用绘制
17:09:20 [ROI] 绘制器初始化成功
17:09:20 [ROI] 成功将ROI绘制器尺寸更新为: 1280x720
17:09:20 [ROI] 尺寸更新后重新加载 3 个ROI
17:09:22 [检测] 开始从数据库加载ROI检测参数...
17:09:22 [检测] ✅ ROI mcvqi79z332z1t58x1w 参数已从数据库加载并转换为新格式
17:09:22 [检测] 参数详情: {
  "type": "motion",
  "motion_detection": {
    "algorithm": "frame_difference",
    "learningRate": 0.01,
    "detectionThreshold": 50,
    "shadowRemoval": 0.5,
    "threshold": 30,
    "frameInterval": 2,
    "minArea": 300
  }
}
17:09:22 [检测] ✅ ROI mcvqg67fdcngju35cp4 参数已从数据库加载并转换为新格式
17:09:22 [检测] 参数详情: {
  "type": "motion",
  "motion_detection": {
    "algorithm": "frame_difference",
    "learningRate": 0.01,
    "detectionThreshold": 50,
    "shadowRemoval": 0.5,
    "threshold": 30,
    "frameInterval": 2,
    "minArea": 300
  }
}
17:09:22 [检测] ✅ ROI mcvqeh89y83825pbtn8 参数已从数据库加载并转换为新格式
17:09:22 [检测] 参数详情: {
  "type": "motion",
  "motion_detection": {
    "algorithm": "frame_difference",
    "learningRate": 0.01,
    "detectionThreshold": 50,
    "shadowRemoval": 0.5,
    "threshold": 30,
    "frameInterval": 2,
    "minArea": 300
  }
}
17:09:22 [检测] ROI参数加载完成，共加载 3 个ROI配置
17:09:22 [CONFIG] 准备发送配置更新:
17:09:22 [CONFIG] - 全局设置: {"delay_time":5,"pause_threshold":15,"cooldown_time":3}
17:09:22 [CONFIG] ⚠️ WebSocket未连接，配置将在连接后发送
17:09:22 [检测] 当前ROI检测器配置: {
  "mcvqi79z332z1t58x1w": {
    "type": "motion",
    "motion_detection": {
      "algorithm": "frame_difference",
      "learningRate": 0.01,
      "detectionThreshold": 50,
      "shadowRemoval": 0.5,
      "threshold": 30,
      "frameInterval": 2,
      "minArea": 300
    }
  },
  "mcvqg67fdcngju35cp4": {
    "type": "motion",
    "motion_detection": {
      "algorithm": "frame_difference",
      "learningRate": 0.01,
      "detectionThreshold": 50,
      "shadowRemoval": 0.5,
      "threshold": 30,
      "frameInterval": 2,
      "minArea": 300
    }
  },
  "mcvqeh89y83825pbtn8": {
    "type": "motion",
    "motion_detection": {
      "algorithm": "frame_difference",
      "learningRate": 0.01,
      "detectionThreshold": 50,
      "shadowRemoval": 0.5,
      "threshold": 30,
      "frameInterval": 2,
      "minArea": 300
    }
  }
}
17:09:22 [WARNING] WebSocket未连接，无法同步ROI配置
17:09:22 [WS] WebSocket连接状态: connecting
17:09:22 [WS] WebSocket连接状态: connected
17:09:22 [WS] WebSocket连接成功
17:09:22 [WS] 已发送全局设置到后端
17:09:22 [CONFIG] 准备发送配置更新:
17:09:22 [CONFIG] - 全局设置: {"delay_time":5,"pause_threshold":15,"cooldown_time":3}
17:09:22 [WS-SEND] 发送配置到后端: {
  "global_settings": {
    "delay_time": 5,
    "pause_threshold": 15,
    "cooldown_time": 3
  }
}
17:09:22 [CONFIG] ✅ 配置已发送到后端
17:09:22 [检测] 开始ROI检测，检测模式将根据ROI参数动态确定
17:09:22 [检测] ROI区域检测已启动
17:09:22 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:22 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:23 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:23 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:23 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:23 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:23 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:23 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:23 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:23 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:23 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:23 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:24 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:24 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:24 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:24 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:24 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:24 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:24 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:24 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:24 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:24 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:25 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:25 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:25 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:25 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:25 [VIDEO] 视频实际尺寸: 1280x720
17:09:25 [VIDEO] ℹ️ 注意：检测到非标准尺寸: 1280x720
17:09:25 [ROI] 初始化绘制器，Canvas尺寸: 1280x720
17:09:25 [ROI] Canvas尺寸检查: 1280x720
17:09:25 [ROI] 准备实例化ROIDrawer, canvas=true, context=true
17:09:25 [ROI] 初始化绘制器 - 未选择属性，禁用绘制
17:09:25 [ROI] 绘制器初始化成功
17:09:25 [ROI] 成功将ROI绘制器尺寸更新为: 1280x720
17:09:25 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:25 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:25 [ROI] 尺寸更新后重新加载 3 个ROI
17:09:25 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:25 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:25 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:25 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:26 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:26 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:26 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:26 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:26 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:26 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:26 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:26 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:26 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:26 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:27 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:27 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:27 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:27 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:27 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:27 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:27 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:27 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:27 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:27 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:28 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:28 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:28 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:28 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:28 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:28 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:28 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:28 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:28 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:28 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:29 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:29 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:29 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:29 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:29 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:29 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
17:09:29 [CAPTURE-DEBUG] ✅ 开始截图，视频尺寸: 1280x720，ROI数量: 3
17:09:29 [FRAME-SEND] 检测模式分析: 方向检测ROI=0个, 运动检测ROI=3个, 选择模式=motion
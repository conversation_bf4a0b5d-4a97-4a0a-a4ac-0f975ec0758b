#!/usr/bin/env python3
"""
详细调试MotionDetector
逐步检查每个处理步骤
"""

import numpy as np
import cv2
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.video_detection import MotionDetector

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def debug_motion_detector_detailed():
    """详细调试MotionDetector的每个步骤"""
    print("🔬 详细调试MotionDetector")
    print("=" * 50)
    
    # 创建MotionDetector
    motion_params = {
        'minArea': 50,
        'detectionThreshold': 20,
        'learningRate': 0.1,
        'shadowsThreshold': 0.5
    }
    
    motion_detector = MotionDetector(motion_params)
    
    # 创建测试帧
    frame1 = np.zeros((480, 640, 3), dtype=np.uint8)
    frame1[:, :] = (50, 50, 50)
    
    frame2 = frame1.copy()
    cv2.rectangle(frame2, (140, 120), (160, 140), (255, 255, 255), -1)
    
    # 创建ROI掩码
    roi_mask = np.zeros((480, 640), dtype=np.uint8)
    cv2.rectangle(roi_mask, (100, 100), (200, 200), 255, -1)
    
    print("测试配置:")
    print(f"  参数: {motion_params}")
    print(f"  帧1: 纯灰色背景")
    print(f"  帧2: 灰色背景 + 白色矩形")
    print(f"  ROI: (100,100)-(200,200)")
    
    # 处理第一帧
    print(f"\n=== 处理第一帧 ===")
    debug_single_frame(motion_detector, frame1, roi_mask, "第一帧")
    
    # 处理第二帧
    print(f"\n=== 处理第二帧 ===")
    result = debug_single_frame(motion_detector, frame2, roi_mask, "第二帧")
    
    return result

def debug_single_frame(motion_detector, frame, roi_mask, frame_name):
    """调试单帧处理"""
    print(f"\n--- {frame_name} 处理步骤 ---")
    
    # 步骤1: 获取处理后的ROI
    print("步骤1: 获取处理后的ROI")
    gray_roi, mask_roi, offset = motion_detector._get_processed_roi(frame, roi_mask)
    
    if gray_roi is None:
        print("  ❌ gray_roi为None")
        return False
    
    print(f"  ✅ gray_roi形状: {gray_roi.shape}")
    print(f"  ✅ mask_roi形状: {mask_roi.shape if mask_roi is not None else 'None'}")
    print(f"  ✅ 偏移量: {offset}")
    print(f"  ✅ gray_roi值范围: {gray_roi.min()} - {gray_roi.max()}")
    
    # 步骤2: 应用背景减除
    print(f"\n步骤2: 应用背景减除")
    fg_mask = motion_detector.bg_subtractor.apply(gray_roi, learningRate=motion_detector.params['learningRate'])
    
    print(f"  ✅ fg_mask形状: {fg_mask.shape}")
    print(f"  ✅ fg_mask值范围: {fg_mask.min()} - {fg_mask.max()}")
    print(f"  ✅ 前景像素数: {np.sum(fg_mask > 0)}")
    print(f"  ✅ 255像素数: {np.sum(fg_mask == 255)}")
    print(f"  ✅ 127像素数: {np.sum(fg_mask == 127)}")
    
    # 步骤3: 阴影处理
    print(f"\n步骤3: 阴影处理")
    original_fg_mask = fg_mask.copy()
    
    if motion_detector.params['shadowsThreshold'] > 0:
        fg_mask = cv2.threshold(fg_mask, 200, 255, cv2.THRESH_BINARY)[1]
        print(f"  ✅ 使用阴影检测，阈值=200")
    else:
        fg_mask = cv2.threshold(fg_mask, 127, 255, cv2.THRESH_BINARY)[1]
        print(f"  ✅ 不使用阴影检测，阈值=127")
    
    print(f"  ✅ 处理前前景像素数: {np.sum(original_fg_mask > 0)}")
    print(f"  ✅ 处理后前景像素数: {np.sum(fg_mask > 0)}")
    
    # 步骤4: 形态学操作
    print(f"\n步骤4: 形态学操作")
    before_morph = np.sum(fg_mask > 0)
    
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
    after_open = np.sum(fg_mask > 0)
    
    fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, kernel)
    after_close = np.sum(fg_mask > 0)
    
    print(f"  ✅ 形态学前: {before_morph} 像素")
    print(f"  ✅ OPEN后: {after_open} 像素")
    print(f"  ✅ CLOSE后: {after_close} 像素")
    
    # 步骤5: 应用ROI掩码
    print(f"\n步骤5: 应用ROI掩码")
    before_roi_mask = np.sum(fg_mask > 0)
    
    if mask_roi is not None:
        fg_mask = cv2.bitwise_and(fg_mask, mask_roi)
        after_roi_mask = np.sum(fg_mask > 0)
        print(f"  ✅ 应用ROI掩码")
        print(f"  ✅ 掩码前: {before_roi_mask} 像素")
        print(f"  ✅ 掩码后: {after_roi_mask} 像素")
    else:
        print(f"  ✅ 无ROI掩码")
    
    # 步骤6: 查找轮廓
    print(f"\n步骤6: 查找轮廓")
    contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    print(f"  ✅ 找到轮廓数: {len(contours)}")
    
    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        print(f"    轮廓 {i+1}: 面积={area:.1f}")
    
    # 步骤7: 过滤轮廓
    print(f"\n步骤7: 过滤轮廓")
    min_area = motion_detector.params['minArea']
    filtered_contours = [cnt for cnt in contours if cv2.contourArea(cnt) >= min_area]
    
    print(f"  ✅ 最小面积阈值: {min_area}")
    print(f"  ✅ 过滤后轮廓数: {len(filtered_contours)}")
    
    for i, contour in enumerate(filtered_contours):
        area = cv2.contourArea(contour)
        print(f"    过滤后轮廓 {i+1}: 面积={area:.1f}")
    
    # 步骤8: 最终结果
    print(f"\n步骤8: 最终结果")
    motion_detected = len(filtered_contours) > 0
    print(f"  ✅ 运动检测结果: {motion_detected}")
    
    return motion_detected

def test_with_different_parameters():
    """测试不同参数配置"""
    print(f"\n🧪 测试不同参数配置")
    print("=" * 50)
    
    # 测试参数组合
    param_sets = [
        {
            'name': '默认参数',
            'params': {
                'minArea': 100,
                'detectionThreshold': 40,
                'learningRate': 0.005,
                'shadowsThreshold': 0.5
            }
        },
        {
            'name': '敏感参数',
            'params': {
                'minArea': 50,
                'detectionThreshold': 20,
                'learningRate': 0.1,
                'shadowsThreshold': 0.5
            }
        },
        {
            'name': '无阴影检测',
            'params': {
                'minArea': 50,
                'detectionThreshold': 20,
                'learningRate': 0.1,
                'shadowsThreshold': 0
            }
        },
        {
            'name': '极敏感参数',
            'params': {
                'minArea': 10,
                'detectionThreshold': 10,
                'learningRate': 0.2,
                'shadowsThreshold': 0
            }
        }
    ]
    
    # 创建测试帧
    frame1 = np.zeros((480, 640, 3), dtype=np.uint8)
    frame1[:, :] = (50, 50, 50)
    
    frame2 = frame1.copy()
    cv2.rectangle(frame2, (140, 120), (160, 140), (255, 255, 255), -1)
    
    roi_mask = np.zeros((480, 640), dtype=np.uint8)
    cv2.rectangle(roi_mask, (100, 100), (200, 200), 255, -1)
    
    results = []
    
    for param_set in param_sets:
        print(f"\n--- 测试: {param_set['name']} ---")
        print(f"参数: {param_set['params']}")
        
        motion_detector = MotionDetector(param_set['params'])
        
        # 处理第一帧
        motion1, contours1 = motion_detector.detect(frame1, roi_mask)
        
        # 处理第二帧
        motion2, contours2 = motion_detector.detect(frame2, roi_mask)
        
        print(f"结果: 运动={motion2}, 轮廓数={len(contours2)}")
        
        if contours2:
            for i, contour in enumerate(contours2):
                area = cv2.contourArea(contour)
                print(f"  轮廓 {i+1}: 面积={area:.1f}")
        
        results.append({
            'name': param_set['name'],
            'motion_detected': motion2,
            'contour_count': len(contours2)
        })
    
    print(f"\n📊 参数测试总结:")
    for result in results:
        status = "✅" if result['motion_detected'] else "❌"
        print(f"  {status} {result['name']}: 运动={result['motion_detected']}, 轮廓={result['contour_count']}")
    
    return any(r['motion_detected'] for r in results)

if __name__ == "__main__":
    try:
        print("🔬 MotionDetector详细调试")
        print("=" * 60)
        
        # 详细调试
        detailed_result = debug_motion_detector_detailed()
        
        # 参数测试
        param_result = test_with_different_parameters()
        
        print("\n" + "=" * 60)
        print("📊 调试总结:")
        print(f"  详细调试: {'✅' if detailed_result else '❌'}")
        print(f"  参数测试: {'✅' if param_result else '❌'}")
        
        if param_result:
            print("\n✅ 找到了工作的参数配置！")
        else:
            print("\n❌ 所有参数配置都失败，需要检查算法实现。")
        
    except Exception as e:
        print(f"\n❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

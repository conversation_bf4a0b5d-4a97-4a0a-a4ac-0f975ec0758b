<template>
  <div class="dashboard-card group-monitor-card">
    <div class="card-header">
      <h3 class="card-title">
        <el-icon><Aim /></el-icon>
        检测组状态监控
      </h3>
    </div>
    <div class="card-content">
      <ul v-if="groups.length" class="group-list">
        <li v-for="group in groups" :key="group.id" class="group-item">
          <div class="group-info">
            <span class="group-name">{{ group.name }}</span>
            <el-tag :type="getStatusType(group.status)" size="small" effect="dark">
              {{ group.status === 'active' ? '运行中' : '已停用' }}
            </el-tag>
          </div>
        </li>
      </ul>
      <el-empty v-else description="暂无检测组" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'
import type { PropType } from 'vue'
import { Aim } from '@element-plus/icons-vue'
import type { DetectionGroup } from '../types/dashboard.types'

defineProps({
  groups: {
    type: Array as PropType<DetectionGroup[]>,
    required: true
  }
})

const getStatusType = (status: string) => {
  if (status === 'active') return 'success'
  return 'info'
}
</script>

<style scoped>
.group-monitor-card {
  grid-column: 1 / span 6;
  grid-row: 4;
}

.group-list {
  list-style: none;
  padding: 0;
  margin: 0;
  height: 100%;
  overflow-y: auto;
}

.group-item {
  padding: 16px 4px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.group-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-name {
  font-weight: 600;
  color: var(--text-color);
}
</style> 
/**
 * 每日检测统计API相关类型定义
 */

// 检测组统计信息
export interface DetectionGroupStatistics {
  group_id: number
  group_name: string
  total_detections: number
  jam_detections: number
  success_rate: number
}

// 压铸机统计信息
export interface DieCasterStatistics {
  die_caster_id: number
  die_caster_name: string
  total_detections: number
  jam_detections: number
  success_rate: number
  detection_groups: DetectionGroupStatistics[]
}

// 检测模板统计信息
export interface TemplateStatistics {
  template_id: number
  template_name: string
  total_detections: number
  jam_detections: number
  success_rate: number
  die_casters: DieCasterStatistics[]
}

// 每日检测统计信息
export interface DailyDetectionStatistics {
  date: string // YYYY-MM-DD
  templates: TemplateStatistics[]
}

// API响应类型
export interface DailyStatisticsResponse {
  success: boolean
  data: DailyDetectionStatistics
  message?: string
}
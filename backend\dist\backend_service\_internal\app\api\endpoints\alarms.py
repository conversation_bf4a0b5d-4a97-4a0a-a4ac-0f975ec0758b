from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from datetime import datetime, timedelta
from enum import Enum

from app.api import deps
from app.models.models import CardDetectionResult, DetectionGroup

router = APIRouter()


class AlarmLevel(str, Enum):
    """报警级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlarmType(str, Enum):
    """报警类型"""
    CARD_DETECTION = "card_detection"
    SYSTEM_ERROR = "system_error"
    DEVICE_OFFLINE = "device_offline"
    PERFORMANCE = "performance"


class AlarmResponse(BaseModel):
    """报警响应模型"""
    id: str
    type: AlarmType
    level: AlarmLevel
    title: str
    message: str
    source: str
    timestamp: str
    is_resolved: bool
    resolved_at: Optional[str] = None
    detection_group_id: Optional[int] = None
    metadata: Dict[str, Any] = {}


class AlarmStatistics(BaseModel):
    """报警统计模型"""
    total_alarms: int
    unresolved_alarms: int
    critical_alarms: int
    high_alarms: int
    medium_alarms: int
    low_alarms: int
    recent_24h: int
    recent_7d: int


class AlarmListResponse(BaseModel):
    """报警列表响应模型"""
    alarms: List[AlarmResponse]
    statistics: AlarmStatistics
    total: int
    page: int
    limit: int


def generate_card_detection_alarms(db: Session, hours: int = 24) -> List[AlarmResponse]:
    """基于卡料检测结果生成报警"""
    alarms = []
    
    # 获取最近指定小时内的异常检测结果
    since_time = datetime.now() - timedelta(hours=hours)
    abnormal_results = db.query(CardDetectionResult).filter(
        CardDetectionResult.is_normal == False,
        CardDetectionResult.timestamp >= since_time
    ).order_by(CardDetectionResult.timestamp.desc()).limit(100).all()
    
    for result in abnormal_results:
        # 获取检测组信息
        detection_group = db.query(DetectionGroup).filter(
            DetectionGroup.id == result.detection_group_id
        ).first()
        
        group_name = detection_group.name if detection_group else f"检测组{result.detection_group_id}"
        
        # 根据检测结果生成报警
        alarm_level = AlarmLevel.HIGH if len(result.undetected_rois or []) > 2 else AlarmLevel.MEDIUM
        
        alarm = AlarmResponse(
            id=f"card_detection_{result.id}",
            type=AlarmType.CARD_DETECTION,
            level=alarm_level,
            title="卡料检测异常",
            message=f"检测组 {group_name} 发现卡料异常，未检测到ROI数量: {len(result.undetected_rois or [])}",
            source=group_name,
            timestamp=result.timestamp.isoformat(),
            is_resolved=False,
            detection_group_id=result.detection_group_id,
            metadata={
                "undetected_rois": result.undetected_rois,
                "detected_rois": result.detected_rois,
                "trigger_roi_id": result.trigger_roi_id
            }
        )
        alarms.append(alarm)
    
    return alarms


def generate_system_alarms() -> List[AlarmResponse]:
    """生成系统相关报警"""
    alarms = []
    
    # 这里可以添加系统监控逻辑，比如CPU、内存使用率过高等
    # 目前返回示例报警
    import psutil
    
    try:
        cpu_usage = psutil.cpu_percent(interval=0.1)
        memory_usage = psutil.virtual_memory().percent
        
        # CPU使用率过高报警
        if cpu_usage > 80:
            alarms.append(AlarmResponse(
                id=f"system_cpu_{int(datetime.now().timestamp())}",
                type=AlarmType.PERFORMANCE,
                level=AlarmLevel.HIGH,
                title="CPU使用率过高",
                message=f"当前CPU使用率: {cpu_usage:.1f}%，超过80%阈值",
                source="系统监控",
                timestamp=datetime.now().isoformat(),
                is_resolved=False,
                metadata={"cpu_usage": cpu_usage}
            ))
        
        # 内存使用率过高报警
        if memory_usage > 85:
            alarms.append(AlarmResponse(
                id=f"system_memory_{int(datetime.now().timestamp())}",
                type=AlarmType.PERFORMANCE,
                level=AlarmLevel.HIGH,
                title="内存使用率过高",
                message=f"当前内存使用率: {memory_usage:.1f}%，超过85%阈值",
                source="系统监控",
                timestamp=datetime.now().isoformat(),
                is_resolved=False,
                metadata={"memory_usage": memory_usage}
            ))
    except Exception:
        pass
    
    return alarms


@router.get("/", response_model=AlarmListResponse)
def get_alarms(
    db: Session = Depends(deps.get_db),
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=100),
    level: Optional[AlarmLevel] = Query(None),
    type: Optional[AlarmType] = Query(None),
    resolved: Optional[bool] = Query(None),
    hours: int = Query(24, ge=1, le=168)  # 最近多少小时，最多7天
) -> Any:
    """
    获取报警列表
    """
    all_alarms = []
    
    # 生成卡料检测报警
    card_alarms = generate_card_detection_alarms(db, hours)
    all_alarms.extend(card_alarms)
    
    # 生成系统报警
    system_alarms = generate_system_alarms()
    all_alarms.extend(system_alarms)
    
    # 过滤报警
    filtered_alarms = all_alarms
    
    if level:
        filtered_alarms = [a for a in filtered_alarms if a.level == level]
    
    if type:
        filtered_alarms = [a for a in filtered_alarms if a.type == type]
    
    if resolved is not None:
        filtered_alarms = [a for a in filtered_alarms if a.is_resolved == resolved]
    
    # 按时间排序
    filtered_alarms.sort(key=lambda x: x.timestamp, reverse=True)
    
    # 分页
    total = len(filtered_alarms)
    start_idx = (page - 1) * limit
    end_idx = start_idx + limit
    paginated_alarms = filtered_alarms[start_idx:end_idx]
    
    # 统计信息
    statistics = AlarmStatistics(
        total_alarms=len(all_alarms),
        unresolved_alarms=len([a for a in all_alarms if not a.is_resolved]),
        critical_alarms=len([a for a in all_alarms if a.level == AlarmLevel.CRITICAL]),
        high_alarms=len([a for a in all_alarms if a.level == AlarmLevel.HIGH]),
        medium_alarms=len([a for a in all_alarms if a.level == AlarmLevel.MEDIUM]),
        low_alarms=len([a for a in all_alarms if a.level == AlarmLevel.LOW]),
        recent_24h=len([a for a in all_alarms if datetime.fromisoformat(a.timestamp.replace('Z', '+00:00').replace('+00:00', '')) > datetime.now() - timedelta(hours=24)]),
        recent_7d=len([a for a in all_alarms if datetime.fromisoformat(a.timestamp.replace('Z', '+00:00').replace('+00:00', '')) > datetime.now() - timedelta(days=7)])
    )
    
    return AlarmListResponse(
        alarms=paginated_alarms,
        statistics=statistics,
        total=total,
        page=page,
        limit=limit
    )


@router.get("/statistics", response_model=AlarmStatistics)
def get_alarm_statistics(
    db: Session = Depends(deps.get_db),
    hours: int = Query(24, ge=1, le=168)
) -> Any:
    """
    获取报警统计信息
    """
    all_alarms = []
    
    # 生成所有报警
    card_alarms = generate_card_detection_alarms(db, hours)
    all_alarms.extend(card_alarms)
    
    system_alarms = generate_system_alarms()
    all_alarms.extend(system_alarms)
    
    # 计算统计信息
    return AlarmStatistics(
        total_alarms=len(all_alarms),
        unresolved_alarms=len([a for a in all_alarms if not a.is_resolved]),
        critical_alarms=len([a for a in all_alarms if a.level == AlarmLevel.CRITICAL]),
        high_alarms=len([a for a in all_alarms if a.level == AlarmLevel.HIGH]),
        medium_alarms=len([a for a in all_alarms if a.level == AlarmLevel.MEDIUM]),
        low_alarms=len([a for a in all_alarms if a.level == AlarmLevel.LOW]),
        recent_24h=len([a for a in all_alarms if datetime.fromisoformat(a.timestamp.replace('Z', '+00:00').replace('+00:00', '')) > datetime.now() - timedelta(hours=24)]),
        recent_7d=len([a for a in all_alarms if datetime.fromisoformat(a.timestamp.replace('Z', '+00:00').replace('+00:00', '')) > datetime.now() - timedelta(days=7)])
    )
<template>
  <div class="global-settings-container">
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <h3>全局检测参数设置</h3>
        </div>
      </template>
      <div class="settings-content">
        <el-form label-position="top" :model="settings" class="settings-form">

          <el-form-item label="延时时间 (秒)" class="form-item-responsive">
            <el-tooltip
              content="检测开始前的延时时间"
              placement="top"
            >
              <el-input-number
                v-model="settings.delayTime"
                :min="0"
                :max="30"
                :step="1"
                :controls-position="'right'"
                class="input-number-responsive"
                @change="handleSettingsChange"
              />
            </el-tooltip>
          </el-form-item>
          
          <el-form-item label="设备暂停阈值 (秒)" class="form-item-responsive">
            <el-tooltip
              content="无压铸机运动后系统进入暂停状态的时间"
              placement="top"
            >
              <el-input-number
                v-model="settings.pauseThreshold"
                :min="5"
                :max="60"
                :step="5"
                :controls-position="'right'"
                class="input-number-responsive"
                @change="handleSettingsChange"
              />
            </el-tooltip>
          </el-form-item>
          
          <el-form-item label="检测冷却时间 (秒)" class="form-item-responsive">
            <el-tooltip
              content="检测完成后系统冷却状态的持续时间"
              placement="top"
            >
              <el-input-number
                v-model="settings.cooldownTime"
                :min="1"
                :max="30"
                :step="1"
                :controls-position="'right'"
                class="input-number-responsive"
                @change="handleSettingsChange"
              />
            </el-tooltip>
          </el-form-item>
        </el-form>
        
        <div class="settings-actions">
          <el-button type="primary" @click="applySettings" class="action-button">应用设置</el-button>
          <el-button @click="resetSettings" class="action-button">重置</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineEmits, defineProps, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 定义props
const props = defineProps<{
  settings: {
    delayTime: number
    pauseThreshold: number
    cooldownTime: number
  }
}>()

const emit = defineEmits(['update:settings', 'apply-settings'])

// 默认设置
const defaultSettings = {
  delayTime: 5,       // 延时时间（秒）
  pauseThreshold: 15, // 设备暂停阈值（秒）
  cooldownTime: 3     // 检测冷却时间（秒）
}

// 当前设置 - 使用props初始化
const settings = reactive({...props.settings})

// 是否有未保存的更改
const hasChanges = ref(false)

// 监听props变化，同步到本地settings
watch(() => props.settings, (newSettings) => {
  Object.assign(settings, newSettings)
  hasChanges.value = false
}, { deep: true })

// 处理设置变更
const handleSettingsChange = () => {
  hasChanges.value = true
  // 实时发送设置更新
  emit('update:settings', {...settings})
}

// 应用设置
const applySettings = () => {
  emit('update:settings', {...settings})
  emit('apply-settings', {...settings})
  hasChanges.value = false
  ElMessage.success('设置已应用')
}

// 重置设置
const resetSettings = () => {
  Object.assign(settings, defaultSettings)
  emit('update:settings', {...settings})
  emit('apply-settings', {...settings})
  hasChanges.value = false
  ElMessage.info('设置已重置')
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化时发送一次设置
  emit('apply-settings', {...settings})
})
</script>

<style scoped>
.global-settings-container {
  margin-bottom: 12px;
  width: 100%;
}

.settings-card {
  border-radius: 6px;
  width: 100%;
}

.settings-card :deep(.el-card__header) {
  padding: 12px 16px;
}

.settings-card :deep(.el-card__body) {
  padding: 12px 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.settings-content {
  width: 100%;
}

.settings-form {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: flex-end;
  flex-wrap: wrap;
  width: 100%;
}

.settings-form :deep(.el-form-item) {
  margin-bottom: 8px;
}

.settings-form :deep(.el-form-item__label) {
  font-size: 12px;
  line-height: 1.2;
  margin-bottom: 4px;
}

.form-item-responsive {
  flex: 1;
  min-width: 160px;
  max-width: 100%;
}

.input-number-responsive {
  width: 100%;
  min-width: 100px;
}

.input-number-responsive :deep(.el-input__inner) {
  height: 28px;
  line-height: 28px;
  font-size: 12px;
}

.input-number-responsive :deep(.el-input-number__increase),
.input-number-responsive :deep(.el-input-number__decrease) {
  height: 14px;
  line-height: 14px;
}

.settings-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
  flex-wrap: wrap;
}

.action-button {
  min-width: 60px;
  height: 28px;
  font-size: 12px;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-item-responsive {
    min-width: 160px;
  }
  
  .input-number-responsive {
    min-width: 110px;
  }
}

@media (max-width: 992px) {
  .settings-form {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .form-item-responsive {
    flex: none;
    min-width: auto;
    max-width: none;
  }
  
  .input-number-responsive {
    width: 100%;
    min-width: 100px;
  }
}

@media (max-width: 768px) {
  .global-settings-container {
    margin-bottom: 15px;
  }
  
  .card-header h3 {
    font-size: 14px;
  }
  
  .settings-actions {
    justify-content: center;
    margin-top: 12px;
  }
  
  .action-button {
    flex: 1;
    max-width: 120px;
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  .settings-form {
    gap: 10px;
  }
  
  .settings-actions {
    flex-direction: column;
    gap: 8px;
    margin-top: 10px;
  }
  
  .action-button {
    max-width: none;
    min-width: auto;
    width: 100%;
  }
  
  .input-number-responsive {
    min-width: 90px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .card-header h3 {
    font-size: 13px;
  }
  
  .settings-form {
    gap: 8px;
  }
  
  .input-number-responsive {
    min-width: 80px;
  }
}
</style>
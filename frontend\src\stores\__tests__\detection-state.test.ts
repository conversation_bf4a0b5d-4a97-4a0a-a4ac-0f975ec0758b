import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useDetectionStateStore } from '../detection-state'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

// Mock window.open
Object.defineProperty(window, 'open', {
  writable: true,
  value: vi.fn(() => ({
    focus: vi.fn(),
    close: vi.fn(),
    closed: false
  }))
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
})

describe('useDetectionStateStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  it('should initialize with no running detection', () => {
    const store = useDetectionStateStore()
    
    expect(store.isDetectionRunning).toBe(false)
    expect(store.currentRunningDetection).toBeNull()
    expect(store.currentRunningTemplateId).toBeNull()
  })

  it('should start detection successfully', async () => {
    const store = useDetectionStateStore()
    
    const result = await store.startDetection('template-1', 'Test Template')
    
    expect(result).toBe(true)
    expect(store.isDetectionRunning).toBe(true)
    expect(store.currentRunningDetection?.templateId).toBe('template-1')
    expect(store.currentRunningDetection?.templateName).toBe('Test Template')
    expect(localStorageMock.setItem).toHaveBeenCalled()
  })

  it('should prevent starting multiple detections', async () => {
    const store = useDetectionStateStore()
    
    // Start first detection
    await store.startDetection('template-1', 'Test Template 1')
    
    // Try to start second detection
    const result = await store.startDetection('template-2', 'Test Template 2')
    
    expect(result).toBe(false)
    expect(store.currentRunningDetection?.templateId).toBe('template-1')
  })

  it('should stop detection successfully', async () => {
    const store = useDetectionStateStore()
    
    // Start detection first
    await store.startDetection('template-1', 'Test Template')
    
    // Stop detection
    const result = await store.stopDetection()
    
    expect(result).toBe(true)
    expect(store.isDetectionRunning).toBe(false)
    expect(store.currentRunningDetection).toBeNull()
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('currentRunningDetection')
  })

  it('should check if template can start', () => {
    const store = useDetectionStateStore()
    
    // No detection running - should allow any template
    expect(store.canStartTemplate('template-1')).toBe(true)
    expect(store.canStartTemplate('template-2')).toBe(true)
  })

  it('should restore detection state from localStorage', () => {
    const savedState = {
      templateId: 'template-1',
      templateName: 'Test Template',
      startTime: new Date().toISOString(),
      status: 'running'
    }
    
    localStorageMock.getItem.mockReturnValue(JSON.stringify(savedState))
    
    const store = useDetectionStateStore()
    
    expect(store.currentRunningDetection?.templateId).toBe('template-1')
    expect(store.currentRunningDetection?.templateName).toBe('Test Template')
    expect(store.isDetectionRunning).toBe(true)
  })

  it('should clear expired state from localStorage', () => {
    const expiredState = {
      templateId: 'template-1',
      templateName: 'Test Template',
      startTime: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString(), // 25 hours ago
      status: 'running'
    }
    
    localStorageMock.getItem.mockReturnValue(JSON.stringify(expiredState))
    
    const store = useDetectionStateStore()
    
    expect(store.currentRunningDetection).toBeNull()
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('currentRunningDetection')
  })
})

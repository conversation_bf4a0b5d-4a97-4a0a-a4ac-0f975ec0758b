<template>
  <div class="test-page">
    <h1>检测看板测试页面</h1>
    
    <div class="test-buttons">
      <el-button type="primary" @click="openDetectionMode">
        打开检测模式页面
      </el-button>
      
      <el-button type="success" @click="openDashboard">
        打开检测看板
      </el-button>
    </div>
    
    <div class="test-urls">
      <h3>测试URL:</h3>
      <p>检测模式: <code>{{ detectionModeUrl }}</code></p>
      <p>检测看板: <code>{{ dashboardUrl }}</code></p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const detectionModeUrl = computed(() => {
  return '/video-preview-cur_dection_JIANCE?detectionMode=true&templateId=1&detectionGroupId=1'
})

const dashboardUrl = computed(() => {
  return '/detection-dashboard'
})

const openDetectionMode = () => {
  window.open(detectionModeUrl.value, '_blank')
}

const openDashboard = () => {
  router.push('/detection-dashboard')
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-buttons {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.test-urls {
  margin-top: 30px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.test-urls code {
  background: #e6f7ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: monospace;
}
</style>
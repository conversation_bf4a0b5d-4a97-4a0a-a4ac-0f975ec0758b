from typing import Dict, Any
import json
from datetime import datetime

from app.services.websocket_manager import WebSocketConnectionManager
from .frame_processor import FrameProcessor

class MessageHandler:
    """WebSocket消息处理器"""
    
    def __init__(self, connection_manager: WebSocketConnectionManager, detection_instances: Dict[str, Any]):
        self.connection_manager = connection_manager
        self.detection_instances = detection_instances
        self.frame_processor = FrameProcessor(connection_manager, detection_instances)
    
    async def process_detection_message(self, message: Dict[str, Any], websocket, client_id: str):
        """处理检测相关的消息"""
        message_type = message.get("type")

        if message_type == "frame":
            # 处理传统视频帧
            await self.frame_processor.process_detection_frame(message, websocket, client_id)
        
        elif message_type == "roi_frames":
            # 处理ROI截图数据
            await self.frame_processor.process_detection_frame(message, websocket, client_id)
        
        elif message_type == "roi_detection_control":
            # ROI检测控制命令
            await self.handle_roi_detection_control(websocket, client_id, message)

        elif message_type == "reset_stuck_detection":
            # 重置卡料检测状态
            if client_id in self.detection_instances:
                detection_manager = self.detection_instances[client_id].get("detection_manager")
                # 检测状态重置功能已移除

        elif message_type == "update_config":
            # 更新检测配置
            await self.handle_update_config(message, websocket, client_id)
        
        elif message_type == "ping":
            # 心跳检测
            await self.connection_manager.send_personal_message(
                json.dumps({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }),
                websocket
            )
    
    async def handle_update_config(self, message: Dict[str, Any], websocket, client_id: str):
        """处理配置更新"""
        config = message.get("config", {})

        if client_id in self.detection_instances:
            detection_instance = self.detection_instances[client_id]
            detection_manager = detection_instance["detection_manager"]

            # 更新配置
            if "detection_mode" in config:
                detection_instance["mode"] = config["detection_mode"]

            # 更新检测算法
            if "detection_algorithm" in config:
                detection_instance["detection_algorithm"] = config["detection_algorithm"]
            
            # 更新检测器参数
            if "motion_params" in config:
                motion_detector = detection_instance["motion_detector"]
                motion_detector.update_params(config["motion_params"])

            # 更新帧差法参数
            if "frame_difference_params" in config:
                frame_differencer = detection_instance["frame_differencer"]
                frame_differencer.update_params(config["frame_difference_params"])

            # 更新方向检测器参数
            if "direction_params" in config:
                direction_detector = detection_instance["direction_detector"]
                direction_detector.update_params(config["direction_params"])
            
            # 更新ROI检测器配置
            if "roi_detectors" in config:
                await self.update_roi_detectors(config["roi_detectors"], detection_instance, detection_manager, client_id)
            
            # 更新全局设置（支持动态更新）
            if "global_settings" in config:
                old_settings = detection_manager.get_global_settings()
                detection_manager.update_global_settings(config["global_settings"])
                new_settings = detection_manager.get_global_settings()

                # 卡料检测器参数更新功能已移除

            # 请求获取全局设置
            if config.get("request_global_settings", False):
                global_settings = detection_manager.get_global_settings()
                await self.connection_manager.send_personal_message(
                    json.dumps({
                        "type": "global_settings",
                        "settings": global_settings
                    }),
                    websocket
                )
            
            await self.connection_manager.send_personal_message(
                json.dumps({
                    "type": "config_updated",
                    "message": "Configuration updated"
                }),
                websocket
            )
    
    async def update_roi_detectors(self, roi_detectors_config: Dict[str, Any], detection_instance: Dict[str, Any], detection_manager, client_id: str):
        """更新ROI检测器配置"""
        for roi_id, detector_config in roi_detectors_config.items():
            detector_type = detector_config.get("type", "background_subtraction")

            # 根据检测器类型准备参数
            detector_params = {}

            if detector_type == "direction":
                # 方向检测器参数 - 处理前端发送的参数格式
                # 前端可能发送两种格式：标准化格式或原始格式
                if 'motion_params' in detector_config:
                    # 标准化格式（前端setRoiDetector处理后的格式）
                    motion_params = detector_config.get('motion_params', {})

                    detector_params = {
                        'detector_type': detector_config.get('detector_type', 'background_subtraction'),
                        'motion_params': motion_params,
                        'minDisplacement': detector_config.get('minDisplacement', 2),
                        'maxPatience': detector_config.get('maxPatience', 3),
                        'consecutiveThreshold': detector_config.get('consecutiveThreshold', 3)
                    }
                else:
                    # 原始格式（支持新格式和旧格式）
                    # 新格式
                    motion_detection = detector_config.get('motion_detection', {})
                    direction_detection = detector_config.get('direction_detection', {})

                    # 旧格式兼容
                    if not motion_detection:
                        前置背景检测 = detector_config.get('前置背景检测', {})
                        motion_detection = {
                            'enabled': 前置背景检测.get('enabled', True),
                            'minArea': 前置背景检测.get('minArea', 500),
                            'motionThreshold': 前置背景检测.get('motionThreshold', 50),
                            'backgroundUpdateRate': 前置背景检测.get('backgroundUpdateRate', 0.01)
                        }

                    if not direction_detection:
                        后置方向检测 = detector_config.get('后置方向检测', {})
                        direction_detection = {
                            'minDisplacement': 后置方向检测.get('minDisplacement', 2),
                            'maxPatience': 后置方向检测.get('maxPatience', 3),
                            'consecutiveDetectionThreshold': 后置方向检测.get('consecutiveDetectionThreshold', 3)
                        }

                    detector_params = {
                        'detector_type': 'background_subtraction',
                        'motion_params': {
                            'enabled': motion_detection.get('enabled', True),
                            'minArea': motion_detection.get('minArea', 500),
                            'detectionThreshold': motion_detection.get('motionThreshold', 50),
                            'learningRate': motion_detection.get('backgroundUpdateRate', 0.01),
                            'shadowsThreshold': 0.5
                        },
                        'minDisplacement': direction_detection.get('minDisplacement', 2),
                        'maxPatience': direction_detection.get('maxPatience', 3),
                        'consecutiveThreshold': direction_detection.get('consecutiveDetectionThreshold', 3)
                    }

                # 使用方向检测器
                roi_detector = detection_manager.get_detector(
                    f"{client_id}_roi_{roi_id}",
                    "direction",
                    detector_params
                )

            elif detector_type == "frame_difference":
                # 帧差法参数 - 支持新格式和旧格式
                motion_detection = detector_config.get('motion_detection', {})
                运动检测 = detector_config.get('运动检测', {})

                detector_params = {
                    'minArea': motion_detection.get('minArea') or 运动检测.get('minArea') or detector_config.get('minArea', 100),
                    'threshold': motion_detection.get('threshold') or 运动检测.get('threshold') or detector_config.get('threshold', 30),
                    'frameInterval': motion_detection.get('frameInterval') or 运动检测.get('frameInterval') or detector_config.get('frameInterval', 2)
                }

                # 使用帧差检测器
                roi_detector = detection_manager.get_detector(
                    f"{client_id}_roi_{roi_id}",
                    "frame_difference",
                    detector_params
                )

            else:
                # 背景减除法参数 - 支持新格式和旧格式
                motion_detection = detector_config.get('motion_detection', {})
                运动检测 = detector_config.get('运动检测', {})

                detector_params = {
                    'minArea': motion_detection.get('minArea') or 运动检测.get('minArea') or detector_config.get('minArea', 100),
                    'detectionThreshold': motion_detection.get('detectionThreshold') or 运动检测.get('detectionThreshold') or detector_config.get('detectionThreshold', 40),
                    'learningRate': motion_detection.get('learningRate') or 运动检测.get('learningRate') or detector_config.get('learningRate', 0.005),
                    'shadowsThreshold': motion_detection.get('shadowRemoval') or 运动检测.get('shadowRemoval') or detector_config.get('shadowsThreshold', 0.5)
                }

                # 使用运动检测器
                roi_detector = detection_manager.get_detector(
                    f"{client_id}_roi_{roi_id}",
                    "motion",
                    detector_params
                )

            # 保存到ROI检测器映射
            detection_instance["roi_detectors"][roi_id] = roi_detector
    
    async def handle_roi_detection_control(self, websocket, client_id: str, message: dict):
        """
        处理ROI检测控制命令
        """
        try:
            command = message.get("command", {})
            action = command.get("action", "")
            roi_ids = command.get("roi_ids", [])
            reason = command.get("reason", "")

            if client_id not in self.detection_instances:
                return

            detection_instance = self.detection_instances[client_id]

            # 初始化ROI控制状态（如果不存在）
            if "roi_control_states" not in detection_instance:
                detection_instance["roi_control_states"] = {}

            roi_control_states = detection_instance["roi_control_states"]

            # 处理不同的控制命令
            if action == "start":
                for roi_id in roi_ids:
                    roi_control_states[roi_id] = {
                        "is_active": True,
                        "activated_at": datetime.now().isoformat(),
                        "reason": reason
                    }

            elif action == "stop":
                for roi_id in roi_ids:
                    roi_control_states[roi_id] = {
                        "is_active": False,
                        "deactivated_at": datetime.now().isoformat(),
                        "reason": reason
                    }

            elif action == "start_batch":
                for roi_id in roi_ids:
                    roi_control_states[roi_id] = {
                        "is_active": True,
                        "activated_at": datetime.now().isoformat(),
                        "reason": reason
                    }

            elif action == "stop_batch":
                for roi_id in roi_ids:
                    roi_control_states[roi_id] = {
                        "is_active": False,
                        "deactivated_at": datetime.now().isoformat(),
                        "reason": reason
                    }

            # 发送确认消息
            await self.connection_manager.send_personal_message(
                json.dumps({
                    "type": "roi_control_response",
                    "success": True,
                    "action": action,
                    "roi_ids": roi_ids,
                    "message": f"ROI检测控制命令执行成功: {action}"
                }),
                websocket
            )

        except Exception as e:
            await self.connection_manager.send_personal_message(
                json.dumps({
                    "type": "roi_control_response",
                    "success": False,
                    "error": str(e),
                    "message": "ROI检测控制命令执行失败"
                }),
                websocket
            )
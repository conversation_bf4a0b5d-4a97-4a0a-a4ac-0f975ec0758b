import { ref, computed, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';

// 定义类型
export interface AlgorithmLog {
  timestamp: number;
  roi_id: string;
  roi_name?: string;
  attribute?: string;
  type: 'motion' | 'direction' | string;
  is_pre_motion?: boolean;
  enabled?: boolean;
  input_params: Record<string, any>;
  results: Record<string, any>;
}

export interface ROI {
  roi_id: string;
  name?: string;
  attribute?: string;
  [key: string]: any;
}

/**
 * 算法调试日志功能
 */
export function useAlgorithmDebugLog() {
  // 日志数据
  const logs = ref<AlgorithmLog[]>([]);
  const filterType = ref('all');
  const selectedRoiId = ref('');
  const autoScroll = ref(true);
  const logContainer = ref<HTMLDivElement | null>(null);

  // 过滤后的日志
  const filteredLogs = computed(() => {
    return logs.value
      .filter(log => {
        // 根据算法类型过滤
        if (filterType.value !== 'all' && log.type !== filterType.value) {
          return false;
        }
        
        // 根据ROI ID过滤
        if (selectedRoiId.value && log.roi_id !== selectedRoiId.value) {
          return false;
        }
        
        return true;
      })
      .sort((a, b) => b.timestamp - a.timestamp); // 时间倒序排列
  });

  // 格式化时间
  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}.${date.getMilliseconds().toString().padStart(3, '0')}`;
  };

  // 格式化参数显示
  const formatParams = (params: Record<string, any> | null | undefined): string => {
    if (!params) return '无参数';
    try {
      return JSON.stringify(params, null, 2);
    } catch (e) {
      return String(params);
    }
  };

  // 获取算法名称
  const getAlgorithmName = (log: AlgorithmLog): string => {
    if (!log) return '未知算法';
    
    if (log.type === 'motion') {
      const algorithm = log.input_params?.algorithm || log.results?.algorithm;
      if (algorithm === 'background_subtraction') {
        return '运动检测 - 背景去除法';
      } else if (algorithm === 'frame_difference') {
        return '运动检测 - 帧差法';
      } else {
        return '运动检测 - ' + (algorithm || '未知');
      }
    } else if (log.type === 'direction') {
      if (log.is_pre_motion === true) {
        return '方向检测 - 前置运动检测';
      } else {
        return '方向检测 - 方向算法';
      }
    }
    
    return log.type || '未知算法';
  };

  // 添加日志
  const addLog = (logData: Partial<AlgorithmLog>): void => {
    // 确保日志数据有timestamp
    if (!logData.timestamp) {
      logData.timestamp = Date.now();
    }
    
    logs.value.push(logData as AlgorithmLog);
    
    // 限制日志最大数量
    if (logs.value.length > 500) {
      logs.value = logs.value.slice(logs.value.length - 500);
    }
    
    // 如果开启自动滚动，滚动到底部
    if (autoScroll.value) {
      nextTick(() => {
        scrollToBottom();
      });
    }
  };

  // 清空日志
  const clearLogs = (): void => {
    logs.value = [];
    ElMessage.success('日志已清空');
  };

  // 导出日志
  const exportLogs = (): void => {
    try {
      const data = JSON.stringify(logs.value, null, 2);
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `algorithm-logs-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      ElMessage.success('日志导出成功');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      ElMessage.error(`日志导出失败: ${errorMessage}`);
    }
  };

  // 滚动到底部
  const scrollToBottom = (): void => {
    if (logContainer.value) {
      logContainer.value.scrollTop = logContainer.value.scrollHeight;
    }
  };

  // 监听过滤条件变化
  watch([filterType, selectedRoiId], () => {
    if (autoScroll.value) {
      nextTick(() => {
        scrollToBottom();
      });
    }
  });

  return {
    logs,
    filteredLogs,
    filterType,
    selectedRoiId,
    autoScroll,
    logContainer,
    addLog,
    clearLogs,
    exportLogs,
    formatTime,
    formatParams,
    getAlgorithmName,
    scrollToBottom
  };
} 
import { createRouter, createWebHashHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import MainLayout from '@/layouts/MainLayout.vue'
import DetectionDashboardLayout from '@/layouts/DetectionDashboardLayout.vue'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/LoginView.vue'),
      meta: {
        title: '登录',
        requiresAuth: false
      }
    },
    {
      path: '/',
      component: MainLayout,
      meta: {
        requiresAuth: true
      },
      children: [
        {
          path: '',
          redirect: '/data-dashboard'
        },
        {
          path: 'data-dashboard',
          name: 'data-dashboard',
          component: () => import('@/views/data-dashboard/DataDashboard.vue'),
          meta: {
            title: '数据看板',
            icon: 'data-board'
          }
        },
        {
          path: 'monitoring-dashboard',
          name: 'monitoring-dashboard',
          component: () => import('@/views/dashboard/simple.vue'),
          meta: {
            title: '监控看板',
            icon: 'data-analysis'
          }
        },
        {
          path: 'video-preview',
          name: 'video-preview',
          component: () => import('@/views/VideoPreviewView.vue'),
          meta: {
            title: '视频预览(原版)',
            icon: 'video-play'
          }
        },

        {
          path: 'video-preview-cur',
          name: 'video-preview-cur',
          component: () => import('@/views/video-preview-cur/index.vue'),
          meta: {
            title: '视频预览(新版)',
            icon: 'video-camera'
          }
        },



        {
          path: 'detection-config',
          name: 'detection-config',
          component: () => import('@/views/detection-config/index.vue'),
          meta: {
            title: '检测配置',
            icon: 'setting'
          }
        },

        {
          path: 'preset-schedule',
          name: 'preset-schedule',
          component: () => import('@/views/preset-schedule/PresetScheduleManager.vue'),
          meta: {
            title: '预设检测计划',
            icon: 'clock'
          }
        },

        {
          path: 'device-management',
          name: 'device-management',
          component: () => import('@/views/DeviceManagementView.vue'),
          meta: {
            title: '设备管理',
            icon: 'cpu'
          }
        },

        {
          path: 'system-settings',
          name: 'system-settings',
          component: () => import('@/views/SystemSettingsView.vue'),
          meta: {
            title: '系统设置',
            icon: 'tools'
          }
        },
        {
          path: 'database',
          name: 'database',
          component: () => import('@/views/DatabaseView.vue'),
          meta: {
            title: '数据库管理',
            icon: 'database'
          }
        },
        {
          path: 'video-test',
          name: 'video-test',
          component: () => import('@/views/VideoTestView.vue'),
          meta: {
            title: '视频测试',
            icon: 'video-play'
          }
        },
        {
          path: 'roi-config',
          name: 'roi-config',
          component: () => import('@/views/ROIConfigView.vue'),
          meta: {
            title: 'ROI配置',
            icon: 'aim'
          }
        },
        {
          path: 'profile',
          name: 'profile',
          component: () => import('@/views/ProfileView.vue'),
          meta: {
            title: '个人信息',
            icon: 'user'
          }
        },
        {
          path: 'admin',
          name: 'admin',
          component: () => import('@/views/AdminView.vue'),
          meta: {
            title: '后端管理',
            icon: 'setting'
          }
        },

        {
          path: 'detection-test',
          name: 'detection-test',
          component: () => import('@/views/detection-dashboard/test.vue'),
          meta: {
            title: '检测功能测试',
            icon: 'test'
          }
        }
      ]
    },
    {
      path: '/detection-dashboard',
      component: DetectionDashboardLayout,
      meta: {
        requiresAuth: false
      },
      children: [
        {
          path: '',
          name: 'detection-dashboard',
          component: () => import('@/views/detection-dashboard/index.vue'),
          meta: {
            title: '检测看板监测',
            requiresAuth: false
          }
        }
      ]
    },
    {
      path: '/video-preview-detection',
      name: 'video-preview-detection',
      component: () => import('@/views/video-preview-cur_dection/index.vue'),
      meta: {
        title: 'ROI配置',
        requiresAuth: false
      }
    },
    {
      path: '/video-preview-cur_dection_JIANCE',
      name: 'video-preview-cur_dection_JIANCE',
      component: () => import('@/views/video-preview-cur_dection_JIANCE/index.vue'),
      meta: {
        title: '检测模板预览',
        requiresAuth: false
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('@/views/NotFoundView.vue'),
      meta: {
        title: '页面不存在',
        requiresAuth: false
      }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title || '压铸件智能检测系统'}`
  
  // 如果是检测相关页面，直接放行，不进行任何权限检查
  if (to.name === 'video-preview-detection' || to.name === 'video-preview-cur_dection_JIANCE' || to.name === 'detection-dashboard') {
    next()
    return
  }
  
  // 简化版权限检查，暂不实现复杂权限管理
  const userStore = useUserStore()
  
  // 如果需要认证但用户未登录，重定向到登录页
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next({ name: 'login', query: { redirect: to.fullPath } })
  } else {
    next()
  }
})

export default router

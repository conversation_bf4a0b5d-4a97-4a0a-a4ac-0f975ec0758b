#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试创建预设计划API的500错误
"""

import requests
import json
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def get_auth_token():
    """获取认证token"""
    try:
        login_data = {
            "username": "admin",
            "password": "123456"
        }
        response = requests.post(f"{API_BASE}/auth/login/json/", json=login_data)
        if response.status_code == 200:
            token = response.json().get("access_token")
            print("✅ 认证成功")
            return token
        else:
            print(f"❌ 认证失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 认证异常: {e}")
        return None

def test_detection_templates(headers):
    """测试检测模板API"""
    print("\n检查可用的检测模板:")
    try:
        response = requests.get(f"{API_BASE}/detection-templates/", headers=headers)
        print(f"GET /detection-templates/ 状态码: {response.status_code}")
        if response.status_code == 200:
            templates = response.json()
            print(f"✅ 获取到 {len(templates)} 个检测模板")
            for template in templates:
                print(f"  - ID: {template.get('id')}, 名称: {template.get('name')}")
            return templates
        else:
            print(f"❌ 获取检测模板失败: {response.text}")
            return []
    except Exception as e:
        print(f"❌ 获取检测模板异常: {e}")
        return []

def test_create_with_different_data(headers, templates):
    """测试不同的创建数据格式"""
    print("\n测试不同的创建数据格式:")
    
    # 获取第一个可用的模板ID
    template_id = 1
    if templates:
        template_id = templates[0].get('id', 1)
    
    test_cases = [
        {
            "name": "最小数据集",
            "data": {
                "name": f"测试计划_{datetime.now().strftime('%H%M%S')}",
                "detectionTemplateId": template_id,
                "scheduleType": "daily",
                "scheduleTime": "09:00",
                "isEnabled": True
            }
        },
        {
            "name": "完整数据集",
            "data": {
                "name": f"完整测试计划_{datetime.now().strftime('%H%M%S')}",
                "description": "API修复验证测试",
                "detectionTemplateId": template_id,
                "scheduleType": "daily",
                "scheduleTime": "09:00",
                "isEnabled": True,
                "scheduleDays": [1, 2, 3, 4, 5],  # 工作日
                "scheduleDate": None
            }
        },
        {
            "name": "字符串模板ID",
            "data": {
                "name": f"字符串ID测试_{datetime.now().strftime('%H%M%S')}",
                "detectionTemplateId": str(template_id),
                "scheduleType": "daily",
                "scheduleTime": "09:00",
                "isEnabled": True
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试 {test_case['name']}:")
        print(f"数据: {json.dumps(test_case['data'], indent=2, ensure_ascii=False)}")
        
        try:
            response = requests.post(f"{API_BASE}/preset-schedules/", headers=headers, json=test_case['data'])
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 创建成功")
                result = response.json()
                print(f"创建的计划ID: {result.get('id')}")
                return result
            else:
                print(f"❌ 创建失败: {response.text}")
                
                # 尝试解析错误详情
                try:
                    error_detail = response.json()
                    print(f"错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
                except:
                    pass
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        print("-" * 50)
    
    return None

def main():
    print("调试创建预设计划API的500错误...\n")
    
    # 1. 获取认证token
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token，停止测试")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 2. 检查检测模板
    templates = test_detection_templates(headers)
    
    # 3. 测试不同的创建数据格式
    result = test_create_with_different_data(headers, templates)
    
    print("\n" + "="*60)
    print("调试总结:")
    print("="*60)
    if result:
        print("✅ 找到有效的创建数据格式")
        print("✅ 创建预设计划API正常工作")
    else:
        print("❌ 所有创建数据格式都失败")
        print("❌ 需要检查后端创建逻辑或数据库配置")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
ROI参数格式迁移脚本
将现有的中文键名参数格式迁移为英文键名格式
"""

import sqlite3
import json
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def convert_yazhu_params(old_params):
    """转换yazhu类型ROI参数为新格式"""
    if not old_params:
        return {
            "type": "direction",
            "motion_detection": {
                "enabled": True,
                "backgroundUpdateRate": 0.01,
                "motionThreshold": 50,
                "minArea": 500
            },
            "direction_detection": {
                "consecutiveDetectionThreshold": 3,
                "minDisplacement": 2,
                "maxPatience": 3
            }
        }
    
    # 如果已经是新格式，直接返回
    if 'motion_detection' in old_params and 'direction_detection' in old_params:
        return old_params
    
    # 处理旧格式
    motion_detection = {}
    direction_detection = {}
    
    if '前置背景检测' in old_params:
        front_params = old_params['前置背景检测']
        motion_detection = {
            "enabled": front_params.get('enabled', True),
            "backgroundUpdateRate": front_params.get('backgroundUpdateRate', 0.01),
            "motionThreshold": front_params.get('motionThreshold', 50),
            "minArea": front_params.get('minArea', 500)
        }
    
    if '后置方向检测' in old_params:
        back_params = old_params['后置方向检测']
        direction_detection = {
            "consecutiveDetectionThreshold": back_params.get('consecutiveDetectionThreshold', 3),
            "minDisplacement": back_params.get('minDisplacement', 2),
            "maxPatience": back_params.get('maxPatience', 3)
        }
    
    # 处理扁平化格式
    if not motion_detection:
        motion_detection = {
            "enabled": old_params.get('enabled', True),
            "backgroundUpdateRate": old_params.get('backgroundUpdateRate', 0.01),
            "motionThreshold": old_params.get('motionThreshold', 50),
            "minArea": old_params.get('minArea', 500)
        }
    
    if not direction_detection:
        direction_detection = {
            "consecutiveDetectionThreshold": old_params.get('consecutiveDetectionThreshold', 3),
            "minDisplacement": old_params.get('minDisplacement', 2),
            "maxPatience": old_params.get('maxPatience', 3)
        }
    
    return {
        "type": "direction",
        "motion_detection": motion_detection,
        "direction_detection": direction_detection
    }

def convert_pailiao_params(old_params):
    """转换pailiao类型ROI参数为新格式"""
    if not old_params:
        return {
            "type": "motion",
            "motion_detection": {
                "algorithm": "frame_difference",
                "learningRate": 0.01,
                "detectionThreshold": 50,
                "shadowRemoval": 0.5,
                "threshold": 30,
                "frameInterval": 2,
                "minArea": 300
            }
        }
    
    # 如果已经是新格式，直接返回
    if 'motion_detection' in old_params:
        return old_params
    
    # 处理旧格式
    motion_detection = {}
    
    if '运动检测' in old_params:
        motion_params = old_params['运动检测']
        algorithm = motion_params.get('algorithm', '帧差法')
        motion_detection = {
            "algorithm": "background_subtraction" if algorithm == '背景减除法' else "frame_difference",
            "learningRate": motion_params.get('learningRate', 0.01),
            "detectionThreshold": motion_params.get('detectionThreshold', 50),
            "shadowRemoval": motion_params.get('shadowRemoval', 0.5),
            "threshold": motion_params.get('threshold', 30),
            "frameInterval": motion_params.get('frameInterval', 2),
            "minArea": motion_params.get('minArea', 300)
        }
    else:
        # 处理扁平化格式
        algorithm = old_params.get('algorithm', '帧差法')
        motion_detection = {
            "algorithm": "background_subtraction" if algorithm == '背景减除法' else "frame_difference",
            "learningRate": old_params.get('learningRate', 0.01),
            "detectionThreshold": old_params.get('detectionThreshold', 50),
            "shadowRemoval": old_params.get('shadowRemoval', 0.5),
            "threshold": old_params.get('threshold', 30),
            "frameInterval": old_params.get('frameInterval', 2),
            "minArea": old_params.get('minArea', 300)
        }
    
    return {
        "type": "motion",
        "motion_detection": motion_detection
    }

def migrate_roi_params(db_path):
    """执行ROI参数迁移"""
    logger.info(f"开始迁移ROI参数，数据库路径: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 查询所有ROI配置
        cursor.execute("SELECT id, roi_id, roi_type, algorithm_params FROM roi_configs")
        rows = cursor.fetchall()
        
        updated_count = 0
        
        for row in rows:
            config_id, roi_id, roi_type, algorithm_params_str = row
            
            if not algorithm_params_str:
                logger.info(f"跳过ROI {roi_id}，无参数数据")
                continue
            
            try:
                old_params = json.loads(algorithm_params_str)
                
                # 根据ROI类型转换参数
                if roi_type == 'yazhu':
                    new_params = convert_yazhu_params(old_params)
                elif roi_type == 'pailiao':
                    new_params = convert_pailiao_params(old_params)
                else:
                    logger.warning(f"未知的ROI类型: {roi_type}，跳过ROI {roi_id}")
                    continue
                
                # 更新数据库
                new_params_str = json.dumps(new_params, ensure_ascii=False)
                cursor.execute(
                    "UPDATE roi_configs SET algorithm_params = ? WHERE id = ?",
                    (new_params_str, config_id)
                )
                
                updated_count += 1
                logger.info(f"✅ 已更新ROI {roi_id} ({roi_type})的参数格式")
                
            except json.JSONDecodeError as e:
                logger.error(f"❌ ROI {roi_id} 参数JSON解析失败: {e}")
                continue
            except Exception as e:
                logger.error(f"❌ 更新ROI {roi_id} 失败: {e}")
                continue
        
        # 提交更改
        conn.commit()
        logger.info(f"🎉 迁移完成！共更新了 {updated_count} 个ROI配置")
        
    except Exception as e:
        logger.error(f"❌ 迁移过程中发生错误: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def main():
    """主函数"""
    # 数据库路径
    db_path = Path(__file__).parent.parent / "die_casting_detection.db"
    
    if not db_path.exists():
        logger.error(f"数据库文件不存在: {db_path}")
        return
    
    # 备份数据库
    backup_path = db_path.with_suffix('.db.backup')
    import shutil
    shutil.copy2(db_path, backup_path)
    logger.info(f"已创建数据库备份: {backup_path}")
    
    try:
        migrate_roi_params(db_path)
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        # 恢复备份
        shutil.copy2(backup_path, db_path)
        logger.info("已从备份恢复数据库")
        raise

if __name__ == "__main__":
    main()

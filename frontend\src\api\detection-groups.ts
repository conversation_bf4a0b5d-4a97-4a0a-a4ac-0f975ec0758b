import { get, post, put, del } from '@/utils/api'
import type { DetectionGroup } from '@/types'

/**
 * 获取所有检测组
 */
export const getDetectionGroups = (): Promise<DetectionGroup[]> => {
  return get<DetectionGroup[]>('/detection-groups/')
}

/**
 * 获取特定检测组
 */
export const getDetectionGroup = (id: number): Promise<DetectionGroup> => {
  return get<DetectionGroup>(`/detection-groups/${id}/`)
}

/**
 * 根据模板ID获取关联的检测组
 */
export const getDetectionGroupsByTemplate = (templateId: number): Promise<DetectionGroup[]> => {
  return get<DetectionGroup[]>(`/detection-groups/by-template/${templateId}`)
}

/**
 * 创建新检测组
 */
export const createDetectionGroup = (data: Partial<DetectionGroup>): Promise<DetectionGroup> => {
  return post<DetectionGroup>('/detection-groups/', data)
}

/**
 * 更新检测组
 */
export const updateDetectionGroup = (id: number, data: Partial<DetectionGroup>): Promise<DetectionGroup> => {
  return put<DetectionGroup>(`/detection-groups/${id}/`, data)
}

/**
 * 删除检测组
 */
export const deleteDetectionGroup = (id: number): Promise<DetectionGroup> => {
  return del<DetectionGroup>(`/detection-groups/${id}/`)
}

// 更新检测组配置
export const updateDetectionGroupConfig = (id: number, config: Record<string, any>) => {
  return put(`/detection-groups/${id}/config/`, config)
}

/**
 * 启动检测组
 */
export const startDetectionGroup = (id: number): Promise<DetectionGroup> => {
  return post<DetectionGroup>(`/detection-groups/${id}/start/`, {})
}

/**
 * 停止检测组
 */
export const stopDetectionGroup = (id: number): Promise<DetectionGroup> => {
  return post<DetectionGroup>(`/detection-groups/${id}/stop/`, {})
}

// 获取检测组所有ROI
export const getDetectionGroupROIs = (id: number) => {
  return get(`/detection-groups/${id}/rois/`)
}

// 创建ROI
export const createROI = (detectionGroupId: number, roiData: any) => {
  return post(`/detection-groups/${detectionGroupId}/rois/`, roiData)
}

// 更新ROI
export const updateROI = (detectionGroupId: number, roiId: string, roiData: any) => {
  return put(`/detection-groups/${detectionGroupId}/rois/${roiId}/`, roiData)
}

// 删除ROI
export const deleteROI = (detectionGroupId: number, roiId: string) => {
  return del(`/detection-groups/${detectionGroupId}/rois/${roiId}/`)
}
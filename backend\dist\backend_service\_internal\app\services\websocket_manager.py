import asyncio
import json
import logging
from typing import Dict, List, Set, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime
# 移除对 VideoDetectionService 的导入
from sqlalchemy.orm import Session
from ..db.session import get_db

logger = logging.getLogger(__name__)

class WebSocketData:
    """WebSocket连接数据"""
    def __init__(self, websocket: WebSocket, client_id: str):
        self.websocket = websocket
        self.client_id = client_id
        self.connected_at = datetime.now()
        self.last_activity = datetime.now()
        self.metadata: Dict[str, Any] = {}
    
    def update_activity(self):
        """更新最后活动时间"""
        self.last_activity = datetime.now()

class WebSocketConnectionManager:
    """WebSocket连接管理器"""
    def __init__(self):
        self.active_connections: Dict[str, WebSocketData] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str) -> bool:
        """建立连接"""
        try:
            await websocket.accept()
            self.active_connections[client_id] = WebSocketData(websocket, client_id)
            logger.info(f"WebSocket client connected: {client_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect WebSocket client {client_id}: {e}")
            return False
    
    def disconnect(self, websocket: WebSocket):
        """断开连接"""
        try:
            # 查找连接对应的客户端ID
            client_id = None
            for cid, conn_data in list(self.active_connections.items()):
                if conn_data.websocket == websocket:
                    client_id = cid
                    break
            
            if client_id:
                if client_id in self.active_connections:
                    del self.active_connections[client_id]
                logger.info(f"WebSocket client disconnected: {client_id}")
        except Exception as e:
            logger.error(f"Error during WebSocket disconnect: {e}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
    
    async def send_message(self, client_id: str, message: Any):
        """向指定客户端发送消息"""
        if client_id in self.active_connections:
            try:
                conn_data = self.active_connections[client_id]
                await conn_data.websocket.send_text(json.dumps(message))
                conn_data.update_activity()
            except Exception as e:
                logger.error(f"Error sending message to client {client_id}: {e}")
                # 可能连接已断开但未正确清理
                self.disconnect(self.active_connections[client_id].websocket)
    
    async def broadcast(self, message: str, exclude: Optional[str] = None):
        """广播消息给所有连接的客户端"""
        disconnected = []
        
        for client_id, conn_data in list(self.active_connections.items()):
            if exclude and client_id == exclude:
                continue
            
            try:
                await conn_data.websocket.send_text(message)
                conn_data.update_activity()
            except WebSocketDisconnect:
                disconnected.append(client_id)
            except Exception as e:
                logger.error(f"Error broadcasting to client {client_id}: {e}")
                disconnected.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected:
            if client_id in self.active_connections:
                del self.active_connections[client_id]
    
    def get_client_count(self) -> int:
        """获取当前连接的客户端数量"""
        return len(self.active_connections)
    
    def get_client_ids(self) -> List[str]:
        """获取所有连接的客户端ID"""
        return list(self.active_connections.keys())
    
    def is_connected(self, client_id: str) -> bool:
        """检查客户端是否已连接"""
        return client_id in self.active_connections
    
    def get_connection_data(self, client_id: str) -> Optional[WebSocketData]:
        """获取连接数据"""
        return self.active_connections.get(client_id)
    
    def set_metadata(self, client_id: str, key: str, value: Any) -> bool:
        """设置连接元数据"""
        if client_id in self.active_connections:
            self.active_connections[client_id].metadata[key] = value
            return True
        return False
    
    def get_metadata(self, client_id: str, key: str, default: Any = None) -> Any:
        """获取连接元数据"""
        if client_id in self.active_connections:
            return self.active_connections[client_id].metadata.get(key, default)
        return default

# 创建全局连接管理器实例
connection_manager = WebSocketConnectionManager()
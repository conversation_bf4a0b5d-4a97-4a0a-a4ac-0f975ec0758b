import logging
from sqlalchemy.orm import Session

from app.core.security import get_password_hash
from app.models.models import User, VideoSource, DieCaster, DetectionGroup, SystemLog

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def init_db(db: Session) -> None:
    """初始化数据库，添加初始数据"""
    # 创建管理员用户
    create_admin_user(db)
    # 创建测试数据
    create_test_data(db)


def create_admin_user(db: Session) -> None:
    """创建管理员用户"""
    # 检查是否已存在管理员用户
    admin = db.query(User).filter(User.username == "admin").first()
    if not admin:
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            role="admin",
            is_active=True
        )
        db.add(admin_user)
        db.commit()
        logger.info("管理员用户已创建")
    else:
        logger.info("管理员用户已存在")


def create_test_data(db: Session) -> None:
    """创建测试数据"""
    # 检查是否已存在测试数据
    existing_video_source = db.query(VideoSource).filter(VideoSource.name == "测试视频源").first()
    if not existing_video_source:
        # 创建测试视频源
        video_source = VideoSource(
            name="测试视频源",
            description="用于测试的视频源",
            source_type="本地文件",
            path="test_video.mp4"
        )
        db.add(video_source)
        db.commit()
        db.refresh(video_source)
        logger.info("测试视频源已创建")
        
        # 创建测试压铸机
        die_caster = DieCaster(
            name="测试压铸机",
            description="用于测试的压铸机",
            ip_address="*************",
            port=8080,
            status="offline"
        )
        db.add(die_caster)
        db.commit()
        db.refresh(die_caster)
        logger.info("测试压铸机已创建")
        
        # 创建测试检测组
        detection_group = DetectionGroup(
            name="测试检测组",
            die_caster_id=die_caster.id,
            video_source_id=video_source.id,
            config_json={
                "rois": [
                    {
                        "id": "roi1",
                        "name": "压铸区",
                        "type": "pressure",
                        "shape": "rectangle",
                        "points": [
                            {"x": 100, "y": 100},
                            {"x": 300, "y": 300}
                        ],
                        "parameters": {
                            "threshold": 50,
                            "min_area": 500
                        }
                    },
                    {
                        "id": "roi2",
                        "name": "排料口",
                        "type": "outlet",
                        "shape": "polygon",
                        "points": [
                            {"x": 400, "y": 100},
                            {"x": 600, "y": 100},
                            {"x": 500, "y": 300}
                        ],
                        "parameters": {
                            "threshold": 40,
                            "direction": "down",
                            "sensitivity": 0.8
                        }
                    }
                ],
                "global_parameters": {
                    "frame_skip": 2,
                    "cool_down_time": 5,
                    "detection_interval": 0.5
                }
            },
            status="inactive"
        )
        db.add(detection_group)
        db.commit()
        logger.info("测试检测组已创建")
    else:
        logger.info("测试数据已存在")
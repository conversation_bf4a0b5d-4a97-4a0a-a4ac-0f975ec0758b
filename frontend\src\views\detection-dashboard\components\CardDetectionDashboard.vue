<template>
  <div class="card-detection-dashboard">
    <!-- 当前运行模板 -->
    <!-- <div class="running-template-section">
      <h3 class="section-title">当前运行模板</h3>
      <div class="template-info">
        <div v-if="runningTemplates.length > 0" class="template-list">
          <div 
            v-for="template in runningTemplates" 
            :key="template.id"
            class="template-item"
          >
            <div class="template-name">{{ template.name }}</div>
            <div class="template-status">运行中</div>
          </div>
        </div>
        <div v-else class="no-template">
          <el-icon><Warning /></el-icon>
          <span>暂无运行模板</span>
        </div>
      </div>
    </div> -->

    <!-- 今日统计 -->
    <div class="stats-section">
      <div class="section-header">
        <h3 class="section-title">今日统计</h3>
        <button 
          class="detail-btn" 
          @click="showDailyStatsModal = true"
          title="查看详细统计"
        >
          <el-icon><View /></el-icon>
          详情
        </button>
      </div>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ overview.total_detections_today || 0 }}</div>
          <div class="stat-label">总检测次数</div>
        </div>
        <div class="stat-item success">
          <div class="stat-value">{{ overview.normal_detections_today || 0 }}</div>
          <div class="stat-label">正常检测</div>
        </div>
        <div class="stat-item error">
          <div class="stat-value">{{ overview.jam_detections_today || 0 }}</div>
          <div class="stat-label">卡料检测</div>
        </div>
        <div class="stat-item rate">
          <div class="stat-value">{{ ((overview.success_rate_today || 0) * 100).toFixed(1) }}%</div>
          <div class="stat-label">成功率</div>
        </div>
      </div>
    </div>

    <!-- 本周统计 -->
    <div class="stats-section">
      <h3 class="section-title">本周统计</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ overview.total_detections_week || 0 }}</div>
          <div class="stat-label">总检测次数</div>
        </div>
        <div class="stat-item success">
          <div class="stat-value">{{ overview.normal_detections_week || 0 }}</div>
          <div class="stat-label">正常检测</div>
        </div>
        <div class="stat-item error">
          <div class="stat-value">{{ overview.jam_detections_week || 0 }}</div>
          <div class="stat-label">卡料检测</div>
        </div>
        <div class="stat-item rate">
          <div class="stat-value">{{ ((overview.success_rate_week || 0) * 100).toFixed(1) }}%</div>
          <div class="stat-label">成功率</div>
        </div>
      </div>
    </div>

    <!-- 平均检测时间 -->
    <div class="avg-time-section">
      <h3 class="section-title">平均检测时间</h3>
      <div class="avg-time-value">
        <span class="time-number">{{ (overview.avg_detection_time || 0).toFixed(2) }}</span>
        <span class="time-unit">毫秒</span>
      </div>
    </div>

    <!-- 最近检测结果 -->
    <div class="recent-results-section">
      <div class="section-header">
        <h3 class="section-title">最近检测结果</h3>
        <div class="refresh-controls">
          <span class="refresh-label">刷新间隔:</span>
          <el-select 
            v-model="refreshInterval" 
            @change="setRefreshInterval"
            size="small"
            style="width: 100px"
          >
            <el-option label="1秒" :value="1000" />
            <el-option label="3秒" :value="3000" />
            <el-option label="5秒" :value="5000" />
            <el-option label="10秒" :value="10000" />
            <el-option label="30秒" :value="30000" />
          </el-select>
        </div>
      </div>
      <div class="results-list">
        <div 
          v-for="result in processedRecentResults.slice(0, 10)" 
          :key="result.id"
          class="result-item"
          :class="{ 'jam': result.is_jam }"
        >
          <div class="result-status">
            <el-icon v-if="result.is_jam" class="jam-icon"><Warning /></el-icon>
            <el-icon v-else class="normal-icon"><Check /></el-icon>
          </div>
          <div class="result-info">
            <div class="result-time">{{ formatTime(result.timestamp) }}</div>
            <div class="result-desc">{{ result.is_jam ? '卡料检测' : '正常检测' }}</div>
            <div v-if="result.undetected_rois && result.undetected_rois.length > 0" class="result-rois">
              异常ROI: {{ result.undetected_rois.join(', ') }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 每日统计详情弹窗 -->
    <DailyStatisticsModal 
      :visible="showDailyStatsModal" 
      @close="showDailyStatsModal = false" 
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Warning, Check, View } from '@element-plus/icons-vue'
import { getCardDetectionOverview, cardDetectionApi } from '@/api/card-detection'
import { getRunningDetectionTemplates } from '@/api/detection-templates'
import DailyStatisticsModal from '@/components/DailyStatisticsModal.vue'

// 响应式数据
const overview = ref({
  total_detections_today: 0,
  jam_detections_today: 0,
  normal_detections_today: 0,
  success_rate_today: 0,
  total_detections_week: 0,
  jam_detections_week: 0,
  normal_detections_week: 0,
  success_rate_week: 0,
  avg_detection_time: 0,
  recent_results: []
})

const runningTemplates = ref([])
const recentResults = ref([])
const loading = ref(false)
const refreshInterval = ref(5000) // 默认5秒刷新间隔
let refreshTimer = null

// 每日统计弹窗状态
const showDailyStatsModal = ref(false)

// 计算属性：处理检测结果数据
const processedRecentResults = computed(() => {
  return recentResults.value.map(result => ({
    ...result,
    is_jam: !result.is_normal
  }))
})

// 加载概览数据
const loadOverviewData = async () => {
  try {
    const [overviewData, templatesData] = await Promise.all([
      getCardDetectionOverview(),
      getRunningDetectionTemplates()
    ])
    
    if (overviewData) {
      overview.value = overviewData
    }
    
    if (templatesData) {
      runningTemplates.value = templatesData
    }
  } catch (error) {
    console.error('加载概览数据失败:', error)
    ElMessage.error('加载概览数据失败')
  }
}

// 加载最新检测结果
const loadRecentResults = async () => {
  try {
    const results = await cardDetectionApi.getRecentResults({ limit: 10 })
    if (results) {
      recentResults.value = results
    }
  } catch (error) {
    console.error('加载最新检测结果失败:', error)
  }
}

// 加载所有数据
const loadData = async () => {
  try {
    loading.value = true
    await Promise.all([
      loadOverviewData(),
      loadRecentResults()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timeString) => {
  const date = new Date(timeString)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 开始自动刷新
const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    loadRecentResults() // 只刷新检测结果，概览数据不需要频繁刷新
  }, refreshInterval.value)
}

// 设置刷新间隔
const setRefreshInterval = (interval) => {
  refreshInterval.value = interval
  if (refreshTimer) {
    stopAutoRefresh()
    startAutoRefresh()
  }
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 组件挂载
onMounted(() => {
  loadData()
  startAutoRefresh()
})

// 组件卸载
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.card-detection-dashboard {
  height: 100%;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);
  overflow-y: auto;
  box-sizing: border-box;
  font-size: 11px;
  display: flex;
  flex-direction: column;
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  .card-detection-dashboard {
    background: var(--el-bg-color-page);
    border-color: var(--el-border-color-light);
  }
  
  .template-item {
    background: var(--el-color-success-light-9);
    border-color: var(--el-color-success-light-7);
  }
  
  .stat-item {
    background: var(--el-bg-color);
    border-color: var(--el-border-color-lighter);
  }
  
  .result-item {
    background: var(--el-bg-color);
    border-color: var(--el-border-color-lighter);
  }
}

.section-title {
  margin: 0 0 6px 0;
  font-size: 12px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 3px;
}

/* 运行模板部分 */
.running-template-section {
  margin-bottom: 12px;
}

.template-info {
  min-height: 30px;
}

.template-list {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.template-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 6px;
  background: var(--el-color-success-light-9);
  border-radius: 3px;
  border: 1px solid var(--el-color-success-light-7);
}

.template-name {
  font-size: 10px;
  font-weight: 500;
  color: var(--el-color-success-dark-2);
}

.template-status {
  font-size: 9px;
  color: var(--el-color-success);
  background: var(--el-color-success-light-8);
  padding: 1px 4px;
  border-radius: 8px;
}

.no-template {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 6px;
  color: var(--el-text-color-placeholder);
  font-size: 10px;
  text-align: center;
  justify-content: center;
}

/* 统计部分 */
.stats-section {
  margin-bottom: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.stat-item {
  padding: 6px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
  text-align: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  box-shadow: 0 1px 4px var(--el-box-shadow-light);
}

.stat-item.success {
  border-color: var(--el-color-success-light-7);
  background: var(--el-color-success-light-9);
}

.stat-item.error {
  border-color: var(--el-color-danger-light-7);
  background: var(--el-color-danger-light-9);
}

.stat-item.rate {
  border-color: var(--el-color-primary-light-7);
  background: var(--el-color-primary-light-9);
}

.stat-value {
  font-size: 13px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin-bottom: 1px;
}

.stat-label {
  font-size: 9px;
  color: var(--el-text-color-regular);
}

/* 平均时间部分 */
.avg-time-section {
  margin-bottom: 12px;
}

.avg-time-value {
  text-align: center;
  padding: 8px;
  background: var(--el-color-info-light-9);
  border-radius: 4px;
  border: 1px solid var(--el-color-info-light-7);
}

.time-number {
  font-size: 16px;
  font-weight: 700;
  color: var(--el-color-info-dark-2);
}

.time-unit {
  font-size: 10px;
  color: var(--el-text-color-regular);
  margin-left: 3px;
}

/* 最近结果部分 */
.recent-results-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.detail-btn {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 3px 6px;
  background: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
  border-radius: 3px;
  color: var(--el-color-primary);
  font-size: 9px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.detail-btn:hover {
  background: var(--el-color-primary-light-8);
  border-color: var(--el-color-primary-light-6);
  color: var(--el-color-primary-dark-2);
}

.detail-btn .el-icon {
  font-size: 10px;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.refresh-label {
  font-size: 10px;
  color: var(--el-text-color-regular);
  white-space: nowrap;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 3px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 6px;
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
}

.result-item:hover {
  background: var(--el-fill-color);
}

.result-item.jam {
  border-color: var(--el-color-danger-light-7);
  background: var(--el-color-danger-light-9);
}

.result-status {
  flex-shrink: 0;
}

.jam-icon {
  color: var(--el-color-danger);
  font-size: 12px;
}

.normal-icon {
  color: var(--el-color-success);
  font-size: 12px;
}

.result-info {
  flex: 1;
  min-width: 0;
}

.result-time {
  font-size: 9px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.result-desc {
  font-size: 8px;
  color: var(--el-text-color-regular);
}

.result-rois {
  font-size: 7px;
  color: var(--el-color-warning);
  margin-top: 1px;
  font-weight: 500;
}

/* 滚动条样式 */
.card-detection-dashboard::-webkit-scrollbar,
.results-list::-webkit-scrollbar {
  width: 3px;
}

.card-detection-dashboard::-webkit-scrollbar-track,
.results-list::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
}

.card-detection-dashboard::-webkit-scrollbar-thumb,
.results-list::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 2px;
}

.card-detection-dashboard::-webkit-scrollbar-thumb:hover,
.results-list::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 暗色模式支持 */
.dark-theme .card-detection-dashboard {
  background: var(--bg-color-soft);
  border-color: var(--border-color);
}

.dark-theme .section-title {
  color: var(--text-color);
}

.dark-theme .template-item {
  background: var(--bg-color-mute);
  border-color: var(--success-color);
}

.dark-theme .template-name {
  color: var(--success-color-light);
}

.dark-theme .template-status {
  background: var(--bg-color-mute);
  color: var(--success-color-light);
}

.dark-theme .stat-item {
  background: var(--bg-color-mute);
  border-color: var(--border-color);
}

.dark-theme .stat-item.success {
  background: var(--bg-color-mute);
  border-color: var(--success-color);
}

.dark-theme .stat-item.error {
  background: var(--bg-color-mute);
  border-color: var(--danger-color);
}

.dark-theme .stat-item.rate {
  background: var(--bg-color-mute);
  border-color: var(--primary-color);
}

.dark-theme .avg-time-value {
  background: var(--bg-color-mute);
  border-color: var(--info-color);
}

.dark-theme .time-number {
  color: var(--info-color);
}

.dark-theme .result-item {
  background: var(--bg-color-mute);
  border-color: var(--border-color);
}

.dark-theme .result-item:hover {
  background: var(--bg-color-hover);
}

.dark-theme .result-item.jam {
  background: var(--bg-color-mute);
  border-color: var(--danger-color);
}
</style>
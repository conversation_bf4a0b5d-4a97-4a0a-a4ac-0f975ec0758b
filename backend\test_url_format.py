import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def get_auth_token(username="admin", password="123456"):
    """获取认证token"""
    login_url = f"{API_BASE}/auth/login/json"
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data["access_token"]
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_api_with_auth(method, url, data=None):
    """使用认证测试API接口"""
    token = get_auth_token()
    if not token:
        print(f"无法获取token，跳过 {method} {url}")
        return None
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers)
        
        print(f"{method} {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code < 400:
            print("✅ 成功")
        else:
            print("❌ 失败")
            
        try:
            response_data = response.json()
            print(f"响应: {response_data}")
        except:
            print(f"响应: {response.text}")
        print("-" * 50)
        
        return response
    except Exception as e:
        print(f"请求失败: {e}")
        print("-" * 50)
        return None

def main():
    print("测试URL格式对删除API的影响...\n")
    
    # 1. 创建一个测试计划
    test_schedule_data = {
        "name": "URL格式测试计划",
        "description": "用于测试URL格式的计划",
        "templateId": 1,
        "date": "2025-01-26",
        "startTime": "10:00",
        "endTime": "11:00",
        "isEnabled": True
    }
    
    print("1. 创建测试计划:")
    response = test_api_with_auth("POST", f"{API_BASE}/preset-schedules/", test_schedule_data)
    
    if not response or response.status_code != 200:
        print("创建失败，无法继续测试")
        return
    
    new_schedule = response.json()
    test_id = new_schedule.get('id')
    print(f"创建成功，测试ID: {test_id}")
    
    # 2. 测试不同的URL格式进行删除
    test_urls = [
        f"{API_BASE}/preset-schedules/{test_id}",      # 不带斜杠（后端路由格式）
        f"{API_BASE}/preset-schedules/{test_id}/",     # 带斜杠（前端使用格式）
    ]
    
    print(f"\n2. 测试不同URL格式删除计划 ID: {test_id}")
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n测试 {i}: {url}")
        response = test_api_with_auth("DELETE", url)
        
        if response and response.status_code == 200:
            print("✅ 删除成功！")
            print("这个URL格式是正确的")
            break
        elif response and response.status_code == 404:
            print("❌ 404错误 - 路由不匹配")
            print("这个URL格式有问题")
        else:
            print(f"❌ 其他错误，状态码: {response.status_code if response else 'None'}")
        
        # 如果第一个URL失败了，重新创建计划用于第二个URL测试
        if i == 1 and (not response or response.status_code != 200):
            print("\n重新创建计划用于下一个URL测试...")
            response = test_api_with_auth("POST", f"{API_BASE}/preset-schedules/", test_schedule_data)
            if response and response.status_code == 200:
                new_schedule = response.json()
                test_id = new_schedule.get('id')
                # 更新第二个测试URL
                test_urls[1] = f"{API_BASE}/preset-schedules/{test_id}/"
    
    # 3. 测试其他API的URL格式
    print("\n3. 测试其他API的URL格式兼容性:")
    
    # 创建另一个测试计划
    response = test_api_with_auth("POST", f"{API_BASE}/preset-schedules/", test_schedule_data)
    if response and response.status_code == 200:
        new_schedule = response.json()
        test_id = new_schedule.get('id')
        
        # 测试GET请求的URL格式
        get_urls = [
            f"{API_BASE}/preset-schedules/{test_id}",
            f"{API_BASE}/preset-schedules/{test_id}/"
        ]
        
        for url in get_urls:
            print(f"\nGET {url}")
            response = test_api_with_auth("GET", url)
        
        # 清理：删除测试计划
        print("\n清理测试数据...")
        test_api_with_auth("DELETE", f"{API_BASE}/preset-schedules/{test_id}")
    
    print("\n测试完成！")
    print("\n结论:")
    print("- 如果带斜杠的URL返回404，说明FastAPI路由不支持尾部斜杠")
    print("- 前端应该使用不带斜杠的URL格式")
    print("- 或者后端需要配置支持尾部斜杠的路由")

if __name__ == "__main__":
    main()
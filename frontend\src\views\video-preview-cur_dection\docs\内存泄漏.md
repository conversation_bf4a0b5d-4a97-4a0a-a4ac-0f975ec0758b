
          
通过分析您的代码和海康威视WebSDK官方文档，我发现了导致浏览器卡顿的几个主要原因：

## 主要问题

### 1. **缺少资源清理机制**
在 <mcfile name="index.vue" path="c:\Users\<USER>\Desktop\web_last_070620\frontend\src\views\video-preview-cur_dection\index.vue"></mcfile> 中，`onUnmounted` 钩子为空，没有实现WebSDK资源的清理。

### 2. **WebSDK实例未正确销毁**
在 <mcfile name="useWebSDK.ts" path="c:\Users\<USER>\Desktop\web_last_070620\frontend\src\views\video-preview-cur_dection\useWebSDK.ts"></mcfile> 中，缺少组件卸载时的WebSDK实例清理逻辑。

### 3. **WebSocket连接未完全清理**
虽然 <mcfile name="useMotionDetection.ts" path="c:\Users\<USER>\Desktop\web_last_070620\frontend\src\views\video-preview-cur_dection\useMotionDetection.ts"></mcfile> 在 `onUnmounted` 中调用了 `stopDetection` 和 `wsClient.disconnect()`，但WebSDK的视频流连接可能仍在运行。

## 解决方案

### 1. **完善组件卸载清理**
在 <mcfile name="index.vue" path="c:\Users\<USER>\Desktop\web_last_070620\frontend\src\views\video-preview-cur_dection\index.vue"></mcfile> 的 `onUnmounted` 钩子中添加：

```javascript
onUnmounted(() => {
  // 停止所有预览
  stopPreview()
  // 停止检测
  stopDetection()
  // 断开WebSocket
  wsClient?.disconnect()
})
```

### 2. **在useWebSDK中添加清理函数**
在 <mcfile name="useWebSDK.ts" path="c:\Users\<USER>\Desktop\web_last_070620\frontend\src\views\video-preview-cur_dection\useWebSDK.ts"></mcfile> 中添加：

```javascript
const cleanup = () => {
  // 停止所有窗口播放
  if (window.WebVideoCtrl) {
    window.WebVideoCtrl.I_StopAll()
  }
  // 重置状态
  isPreviewActive.value = false
  isConnected.value = false
}

// 在组合式函数返回中暴露cleanup
return {
  // ... 其他返回值
  cleanup
}
```

### 3. **优化视频源切换**
在切换视频源时，确保先完全停止当前播放再开始新的播放，避免多个视频流同时运行。

### 4. **添加定时清理机制**
考虑添加定时器定期检查和清理无用的WebSDK资源：

```javascript
// 定期清理无用资源
setInterval(() => {
  if (window.WebVideoCtrl) {
    // 检查窗口状态并清理无效连接
    for (let i = 0; i < 16; i++) {
      const windowStatus = window.WebVideoCtrl.I_GetWindowStatus(i)
      if (windowStatus && windowStatus.iPlayStatus === 0) {
        // 清理无效窗口
      }
    }
  }
}, 30000) // 每30秒检查一次
```

### 5. **内存监控**
添加内存使用监控，当内存使用过高时主动清理资源。

## 根本原因

海康威视WebSDK是基于WebSocket和WebAssembly的重量级组件，如果不正确管理其生命周期，会导致：
- WebSocket连接累积
- 视频解码器实例未释放
- 内存中的视频帧数据未清理
- 定时器和事件监听器未移除

通过实施上述解决方案，可以有效解决浏览器卡顿问题。建议优先实施前3个解决方案，它们能解决大部分内存泄漏问题。
        
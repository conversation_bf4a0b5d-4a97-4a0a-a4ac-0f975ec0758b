/* 检测信息组件样式 */
.detection-info {
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px var(--shadow-color);
  transition: all 0.3s ease;
}

.detection-info:hover {
  box-shadow: 0 4px 12px var(--shadow-color-hover);
}

/* 检测信息标题 */
.detection-info__title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-indicator__dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator--idle {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--info-color);
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

.status-indicator--idle .status-indicator__dot {
  background: var(--info-color);
}

.status-indicator--running {
  background: rgba(var(--success-color-rgb), 0.1);
  color: var(--success-color);
  border: 1px solid rgba(var(--success-color-rgb), 0.2);
}

.status-indicator--running .status-indicator__dot {
  background: var(--success-color);
}

.status-indicator--stopped {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--info-color);
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

.status-indicator--stopped .status-indicator__dot {
  background: var(--info-color);
}

.status-indicator--error {
  background: rgba(var(--danger-color-rgb), 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(var(--danger-color-rgb), 0.2);
}

.status-indicator--error .status-indicator__dot {
  background: var(--error-color);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 模式选择器 */
.mode-selector {
  margin: 16px 0;
}

.mode-selector__label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-soft);
  margin-bottom: 8px;
}

.mode-selector__group {
  display: flex;
  gap: 8px;
}

.mode-selector__option {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-color);
  color: var(--text-color-soft);
  font-size: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mode-selector__option:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.mode-selector__option--active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-color-inverse, #ffffff);
}

.mode-selector__option--active:hover {
  background: var(--primary-color-light);
  border-color: var(--primary-color-light);
}

/* 算法显示 */
.algorithm-display {
  margin: 16px 0;
}

.algorithm-display__label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-soft);
  margin-bottom: 8px;
}

.algorithm-display__value {
  padding: 8px 12px;
  background: var(--bg-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 12px;
  color: var(--text-color);
  font-family: 'Courier New', monospace;
}

/* 检测结果 */
.detection-results {
  margin: 16px 0;
}

.detection-results__label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-soft);
  margin-bottom: 8px;
}

.detection-results__list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-color-mute);
}

.detection-results__item {
  padding: 8px 12px;
  border-bottom: 1px solid var(--border-color);
  font-size: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detection-results__item:last-child {
  border-bottom: none;
}

.detection-results__item--positive {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
}

.detection-results__item--negative {
  background: rgba(var(--danger-color-rgb), 0.1);
  color: var(--error-color);
}

.detection-results__roi-name {
  font-weight: 500;
}

.detection-results__status {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  background: var(--bg-color-hover);
  color: var(--text-color-mute);
}

.detection-results__status--detected {
  background: rgba(var(--success-color-rgb), 0.2);
  color: var(--success-color);
}

.detection-results__status--clear {
  background: rgba(var(--warning-color-rgb), 0.2);
  color: var(--warning-color);
}

/* 统计信息 */
.detection-stats {
  margin: 16px 0;
}

.detection-stats__label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-soft);
  margin-bottom: 8px;
}

.detection-stats__grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.detection-stats__item {
  padding: 8px;
  background: var(--bg-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  text-align: center;
}

.detection-stats__value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.detection-stats__name {
  font-size: 10px;
  color: var(--text-color-mute);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 控制按钮 */
.detection-controls {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}

.detection-controls__button {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.detection-controls__button--start {
  background: var(--success-color);
  color: var(--text-color-inverse, #ffffff);
}

.detection-controls__button--start:hover {
  background: var(--success-color-light, #85ce61);
}

.detection-controls__button--start:disabled {
  background: var(--text-color-mute);
  cursor: not-allowed;
}

.detection-controls__button--stop {
  background: var(--error-color);
  color: var(--text-color-inverse, #ffffff);
}

.detection-controls__button--stop:hover {
  background: var(--danger-color-light, #f78989);
}

.detection-controls__button--stop:disabled {
  background: var(--text-color-mute);
  cursor: not-allowed;
}

/* 兼容旧版本类名 */
.detection-info-section {
  margin-top: 10px;
  padding: 12px;
  background-color: var(--bg-color-soft);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  min-height: 120px;
  display: flex;
  flex-direction: column;
}

.detection-info-section.compact {
  padding: 8px;
  min-height: 80px;
}

.detection-info-section h4 {
  margin: 0 0 12px 0;
  color: var(--text-color, #303133);
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detection-info-section.compact h4 {
  font-size: 14px;
  margin-bottom: 8px;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1; /* 占据剩余空间 */
}

.detection-info-section.compact .info-content {
  gap: 6px;
}

/* 检测状态 */
.detection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--text-color-mute, #909399);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.status-indicator.active {
  background-color: var(--success-color, #67c23a);
  box-shadow: 0 0 0 3px rgba(var(--success-color-rgb), 0.2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--success-color-rgb), 0.4);
  }
  50% {
    box-shadow: 0 0 0 6px rgba(var(--success-color-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--success-color-rgb), 0);
  }
}

.status-text {
  font-size: 14px;
  color: var(--text-color, #303133);
  font-weight: 500;
}

.detection-info-section.compact .status-text {
  font-size: 12px;
}

/* 检测模式 */
.detection-mode {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.mode-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-color, #303133);
}

.detection-info-section.compact .mode-label {
  font-size: 12px;
}

.mode-selector {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.mode-selector label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-color-soft, #606266);
  cursor: pointer;
  transition: color 0.3s ease;
}

.mode-selector label:hover {
  color: var(--primary-color, #409eff);
}

.mode-selector input[type="radio"] {
  margin: 0;
  accent-color: var(--primary-color, #409eff);
}

/* 检测算法 */
.detection-algorithm {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.algorithm-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-color, #303133);
  min-width: 70px;
}

.detection-info-section.compact .algorithm-label {
  font-size: 12px;
  min-width: 60px;
}

.algorithm-value {
  font-size: 13px;
  color: var(--primary-color, #409eff);
  font-weight: 500;
}

.detection-info-section.compact .algorithm-value {
  font-size: 12px;
}

/* 检测结果 */
.detection-result {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 8px;
  background-color: var(--bg-color, #fff);
  border-radius: 4px;
  border: 1px solid var(--border-color-light, #dcdfe6);
  flex: 1; /* 占据剩余空间 */
  min-height: 0; /* 允许收缩 */
}

.detection-info-section.compact .detection-result {
  padding: 6px;
  gap: 4px;
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 20px;
}

.result-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-color-soft, #606266);
  min-width: 70px;
  flex-shrink: 0;
}

.detection-info-section.compact .result-label {
  font-size: 11px;
  min-width: 60px;
}

.result-value {
  font-size: 12px;
  color: var(--text-color, #606266);
  text-align: right;
  flex: 1;
}

.detection-info-section.compact .result-value {
  font-size: 11px;
}

.result-positive {
  color: var(--success-color, #67c23a) !important;
  font-weight: 600;
}

.result-warning {
  color: var(--warning-color, #e6a23c) !important;
  font-weight: 600;
}

/* ROI检测结果 */
.roi-results {
  margin-top: 8px;
  border-top: 1px dashed var(--border-color-light, #dcdfe6);
  padding-top: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.detection-info-section.compact .roi-results {
  margin-top: 6px;
  padding-top: 6px;
  max-height: 120px;
}

.roi-results-header {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-color, #303133);
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detection-info-section.compact .roi-results-header {
  font-size: 11px;
  margin-bottom: 4px;
}

.roi-result-item {
  margin-bottom: 6px;
  padding: 6px;
  background-color: var(--bg-color-soft, #f5f7fa);
  border-radius: 3px;
  border-left: 3px solid transparent;
  transition: all 0.3s ease;
}

.detection-info-section.compact .roi-result-item {
  margin-bottom: 4px;
  padding: 4px;
}

.roi-result-item.active {
  border-left-color: var(--success-color, #67c23a);
  background-color: rgba(var(--success-color-rgb), 0.05);
}

.roi-name {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-color, #303133);
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detection-info-section.compact .roi-name {
  font-size: 11px;
  margin-bottom: 2px;
}

.roi-status {
  font-size: 11px;
  color: var(--text-color-mute, #909399);
}

.detection-info-section.compact .roi-status {
  font-size: 10px;
}

.roi-status.roi-active {
  color: var(--success-color, #67c23a);
  font-weight: 600;
}

.roi-details {
  margin-top: 4px;
  font-size: 10px;
  color: var(--text-color-soft, #606266);
  display: flex;
  gap: 8px;
}

.detection-info-section.compact .roi-details {
  margin-top: 2px;
  font-size: 9px;
  gap: 6px;
}

/* 检测统计信息 */
.detection-stats {
  padding: 8px;
  background-color: var(--bg-color, #fff);
  border-radius: 4px;
  border: 1px solid var(--border-color-light, #dcdfe6);
  margin-top: 8px;
}

.detection-info-section.compact .detection-stats {
  padding: 6px;
  margin-top: 6px;
}

.stats-header {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-color, #303133);
  margin-bottom: 6px;
}

.detection-info-section.compact .stats-header {
  font-size: 11px;
  margin-bottom: 4px;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  min-height: 16px;
}

.detection-info-section.compact .stats-item {
  margin-bottom: 2px;
  min-height: 14px;
}

.stats-label {
  font-size: 11px;
  color: var(--text-color-soft, #606266);
}

.detection-info-section.compact .stats-label {
  font-size: 10px;
}

.stats-value {
  font-size: 11px;
  font-weight: 600;
  color: var(--text-color, #303133);
}

.detection-info-section.compact .stats-value {
  font-size: 10px;
}

/* 检测控制按钮 */
.detection-controls {
  display: flex;
  gap: 8px;
  margin-top: auto; /* 推到底部 */
  padding-top: 8px;
  border-top: 1px solid var(--border-color-light, #dcdfe6);
}

.detection-info-section.compact .detection-controls {
  gap: 6px;
  padding-top: 6px;
}

.detection-controls.hidden {
  display: none;
}

.detection-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.detection-info-section.compact .detection-btn {
  padding: 6px 8px;
  font-size: 11px;
  min-height: 28px;
}

.detection-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-start {
  background-color: var(--success-color, #67c23a);
  color: var(--text-color-inverse, #ffffff);
}

.btn-start:hover:not(:disabled) {
  background-color: var(--success-color-light, #85ce61);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(var(--success-color-rgb), 0.3);
}

.btn-stop {
  background-color: var(--danger-color, #f56c6c);
  color: var(--text-color-inverse, #ffffff);
}

.btn-stop:hover:not(:disabled) {
  background-color: var(--danger-color-light, #f78989);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(var(--danger-color-rgb), 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detection-info-section {
    padding: 8px;
    min-height: 100px;
  }
  
  .info-content {
    gap: 6px;
  }
  
  .mode-selector {
    flex-direction: column;
    gap: 6px;
  }
  
  .detection-controls {
    flex-direction: column;
    gap: 6px;
  }
  
  .detection-btn {
    min-height: 36px;
  }
}

/* 滚动条样式 */
.roi-results::-webkit-scrollbar {
  width: 4px;
}

.roi-results::-webkit-scrollbar-track {
  background: var(--bg-color-soft, #f5f7fa);
  border-radius: 2px;
}

.roi-results::-webkit-scrollbar-thumb {
  background: var(--border-color, #e4e7ed);
  border-radius: 2px;
}

.roi-results::-webkit-scrollbar-thumb:hover {
  background: var(--text-color-mute, #909399);
}
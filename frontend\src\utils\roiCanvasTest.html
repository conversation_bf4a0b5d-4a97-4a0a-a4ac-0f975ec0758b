<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROI画布问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .video-container {
            position: relative;
            width: 640px;
            height: 360px;
            background: #000;
            border: 2px solid #ccc;
            margin: 20px auto;
        }
        
        .roi-display-canvas {
            position: absolute;
            top: 0;
            left: 0;
            max-width: 100%;
            max-height: 100%;
            pointer-events: auto;
            z-index: 20;
            cursor: crosshair;
            object-fit: contain;
            border: 2px solid red;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d7ff;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ROI画布问题调试</h1>
        
        <div class="info">
            <strong>目标:</strong> 验证画布元素是否能正确获取和初始化
            <br><strong>步骤:</strong> 1. 检查画布元素 → 2. 初始化绘制器 → 3. 测试绘制
        </div>
        
        <div class="video-container">
            <canvas 
                id="roiDisplayCanvas"
                class="roi-display-canvas"
                width="640"
                height="360"
            ></canvas>
        </div>
        
        <div class="controls">
            <button id="checkCanvasBtn" class="btn btn-primary">检查画布</button>
            <button id="initDrawerBtn" class="btn btn-success">初始化绘制器</button>
            <button id="testDrawBtn" class="btn btn-primary">测试绘制</button>
        </div>
        
        <div id="logArea" class="log-area"></div>
    </div>

    <script src="roiDrawer.js"></script>
    <script>
        let roiDrawer = null
        
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea')
            const time = new Date().toLocaleTimeString()
            const entry = document.createElement('div')
            entry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black'
            entry.textContent = `[${time}] ${message}`
            logArea.appendChild(entry)
            logArea.scrollTop = logArea.scrollHeight
            console.log(message)
        }
        
        document.getElementById('checkCanvasBtn').addEventListener('click', () => {
            log('=== 检查画布元素 ===')
            
            const canvas = document.getElementById('roiDisplayCanvas')
            log(`画布元素存在: ${!!canvas}`)
            
            if (canvas) {
                log(`标签名: ${canvas.tagName}`)
                log(`实际尺寸: ${canvas.width} x ${canvas.height}`)
                log(`显示尺寸: ${canvas.offsetWidth} x ${canvas.offsetHeight}`)
                log(`样式尺寸: ${canvas.style.width} x ${canvas.style.height}`)
                log(`父元素: ${canvas.parentElement?.className}`)
                log(`在DOM中: ${document.contains(canvas)}`)
                
                const ctx = canvas.getContext('2d')
                log(`上下文获取: ${!!ctx}`, ctx ? 'success' : 'error')
                
                if (ctx) {
                    log('画布状态正常', 'success')
                } else {
                    log('画布上下文获取失败', 'error')
                }
            } else {
                log('画布元素未找到', 'error')
            }
        })
        
        document.getElementById('initDrawerBtn').addEventListener('click', () => {
            log('=== 初始化ROI绘制器 ===')
            
            const canvas = document.getElementById('roiDisplayCanvas')
            if (!canvas) {
                log('画布元素未找到，无法初始化', 'error')
                return
            }
            
            try {
                roiDrawer = new ROIDrawer(canvas, {
                    videoWidth: 640,
                    videoHeight: 360,
                    colors: {
                        pailiao: '#00ffff',
                        yazhu: '#ff0000',
                        default: ['#00ff00']
                    },
                    debugMode: true
                })
                
                log('ROI绘制器初始化成功', 'success')
                log(`绘制器实例: ${!!roiDrawer}`)
                log(`画布引用: ${!!roiDrawer.canvas}`)
                log(`上下文引用: ${!!roiDrawer.ctx}`)
                
            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error')
                console.error(error)
            }
        })
        
        document.getElementById('testDrawBtn').addEventListener('click', () => {
            log('=== 测试绘制功能 ===')
            
            if (!roiDrawer) {
                log('请先初始化绘制器', 'error')
                return
            }
            
            // 测试手动绘制
            const ctx = roiDrawer.ctx
            if (ctx) {
                log('开始手动绘制测试...')
                
                // 清空画布
                ctx.clearRect(0, 0, 640, 360)
                
                // 绘制测试矩形
                ctx.strokeStyle = '#ff0000'
                ctx.lineWidth = 3
                ctx.strokeRect(50, 50, 200, 100)
                
                // 绘制测试文字
                ctx.fillStyle = '#00ff00'
                ctx.font = '16px Arial'
                ctx.fillText('测试绘制成功!', 60, 80)
                
                log('手动绘制完成', 'success')
                
                // 测试ROI加载
                const testROI = {
                    id: 'test_1',
                    name: '测试ROI',
                    type: 'polygon',
                    attribute: 'pailiao',
                    color: '#00ffff',
                    points: [
                        { x: 300, y: 150 },
                        { x: 450, y: 150 },
                        { x: 450, y: 250 },
                        { x: 300, y: 250 }
                    ]
                }
                
                const success = roiDrawer.loadROIList([testROI])
                if (success) {
                    log('ROI加载测试成功', 'success')
                } else {
                    log('ROI加载测试失败', 'error')
                }
                
            } else {
                log('画布上下文不可用', 'error')
            }
        })
        
        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            log('页面加载完成')
            log('请按顺序点击: 1.检查画布 → 2.初始化绘制器 → 3.测试绘制')
        })
    </script>
</body>
</html>

# 监控看板快速启动指南

## 🚀 立即体验

### 1. 启动项目
```bash
# 前端
cd frontend
npm run dev

# 后端
cd backend
python -m uvicorn app.main:app --reload
```

### 2. 访问看板
1. 打开浏览器访问 `http://localhost:5173`
2. 登录系统（使用现有账号）
3. 在左侧菜单中点击 **"监控看板"**

## 📊 功能演示

### 系统概览
- **设备状态**: 显示 3/4 台设备在线
- **检测组**: 显示 6/9 个检测组活跃
- **报警信息**: 显示 3 条待处理报警
- **GPU加速**: 显示 NVIDIA RTX 3080 已启用
- **检测性能**: 显示 25 FPS 处理速度

### 设备监控
- **压铸机1**: 在线，2个检测组，4个ROI
- **压铸机2**: 在线，2个检测组，2个ROI  
- **压铸机3**: 离线，2个检测组，8个ROI
- **压铸机4**: 在线，3个检测组，6个ROI

### 实时检测
- **检测组1**: 监测中，压铸机1，2个ROI，1个报警
- **检测组2**: 空闲，压铸机1，2个ROI
- **检测组3**: 监测中，压铸机2，1个ROI，2个报警
- **检测组4**: 冷却，压铸机2，1个ROI
- **检测组5-6**: 离线，压铸机3

### 视频监控
- **摄像头1-2**: 在线，支持ROI绘制
- **摄像头3**: 离线
- **布局选项**: 1x1, 2x2, 3x3 网格

### 报警管理
- **卡料报警**: 压铸机1-检测组1，高级别，待处理
- **设备离线**: 压铸机3，中级别，已处理
- **筛选功能**: 按级别和状态筛选

### 性能监控
- **CPU使用率**: 45%
- **内存使用率**: 62%
- **GPU使用率**: 78%
- **磁盘使用率**: 35%
- **网络流量**: 上传512KB/s，下载1MB/s
- **检测性能**: 25 FPS，120ms延迟

### 系统日志
- **信息日志**: 系统启动完成
- **警告日志**: 压铸机3连接超时
- **错误日志**: 检测组1发生卡料报警
- **筛选功能**: 按日志级别筛选

### 数据统计
- **今日检测**: 1250次
- **今日报警**: 8次
- **卡料率**: 0.64%
- **平均处理时间**: 85ms
- **系统运行时间**: 15天8小时32分钟
- **检测准确率**: 98.5%

## 🎨 主题切换

1. 点击右上角的主题切换按钮
2. 看板会自动适应亮色/暗色主题
3. 所有组件和图标都会相应调整

## 📱 响应式测试

### 大屏显示 (>1400px)
- 完整三栏布局
- 所有功能面板同时显示

### 中等屏幕 (1200-1400px)  
- 紧凑三栏布局
- 面板宽度自适应

### 小屏设备 (<1200px)
- 单栏堆叠布局
- 面板横向滚动

### 移动端适配
- 响应式网格布局
- 触摸友好的交互

## 🔧 交互功能

### 全屏显示
- 点击右上角"全屏显示"按钮
- 支持ESC键退出全屏

### 数据刷新
- 点击"刷新数据"按钮手动刷新
- 系统每30秒自动刷新

### 筛选和搜索
- 报警面板：按级别和状态筛选
- 检测面板：按状态筛选
- 日志面板：按级别筛选

### 设备交互
- 点击设备卡片查看详情
- 选择检测组查看相关视频

### 视频控制
- 切换网格布局 (1x1/2x2/3x3)
- ROI区域显示
- 视频状态监控

## 🎯 核心算法展示

### 运动检测
- **算法**: MOG2背景减除
- **准确率**: 98.2%
- **处理速度**: 25 FPS
- **状态**: 活跃运行

### 方向检测  
- **算法**: 基于运动中心跟踪
- **准确率**: 96.8%
- **处理速度**: 23 FPS
- **状态**: 活跃运行

### 卡料检测
- **算法**: 综合运动和方向分析
- **准确率**: 99.1%
- **响应时间**: 120ms
- **状态**: 活跃运行

## 📈 性能指标

### 系统性能
- **前端渲染**: 60 FPS
- **数据更新**: 实时
- **内存占用**: 优化良好
- **响应时间**: <100ms

### 检测性能
- **处理速度**: 25 FPS
- **检测延迟**: 120ms
- **准确率**: 98.5%
- **误报率**: <2%

## 🔍 故障排除

### 页面无法加载
1. 检查控制台错误信息
2. 确认路由配置正确
3. 验证组件导入路径

### 数据不显示
1. 检查模拟数据是否正确
2. 确认组件props传递
3. 验证计算属性逻辑

### 样式异常
1. 检查CSS变量定义
2. 确认主题文件导入
3. 验证响应式断点

### 交互无响应
1. 检查事件绑定
2. 确认方法定义
3. 验证数据更新逻辑

## 📞 技术支持

如遇到问题，请：
1. 查看浏览器控制台
2. 检查网络请求
3. 确认组件状态
4. 验证数据流向

---

**享受您的监控看板体验！** 🎉

<template>
  <div class="dashboard-card alarm-center-card">
    <div class="card-header">
      <h3 class="card-title">
        <el-icon><WarningFilled /></el-icon>
        智能报警中心
      </h3>
      <div class="header-actions">
        <el-button type="primary" link>历史</el-button>
      </div>
    </div>
    <div class="card-content">
      <div class="alarm-stats">
        <div class="stat-item">
          <span class="stat-value">{{ alarmStats.unresolved }}</span>
          <span class="stat-label">未解决</span>
        </div>
        <div class="stat-item">
          <span class="stat-value text-critical">{{ alarmStats.critical }}</span>
          <span class="stat-label">严重</span>
        </div>
        <div class="stat-item">
          <span class="stat-value text-high">{{ alarmStats.high }}</span>
          <span class="stat-label">高</span>
        </div>
        <div class="stat-item">
          <span class="stat-value text-medium">{{ alarmStats.medium }}</span>
          <span class="stat-label">中</span>
        </div>
      </div>
      <el-divider />
      <ul v-if="alarmList.length" class="alarm-list">
        <li v-for="alarm in alarmList" :key="alarm.id" class="alarm-item">
          <el-icon :class="getAlarmClass(alarm.level)"><component :is="getAlarmIcon(alarm.level)" /></el-icon>
          <span class="alarm-message">{{ alarm.message }}</span>
          <span class="alarm-time">{{ alarm.time }}</span>
        </li>
      </ul>
      <el-empty v-else description="暂无报警" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PropType } from 'vue'
import { WarningFilled, CircleClose, Warning, InfoFilled, Select } from '@element-plus/icons-vue'
import type { AlarmList } from '../types/dashboard.types'

const props = defineProps({
  alarms: {
    type: Object as PropType<AlarmList | null>,
    required: true
  }
})

const alarmStats = computed(() => {
  const stats = props.alarms?.statistics
  return {
    unresolved: stats?.unresolved_alarms ?? 0,
    critical: stats?.critical_alarms ?? 0,
    high: stats?.high_alarms ?? 0,
    medium: stats?.medium_alarms ?? 0,
  }
})

const alarmList = computed(() => {
  if (!props.alarms?.items) return []
  return props.alarms.items.slice(0, 5).map(alarm => ({
    id: alarm.id,
    level: alarm.level,
    message: alarm.title,
    time: new Date(alarm.timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }))
})


const getAlarmIcon = (level: string) => {
  switch (level) {
    case 'critical': return CircleClose
    case 'high': return WarningFilled
    case 'medium': return Warning
    default: return InfoFilled
  }
}

const getAlarmClass = (level: string) => {
  switch (level) {
    case 'critical': return 'text-critical'
    case 'high': return 'text-high'
    case 'medium': return 'text-medium'
    default: return 'text-low'
  }
}
</script>

<style scoped>
.alarm-center-card {
  grid-column: 7 / -1;
  grid-row: 4;
}
.header-actions {
  display: flex;
  gap: 8px;
}
.card-content {
  display: flex;
  flex-direction: column;
}
.alarm-stats {
  display: flex;
  justify-content: space-around;
  padding: 0 10px 10px;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}
.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
}
.stat-label {
  font-size: 12px;
  color: var(--text-color-mute);
}
.el-divider {
  margin: 0 0 10px;
}
.alarm-list {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;
  overflow-y: auto;
}
.alarm-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 4px;
  font-size: 14px;
  border-bottom: 1px solid var(--border-color);
}
.alarm-item:last-child {
  border-bottom: none;
}
.alarm-item .el-icon {
  font-size: 18px;
  flex-shrink: 0;
}
.alarm-message {
  font-weight: 500;
  color: var(--text-color);
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.alarm-time {
  font-size: 13px;
  color: var(--text-color-mute);
  margin-left: auto;
  flex-shrink: 0;
}
.text-critical {
  color: var(--danger-color);
}
.text-high {
  color: var(--el-color-danger-light-3);
}
.text-medium {
  color: var(--warning-color);
}
.text-low {
  color: var(--success-color);
}
</style> 
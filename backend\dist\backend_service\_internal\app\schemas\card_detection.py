from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class CardDetectionResultBase(BaseModel):
    """卡料检测结果基础模型"""
    detection_group_id: int = Field(..., description="检测组ID")
    timestamp: datetime = Field(..., description="检测时间戳")
    is_normal: bool = Field(..., description="是否正常（True=正常，False=卡料）")
    detection_time: float = Field(..., description="检测耗时（秒）")
    undetected_rois: Optional[List[str]] = Field(None, description="未检测到的ROI列表")
    detected_rois: Optional[List[str]] = Field(None, description="已检测到的ROI列表")
    trigger_roi_id: Optional[str] = Field(None, description="触发检测的ROI ID")
    result_details: Optional[Dict[str, Any]] = Field(None, description="详细检测信息")


class CardDetectionResultCreate(CardDetectionResultBase):
    """创建卡料检测结果的模型"""
    pass


class CardDetectionResultUpdate(BaseModel):
    """更新卡料检测结果的模型"""
    result_details: Optional[Dict[str, Any]] = Field(None, description="详细检测信息")


class CardDetectionResult(CardDetectionResultBase):
    """卡料检测结果响应模型"""
    id: int = Field(..., description="记录ID")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class CardDetectionStatistics(BaseModel):
    """卡料检测统计信息模型"""
    detection_group_id: int = Field(..., description="检测组ID")
    total_count: int = Field(..., description="总检测次数")
    normal_count: int = Field(..., description="正常次数")
    jam_count: int = Field(..., description="卡料次数")
    success_rate: float = Field(..., description="成功率（0-1）")
    avg_detection_time: float = Field(..., description="平均检测时间（秒）")
    last_detection_time: Optional[datetime] = Field(None, description="最后检测时间")
    recent_results: List[CardDetectionResult] = Field(default_factory=list, description="最近的检测结果")


class CardDetectionQuery(BaseModel):
    """卡料检测查询参数模型"""
    detection_group_id: Optional[int] = Field(None, description="检测组ID")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    is_normal: Optional[bool] = Field(None, description="是否正常")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")


class CardDetectionResultList(BaseModel):
    """卡料检测结果列表响应模型"""
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    items: List[CardDetectionResult] = Field(..., description="检测结果列表")


class CardDetectionDashboard(BaseModel):
    """卡料检测看板数据模型"""
    total_detections_today: int = Field(..., description="今日总检测次数")
    normal_detections_today: int = Field(..., description="今日正常次数")
    jam_detections_today: int = Field(..., description="今日卡料次数")
    success_rate_today: float = Field(..., description="今日成功率")
    
    total_detections_week: int = Field(..., description="本周总检测次数")
    normal_detections_week: int = Field(..., description="本周正常次数")
    jam_detections_week: int = Field(..., description="本周卡料次数")
    success_rate_week: float = Field(..., description="本周成功率")
    
    avg_detection_time: float = Field(..., description="平均检测时间")
    recent_results: List[CardDetectionResult] = Field(default_factory=list, description="最近检测结果")
    
    # 按小时统计的数据（用于图表）
    hourly_stats: List[Dict[str, Any]] = Field(default_factory=list, description="按小时统计数据")
    # 按检测组统计的数据
    group_stats: List[Dict[str, Any]] = Field(default_factory=list, description="按检测组统计数据")


class CardDetectionTrend(BaseModel):
    """卡料检测趋势数据模型"""
    date: str = Field(..., description="日期")
    total_count: int = Field(..., description="总检测次数")
    normal_count: int = Field(..., description="正常次数")
    jam_count: int = Field(..., description="卡料次数")
    success_rate: float = Field(..., description="成功率")
    avg_detection_time: float = Field(..., description="平均检测时间")


class CardDetectionResultSimple(BaseModel):
    """简化的卡料检测结果模型 - 只返回核心字段"""
    id: int = Field(..., description="卡料检测ID")
    detection_group_id: int = Field(..., description="检测组ID")
    timestamp: datetime = Field(..., description="检测时间")
    is_normal: bool = Field(..., description="1=正常，0=卡料")
    undetected_rois: Optional[List[str]] = Field(None, description="异常ROI")

    class Config:
        from_attributes = True


class DetectionGroupStatistics(BaseModel):
    """检测组统计信息模型"""
    group_id: int = Field(..., description="检测组ID")
    group_name: str = Field(..., description="检测组名称")
    total_detections: int = Field(..., description="总检测次数")
    jam_detections: int = Field(..., description="卡料次数")
    success_rate: float = Field(..., description="成功率")


class DieCasterStatistics(BaseModel):
    """压铸机统计信息模型"""
    die_caster_id: int = Field(..., description="压铸机ID")
    die_caster_name: str = Field(..., description="压铸机名称")
    total_detections: int = Field(..., description="总检测次数")
    jam_detections: int = Field(..., description="卡料次数")
    success_rate: float = Field(..., description="成功率")
    detection_groups: List[DetectionGroupStatistics] = Field(default_factory=list, description="检测组统计")


class TemplateStatistics(BaseModel):
    """检测模板统计信息模型"""
    template_id: int = Field(..., description="模板ID")
    template_name: str = Field(..., description="模板名称")
    total_detections: int = Field(..., description="总检测次数")
    jam_detections: int = Field(..., description="卡料次数")
    success_rate: float = Field(..., description="成功率")
    die_casters: List[DieCasterStatistics] = Field(default_factory=list, description="压铸机统计")


class DailyDetectionStatistics(BaseModel):
    """每日检测统计信息模型"""
    date: str = Field(..., description="日期 (YYYY-MM-DD)")
    templates: List[TemplateStatistics] = Field(default_factory=list, description="检测模板统计")
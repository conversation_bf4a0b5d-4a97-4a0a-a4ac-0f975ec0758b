<template>
  <div class="theme-toggle">
    <el-switch
      v-model="isDarkMode"
      inline-prompt
      :active-icon="Moon"
      :inactive-icon="Sunny"
      @change="toggleTheme"
      class="theme-switch"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { <PERSON>, <PERSON> } from '@element-plus/icons-vue'

const isDarkMode = ref(false)

// 切换主题
const toggleTheme = (value: boolean) => {
  if (value) {
    document.documentElement.classList.add('dark-theme')
    document.body.setAttribute('class', 'dark-theme')
    localStorage.setItem('theme', 'dark')
  } else {
    document.documentElement.classList.remove('dark-theme')
    document.body.removeAttribute('class')
    localStorage.setItem('theme', 'light')
  }
  
  // 触发自定义事件，通知其他组件主题已更改
  window.dispatchEvent(new CustomEvent('themeChange'))
}

// 初始化主题
onMounted(() => {
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme === 'dark') {
    isDarkMode.value = true
    document.documentElement.classList.add('dark-theme')
    document.body.setAttribute('class', 'dark-theme')
  } else {
    isDarkMode.value = false
    document.documentElement.classList.remove('dark-theme')
    document.body.removeAttribute('class')
  }
})
</script>

<style scoped>
.theme-toggle {
  cursor: pointer;
  margin-right: 8px;
}

.theme-switch :deep(.el-switch__core) {
  background-color: var(--bg-color-soft);
  border-color: var(--border-color);
}

.theme-switch :deep(.el-switch__core .el-switch__action) {
  background-color: var(--text-color-mute);
}

.theme-switch.is-checked :deep(.el-switch__core) {
  background-color: var(--primary-color-light);
  border-color: var(--primary-color);
}

.theme-switch.is-checked :deep(.el-switch__core .el-switch__action) {
  background-color: var(--primary-color);
}

.theme-switch :deep(.el-switch__inner .is-icon) {
  color: var(--text-color);
}
</style> 
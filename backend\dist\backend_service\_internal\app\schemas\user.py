from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr


# 共享属性
class UserBase(BaseModel):
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = True
    is_superuser: Optional[bool] = False
    username: Optional[str] = None
    role: Optional[str] = "operator"


# 创建时需要的属性
class UserCreate(UserBase):
    email: EmailStr
    username: str
    password: str


# 更新时可以更新的属性
class UserUpdate(UserBase):
    password: Optional[str] = None


# API响应中包含的属性
class UserInDBBase(UserBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


# 返回给API的用户信息
class User(UserInDBBase):
    pass


# 存储在DB中的用户信息（包含哈希密码）
class UserInDB(UserInDBBase):
    hashed_password: str
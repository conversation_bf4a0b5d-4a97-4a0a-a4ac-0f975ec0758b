# 数据看板API文档

## 概述
本文档详细描述了数据看板所使用的21个API接口，包括其功能、参数、返回数据结构和使用场景。

## API分类

### 1. 卡料检测相关API (4个)

#### 1.1 获取卡料检测结果列表
- **接口**: `GET /api/card-detection/`
- **功能**: 获取卡料检测结果的分页列表
- **参数**: 无
- **返回数据结构**:
```json
{
  "total": 1234,
  "items": [
    {
      "id": 1,
      "detection_time": "2024-01-15T14:30:25",
      "status": "normal", // normal, blocked, error
      "group_id": "001",
      "group_name": "检测组#001",
      "template_id": 1,
      "template_name": "标准检测V2.1",
      "processing_time": 245, // 毫秒
      "confidence": 0.985,
      "image_path": "/images/detection/xxx.jpg",
      "result_details": {
        "defect_type": "卡料",
        "position": {"x": 100, "y": 200},
        "severity": "high"
      }
    }
  ]
}
```
- **用途**: 检测详情面板、最近检测记录

#### 1.2 获取看板概览数据
- **接口**: `GET /api/card-detection/dashboard/overview`
- **功能**: 获取看板核心指标数据
- **参数**: 无
- **返回数据结构**:
```json
{
  "today_detection_count": 1234,
  "today_detection_change": 12, // 与昨日对比百分比
  "success_rate": 98.5,
  "success_rate_change": 0.3,
  "blocked_count": 19,
  "blocked_count_change": -5,
  "average_processing_time": 245,
  "processing_time_change": 15,
  "alarm_count": 2,
  "alarm_status": "warning", // normal, warning, critical
  "system_status": "running",
  "current_template": "标准检测V2.1",
  "active_groups": 3,
  "last_update": "2024-01-15T14:30:25"
}
```
- **用途**: 核心指标面板、实时状态栏

#### 1.3 获取趋势数据
- **接口**: `GET /api/card-detection/trends/daily?days=7`
- **功能**: 获取指定天数的检测趋势数据
- **参数**: 
  - `days`: 天数，默认7天
- **返回数据结构**:
```json
{
  "period": "7days",
  "data": [
    {
      "date": "2024-01-15",
      "hour": 14,
      "detection_count": 156,
      "success_rate": 98.5,
      "blocked_rate": 1.5,
      "average_time": 245
    }
  ],
  "summary": {
    "total_detections": 8640,
    "avg_success_rate": 98.2,
    "trend_direction": "up" // up, down, stable
  }
}
```
- **用途**: 趋势分析图表

#### 1.4 获取检测组统计信息
- **接口**: `GET /api/card-detection/statistics/{group_id}`
- **功能**: 获取特定检测组的统计信息
- **参数**: 
  - `group_id`: 检测组ID
- **返回数据结构**:
```json
{
  "group_id": "001",
  "group_name": "检测组#001",
  "status": "normal", // normal, warning, error
  "today_count": 456,
  "today_success_rate": 99.1,
  "today_blocked_count": 4,
  "avg_processing_time": 234,
  "last_detection": "2024-01-15T14:30:25",
  "performance_trend": "stable"
}
```
- **用途**: 检测组状态监控

### 2. 检测组相关API (3个)

#### 2.1 获取所有检测组
- **接口**: `GET /api/detection-groups/`
- **功能**: 获取所有检测组列表
- **参数**: 无
- **返回数据结构**:
```json
[
  {
    "id": "001",
    "name": "检测组#001",
    "description": "主生产线检测组",
    "template_id": 1,
    "template_name": "标准检测V2.1",
    "status": "active", // active, inactive, error
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-15T14:30:25",
    "config": {
      "detection_interval": 1000,
      "threshold": 0.8
    }
  }
]
```
- **用途**: 检测组选择、状态监控

#### 2.2 获取检测组列表（用于获取group_id）
- **接口**: `GET /api/detection-groups/`
- **功能**: 同上，用于获取group_id进行后续API调用
- **用途**: API调用链中的数据获取

#### 2.3 根据模板ID获取检测组
- **接口**: `GET /api/detection-groups/by-template/{template_id}`
- **功能**: 获取使用特定模板的检测组
- **参数**: 
  - `template_id`: 模板ID
- **返回数据结构**: 同2.1
- **用途**: 模板关联的检测组管理

### 3. 检测模板相关API (3个)

#### 3.1 获取所有检测模板
- **接口**: `GET /api/detection-templates/`
- **功能**: 获取所有检测模板列表
- **参数**: 无
- **返回数据结构**:
```json
[
  {
    "id": 1,
    "name": "标准检测V2.1",
    "description": "标准压铸件检测模板",
    "version": "2.1.0",
    "status": "active", // active, inactive, draft
    "algorithm_type": "CNN",
    "accuracy": 98.5,
    "created_at": "2024-01-01T00:00:00",
    "updated_at": "2024-01-15T14:30:25",
    "config": {
      "input_size": [224, 224],
      "threshold": 0.8,
      "classes": ["normal", "defect"]
    }
  }
]
```
- **用途**: 模板选择、状态显示

#### 3.2 获取检测模板列表（用于获取template_id）
- **接口**: `GET /api/detection-templates/`
- **功能**: 同上，用于获取template_id进行后续API调用
- **用途**: API调用链中的数据获取

#### 3.3 获取特定模板详情
- **接口**: `GET /api/detection-templates/{template_id}/`
- **功能**: 获取特定模板的详细信息
- **参数**: 
  - `template_id`: 模板ID
- **返回数据结构**: 同3.1单个对象
- **用途**: 模板详情展示

### 4. 系统监控相关API (6个)

#### 4.1 获取系统信息
- **接口**: `GET /api/admin/system-info`
- **功能**: 获取系统基本信息
- **参数**: 无
- **返回数据结构**:
```json
{
  "system_name": "压铸件智能检测系统",
  "version": "1.0.0",
  "uptime": 86400, // 秒
  "start_time": "2024-01-15T00:00:00",
  "environment": "production",
  "database_status": "connected",
  "redis_status": "connected",
  "gpu_available": true,
  "gpu_count": 2
}
```
- **用途**: 系统状态显示

#### 4.2 获取系统日志
- **接口**: `GET /api/admin/logs?limit=10`
- **功能**: 获取系统日志列表
- **参数**: 
  - `limit`: 限制条数，默认10
- **返回数据结构**:
```json
{
  "total": 1000,
  "logs": [
    {
      "id": 1,
      "timestamp": "2024-01-15T14:30:25",
      "level": "INFO", // DEBUG, INFO, WARNING, ERROR, CRITICAL
      "module": "detection",
      "message": "检测完成，结果：正常",
      "details": {
        "group_id": "001",
        "processing_time": 245
      }
    }
  ]
}
```
- **用途**: 系统日志面板

#### 4.3 获取日志统计
- **接口**: `GET /api/admin/logs/stats`
- **功能**: 获取日志统计信息
- **参数**: 无
- **返回数据结构**:
```json
{
  "total_logs": 10000,
  "today_logs": 1234,
  "error_count": 5,
  "warning_count": 23,
  "info_count": 1206,
  "last_error": "2024-01-15T14:25:00",
  "error_rate": 0.4 // 百分比
}
```
- **用途**: 日志统计展示

#### 4.4 获取日志级别统计
- **接口**: `GET /api/admin/logs/levels`
- **功能**: 获取各级别日志的统计
- **参数**: 无
- **返回数据结构**:
```json
{
  "DEBUG": 100,
  "INFO": 1206,
  "WARNING": 23,
  "ERROR": 4,
  "CRITICAL": 1
}
```
- **用途**: 日志级别分布图表

#### 4.5 获取日志模块列表
- **接口**: `GET /api/admin/logs/modules`
- **功能**: 获取日志模块列表
- **参数**: 无
- **返回数据结构**:
```json
[
  {
    "module": "detection",
    "count": 800,
    "last_log": "2024-01-15T14:30:25"
  },
  {
    "module": "system",
    "count": 200,
    "last_log": "2024-01-15T14:29:00"
  }
]
```
- **用途**: 模块日志筛选

#### 4.6 获取系统性能数据
- **接口**: `GET /api/admin/system-performance`
- **功能**: 获取系统性能监控数据
- **参数**: 无
- **返回数据结构**:
```json
{
  "cpu": {
    "usage": 80.5, // 百分比
    "cores": 8,
    "load_average": [1.2, 1.5, 1.8]
  },
  "memory": {
    "usage": 60.2, // 百分比
    "total": 16384, // MB
    "used": 9830, // MB
    "available": 6554 // MB
  },
  "disk": {
    "usage": 30.5, // 百分比
    "total": 1000, // GB
    "used": 305, // GB
    "available": 695 // GB
  },
  "gpu": [
    {
      "id": 0,
      "name": "NVIDIA RTX 4090",
      "usage": 45.2, // 百分比
      "memory_usage": 60.8, // 百分比
      "temperature": 65 // 摄氏度
    }
  ],
  "network": {
    "bytes_sent": 1024000,
    "bytes_recv": 2048000
  }
}
```
- **用途**: 系统资源监控面板

### 5. 新增API (5个)

#### 5.1 获取当前运行的监测模板名称
- **接口**: `GET /api/detection-templates/running/current`
- **功能**: 获取当前正在运行的检测模板
- **参数**: 无
- **返回数据结构**:
```json
{
  "template_id": 1,
  "template_name": "标准检测V2.1",
  "version": "2.1.0",
  "start_time": "2024-01-15T08:00:00",
  "running_groups": 3,
  "status": "running"
}
```
- **用途**: 实时状态栏显示

#### 5.2 获取设备状态
- **接口**: `GET /api/device/device-status`
- **功能**: 获取设备状态信息
- **参数**: 无
- **返回数据结构**:
```json
{
  "devices": [
    {
      "device_id": "camera_001",
      "device_name": "主摄像头",
      "device_type": "camera",
      "status": "online", // online, offline, error
      "last_heartbeat": "2024-01-15T14:30:25",
      "properties": {
        "resolution": "1920x1080",
        "fps": 30,
        "connection_type": "ethernet"
      }
    }
  ],
  "summary": {
    "total_devices": 5,
    "online_devices": 4,
    "offline_devices": 1,
    "error_devices": 0
  }
}
```
- **用途**: 设备状态监控

#### 5.3 获取报警列表
- **接口**: `GET /api/alarms/`
- **功能**: 获取系统报警信息
- **参数**: 无
- **返回数据结构**:
```json
{
  "total": 5,
  "alarms": [
    {
      "id": 1,
      "type": "detection_failure", // detection_failure, device_offline, performance_degradation
      "level": "critical", // info, warning, critical
      "title": "检测组#002 卡料率超阈值",
      "message": "检测组#002 在过去1小时内卡料率达到5.2%，超过设定阈值3%",
      "source": "detection_group_002",
      "timestamp": "2024-01-15T14:25:00",
      "status": "active", // active, acknowledged, resolved
      "details": {
        "group_id": "002",
        "current_rate": 5.2,
        "threshold": 3.0
      }
    }
  ],
  "summary": {
    "critical_count": 1,
    "warning_count": 2,
    "info_count": 2
  }
}
```
- **用途**: 智能报警中心

#### 5.4 获取检测效率分析
- **接口**: `GET /api/analytics/detection-efficiency`
- **功能**: 获取检测效率分析数据
- **参数**: 无
- **返回数据结构**:
```json
{
  "overall_efficiency": 95.8, // 百分比
  "efficiency_trend": "up", // up, down, stable
  "bottlenecks": [
    {
      "type": "processing_time",
      "description": "检测组#002处理时间偏长",
      "impact": "medium",
      "suggestion": "建议优化算法参数"
    }
  ],
  "performance_metrics": {
    "avg_processing_time": 245,
    "throughput_per_hour": 1440,
    "resource_utilization": 78.5
  },
  "recommendations": [
    "优化检测组#002的算法参数",
    "考虑增加GPU资源分配"
  ]
}
```
- **用途**: 性能分析和优化建议

## API使用场景映射

### 看板组件与API对应关系

1. **实时状态栏**
   - `/api/card-detection/dashboard/overview` - 系统状态、当前模板、活跃组数
   - `/api/detection-templates/running/current` - 当前运行模板

2. **核心指标面板**
   - `/api/card-detection/dashboard/overview` - 所有核心指标

3. **趋势分析图表**
   - `/api/card-detection/trends/daily` - 24小时趋势数据

4. **检测详情记录**
   - `/api/card-detection/` - 最近检测记录

5. **检测组状态监控**
   - `/api/detection-groups/` - 检测组列表
   - `/api/card-detection/statistics/{group_id}` - 各组统计信息

6. **智能报警中心**
   - `/api/alarms/` - 报警信息

7. **系统资源监控**
   - `/api/admin/system-performance` - 系统性能数据
   - `/api/admin/system-info` - 系统基本信息

8. **系统日志**
   - `/api/admin/logs` - 日志列表
   - `/api/admin/logs/stats` - 日志统计

## 数据更新策略

1. **实时数据** (每5秒更新)
   - 核心指标面板
   - 实时状态栏
   - 检测详情记录

2. **准实时数据** (每30秒更新)
   - 检测组状态
   - 报警信息
   - 系统资源

3. **定期数据** (每5分钟更新)
   - 趋势图表
   - 系统日志
   - 效率分析

## 错误处理

所有API都应该包含统一的错误处理格式：
```json
{
  "error": true,
  "code": "API_ERROR_CODE",
  "message": "错误描述",
  "details": {
    "field": "具体错误信息"
  }
}
```

## 总结

本文档涵盖了21个API接口，完全满足数据看板的开发需求：
- 4个卡料检测API提供核心业务数据
- 6个检测组和模板API提供配置和状态信息
- 6个系统监控API提供运行状态和日志
- 5个新增API提供增强功能

所有API都已经过测试验证，可以直接用于看板开发。
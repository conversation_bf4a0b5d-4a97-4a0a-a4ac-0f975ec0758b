/**
 * ROI参数常量定义
 * 统一的ROI参数格式，所有组件都必须使用这些常量
 */

// ROI类型常量
export const ROI_TYPES = {
  YAZHU: 'yazhu',
  PAILIAO: 'pailiao'
} as const

// 检测器类型常量
export const DETECTOR_TYPES = {
  DIRECTION: 'direction',
  MOTION: 'motion'
} as const

// 算法类型常量
export const ALGORITHM_TYPES = {
  BACKGROUND_SUBTRACTION: 'background_subtraction',
  FRAME_DIFFERENCE: 'frame_difference'
} as const

// yazhu类型ROI的默认参数
export const YAZHU_DEFAULT_PARAMS = {
  type: DETECTOR_TYPES.DIRECTION,
  motion_detection: {
    enabled: true,
    backgroundUpdateRate: 0.01,
    motionThreshold: 50,
    minArea: 500
  },
  direction_detection: {
    consecutiveDetectionThreshold: 3,
    minDisplacement: 2,
    maxPatience: 3
  }
}

// pailiao类型ROI的默认参数
export const PAILIAO_DEFAULT_PARAMS = {
  type: DETECTOR_TYPES.MOTION,
  motion_detection: {
    algorithm: ALGORITHM_TYPES.FRAME_DIFFERENCE,
    // 背景减除法参数
    learningRate: 0.01,
    detectionThreshold: 50,
    shadowRemoval: 0.5,
    // 帧差法参数
    threshold: 30,
    frameInterval: 2,
    // 通用参数
    minArea: 300
  }
}

// 参数验证函数
export function validateROIParams(roiType: string, params: any): boolean {
  if (roiType === ROI_TYPES.YAZHU) {
    return (
      params.type === DETECTOR_TYPES.DIRECTION &&
      params.motion_detection &&
      params.direction_detection &&
      typeof params.motion_detection.enabled === 'boolean' &&
      typeof params.motion_detection.backgroundUpdateRate === 'number' &&
      typeof params.motion_detection.motionThreshold === 'number' &&
      typeof params.motion_detection.minArea === 'number' &&
      typeof params.direction_detection.consecutiveDetectionThreshold === 'number' &&
      typeof params.direction_detection.minDisplacement === 'number' &&
      typeof params.direction_detection.maxPatience === 'number'
    )
  } else if (roiType === ROI_TYPES.PAILIAO) {
    return (
      params.type === DETECTOR_TYPES.MOTION &&
      params.motion_detection &&
      typeof params.motion_detection.algorithm === 'string' &&
      typeof params.motion_detection.minArea === 'number'
    )
  }
  return false
}

// 获取默认参数
export function getDefaultParams(roiType: string) {
  switch (roiType) {
    case ROI_TYPES.YAZHU:
      return JSON.parse(JSON.stringify(YAZHU_DEFAULT_PARAMS)) // 深拷贝
    case ROI_TYPES.PAILIAO:
      return JSON.parse(JSON.stringify(PAILIAO_DEFAULT_PARAMS)) // 深拷贝
    default:
      throw new Error(`Unknown ROI type: ${roiType}`)
  }
}

// 参数转换函数：从旧格式转换为新格式
export function convertLegacyParams(roiType: string, legacyParams: any) {
  if (roiType === ROI_TYPES.YAZHU) {
    // 处理yazhu类型的旧格式参数
    if (legacyParams.前置背景检测 || legacyParams.后置方向检测) {
      return {
        type: DETECTOR_TYPES.DIRECTION,
        motion_detection: {
          enabled: legacyParams.前置背景检测?.enabled ?? true,
          backgroundUpdateRate: legacyParams.前置背景检测?.backgroundUpdateRate ?? 0.01,
          motionThreshold: legacyParams.前置背景检测?.motionThreshold ?? 50,
          minArea: legacyParams.前置背景检测?.minArea ?? 500
        },
        direction_detection: {
          consecutiveDetectionThreshold: legacyParams.后置方向检测?.consecutiveDetectionThreshold ?? 3,
          minDisplacement: legacyParams.后置方向检测?.minDisplacement ?? 2,
          maxPatience: legacyParams.后置方向检测?.maxPatience ?? 3
        }
      }
    }
    // 处理扁平化格式
    if (legacyParams.type === 'direction') {
      return {
        type: DETECTOR_TYPES.DIRECTION,
        motion_detection: {
          enabled: legacyParams.enabled ?? true,
          backgroundUpdateRate: legacyParams.backgroundUpdateRate ?? 0.01,
          motionThreshold: legacyParams.motionThreshold ?? 50,
          minArea: legacyParams.minArea ?? 500
        },
        direction_detection: {
          consecutiveDetectionThreshold: legacyParams.consecutiveDetectionThreshold ?? 3,
          minDisplacement: legacyParams.minDisplacement ?? 2,
          maxPatience: legacyParams.maxPatience ?? 3
        }
      }
    }
  } else if (roiType === ROI_TYPES.PAILIAO) {
    // 处理pailiao类型的旧格式参数
    if (legacyParams.运动检测) {
      return {
        type: DETECTOR_TYPES.MOTION,
        motion_detection: {
          algorithm: legacyParams.运动检测.algorithm === '背景减除法' 
            ? ALGORITHM_TYPES.BACKGROUND_SUBTRACTION 
            : ALGORITHM_TYPES.FRAME_DIFFERENCE,
          learningRate: legacyParams.运动检测.learningRate ?? 0.01,
          detectionThreshold: legacyParams.运动检测.detectionThreshold ?? 50,
          shadowRemoval: legacyParams.运动检测.shadowRemoval ?? 0.5,
          threshold: legacyParams.运动检测.threshold ?? 30,
          frameInterval: legacyParams.运动检测.frameInterval ?? 2,
          minArea: legacyParams.运动检测.minArea ?? 300
        }
      }
    }
    // 处理扁平化格式
    if (legacyParams.type === 'motion') {
      return {
        type: DETECTOR_TYPES.MOTION,
        motion_detection: {
          algorithm: legacyParams.algorithm === '背景减除法' 
            ? ALGORITHM_TYPES.BACKGROUND_SUBTRACTION 
            : ALGORITHM_TYPES.FRAME_DIFFERENCE,
          learningRate: legacyParams.learningRate ?? 0.01,
          detectionThreshold: legacyParams.detectionThreshold ?? 50,
          shadowRemoval: legacyParams.shadowRemoval ?? 0.5,
          threshold: legacyParams.threshold ?? 30,
          frameInterval: legacyParams.frameInterval ?? 2,
          minArea: legacyParams.minArea ?? 300
        }
      }
    }
  }
  
  // 如果无法转换，返回默认参数
  return getDefaultParams(roiType)
}

from typing import Any, List, Dict

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.models import DetectionGroup
from app.schemas.die_caster import DetectionGroupCreate, DetectionGroupUpdate, DetectionGroup as DetectionGroupSchema

router = APIRouter()


@router.get("/", response_model=List[DetectionGroupSchema])
def read_detection_groups(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取所有检测组
    """
    detection_groups = db.query(DetectionGroup).offset(skip).limit(limit).all()
    return detection_groups


@router.get("/by-template/{template_id}", response_model=List[DetectionGroupSchema])
def read_detection_groups_by_template(
    *,
    db: Session = Depends(deps.get_db),
    template_id: int,
) -> Any:
    """
    根据模板ID获取关联的检测组
    """
    detection_groups = db.query(DetectionGroup).filter(DetectionGroup.template_id == template_id).all()
    return detection_groups


@router.post("/", response_model=DetectionGroupSchema)
def create_detection_group(
    *,
    db: Session = Depends(deps.get_db),
    detection_group_in: DetectionGroupCreate,
) -> Any:
    """
    创建新检测组
    """
    detection_group = DetectionGroup(
        name=detection_group_in.name,
        template_id=detection_group_in.template_id,
        die_caster_id=detection_group_in.die_caster_id,
        video_source_id=detection_group_in.video_source_id,
        config_json=detection_group_in.config_json,
        status=detection_group_in.status,
    )
    db.add(detection_group)
    db.commit()
    db.refresh(detection_group)
    return detection_group


@router.put("/{detection_group_id}", response_model=DetectionGroupSchema)
@router.put("/{detection_group_id}/", response_model=DetectionGroupSchema)
def update_detection_group(
    *,
    db: Session = Depends(deps.get_db),
    detection_group_id: int,
    detection_group_in: DetectionGroupUpdate,
) -> Any:
    """
    更新检测组
    """
    detection_group = db.query(DetectionGroup).filter(DetectionGroup.id == detection_group_id).first()
    if not detection_group:
        raise HTTPException(status_code=404, detail="检测组不存在")
    
    update_data = detection_group_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(detection_group, field, value)
    
    db.add(detection_group)
    db.commit()
    db.refresh(detection_group)
    return detection_group


@router.get("/{detection_group_id}", response_model=DetectionGroupSchema)
@router.get("/{detection_group_id}/", response_model=DetectionGroupSchema)
def read_detection_group(
    *,
    db: Session = Depends(deps.get_db),
    detection_group_id: int,
) -> Any:
    """
    获取特定检测组
    """
    detection_group = db.query(DetectionGroup).filter(DetectionGroup.id == detection_group_id).first()
    if not detection_group:
        raise HTTPException(status_code=404, detail="检测组不存在")
    return detection_group


@router.delete("/{detection_group_id}", response_model=DetectionGroupSchema)
@router.delete("/{detection_group_id}/", response_model=DetectionGroupSchema)
def delete_detection_group(
    *,
    db: Session = Depends(deps.get_db),
    detection_group_id: int,
) -> Any:
    """
    删除检测组
    """
    detection_group = db.query(DetectionGroup).filter(DetectionGroup.id == detection_group_id).first()
    if not detection_group:
        raise HTTPException(status_code=404, detail="检测组不存在")
    db.delete(detection_group)
    db.commit()
    return detection_group

@router.post("/{detection_group_id}/start", response_model=DetectionGroupSchema)
@router.post("/{detection_group_id}/start/", response_model=DetectionGroupSchema)
def start_detection_group(
    *,
    db: Session = Depends(deps.get_db),
    detection_group_id: int,
) -> Any:
    """
    启动检测组
    """
    detection_group = db.query(DetectionGroup).filter(DetectionGroup.id == detection_group_id).first()
    if not detection_group:
        raise HTTPException(status_code=404, detail="检测组不存在")
    
    detection_group.status = "active"
    db.add(detection_group)
    db.commit()
    db.refresh(detection_group)
    return detection_group

@router.post("/{detection_group_id}/stop", response_model=DetectionGroupSchema)
@router.post("/{detection_group_id}/stop/", response_model=DetectionGroupSchema)
def stop_detection_group(
    *,
    db: Session = Depends(deps.get_db),
    detection_group_id: int,
) -> Any:
    """
    停止检测组
    """
    detection_group = db.query(DetectionGroup).filter(DetectionGroup.id == detection_group_id).first()
    if not detection_group:
        raise HTTPException(status_code=404, detail="检测组不存在")
    
    detection_group.status = "inactive"
    db.add(detection_group)
    db.commit()
    db.refresh(detection_group)
    return detection_group

@router.put("/{detection_group_id}/config", response_model=DetectionGroupSchema)
@router.put("/{detection_group_id}/config/", response_model=DetectionGroupSchema)
def update_detection_group_config(
    *,
    db: Session = Depends(deps.get_db),
    detection_group_id: int,
    config: Dict[str, Any],
) -> Any:
    """
    更新检测组配置
    """
    detection_group = db.query(DetectionGroup).filter(DetectionGroup.id == detection_group_id).first()
    if not detection_group:
        raise HTTPException(status_code=404, detail="检测组不存在")
    
    detection_group.config_json = config
    db.add(detection_group)
    db.commit()
    db.refresh(detection_group)
    return detection_group

@router.get("/{detection_group_id}/rois", response_model=List[Dict[str, Any]])
@router.get("/{detection_group_id}/rois/", response_model=List[Dict[str, Any]])
def get_detection_group_rois(
    *,
    db: Session = Depends(deps.get_db),
    detection_group_id: int,
) -> Any:
    """
    获取检测组的所有ROI
    """
    detection_group = db.query(DetectionGroup).filter(DetectionGroup.id == detection_group_id).first()
    if not detection_group:
        raise HTTPException(status_code=404, detail="检测组不存在")
    
    # 从config_json中提取ROI信息，如果没有则返回空列表
    rois = detection_group.config_json.get("rois", []) if detection_group.config_json else []
    return rois

@router.post("/{detection_group_id}/rois", response_model=Dict[str, Any])
@router.post("/{detection_group_id}/rois/", response_model=Dict[str, Any])
def create_roi(
    *,
    db: Session = Depends(deps.get_db),
    detection_group_id: int,
    roi_data: Dict[str, Any],
) -> Any:
    """
    为检测组创建新的ROI
    """
    detection_group = db.query(DetectionGroup).filter(DetectionGroup.id == detection_group_id).first()
    if not detection_group:
        raise HTTPException(status_code=404, detail="检测组不存在")
    
    # 确保config_json存在
    if not detection_group.config_json:
        detection_group.config_json = {}
    
    # 确保rois列表存在
    if "rois" not in detection_group.config_json:
        detection_group.config_json["rois"] = []
    
    # 添加新的ROI
    detection_group.config_json["rois"].append(roi_data)
    
    db.add(detection_group)
    db.commit()
    db.refresh(detection_group)
    
    return roi_data

@router.put("/{detection_group_id}/rois/{roi_id}", response_model=Dict[str, Any])
@router.put("/{detection_group_id}/rois/{roi_id}/", response_model=Dict[str, Any])
def update_roi(
    *,
    db: Session = Depends(deps.get_db),
    detection_group_id: int,
    roi_id: str,
    roi_data: Dict[str, Any],
) -> Any:
    """
    更新检测组中的ROI
    """
    detection_group = db.query(DetectionGroup).filter(DetectionGroup.id == detection_group_id).first()
    if not detection_group:
        raise HTTPException(status_code=404, detail="检测组不存在")
    
    # 确保config_json和rois存在
    if not detection_group.config_json or "rois" not in detection_group.config_json:
        raise HTTPException(status_code=404, detail="ROI不存在")
    
    # 查找并更新ROI
    roi_index = None
    for i, roi in enumerate(detection_group.config_json["rois"]):
        if roi.get("id") == roi_id:
            roi_index = i
            break
    
    if roi_index is None:
        raise HTTPException(status_code=404, detail="ROI不存在")
    
    detection_group.config_json["rois"][roi_index] = roi_data
    
    db.add(detection_group)
    db.commit()
    db.refresh(detection_group)
    
    return roi_data

@router.delete("/{detection_group_id}/rois/{roi_id}", response_model=Dict[str, Any])
@router.delete("/{detection_group_id}/rois/{roi_id}/", response_model=Dict[str, Any])
def delete_roi(
    *,
    db: Session = Depends(deps.get_db),
    detection_group_id: int,
    roi_id: str,
) -> Any:
    """
    删除检测组中的ROI
    """
    detection_group = db.query(DetectionGroup).filter(DetectionGroup.id == detection_group_id).first()
    if not detection_group:
        raise HTTPException(status_code=404, detail="检测组不存在")
    
    # 确保config_json和rois存在
    if not detection_group.config_json or "rois" not in detection_group.config_json:
        raise HTTPException(status_code=404, detail="ROI不存在")
    
    # 查找并删除ROI
    roi_index = None
    deleted_roi = None
    for i, roi in enumerate(detection_group.config_json["rois"]):
        if roi.get("id") == roi_id:
            roi_index = i
            deleted_roi = roi
            break
    
    if roi_index is None:
        raise HTTPException(status_code=404, detail="ROI不存在")
    
    detection_group.config_json["rois"].pop(roi_index)
    
    db.add(detection_group)
    db.commit()
    db.refresh(detection_group)
    
    return deleted_roi or {"id": roi_id, "status": "deleted"}
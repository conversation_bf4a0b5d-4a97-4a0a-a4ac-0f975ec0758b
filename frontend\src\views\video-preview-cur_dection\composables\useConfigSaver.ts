/**
 * 配置保存功能模块
 * 负责将ROI配置和全局设置保存到检测组的config_json字段
 */

import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 配置保存接口类型定义
interface SaveConfigData {
  roiIds: string[]  // ROI ID列表
  globalSettings: {
    delayTime: number
    pauseThreshold: number
    cooldownTime: number
    cardDelayTime: number
  }
}

// 认证头获取函数
const getAuthHeaders = () => {
  const token = localStorage.getItem('token')
  return token ? { 'Authorization': `Bearer ${token}` } : {}
}

export function useConfigSaver() {
  const isSaving = ref(false)
  
  /**
   * 保存配置到检测组
   * @param detectionGroupId 检测组ID
   * @param roiList ROI列表
   * @param globalSettings 全局设置
   * @param addOperationInfo 操作日志函数
   */
  const saveConfigToDetectionGroup = async (
    detectionGroupId: number,
    roiList: any[],
    globalSettings: any,
    addOperationInfo: (message: string) => void
  ) => {
    if (isSaving.value) {
      ElMessage.warning('正在保存中，请稍候...')
      return
    }
    
    try {
      isSaving.value = true
      addOperationInfo('[配置保存] 开始保存配置...')
      
      // 1. 提取ROI ID列表
      const roiIds = roiList.map(roi => roi.roi_id).filter(id => id)
      addOperationInfo(`[配置保存] 提取到 ${roiIds.length} 个ROI ID: ${roiIds.join(', ')}`)
      
      // 2. 准备全局设置数据
      const settingsData = {
        delayTime: globalSettings.delayTime || 5,
        pauseThreshold: globalSettings.pauseThreshold || 15,
        cooldownTime: globalSettings.cooldownTime || 3,
        cardDelayTime: globalSettings.cardDelayTime || 5
      }
      addOperationInfo(`[配置保存] 全局设置: ${JSON.stringify(settingsData)}`)
      
      // 3. 构建保存数据
      const configData: SaveConfigData = {
        roiIds,
        globalSettings: settingsData
      }
      
      addOperationInfo(`[配置保存] 正在保存到检测组 ${detectionGroupId}...`)
      
      // 4. 调用API保存配置
      const response = await fetch(`/api/detection-groups/${detectionGroupId}/config`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        },
        body: JSON.stringify(configData)
      })
      
      if (response.ok) {
        const result = await response.json()
        addOperationInfo('[配置保存] ✅ 配置已成功保存到检测组')
        ElMessage.success('配置保存成功！')
        
        // 返回保存结果
        return {
          success: true,
          data: result,
          savedConfig: configData
        }
      } else {
        const errorText = await response.text()
        let errorDetail = errorText
        try {
          const errorJson = JSON.parse(errorText)
          errorDetail = errorJson.detail || errorText
        } catch (e) {
          // 如果不是JSON格式，使用原始文本
        }
        
        addOperationInfo(`[配置保存] ❌ 保存失败: HTTP ${response.status} - ${errorDetail}`)
        ElMessage.error(`配置保存失败: ${errorDetail}`)
        
        return {
          success: false,
          error: errorDetail
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      addOperationInfo(`[配置保存] ❌ 保存异常: ${errorMessage}`)
      ElMessage.error(`配置保存异常: ${errorMessage}`)
      
      return {
        success: false,
        error: errorMessage
      }
    } finally {
      isSaving.value = false
    }
  }
  
  /**
   * 验证配置数据
   * @param roiList ROI列表
   * @param globalSettings 全局设置
   * @param detectionGroupId 检测组ID
   */
  const validateConfigData = (
    roiList: any[],
    globalSettings: any,
    detectionGroupId: number | null
  ): { isValid: boolean; message?: string } => {
    // 检查检测组ID
    if (!detectionGroupId) {
      return {
        isValid: false,
        message: '缺少检测组ID，无法保存配置'
      }
    }
    
    // 检查ROI列表
    if (!roiList || roiList.length === 0) {
      return {
        isValid: false,
        message: '当前没有ROI配置，无需保存'
      }
    }
    
    // 检查全局设置
    if (!globalSettings) {
      return {
        isValid: false,
        message: '缺少全局设置数据'
      }
    }
    
    // 验证ROI ID
    const invalidROIs = roiList.filter(roi => !roi.roi_id)
    if (invalidROIs.length > 0) {
      return {
        isValid: false,
        message: `发现 ${invalidROIs.length} 个ROI缺少ID，请检查ROI配置`
      }
    }
    
    return { isValid: true }
  }
  
  return {
    isSaving,
    saveConfigToDetectionGroup,
    validateConfigData
  }
}
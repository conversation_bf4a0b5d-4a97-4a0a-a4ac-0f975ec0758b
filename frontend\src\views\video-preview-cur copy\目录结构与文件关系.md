文件结构


graph TD
    %% 主组件
    Main["index.vue<br/>视频预览主入口"]
    
    %% 子组件
    VideoPlayer["VideoPlayer.vue<br/>视频播放器组件"]
    VideoSourceSelector["VideoSourceSelector.vue<br/>视频源选择器"]
    ROIToolbar["ROIToolbar.vue<br/>ROI绘制工具栏"]
    ROIManagement["ROIManagement.vue<br/>ROI列表管理"]
    AlgorithmConfig["AlgorithmConfig.vue<br/>算法配置"]
    OperationLog["OperationLog.vue<br/>操作日志显示"]
    
    %% 可复用逻辑
    WebSDK["useWebSDK.ts<br/>WebSDK视频播放逻辑"]
    ROIDrawer["useROIDrawer.ts<br/>ROI绘制逻辑"]
    MotionDetection["useMotionDetection.ts<br/>运动检测逻辑"]
    OpLog["useOperationLog.ts<br/>操作日志逻辑"]
    
    %% 外部依赖
    ROIDrawerJS["@/utils/roiDrawer.js<br/>ROI绘制工具类"]
    WebVideoCtrl["window.WebVideoCtrl<br/>WebSDK全局对象"]
    
    %% 主组件与子组件关系
    Main --> VideoPlayer
    Main --> VideoSourceSelector
    Main --> ROIToolbar
    Main --> ROIManagement
    Main --> AlgorithmConfig
    Main --> OperationLog
    
    %% 主组件与可复用逻辑关系
    Main --> WebSDK
    Main --> ROIDrawer
    Main --> MotionDetection
    Main --> OpLog
    
    %% 可复用逻辑与外部依赖关系
    ROIDrawer --> ROIDrawerJS
    WebSDK --> WebVideoCtrl
    
    %% 数据流关系
    VideoSourceSelector -- "视频源选择事件<br/>@source-change" --> Main
    VideoPlayer -- "视频尺寸变化事件<br/>@video-size-changed" --> Main
    VideoPlayer -- "ROI画布准备事件<br/>@roi-drawer-ready" --> Main
    
    Main -- "视频源数据<br/>:sources" --> VideoSourceSelector
    Main -- "视频播放状态<br/>:is-preview-active" --> VideoPlayer
    Main -- "ROI列表数据<br/>:roi-list" --> VideoPlayer
    Main -- "ROI列表数据<br/>:roi-list" --> ROIManagement
    Main -- "日志数据<br/>:logs" --> OperationLog
    
    %% 功能调用关系
    WebSDK -- "视频播放控制" --> VideoPlayer
    ROIDrawer -- "ROI绘制控制" --> VideoPlayer
    MotionDetection -- "运动检测结果" --> VideoPlayer
    
    %% 状态共享
    WebSDK -- "共享视频状态" --> Main
    ROIDrawer -- "共享ROI状态" --> Main
    MotionDetection -- "共享检测状态" --> Main
    OpLog -- "共享日志状态" --> Main
    
    %% 样式
    style Main fill:#f9f,stroke:#333,stroke-width:2px
    style WebSDK fill:#bbf,stroke:#333,stroke-width:1px
    style ROIDrawer fill:#bbf,stroke:#333,stroke-width:1px
    style MotionDetection fill:#bbf,stroke:#333,stroke-width:1px
    style OpLog fill:#bbf,stroke:#333,stroke-width:1px



数据流


sequenceDiagram
    %% 参与者定义
    participant User as 用户
    participant Main as index.vue
    participant VideoSource as VideoSourceSelector
    participant VideoPlayer as VideoPlayer.vue
    participant WebSDK as useWebSDK.ts
    participant ROIDrawer as useROIDrawer.ts
    participant ROIToolbar as ROIToolbar.vue
    participant ROIManagement as ROIManagement.vue
    
    %% 初始化流程
    User->>Main: 访问视频预览页面
    Main->>WebSDK: 初始化WebSDK
    WebSDK-->>Main: WebSDK初始化完成
    Main->>VideoSource: 获取视频源列表
    VideoSource-->>Main: 显示可用视频源
    
    %% 视频预览流程
    User->>VideoSource: 选择视频源
    VideoSource->>Main: 触发source-change事件
    Main->>WebSDK: 调用onVideoSourceChange
    WebSDK->>WebSDK: 登录设备
    WebSDK->>WebSDK: 获取通道信息
    WebSDK->>WebSDK: 开始预览
    WebSDK-->>VideoPlayer: 视频开始播放
    VideoPlayer-->>Main: 触发video-size-changed事件
    Main->>ROIDrawer: 更新ROI画布尺寸
    
    %% ROI绘制流程
    User->>ROIToolbar: 选择ROI属性(排料口/压铸机)
    ROIToolbar->>Main: 触发attribute-change事件
    Main->>ROIDrawer: 调用onROIAttributeChange
    ROIDrawer-->>VideoPlayer: 更新ROI绘制状态
    
    User->>ROIToolbar: 点击"开始绘制"
    ROIToolbar->>Main: 触发toggle-draw事件
    Main->>ROIDrawer: 调用toggleDrawMode
    ROIDrawer-->>VideoPlayer: 启用ROI绘制模式
    
    User->>VideoPlayer: 在视频上绘制ROI区域
    VideoPlayer->>ROIDrawer: 捕获鼠标事件
    ROIDrawer->>ROIDrawer: 创建新ROI
    ROIDrawer-->>Main: 更新roiList
    Main-->>ROIManagement: 显示新ROI在列表中
    
    %% ROI管理流程
    User->>ROIManagement: 点击ROI项(高亮/删除)
    ROIManagement->>Main: 触发highlight/delete事件
    Main->>ROIDrawer: 调用toggleROIHighlight/deleteROI
    ROIDrawer-->>VideoPlayer: 更新ROI显示




## 目录结构与文件关系

### 主要文件组织
- **index.vue**: 视频预览页面的主入口组件
- **components/**: 包含所有UI组件
- **composables/**: 包含所有可复用的业务逻辑
- **styles/**: 包含CSS样式文件

### 组件层次结构
```
index.vue (主容器)
├── VideoSourceSelector.vue (视频源选择)
├── VideoPlayer.vue (视频播放区域)
├── ROIToolbar.vue (ROI绘制工具栏)
├── ROIManagement.vue (ROI列表管理)
├── AlgorithmConfig.vue (算法配置)
└── OperationLog.vue (操作日志显示)
```

## 数据流关系

### 主要数据流向
1. **视频源数据流**:
   - `useWebSDK` → `index.vue` → `VideoSourceSelector.vue`
   - 用户选择视频源 → `VideoSourceSelector` 触发事件 → `index.vue` 处理 → `useWebSDK` 执行连接

2. **视频播放状态流**:
   - `useWebSDK` → `index.vue` → `VideoPlayer.vue`
   - 视频播放状态变化 → 通过props传递给各组件

3. **ROI绘制数据流**:
   - `useROIDrawer` → `index.vue` → `VideoPlayer.vue`(绘制) / `ROIManagement.vue`(显示列表)
   - 用户通过`ROIToolbar`选择属性和操作 → `index.vue`处理 → `useROIDrawer`执行逻辑 → 更新UI

4. **操作日志流**:
   - 所有操作 → `useOperationLog.addOperationInfo` → `index.vue` → `OperationLog.vue`

## 核心功能模块

### 1. WebSDK视频播放模块 (useWebSDK.ts)
- 负责初始化WebSDK插件
- 管理视频源列表和连接状态
- 处理设备登录、通道选择和视频预览

### 2. ROI绘制模块 (useROIDrawer.ts)
- 管理ROI绘制器实例
- 处理ROI的创建、编辑、删除和高亮
- 提供ROI数据的导入导出功能

### 3. 运动检测模块 (useMotionDetection.ts)
- 连接后端WebSocket获取检测结果
- 管理运动检测的开启和关闭
- 提供检测结果数据给VideoPlayer显示

### 4. 操作日志模块 (useOperationLog.ts)
- 记录系统操作日志
- 提供日志的添加和清除功能

## 组件间通信方式

1. **Props向下传递**:
   - 从父组件向子组件传递数据和状态

2. **事件向上传递**:
   - 子组件通过自定义事件向父组件传递用户操作

3. **Composables共享状态**:
   - 通过Vue Composition API的可复用逻辑共享状态和方法

## 关键交互流程

1. **视频预览流程**:
   - 用户选择视频源 → 登录设备 → 获取通道 → 开始预览 → 视频显示
   - 视频尺寸变化 → 更新ROI画布尺寸

2. **ROI绘制流程**:
   - 选择ROI属性(排料口/压铸机) → 启用绘制模式 → 在视频上绘制 → ROI添加到列表

3. **ROI管理流程**:
   - 在列表中选择ROI → 高亮/编辑/删除 → 更新视频上的显示

4. **运动检测流程**:
   - 开启运动检测 → 连接WebSocket → 接收检测结果 → 在视频上显示

这种架构设计实现了关注点分离，使UI组件专注于渲染和用户交互，而业务逻辑则封装在composables中，便于复用和测试。同时，通过事件和props的组合使用，实现了组件间的松耦合通信。
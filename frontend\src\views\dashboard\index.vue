<template>
  <div class="dashboard-container">
    <!-- 页面标题 -->
    <div class="dashboard-header">
      <h1 class="dashboard-title">压铸件智能检测系统 - 监控看板</h1>
      <div class="header-actions">
        <el-button type="primary" @click="refreshAll">
          <el-icon><refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="toggleFullscreen">
          <el-icon><full-screen /></el-icon>
          全屏显示
        </el-button>
      </div>
    </div>

    <!-- 系统概览卡片 -->
    <div class="overview-section">
      <SystemOverview :overview-data="overviewData" />
    </div>

    <!-- 检测信息轮播 - 重要信息优先展示 -->
    <div class="detection-carousel-section">
      <DetectionCarousel />
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧区域 -->
      <div class="left-panel">
        <!-- 设备状态监控 -->
        <div class="panel-section">
          <DeviceStatusPanel :devices="deviceList" @device-click="handleDeviceClick" />
        </div>

        <!-- 检测模板状态 -->
        <div class="panel-section">
          <TemplateStatusPanel :templates="templateList" />
        </div>
      </div>

      <!-- 中间区域 -->
      <div class="center-panel">
        <!-- 实时检测状态 -->
        <div class="panel-section">
          <RealtimeDetectionPanel
            :detection-groups="detectionGroups"
            @group-select="handleGroupSelect"
          />
        </div>

        <!-- 视频预览网格 -->
        <div class="panel-section">
          <VideoGridPanel
            :video-sources="videoSources"
            :selected-group="selectedGroup"
          />
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-panel">
        <!-- 报警信息 -->
        <div class="panel-section">
          <AlarmPanel :alarms="alarmList" @alarm-handle="handleAlarm" />
        </div>

        <!-- 性能监控 -->
        <div class="panel-section">
          <PerformancePanel :performance-data="performanceData" />
        </div>

        <!-- 系统日志 -->
        <div class="panel-section">
          <SystemLogPanel :logs="systemLogs" />
        </div>
      </div>
    </div>

    <!-- 底部统计区域 -->
    <div class="bottom-section">
      <StatisticsPanel :statistics="statisticsData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, FullScreen } from '@element-plus/icons-vue'

// 导入组件
import SystemOverview from './components/SystemOverview.vue'
import DetectionCarousel from './components/DetectionCarousel.vue'
import DeviceStatusPanel from './components/DeviceStatusPanel.vue'
import TemplateStatusPanel from './components/TemplateStatusPanel.vue'
import RealtimeDetectionPanel from './components/RealtimeDetectionPanel.vue'
import VideoGridPanel from './components/VideoGridPanel.vue'
import AlarmPanel from './components/AlarmPanel.vue'
import PerformancePanel from './components/PerformancePanel.vue'
import SystemLogPanel from './components/SystemLogPanel.vue'
import StatisticsPanel from './components/StatisticsPanel.vue'

// 响应式数据
const overviewData = ref({
  totalDevices: 4,
  activeDevices: 3,
  totalDetectionGroups: 9,
  activeDetectionGroups: 6,
  totalAlarms: 12,
  unhandledAlarms: 3,
  systemStatus: 'normal' // normal, warning, error
})

const deviceList = ref([
  { id: 1, name: '压铸机1', status: 'online', ip: '*************', lastUpdate: new Date() },
  { id: 2, name: '压铸机2', status: 'online', ip: '*************', lastUpdate: new Date() },
  { id: 3, name: '压铸机3', status: 'offline', ip: '*************', lastUpdate: new Date() },
  { id: 4, name: '压铸机4', status: 'online', ip: '*************', lastUpdate: new Date() }
])

const templateList = ref([
  { id: 1, name: '测试模板', status: 'active', deviceCount: 4, groupCount: 9 }
])

const detectionGroups = ref([
  { id: 1, name: '检测组1', deviceId: 1, status: 'monitoring', roiCount: 2, alarmCount: 1 },
  { id: 2, name: '检测组2', deviceId: 1, status: 'idle', roiCount: 2, alarmCount: 0 },
  { id: 3, name: '检测组3', deviceId: 2, status: 'monitoring', roiCount: 1, alarmCount: 2 },
  { id: 4, name: '检测组4', deviceId: 2, status: 'cooldown', roiCount: 1, alarmCount: 0 },
  { id: 5, name: '检测组5', deviceId: 3, status: 'offline', roiCount: 4, alarmCount: 0 },
  { id: 6, name: '检测组6', deviceId: 3, status: 'offline', roiCount: 4, alarmCount: 0 }
])

const videoSources = ref([
  { id: 1, name: '摄像头1', url: 'rtsp://*************/stream1', status: 'online' },
  { id: 2, name: '摄像头2', url: 'rtsp://*************/stream1', status: 'online' },
  { id: 3, name: '摄像头3', url: 'rtsp://192.168.1.203/stream1', status: 'offline' }
])

const alarmList = ref([
  { 
    id: 1, 
    type: 'jam', 
    level: 'high', 
    message: '压铸机1-检测组1发生卡料', 
    timestamp: new Date(),
    status: 'unhandled',
    deviceName: '压铸机1',
    groupName: '检测组1'
  },
  { 
    id: 2, 
    type: 'device_offline', 
    level: 'medium', 
    message: '压铸机3离线', 
    timestamp: new Date(Date.now() - 300000),
    status: 'handled',
    deviceName: '压铸机3'
  }
])

const performanceData = ref({
  cpuUsage: 45,
  memoryUsage: 62,
  gpuUsage: 78,
  diskUsage: 35,
  networkIn: 1024,
  networkOut: 512,
  detectionFps: 25,
  processingLatency: 120
})

const systemLogs = ref([
  { id: 1, level: 'info', message: '系统启动完成', timestamp: new Date() },
  { id: 2, level: 'warning', message: '压铸机3连接超时', timestamp: new Date(Date.now() - 60000) },
  { id: 3, level: 'error', message: '检测组1发生卡料报警', timestamp: new Date(Date.now() - 120000) }
])

const statisticsData = ref({
  todayDetections: 1250,
  todayAlarms: 8,
  jamRate: 0.64,
  avgProcessingTime: 85,
  systemUptime: '15天 8小时 32分钟',
  detectionAccuracy: 98.5
})

const selectedGroup = ref(null)

// 方法
const refreshAll = () => {
  ElMessage.success('数据刷新完成')
  // 这里添加实际的数据刷新逻辑
}

const toggleFullscreen = () => {
  const dashboardElement = document.querySelector('.dashboard-container')
  if (!document.fullscreenElement) {
    if (dashboardElement && dashboardElement.requestFullscreen) {
      dashboardElement.requestFullscreen()
    } else {
      document.documentElement.requestFullscreen()
    }
  } else {
    document.exitFullscreen()
  }
}

const handleDeviceClick = (device: any) => {
  console.log('设备点击:', device)
  // 可以跳转到设备详情页面或显示设备详情弹窗
}

const handleGroupSelect = (group: any) => {
  selectedGroup.value = group
  console.log('选择检测组:', group)
}

const handleAlarm = (alarm: any, action: string) => {
  console.log('处理报警:', alarm, action)
  if (action === 'handle') {
    alarm.status = 'handled'
    ElMessage.success('报警已处理')
  }
}

// 定时刷新数据
let refreshTimer: number | null = null

onMounted(() => {
  // 每30秒刷新一次数据
  refreshTimer = setInterval(() => {
    // 这里添加实际的数据更新逻辑
    console.log('定时刷新数据')
  }, 30000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style>
@import './dashboard-theme.css';
</style>

<style scoped>
.dashboard-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
  color: var(--text-color);
  overflow: hidden;
  margin: -20px; /* 抵消MainLayout的padding */
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: var(--bg-color-soft);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.overview-section {
  flex-shrink: 0;
  padding: 16px 24px;
}

.detection-carousel-section {
  flex-shrink: 0;
  padding: 0 24px 16px 24px;
  height: 600px;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 0 24px;
  min-height: 0;
}

.left-panel {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex-shrink: 0;
}

.center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 0;
}

.right-panel {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex-shrink: 0;
}

.panel-section {
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.bottom-section {
  flex-shrink: 0;
  padding: 16px 24px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .left-panel,
  .right-panel {
    width: 280px;
  }
}

@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
    flex-direction: row;
    overflow-x: auto;
  }
  
  .left-panel .panel-section,
  .right-panel .panel-section {
    min-width: 300px;
  }
}
</style>

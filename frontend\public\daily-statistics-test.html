<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每日检测统计API测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="date"], input[type="text"] {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        .stats-card {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .template-card {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
            padding: 10px;
            margin: 8px 0;
            border-radius: 4px;
        }
        .die-caster-card {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .group-card {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 6px;
            margin: 3px 0;
            border-radius: 4px;
        }
        .api-info {
            background: #fff9c4;
            border: 1px solid #f9c74f;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>每日检测统计API测试工具</h1>
    
    <div class="container">
        <div class="api-info">
            <strong>API端点:</strong> GET /api/card-detection/daily-statistics<br>
            <strong>功能:</strong> 获取每日检测统计信息，包括检测模板、压铸机和检测组的层级统计
        </div>
        
        <div class="test-section">
            <h2>1. 获取每日检测统计</h2>
            <div class="input-group">
                <label for="statisticsDate">选择日期 (可选，默认今天):</label>
                <input type="date" id="statisticsDate">
            </div>
            <button onclick="getDailyStatistics()">获取统计数据</button>
            <button onclick="getDailyStatistics(true)">获取今天数据</button>
            <div id="statisticsResult" class="result"></div>
        </div>
    </div>
    
    <div class="container">
        <div class="test-section">
            <h2>2. 相关API测试</h2>
            <button onclick="getDetectionGroups()">获取检测组列表</button>
            <button onclick="getDetectionTemplates()">获取检测模板列表</button>
            <button onclick="getCardDetectionResults()">获取检测结果</button>
            <div id="relatedApiResult" class="result"></div>
        </div>
    </div>
    
    <div class="container">
        <div class="test-section">
            <h2>3. 错误测试</h2>
            <button onclick="testInvalidDate()">测试无效日期格式</button>
            <div id="errorTestResult" class="result"></div>
        </div>
    </div>

    <script>
        // 获取API基础URL
        function getApiBaseUrl() {
            const hostname = window.location.hostname;
            if (hostname === 'localhost' || hostname === '127.0.0.1') {
                return 'http://localhost:8000/api';
            } else {
                return `http://${hostname}:8000/api`;
            }
        }
        
        const API_BASE_URL = getApiBaseUrl();
        
        // 显示结果
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            
            if (typeof data === 'object') {
                element.textContent = JSON.stringify(data, null, 2);
            } else {
                element.textContent = data;
            }
        }
        
        // 显示加载状态
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.className = 'result loading';
            element.textContent = '正在加载...';
        }
        
        // 格式化统计数据显示
        function formatStatisticsData(data) {
            let formatted = `日期: ${data.date}\n`;
            formatted += `检测模板数量: ${data.templates.length}\n\n`;
            
            data.templates.forEach((template, index) => {
                formatted += `模板 ${index + 1}: ${template.template_name} (ID: ${template.template_id})\n`;
                formatted += `  总检测: ${template.total_detections}, 卡料: ${template.jam_detections}, 成功率: ${(template.success_rate * 100).toFixed(2)}%\n`;
                
                template.die_casters.forEach((dieCaster, dcIndex) => {
                    formatted += `  压铸机 ${dcIndex + 1}: ${dieCaster.die_caster_name} (ID: ${dieCaster.die_caster_id})\n`;
                    formatted += `    总检测: ${dieCaster.total_detections}, 卡料: ${dieCaster.jam_detections}, 成功率: ${(dieCaster.success_rate * 100).toFixed(2)}%\n`;
                    
                    dieCaster.detection_groups.forEach((group, gIndex) => {
                        formatted += `    检测组 ${gIndex + 1}: ${group.group_name} (ID: ${group.group_id})\n`;
                        formatted += `      总检测: ${group.total_detections}, 卡料: ${group.jam_detections}, 成功率: ${(group.success_rate * 100).toFixed(2)}%\n`;
                    });
                });
                formatted += '\n';
            });
            
            return formatted;
        }
        
        // 获取每日检测统计
        async function getDailyStatistics(useToday = false) {
            showLoading('statisticsResult');
            
            try {
                let url = `${API_BASE_URL}/card-detection/daily-statistics`;
                
                if (!useToday) {
                    const dateInput = document.getElementById('statisticsDate');
                    if (dateInput.value) {
                        url += `?date=${dateInput.value}`;
                    }
                }
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    const formattedData = formatStatisticsData(data);
                    showResult('statisticsResult', formattedData, true);
                } else {
                    showResult('statisticsResult', `错误 ${response.status}: ${data.detail || JSON.stringify(data)}`, false);
                }
            } catch (error) {
                showResult('statisticsResult', `请求失败: ${error.message}`, false);
            }
        }
        
        // 获取检测组列表
        async function getDetectionGroups() {
            showLoading('relatedApiResult');
            
            try {
                const response = await fetch(`${API_BASE_URL}/detection-groups/`);
                const data = await response.json();
                
                if (response.ok) {
                    let formatted = `检测组数量: ${data.length}\n\n`;
                    data.slice(0, 5).forEach((group, index) => {
                        formatted += `${index + 1}. ${group.name} (ID: ${group.id})\n`;
                        formatted += `   模板ID: ${group.template_id || 'None'}, 压铸机ID: ${group.die_caster_id}\n`;
                        formatted += `   状态: ${group.status}\n\n`;
                    });
                    if (data.length > 5) {
                        formatted += `... 还有 ${data.length - 5} 个检测组\n`;
                    }
                    showResult('relatedApiResult', formatted, true);
                } else {
                    showResult('relatedApiResult', `错误 ${response.status}: ${JSON.stringify(data)}`, false);
                }
            } catch (error) {
                showResult('relatedApiResult', `请求失败: ${error.message}`, false);
            }
        }
        
        // 获取检测模板列表
        async function getDetectionTemplates() {
            showLoading('relatedApiResult');
            
            try {
                const response = await fetch(`${API_BASE_URL}/detection-templates/`);
                const data = await response.json();
                
                if (response.ok) {
                    let formatted = `检测模板数量: ${data.length}\n\n`;
                    data.forEach((template, index) => {
                        formatted += `${index + 1}. ${template.name} (ID: ${template.id})\n`;
                        formatted += `   描述: ${template.description || 'None'}\n`;
                        formatted += `   状态: ${template.status}\n\n`;
                    });
                    showResult('relatedApiResult', formatted, true);
                } else {
                    showResult('relatedApiResult', `错误 ${response.status}: ${JSON.stringify(data)}`, false);
                }
            } catch (error) {
                showResult('relatedApiResult', `请求失败: ${error.message}`, false);
            }
        }
        
        // 获取检测结果
        async function getCardDetectionResults() {
            showLoading('relatedApiResult');
            
            try {
                const response = await fetch(`${API_BASE_URL}/card-detection/?page=1&page_size=5`);
                const data = await response.json();
                
                if (response.ok) {
                    let formatted = `总记录数: ${data.total}\n`;
                    formatted += `当前页记录数: ${data.items.length}\n\n`;
                    data.items.forEach((item, index) => {
                        formatted += `${index + 1}. 检测组ID: ${item.detection_group_id}\n`;
                        formatted += `   时间: ${item.timestamp}\n`;
                        formatted += `   正常: ${item.is_normal ? '是' : '否'}\n`;
                        formatted += `   检测时间: ${item.detection_time}秒\n\n`;
                    });
                    showResult('relatedApiResult', formatted, true);
                } else {
                    showResult('relatedApiResult', `错误 ${response.status}: ${JSON.stringify(data)}`, false);
                }
            } catch (error) {
                showResult('relatedApiResult', `请求失败: ${error.message}`, false);
            }
        }
        
        // 测试无效日期
        async function testInvalidDate() {
            showLoading('errorTestResult');
            
            try {
                const response = await fetch(`${API_BASE_URL}/card-detection/daily-statistics?date=invalid-date`);
                const data = await response.json();
                
                if (response.status === 400) {
                    showResult('errorTestResult', `正确返回400错误:\n${JSON.stringify(data, null, 2)}`, true);
                } else {
                    showResult('errorTestResult', `意外的响应状态 ${response.status}:\n${JSON.stringify(data, null, 2)}`, false);
                }
            } catch (error) {
                showResult('errorTestResult', `请求失败: ${error.message}`, false);
            }
        }
        
        // 页面加载时设置今天的日期
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('statisticsDate').value = today;
        });
    </script>
</body>
</html>
# 监控看板集成说明

## 集成完成

✅ **路由配置已完成**
- 路径: `/monitoring-dashboard`
- 组件: `@/views/dashboard/index.vue`
- 菜单名称: "监控看板"

✅ **菜单集成已完成**
- 已添加到左侧导航菜单
- 图标: `data-analysis`
- 位置: 仪表盘下方

## 访问方式

1. **通过菜单导航**
   - 登录系统后，在左侧菜单中点击"监控看板"
   - 或直接访问 `http://localhost:5173/monitoring-dashboard`

2. **功能特性**
   - 实时系统状态监控
   - 设备状态管理
   - 检测组监控
   - 视频流监控
   - 报警管理
   - 性能监控
   - 系统日志
   - 数据统计

## 与现有系统的区别

### 仪表盘 (/)
- 简单的数据统计页面
- 静态数据展示
- 基础的图表和统计信息

### 监控看板 (/monitoring-dashboard)
- 实时监控界面
- 动态数据更新
- 复杂的交互功能
- 专业的监控布局

## 样式适配

看板已完全适配项目的主题系统：
- ✅ 支持亮色/暗色主题切换
- ✅ 使用项目统一的CSS变量
- ✅ 兼容Element Plus组件样式
- ✅ 响应式布局设计

## 数据集成

当前使用模拟数据，实际部署时需要：

1. **替换API调用**
```javascript
// 示例：替换设备列表数据
const loadDeviceList = async () => {
  try {
    const response = await api.get('/api/devices')
    deviceList.value = response.data
  } catch (error) {
    console.error('加载设备列表失败:', error)
  }
}
```

2. **WebSocket集成**
```javascript
// 实时数据更新
const ws = new WebSocket('ws://localhost:8000/ws/dashboard')
ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  // 更新相应的数据
}
```

3. **定时刷新**
```javascript
// 每30秒刷新数据
setInterval(() => {
  refreshAllData()
}, 30000)
```

## 性能优化建议

1. **懒加载组件**
   - 非关键组件使用动态导入
   - 减少初始加载时间

2. **数据缓存**
   - 静态数据使用本地缓存
   - 减少不必要的API调用

3. **虚拟滚动**
   - 大量数据列表使用虚拟滚动
   - 提升渲染性能

## 扩展开发

### 添加新的监控面板

1. 在 `components/` 目录下创建新组件
2. 在主看板页面中引入和使用
3. 添加相应的数据处理逻辑

### 自定义主题

1. 修改 `dashboard-theme.css` 文件
2. 添加新的CSS变量
3. 确保暗色模式兼容性

## 故障排除

### 常见问题

1. **菜单图标不显示**
   - 检查 Element Plus 图标是否正确导入
   - 确认图标名称拼写正确

2. **主题切换异常**
   - 检查CSS变量是否正确定义
   - 确认组件使用了正确的变量名

3. **响应式布局问题**
   - 检查媒体查询断点
   - 确认flex布局配置

4. **数据不更新**
   - 检查API接口是否正常
   - 确认WebSocket连接状态
   - 验证数据绑定是否正确

## 技术支持

如需技术支持或有问题反馈，请：
1. 查看控制台错误信息
2. 检查网络请求状态
3. 确认组件导入路径
4. 验证数据格式是否正确

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础监控看板功能
- ✅ 主题系统集成
- ✅ 响应式布局
- ✅ 组件化架构
- ✅ 菜单集成完成

### 计划功能
- 🔄 实时数据集成
- 🔄 WebSocket通信
- 🔄 高级筛选功能
- 🔄 数据导出功能
- 🔄 自定义布局

/**
 * 检测看板相关类型定义
 */

// 布局类型
export type LayoutType = '1x1' | '2x2' | '3x3' | '4x4'

// 检测项目接口
export interface DetectionItem {
  id: string // 唯一标识
  detectionGroupId: string // 检测组ID
  name: string // 显示名称
  templateId?: string // 检测模板ID（可选）
  loading: boolean // 加载状态
  refreshKey: number // 刷新键，用于强制刷新iframe
}

// 添加检测组表单接口
export interface AddDetectionForm {
  detectionGroupId: string
  name: string
  templateId: string
}

// 布局配置接口
export interface LayoutConfig {
  rows: number
  cols: number
  totalSlots: number
}

// 检测看板配置接口
export interface DashboardConfig {
  layout: LayoutType
  detections: DetectionItem[]
  autoRefresh: boolean
  refreshInterval: number // 自动刷新间隔（秒）
}

// 检测状态枚举
export enum DetectionStatus {
  IDLE = 'idle', // 空闲
  LOADING = 'loading', // 加载中
  RUNNING = 'running', // 运行中
  ERROR = 'error' // 错误
}

// 检测统计信息接口
export interface DetectionStats {
  totalDetections: number // 总检测数
  activeDetections: number // 活跃检测数
  errorDetections: number // 错误检测数
  lastUpdateTime: string // 最后更新时间
}

// URL参数接口
export interface DetectionUrlParams {
  detectionMode: boolean
  detectionGroupId: string
  templateId?: string
}

// 检测组信息接口
export interface DetectionGroupInfo {
  id: string
  name: string
  description?: string
  templateId?: string
  status: DetectionStatus
  lastActiveTime?: string
  config?: Record<string, any>
}

// 检测模板信息接口
export interface DetectionTemplateInfo {
  id: string
  name: string
  description?: string
  version: string
  createdAt: string
  updatedAt: string
}

// 看板事件类型
export interface DashboardEvents {
  'layout-change': LayoutType
  'detection-add': DetectionItem
  'detection-remove': string // detection id
  'detection-refresh': string // detection id
  'detection-error': { id: string; error: string }
  'detection-load': string // detection id
}

// 响应式检测项目接口
export interface ReactiveDetectionItem extends DetectionItem {
  status: DetectionStatus
  errorMessage?: string
  lastRefreshTime?: string
}

// 检测看板存储配置
export interface DashboardStorageConfig {
  version: string
  layout: LayoutType
  detections: Omit<DetectionItem, 'loading' | 'refreshKey'>[]
  settings: {
    autoRefresh: boolean
    refreshInterval: number
    theme: 'light' | 'dark' | 'auto'
  }
}

// 导出所有类型
export type {
  LayoutType,
  DetectionItem,
  AddDetectionForm,
  LayoutConfig,
  DashboardConfig,
  DetectionStats,
  DetectionUrlParams,
  DetectionGroupInfo,
  DetectionTemplateInfo,
  DashboardEvents,
  ReactiveDetectionItem,
  DashboardStorageConfig
}

export { DetectionStatus }
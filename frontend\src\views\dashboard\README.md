# 压铸件智能检测系统 - 监控看板

## 概述

这是一个专为压铸件智能检测系统设计的综合性监控看板，提供实时的系统状态监控、设备管理、检测结果展示和性能分析功能。

## 功能特性

### 🎯 核心功能
- **实时监控**: 实时显示设备状态、检测组状态和系统运行情况
- **多设备管理**: 支持多台压铸机的集中监控和管理
- **智能检测**: 集成运动检测、方向检测和卡料检测算法
- **报警管理**: 实时报警提醒和处理流程管理
- **性能监控**: 系统资源使用情况和检测性能指标
- **数据统计**: 详细的检测统计和趋势分析

### 🎨 界面特性
- **响应式设计**: 适配不同屏幕尺寸，支持全屏显示
- **主题切换**: 支持亮色/暗色主题无缝切换
- **组件化架构**: 模块化设计，易于维护和扩展
- **实时更新**: 自动刷新数据，保持信息同步

## 组件结构

```
dashboard/
├── index.vue                    # 主看板页面
├── dashboard-theme.css          # 看板主题样式
├── README.md                   # 说明文档
└── components/                 # 子组件目录
    ├── SystemOverview.vue      # 系统概览组件
    ├── DeviceStatusPanel.vue   # 设备状态面板
    ├── TemplateStatusPanel.vue # 模板状态面板
    ├── RealtimeDetectionPanel.vue # 实时检测面板
    ├── VideoGridPanel.vue      # 视频网格面板
    ├── AlarmPanel.vue          # 报警面板
    ├── PerformancePanel.vue    # 性能监控面板
    ├── SystemLogPanel.vue      # 系统日志面板
    └── StatisticsPanel.vue     # 统计面板
```

## 组件说明

### SystemOverview (系统概览)
- 显示系统整体状态概览
- 包含设备状态、检测组状态、报警信息等关键指标
- 支持GPU加速状态和算法性能展示

### DeviceStatusPanel (设备状态面板)
- 实时显示所有压铸机的在线状态
- 展示设备IP、最后更新时间等详细信息
- 显示每台设备的检测组数量和ROI配置
- 支持设备点击查看详情

### RealtimeDetectionPanel (实时检测面板)
- 显示所有检测组的实时状态
- 支持按状态筛选（监测中、空闲、冷却、离线）
- 展示检测算法运行状态和性能指标
- 提供检测组选择和详情查看

### VideoGridPanel (视频网格面板)
- 支持1x1、2x2、3x3等多种网格布局
- 实时视频流显示和ROI绘制
- 视频源状态监控和管理
- 支持全屏和画面切换

### AlarmPanel (报警面板)
- 实时报警信息展示和管理
- 支持按级别和状态筛选报警
- 提供报警处理和确认功能
- 显示报警统计和趋势

### PerformancePanel (性能监控面板)
- 系统资源使用情况（CPU、内存、GPU、磁盘）
- 网络状态监控
- 检测性能指标（FPS、延迟）
- GPU加速状态和使用率

### SystemLogPanel (系统日志面板)
- 系统运行日志实时显示
- 支持按日志级别筛选
- 错误、警告、信息分类显示

### StatisticsPanel (统计面板)
- 检测统计数据展示
- 算法性能对比分析
- 支持不同时间范围统计
- 趋势分析和数据可视化

## 使用方法

### 1. 基本使用

```vue
<template>
  <DashboardView />
</template>

<script setup>
import DashboardView from '@/views/dashboard/index.vue'
</script>
```

### 2. 数据配置

看板组件使用模拟数据进行展示，实际使用时需要替换为真实的API调用：

```javascript
// 替换模拟数据
const overviewData = ref({
  totalDevices: 4,
  activeDevices: 3,
  // ... 其他数据
})

// 替换为API调用
const loadOverviewData = async () => {
  try {
    const response = await api.getSystemOverview()
    overviewData.value = response.data
  } catch (error) {
    console.error('加载概览数据失败:', error)
  }
}
```

### 3. 主题配置

看板完全支持系统的主题切换功能：

```css
/* 自定义主题变量 */
:root {
  --dashboard-primary-color: #your-color;
  --dashboard-card-bg: #your-bg-color;
}

.dark-theme {
  --dashboard-primary-color: #your-dark-color;
  --dashboard-card-bg: #your-dark-bg-color;
}
```

### 4. 响应式配置

看板支持多种屏幕尺寸：

- **大屏 (>1400px)**: 完整三栏布局
- **中屏 (1200-1400px)**: 紧凑三栏布局
- **小屏 (<1200px)**: 单栏堆叠布局

## 集成指南

### 1. 路由配置

```javascript
// router/index.js
{
  path: '/dashboard',
  name: 'Dashboard',
  component: () => import('@/views/dashboard/index.vue'),
  meta: {
    title: '监控看板',
    requiresAuth: true
  }
}
```

### 2. API集成

```javascript
// api/dashboard.js
export const dashboardApi = {
  // 获取系统概览
  getSystemOverview: () => request.get('/api/dashboard/overview'),
  
  // 获取设备列表
  getDeviceList: () => request.get('/api/devices'),
  
  // 获取检测组状态
  getDetectionGroups: () => request.get('/api/detection-groups'),
  
  // 获取报警列表
  getAlarmList: (params) => request.get('/api/alarms', { params }),
  
  // 获取性能数据
  getPerformanceData: () => request.get('/api/system/performance'),
  
  // 获取系统日志
  getSystemLogs: (params) => request.get('/api/system/logs', { params })
}
```

### 3. WebSocket集成

```javascript
// 实时数据更新
const setupWebSocket = () => {
  const ws = new WebSocket('ws://localhost:8000/ws/dashboard')
  
  ws.onmessage = (event) => {
    const data = JSON.parse(event.data)
    
    switch (data.type) {
      case 'device_status':
        updateDeviceStatus(data.payload)
        break
      case 'detection_result':
        updateDetectionResult(data.payload)
        break
      case 'alarm':
        addNewAlarm(data.payload)
        break
      case 'performance':
        updatePerformanceData(data.payload)
        break
    }
  }
}
```

## 扩展开发

### 1. 添加新组件

```vue
<!-- components/CustomPanel.vue -->
<template>
  <div class="custom-panel">
    <div class="panel-header">
      <h3 class="panel-title">自定义面板</h3>
    </div>
    <div class="panel-content">
      <!-- 面板内容 -->
    </div>
  </div>
</template>

<style scoped>
.custom-panel {
  /* 使用看板主题变量 */
  background-color: var(--dashboard-card-bg);
  border: 1px solid var(--dashboard-card-border);
  box-shadow: var(--dashboard-card-shadow);
}
</style>
```

### 2. 自定义主题

```css
/* 创建自定义主题文件 */
.custom-theme {
  --primary-color: #your-primary-color;
  --dashboard-card-bg: #your-card-bg;
  --dashboard-card-border: #your-border-color;
}
```

## 性能优化

1. **虚拟滚动**: 大量数据列表使用虚拟滚动
2. **懒加载**: 非关键组件使用懒加载
3. **防抖节流**: 频繁更新的数据使用防抖处理
4. **缓存策略**: 静态数据使用本地缓存

## 注意事项

1. 确保所有CSS变量都已正确定义
2. 测试不同主题下的显示效果
3. 验证响应式布局在各种屏幕尺寸下的表现
4. 确保WebSocket连接的稳定性和错误处理
5. 定期清理和优化性能监控数据

## 技术栈

- **Vue 3**: 组合式API
- **TypeScript**: 类型安全
- **Element Plus**: UI组件库
- **CSS Variables**: 主题系统
- **WebSocket**: 实时通信
- **Canvas**: 视频ROI绘制

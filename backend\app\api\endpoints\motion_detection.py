from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
import logging
import asyncio
import json
from sqlalchemy.orm import Session

from app.api import deps
from app.services.video_detection import VideoDetectionManager
from app.services.websocket_manager import connection_manager

logger = logging.getLogger(__name__)
router = APIRouter()

# 全局检测管理器
detection_manager = VideoDetectionManager()

class MotionDetectionConfig(BaseModel):
    """运动检测配置"""
    video_source_id: int
    rtsp_url: str
    sensitivity: int = Field(default=50, ge=1, le=100, description="检测敏感度")
    min_area: int = Field(default=500, ge=100, le=5000, description="最小检测区域")
    enable_direction: bool = Field(default=True, description="是否启用方向检测")
    rois: List[Dict[str, Any]] = Field(default=[], description="ROI区域配置")

class ROIConfig(BaseModel):
    """ROI区域配置"""
    name: str
    type: str  # rectangle, polygon
    points: List[Dict[str, float]]  # [{"x": 0, "y": 0}, ...]
    color: str = "#ff0000"
    detection_type: str = "motion"  # motion, intrusion, line_crossing

@router.post("/start")
async def start_motion_detection(
    config: MotionDetectionConfig,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
):
    """启动运动检测"""
    try:
        logger.info(f"启动运动检测: 视频源ID={config.video_source_id}")
        
        # 检查是否已经在检测
        if detection_manager.is_detection_active(config.video_source_id):
            raise HTTPException(status_code=400, detail="该视频源的运动检测已在运行")
        
        # 配置检测参数
        detection_config = {
            'sensitivity': config.sensitivity,
            'min_area': config.min_area,
            'enable_direction': config.enable_direction,
            'rois': config.rois
        }
        
        # 启动检测
        success = await detection_manager.start_detection(
            video_source_id=config.video_source_id,
            rtsp_url=config.rtsp_url,
            config=detection_config
        )
        
        if success:
            # 启动结果广播任务
            background_tasks.add_task(
                broadcast_detection_results,
                config.video_source_id
            )
            
            return {
                "success": True,
                "message": "运动检测已启动",
                "video_source_id": config.video_source_id
            }
        else:
            raise HTTPException(status_code=500, detail="启动运动检测失败")
            
    except Exception as e:
        logger.error(f"启动运动检测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/stop/{video_source_id}")
async def stop_motion_detection(
    video_source_id: int,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
):
    """停止运动检测"""
    try:
        logger.info(f"停止运动检测: 视频源ID={video_source_id}")
        
        success = await detection_manager.stop_detection(video_source_id)
        
        if success:
            return {
                "success": True,
                "message": "运动检测已停止",
                "video_source_id": video_source_id
            }
        else:
            raise HTTPException(status_code=404, detail="未找到运行中的检测任务")
            
    except Exception as e:
        logger.error(f"停止运动检测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{video_source_id}")
async def get_detection_status(
    video_source_id: int,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
):
    """获取检测状态"""
    try:
        is_active = detection_manager.is_detection_active(video_source_id)
        config = detection_manager.get_detection_config(video_source_id)
        
        return {
            "video_source_id": video_source_id,
            "is_active": is_active,
            "config": config
        }
        
    except Exception as e:
        logger.error(f"获取检测状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/config/{video_source_id}")
async def update_detection_config(
    video_source_id: int,
    config: Dict[str, Any],
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
):
    """更新检测配置"""
    try:
        logger.info(f"更新检测配置: 视频源ID={video_source_id}")
        
        success = detection_manager.update_config(video_source_id, config)
        
        if success:
            return {
                "success": True,
                "message": "检测配置已更新",
                "video_source_id": video_source_id,
                "config": config
            }
        else:
            raise HTTPException(status_code=404, detail="未找到运行中的检测任务")
            
    except Exception as e:
        logger.error(f"更新检测配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/roi/{video_source_id}")
async def save_roi_config(
    video_source_id: int,
    rois: List[ROIConfig],
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
):
    """保存ROI配置"""
    try:
        logger.info(f"保存ROI配置: 视频源ID={video_source_id}, ROI数量={len(rois)}")
        
        # 转换ROI配置格式
        roi_data = []
        for roi in rois:
            roi_data.append({
                "name": roi.name,
                "type": roi.type,
                "points": roi.points,
                "color": roi.color,
                "detection_type": roi.detection_type
            })
        
        # 更新检测配置中的ROI
        if detection_manager.is_detection_active(video_source_id):
            current_config = detection_manager.get_detection_config(video_source_id) or {}
            current_config['rois'] = roi_data
            detection_manager.update_config(video_source_id, current_config)
        
        return {
            "success": True,
            "message": "ROI配置已保存",
            "video_source_id": video_source_id,
            "roi_count": len(rois)
        }
        
    except Exception as e:
        logger.error(f"保存ROI配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/roi/{video_source_id}")
async def get_roi_config(
    video_source_id: int,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
):
    """获取ROI配置"""
    try:
        config = detection_manager.get_detection_config(video_source_id)
        rois = config.get('rois', []) if config else []
        
        return {
            "video_source_id": video_source_id,
            "rois": rois
        }
        
    except Exception as e:
        logger.error(f"获取ROI配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def broadcast_detection_results(video_source_id: int):
    """广播检测结果"""
    logger.info(f"开始广播检测结果: 视频源ID={video_source_id}")
    
    try:
        while detection_manager.is_detection_active(video_source_id):
            # 获取最新的检测结果
            result = detection_manager.get_latest_result(video_source_id)
            
            if result:
                # 广播到所有连接的WebSocket客户端
                message = {
                    "type": "detection_result",
                    "video_source_id": video_source_id,
                    "data": result
                }
                
                await connection_manager.broadcast(json.dumps(message))
            
            # 等待一段时间再获取下一个结果
            await asyncio.sleep(0.1)  # 10fps
            
    except Exception as e:
        logger.error(f"广播检测结果失败: {e}")
    finally:
        logger.info(f"停止广播检测结果: 视频源ID={video_source_id}")

@router.get("/statistics/{video_source_id}")
async def get_detection_statistics(
    video_source_id: int,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
):
    """获取检测统计信息"""
    try:
        stats = detection_manager.get_statistics(video_source_id)
        
        return {
            "video_source_id": video_source_id,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"获取检测统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

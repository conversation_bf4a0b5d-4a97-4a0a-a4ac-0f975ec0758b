// WebSDK 类型定义

declare global {
  interface Window {
    WebVideoCtrl: WebVideoCtrlInterface
  }
}

export interface WebVideoCtrlInterface {
  // 基础功能
  I_SupportNoPlugin(): boolean
  I_InitPlugin(width: number, height: number, options: InitPluginOptions): number
  I_InsertOBJECTPlugin(containerId: string): number
  I_DestroyPlugin(): number
  
  // 设备连接
  I_Login(
    ip: string,
    protocol: number,
    port: number,
    username: string,
    password: string,
    callbacks: LoginCallbacks
  ): void
  I_Logout(deviceIdentify: string): number
  
  // 设备信息
  I_GetDeviceInfo(deviceIdentify: string, callbacks: DeviceInfoCallbacks): void
  I_GetAnalogChannelInfo(deviceIdentify: string, callbacks: ChannelInfoCallbacks): void
  I_GetDigitalChannelInfo(deviceIdentify: string, callbacks: ChannelInfoCallbacks): void
  I_GetZeroChannelInfo(deviceIdentify: string, callbacks: ChannelInfoCallbacks): void
  
  // 播放控制
  I_StartRealPlay(deviceIdentify: string, options: RealPlayOptions): void
  I_Stop(options: StopOptions): void
  I_OpenSound(windowIndex: number): number
  I_CloseSound(windowIndex: number): number
  I_SetVolume(windowIndex: number, volume: number): number
  
  // 录像和回放
  I_StartRecord(options: RecordOptions): number
  I_StopRecord(windowIndex: number): number
  I_SearchByTime(deviceIdentify: string, options: SearchOptions): void
  I_StartPlayback(deviceIdentify: string, options: PlaybackOptions): void
  I_ReversePlayback(windowIndex: number): number
  I_PlaybackPause(windowIndex: number): number
  I_PlaybackResume(windowIndex: number): number
  I_PlaybackSlow(windowIndex: number): number
  I_PlaybackFast(windowIndex: number): number
  I_Frame(windowIndex: number): number
  I_SetPlaybackSpeed(windowIndex: number, speed: number): number
  
  // 抓图
  I_CapturePic(fileName: string, options: CaptureOptions): number
  I2_CapturePic(fileName: string, options: CaptureOptions): number
  
  // 窗口控制
  I_ChangeWndNum(windowType: number): number
  I_GetWindowStatus(windowIndex: number): WindowStatus | null
  I_FullScreen(enable: boolean): number
  
  // 云台控制
  I_PTZControl(direction: number, stop: boolean, options: PTZOptions): number
  I_PTZControlOther(command: number, param1: number, param2: number, options: PTZOptions): number
  
  // 图像控制
  I_EnableElectronicZoom(windowIndex: number, enable: boolean): number
  I_Enable3DZoom(windowIndex: number, enable: boolean): number
  I_SetColor(windowIndex: number, colorType: number, value: number): number
  
  // 配置和维护
  I_ExportDeviceConfig(deviceIdentify: string, callbacks: ExportConfigCallbacks): void
  I_ImportDeviceConfig(deviceIdentify: string, fileName: string, callbacks: ImportConfigCallbacks): void
  I_RestoreDeviceConfig(deviceIdentify: string, callbacks: RestoreConfigCallbacks): void
  I_RebootDevice(deviceIdentify: string, callbacks: RebootCallbacks): void
  I_UpgradeDevice(deviceIdentify: string, fileName: string, callbacks: UpgradeCallbacks): void
  I_GetUpgradeProgress(deviceIdentify: string, callbacks: UpgradeProgressCallbacks): void
  
  // 其他功能
  I_SendHTTPRequest(url: string, method: string, data: string, callbacks: HTTPCallbacks): void
  I_SetSecretKey(windowIndex: number, key: string, keyLength: number): number
  I_SetTextOverlay(windowIndex: number, text: string, options: TextOverlayOptions): number
  I_GetOSDTime(windowIndex: number): string
  I_Reconnect(deviceIdentify: string): number
}

// 初始化选项
export interface InitPluginOptions {
  iWndowType?: number // 窗口分割类型：1-1x1, 2-2x2, 3-3x3, 4-4x4
  bWndFull?: boolean // 是否窗口铺满
  iPlayMode?: number // 播放模式：0-预览, 1-回放, 2-预览和回放
  bDebugMode?: boolean // 是否开启调试模式
  cbSelWnd?: (xmlData: string) => void // 窗口选择回调
  cbInitPluginComplete?: () => void // 插件初始化完成回调
  cbPluginErrorHandler?: (error: any) => void // 插件错误回调
  cbPerformanceLack?: () => void // 性能不足回调
}

// 登录回调
export interface LoginCallbacks {
  success: (xmlDoc: any) => void
  error: (status: number, xml: string) => void
}

// 设备信息回调
export interface DeviceInfoCallbacks {
  success: (xmlDoc: any) => void
  error: (status: number, xml: string) => void
}

// 通道信息回调
export interface ChannelInfoCallbacks {
  success: (xmlDoc: any) => void
  error: (status: number, xml: string) => void
}

// 实时播放选项
export interface RealPlayOptions {
  iWndIndex?: number // 窗口索引
  iStreamType?: number // 码流类型：1-主码流, 2-子码流
  iChannelID?: number // 通道号
  bProxy?: boolean // 是否使用WebSocket代理
  iRtspPort?: number // RTSP端口
  success?: () => void
  error?: () => void
}

// 停止播放选项
export interface StopOptions {
  iWndIndex?: number // 窗口索引
  success?: () => void
  error?: () => void
}

// 录像选项
export interface RecordOptions {
  iWndIndex?: number // 窗口索引
  fileName?: string // 文件名
  recordType?: number // 录像类型
}

// 搜索选项
export interface SearchOptions {
  iChannelID: number // 通道号
  szStartTime: string // 开始时间 "2023-01-01T00:00:00"
  szEndTime: string // 结束时间 "2023-01-01T23:59:59"
  success: (xmlDoc: any) => void
  error: (status: number, xml: string) => void
}

// 回放选项
export interface PlaybackOptions {
  iWndIndex?: number // 窗口索引
  szStartTime: string // 开始时间
  szEndTime: string // 结束时间
  iChannelID: number // 通道号
  bProxy?: boolean // 是否使用WebSocket代理
  success?: () => void
  error?: () => void
}

// 抓图选项
export interface CaptureOptions {
  iWndIndex?: number // 窗口索引
  bDateDir?: boolean // 是否按日期创建目录
}

// 窗口状态
export interface WindowStatus {
  iIndex: number // 窗口索引
  bPlay: boolean // 是否正在播放
  iWidth: number // 视频宽度
  iHeight: number // 视频高度
  iFps: number // 帧率
  iBitRate: number // 码率
  iStreamType: number // 码流类型
  szIP: string // 设备IP
  iChannelID: number // 通道号
}

// 云台控制选项
export interface PTZOptions {
  iWndIndex?: number // 窗口索引
  iPTZSpeed?: number // 云台速度 1-7
}

// 文本叠加选项
export interface TextOverlayOptions {
  iWndIndex?: number // 窗口索引
  left?: number // 左边距
  top?: number // 上边距
  fontColor?: string // 字体颜色
  fontSize?: number // 字体大小
}

// 配置导出回调
export interface ExportConfigCallbacks {
  success: (data: any) => void
  error: (status: number, xml: string) => void
}

// 配置导入回调
export interface ImportConfigCallbacks {
  success: () => void
  error: (status: number, xml: string) => void
}

// 恢复配置回调
export interface RestoreConfigCallbacks {
  success: () => void
  error: (status: number, xml: string) => void
}

// 重启设备回调
export interface RebootCallbacks {
  success: () => void
  error: (status: number, xml: string) => void
}

// 升级设备回调
export interface UpgradeCallbacks {
  success: () => void
  error: (status: number, xml: string) => void
}

// 升级进度回调
export interface UpgradeProgressCallbacks {
  success: (progress: number) => void
  error: (status: number, xml: string) => void
}

// HTTP请求回调
export interface HTTPCallbacks {
  success: (data: any) => void
  error: (status: number, xml: string) => void
}

// 云台方向常量
export enum PTZDirection {
  UP = 1,
  DOWN = 2,
  LEFT = 3,
  RIGHT = 4,
  UP_LEFT = 5,
  UP_RIGHT = 6,
  DOWN_LEFT = 7,
  DOWN_RIGHT = 8,
  ZOOM_IN = 10,
  ZOOM_OUT = 11,
  FOCUS_NEAR = 12,
  FOCUS_FAR = 13,
  IRIS_OPEN = 14,
  IRIS_CLOSE = 15
}

// 错误码
export enum WebSDKErrorCode {
  SUCCESS = 0,
  PLUGIN_NOT_INSTALL = -1,
  PLUGIN_NOT_INIT = -2,
  BROWSER_NOT_SUPPORT = -3,
  INVALID_PARAMETER = -4,
  DEVICE_NOT_LOGIN = -5,
  PLAY_FAILED = -6,
  STOP_FAILED = -7,
  CAPTURE_FAILED = -8,
  RECORD_FAILED = -9,
  PTZ_FAILED = -10
}

// 窗口类型
export enum WindowType {
  SINGLE = 1,
  FOUR = 2,
  NINE = 3,
  SIXTEEN = 4
}

// 码流类型
export enum StreamType {
  MAIN = 1,
  SUB = 2
}

// 协议类型
export enum ProtocolType {
  HTTP = 1,
  HTTPS = 2
}

export {}
"""Add template_id to detection_groups

Revision ID: 2edca0f184a0
Revises: 001
Create Date: 2025-07-05 22:58:38.681599

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2edca0f184a0'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('detection_groups', sa.Column('template_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'detection_groups', 'detection_templates', ['template_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'detection_groups', type_='foreignkey')
    op.drop_column('detection_groups', 'template_id')
    # ### end Alembic commands ###
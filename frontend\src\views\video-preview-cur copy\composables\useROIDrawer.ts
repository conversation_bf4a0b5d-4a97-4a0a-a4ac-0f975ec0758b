import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
// @ts-ignore - 忽略缺少类型定义的警告
import { ROIDrawer } from '@/utils/roiDrawer.js'

/**
 * ROI绘制功能Hook
 * 负责管理ROI区域的绘制、编辑和管理
 * @param addOperationInfo 操作日志记录函数
 */
export function useROIDrawer(addOperationInfo: (message: string) => void) {
  // ROI绘制相关
  const roiDrawerInstance = ref<any>(null)
  const roiList = ref<any[]>([])
  const roiCount = computed(() => roiList.value.length)
  const selectedROIAttribute = ref<'pailiao' | 'yazhu' | null>(null)
  const isDrawingEnabled = ref(false)
  const highlightedROIId = ref<string | null>(null)
  const canvasElement = ref<HTMLCanvasElement | null>(null)

  // ROI分组计算属性
  const pailiaoROIs = computed(() => roiList.value.filter(roi => roi.attribute === 'pailiao'))
  const yazhuROIs = computed(() => roiList.value.filter(roi => roi.attribute === 'yazhu'))

  /**
   * ROI属性显示名称
   * @param attribute ROI属性
   */
  const getAttributeDisplayName = (attribute: string | null) => {
    if (!attribute) return ''
    return attribute === 'pailiao' ? '排料口' : '压铸机'
  }

  /**
   * 获取指定属性的ROI数量
   * @param attribute ROI属性
   */
  const getAttributeROICount = (attribute: string | null) => {
    if (!attribute) return 0
    return roiList.value.filter(roi => roi.attribute === attribute).length
  }

  /**
   * 根据属性获取ROI颜色
   * @param attribute ROI属性
   */
  const getROIColorByAttribute = (attribute: string | null) => {
    if (attribute === 'pailiao') return '#00ffff'  // 青色
    if (attribute === 'yazhu') return '#ff0000'    // 红色
    return '#00ff00'  // 默认绿色
  }

  /**
   * 初始化ROI绘制器
   * @param canvas ROI绘制用的canvas元素
   */
  const initROIDrawer = (canvas: HTMLCanvasElement | null) => {
    if (!canvas) {
      console.error('ROI画布未找到')
      addOperationInfo('[ERROR] ROI画布未找到，初始化失败')
      return
    }

    // 保存canvas引用
    canvasElement.value = canvas

    // 获取canvas尺寸 - 使用canvas自身尺寸而非默认尺寸
    const canvasWidth = canvas.width || 640  // 使用标准默认尺寸
    const canvasHeight = canvas.height || 360 // 使用标准默认尺寸
    
    console.log(`初始化ROI绘制器，Canvas尺寸: ${canvasWidth}x${canvasHeight}`)
    addOperationInfo(`[ROI] 初始化绘制器，Canvas尺寸: ${canvasWidth}x${canvasHeight}`)
    
    // 确保Canvas设置正确
    if (canvas.width !== canvasWidth) canvas.width = canvasWidth
    if (canvas.height !== canvasHeight) canvas.height = canvasHeight
    
    // 确保CSS样式设置正确
    canvas.style.display = 'block'
    canvas.style.position = 'absolute'
    canvas.style.top = '0'
    canvas.style.left = '0'
    canvas.style.pointerEvents = 'auto'

    // 显式添加日志
    addOperationInfo(`[ROI] Canvas尺寸检查: ${canvas.width}x${canvas.height}`)

    try {
      // 如果已经初始化过，先销毁旧实例
      if (roiDrawerInstance.value) {
        console.log('销毁旧的ROI绘制器实例')
        roiDrawerInstance.value.destroy()
        roiDrawerInstance.value = null
      }

      // 确保从import路径正确导入ROIDrawer类
      console.log('正在初始化ROIDrawer...', {
        ROIDrawerClass: typeof ROIDrawer,
        canvasReady: !!canvas,
        canvasContext: !!canvas.getContext('2d'),
        canvasSize: `${canvas.width}x${canvas.height}`
      })
      
      addOperationInfo(`[ROI] 准备实例化ROIDrawer, canvas=${!!canvas}, context=${!!canvas.getContext('2d')}`)

      roiDrawerInstance.value = new ROIDrawer(canvas, {
        videoWidth: canvas.width,
        videoHeight: canvas.height,
        colors: {
          pailiao: '#00ffff',  // 青色
          yazhu: '#ff0000',    // 红色
          default: ['#00ff00', '#ffff00', '#ff00ff', '#0066ff']  // 备用颜色
        },
        debugMode: true  // 启用调试模式
      })

      // 设置事件回调
      roiDrawerInstance.value.onROIAdded = (roi: any) => {
        // 添加属性信息
        roi.attribute = selectedROIAttribute.value
        roi.name = `${getAttributeDisplayName(selectedROIAttribute.value)}${getAttributeROICount(selectedROIAttribute.value) + 1}`
        // 根据属性设置颜色
        roi.color = getROIColorByAttribute(selectedROIAttribute.value)

        roiList.value.push({ ...roi })
        addOperationInfo(`[ROI] 添加${getAttributeDisplayName(selectedROIAttribute.value)}ROI: ${roi.name}`)
      }

      roiDrawerInstance.value.onROIUpdated = (roi: any, index: number) => {
        addOperationInfo(`[ROI] 更新ROI: ${roi.name}`)
        // 更新组件中的ROI列表
        roiList.value[index] = { ...roi }
      }

      roiDrawerInstance.value.onROIDeleted = (roi: any, index: number) => {
        addOperationInfo(`[ROI] 删除ROI: ${roi.name}`)
        // 实际删除操作在deleteROI方法中处理
      }

      // 设置绘制器状态
      if (selectedROIAttribute.value) {
        // 设置当前属性和绘制模式
        roiDrawerInstance.value.setCurrentAttribute(selectedROIAttribute.value)
        roiDrawerInstance.value.setDrawMode('polygon')
        // 根据当前绘制状态设置是否启用
        roiDrawerInstance.value.setEnabled(isDrawingEnabled.value)
        addOperationInfo(`[ROI] 初始化绘制器 - 属性:${getAttributeDisplayName(selectedROIAttribute.value)}, 绘制:${isDrawingEnabled.value ? '启用' : '禁用'}`)
      } else {
        // 如果没有选择属性，默认禁用绘制
        roiDrawerInstance.value.setEnabled(false)
        addOperationInfo('[ROI] 初始化绘制器 - 未选择属性，禁用绘制')
      }

      // 如果有已存在的ROI列表，加载到绘制器中
      if (roiList.value.length > 0) {
        console.log('加载现有ROI到绘制器中:', roiList.value.length)
        roiDrawerInstance.value.loadROIList(roiList.value)
      }

      // 如果有高亮的ROI，设置高亮显示
      if (highlightedROIId.value) {
        console.log('高亮现有ROI:', highlightedROIId.value)
        roiDrawerInstance.value.highlightROI(highlightedROIId.value)
      }

      console.log('ROI绘制器初始化成功')
      addOperationInfo('[ROI] 绘制器初始化成功')
    } catch (error) {
      console.error('ROI绘制器初始化失败:', error)
      addOperationInfo(`[ERROR] ROI绘制器初始化失败: ${error}`)
    }
  }

  /**
   * ROI绘制器准备完成的回调函数
   * 当VideoPlayer组件中的canvas准备好时调用
   * @param canvas ROI绘制用的canvas元素
   */
  const onROIDrawerReady = (canvas: HTMLCanvasElement) => {
    console.log('ROI画布准备完成，开始初始化绘制器', {
      canvas: !!canvas,
      width: canvas?.width,
      height: canvas?.height,
      id: canvas?.id,
      className: canvas?.className
    })
    addOperationInfo(`[ROI] 画布准备完成，尺寸: ${canvas?.width}x${canvas?.height}，开始初始化绘制器`)
    
    // 确保Canvas元素已正确配置
    if (canvas) {
      // 设置正确的尺寸和样式
      if (canvas.width !== 640) canvas.width = 640
      if (canvas.height !== 360) canvas.height = 360
      
      canvas.style.display = 'block'
      canvas.style.position = 'absolute'
      canvas.style.top = '0'
      canvas.style.left = '0'
      canvas.style.pointerEvents = 'auto'
    }
    
    initROIDrawer(canvas)
  }

  /**
   * ROI属性选择变化处理
   */
  const onROIAttributeChange = (attribute?: 'pailiao' | 'yazhu') => {
    // 如果传入了属性参数，则更新当前选择的属性
    if (attribute) {
      selectedROIAttribute.value = attribute
    }

    console.log('ROI属性变化:', selectedROIAttribute.value)
    addOperationInfo(`[ROI] 选择属性: ${selectedROIAttribute.value || '无'}`);

    if (selectedROIAttribute.value) {
      // 如果存在ROI绘制器，设置当前属性
      if (roiDrawerInstance.value) {
        roiDrawerInstance.value.setCurrentAttribute(selectedROIAttribute.value)
        // 只设置绘制模式，不自动启用绘制
        roiDrawerInstance.value.setDrawMode('polygon')
        // 保持当前绘制状态不变
        console.log('设置属性:', selectedROIAttribute.value, '当前绘制状态:', isDrawingEnabled.value)
      }
      addOperationInfo(`[ROI] 选择${getAttributeDisplayName(selectedROIAttribute.value)}属性`)
    } else {
      if (roiDrawerInstance.value) {
        roiDrawerInstance.value.setCurrentAttribute(null)
      }
      addOperationInfo('[ROI] 清除ROI属性选择')
    }
  }

  /**
   * 切换绘制模式
   */
  const toggleDrawMode = () => {
    if (!selectedROIAttribute.value) {
      ElMessage.warning('请先选择ROI属性')
      return
    }

    isDrawingEnabled.value = !isDrawingEnabled.value
    
    // 打印调试信息
    console.log('切换ROI绘制状态:', {
      isDrawingEnabled: isDrawingEnabled.value,
      attribute: selectedROIAttribute.value,
      roiDrawerReady: !!roiDrawerInstance.value
    })
    
    if (roiDrawerInstance.value) {
      roiDrawerInstance.value.setEnabled(isDrawingEnabled.value)
      if (isDrawingEnabled.value) {
        roiDrawerInstance.value.setDrawMode('polygon')
        // 确保当前属性已设置
        roiDrawerInstance.value.setCurrentAttribute(selectedROIAttribute.value)
      }
    } else {
      console.warn('ROI绘制器未初始化，无法切换绘制模式')
      addOperationInfo('[WARNING] ROI绘制器未初始化，请先初始化')
      return
    }

    if (isDrawingEnabled.value) {
      addOperationInfo(`[ROI] 开始绘制${getAttributeDisplayName(selectedROIAttribute.value)}`)
    } else {
      addOperationInfo('[ROI] 绘制已暂停')
    }
  }

  /**
   * 清空所有ROI区域
   */
  const clearROIs = () => {
    roiList.value = []
    highlightedROIId.value = null
    if (roiDrawerInstance.value) {
      roiDrawerInstance.value.clearROIs()
      roiDrawerInstance.value.clearHighlight()
    }
    addOperationInfo('[ROI] 已清空所有ROI区域')
    ElMessage.success('已清空所有ROI区域')
  }

  /**
   * 清空指定属性的ROI
   * @param attribute ROI属性
   */
  const clearAttributeROIs = (attribute: 'pailiao' | 'yazhu') => {
    const beforeCount = roiList.value.length

    // 检查是否要删除当前高亮的ROI
    const highlightedROI = roiList.value.find(roi => roi.id === highlightedROIId.value)
    if (highlightedROI && highlightedROI.attribute === attribute) {
      highlightedROIId.value = null
      if (roiDrawerInstance.value) {
        roiDrawerInstance.value.clearHighlight()
      }
    }

    roiList.value = roiList.value.filter(roi => roi.attribute !== attribute)
    const afterCount = roiList.value.length
    const deletedCount = beforeCount - afterCount

    if (roiDrawerInstance.value) {
      roiDrawerInstance.value.setROIs(roiList.value)
    }

    const attributeName = getAttributeDisplayName(attribute)
    addOperationInfo(`[ROI] 已清空${deletedCount}个${attributeName}ROI区域`)
    ElMessage.success(`已清空${deletedCount}个${attributeName}ROI区域`)
  }

  /**
   * 保存ROI配置
   */
  const saveROIs = async () => {
    if (roiList.value.length === 0) {
      ElMessage.warning('没有ROI区域可保存')
      return
    }

    await saveROIConfigToBackend()
    addOperationInfo(`[ROI] 保存 ${roiList.value.length} 个ROI区域`)
    ElMessage.success(`已保存 ${roiList.value.length} 个ROI区域`)
  }

  /**
   * 保存ROI配置到后端
   */
  const saveROIConfigToBackend = async () => {
    try {
      if (roiList.value.length === 0) return

      // 保存ROI配置到后端
      const response = await fetch(`/api/detection-groups/1/rois`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rois: roiList.value.map(roi => ({
            name: roi.name,
            roi_type: roi.roi_type,
            coordinates: roi.coordinates || roi.points, // 🔥 修复：使用数据库字段名
            color: roi.color
          }))
        })
      })

      if (response.ok) {
        addOperationInfo('[ROI] ROI配置已保存到后端')
      }
    } catch (error) {
      console.error('保存ROI配置失败:', error)
      addOperationInfo('[ERROR] 保存ROI配置失败')
    }
  }

  /**
   * 删除ROI
   * @param roiId ROI的ID
   */
  const deleteROI = (roiId: string) => {
    const index = roiList.value.findIndex(roi => roi.id === roiId)
    if (index >= 0) {
      const roi = roiList.value[index]

      // 如果删除的是当前高亮的ROI，清除高亮状态
      if (highlightedROIId.value === roiId) {
        highlightedROIId.value = null
        if (roiDrawerInstance.value) {
          roiDrawerInstance.value.clearHighlight()
        }
      }

      roiList.value.splice(index, 1)
      addOperationInfo(`[ROI] 删除ROI区域: ${roi.name}`)

      // 使用ROI绘制器更新
      if (roiDrawerInstance.value) {
        roiDrawerInstance.value.setROIs(roiList.value)
      }
    }
  }

  /**
   * 编辑ROI
   * @param roiId ROI的ID
   */
  const editROI = (roiId: string) => {
    const roi = roiList.value.find(roi => roi.id === roiId)
    if (roi) {
      // TODO: 实现ROI编辑功能
      ElMessage.info('ROI编辑功能待实现')
      addOperationInfo(`[ROI] 编辑ROI: ${roi.name}`)
    }
  }

  /**
   * ROI高亮切换
   * @param roiId ROI的ID
   */
  const toggleROIHighlight = (roiId: string) => {
    if (!roiDrawerInstance.value) {
      console.warn('ROI绘制器未初始化')
      return
    }

    // 切换高亮状态
    if (highlightedROIId.value === roiId) {
      // 如果当前ROI已高亮，则清除高亮
      highlightedROIId.value = null
      roiDrawerInstance.value.clearHighlight()
      addOperationInfo('[ROI] 清除高亮显示')
    } else {
      // 高亮新的ROI
      highlightedROIId.value = roiId
      roiDrawerInstance.value.highlightROI(roiId)
      const roi = roiList.value.find(r => r.id === roiId)
      addOperationInfo(`[ROI] 高亮显示: ${roi?.name || roiId}`)
    }
  }

  /**
   * 加载ROI列表到视频中显示
   * @param roiData ROI数据列表
   */
  const loadROIListToVideo = (roiData: any[]) => {
    if (!roiDrawerInstance.value) {
      console.warn('ROI绘制器未初始化，无法加载ROI')
      return false
    }

    try {
      const success = roiDrawerInstance.value.loadROIList(roiData)
      if (success) {
        // 同步到组件的ROI列表
        roiList.value = [...roiDrawerInstance.value.rois]
        addOperationInfo(`[ROI] 成功加载 ${roiData.length} 个ROI到视频中`)
        ElMessage.success(`成功加载 ${roiData.length} 个ROI区域`)
        return true
      } else {
        addOperationInfo('[ROI] ROI加载失败')
        ElMessage.error('ROI加载失败')
        return false
      }
    } catch (error) {
      console.error('加载ROI到视频失败:', error)
      addOperationInfo(`[ERROR] 加载ROI失败: ${error}`)
      ElMessage.error('加载ROI失败')
      return false
    }
  }

  /**
   * 测试ROI加载功能
   */
  const testROILoad = () => {
    console.log('开始测试ROI加载功能')
    addOperationInfo('[TEST] 开始测试ROI加载功能')

    // 创建测试ROI数据
    const testROIs = [
      {
        id: 'test_pailiao_1',
        name: '测试排料口1',
        type: 'polygon',
        attribute: 'pailiao',
        color: '#00ffff',
        points: [
          { x: 100, y: 100 },
          { x: 200, y: 100 },
          { x: 200, y: 150 },
          { x: 100, y: 150 }
        ]
      },
      {
        id: 'test_yazhu_1',
        name: '测试压铸机1',
        type: 'polygon',
        attribute: 'yazhu',
        color: '#ff0000',
        points: [
          { x: 300, y: 200 },
          { x: 400, y: 200 },
          { x: 400, y: 280 },
          { x: 300, y: 280 }
        ]
      }
    ]

    console.log('测试ROI数据:', testROIs)

    // 先清空现有ROI
    roiList.value = []
    if (roiDrawerInstance.value) {
      roiDrawerInstance.value.clearROIs()
    }

    // 加载测试ROI
    const success = loadROIListToVideo(testROIs)
    if (success) {
      addOperationInfo('[TEST] 测试ROI加载成功')
      ElMessage.success('测试ROI加载成功')
    } else {
      addOperationInfo('[TEST] 测试ROI加载失败')
      ElMessage.error('测试ROI加载失败')
    }
  }

  /**
   * 手动初始化ROI绘制器
   */
  const manualInitROI = () => {
    console.log('[ROI] 手动初始化ROI绘制器')
    addOperationInfo('[ROI] 手动初始化ROI绘制器')

    // 清空当前ROI列表
    roiList.value = []
    
    // 清理现有实例
    if (roiDrawerInstance.value) {
      console.log('[ROI] ROI绘制器已存在，重新初始化')
      try {
        roiDrawerInstance.value.destroy()
      } catch (error) {
        console.error('[ROI] 销毁旧ROI绘制器失败:', error)
      }
      roiDrawerInstance.value = null
    }

    // 尝试获取canvas并初始化
    // 首先尝试使用已保存的canvas引用
    if (canvasElement.value) {
      addOperationInfo('[ROI] 使用已保存的canvas引用初始化')
      
      // 默认使用标准视频尺寸
      let videoWidth = 640
      let videoHeight = 360
      
      try {
        // 使用DOM方法获取视频尺寸 - 这是最可靠的方法
        const videoElement = document.querySelector('#divPlugin video') as HTMLVideoElement
        if (videoElement && videoElement.videoWidth && videoElement.videoHeight) {
          videoWidth = videoElement.videoWidth
          videoHeight = videoElement.videoHeight
          addOperationInfo(`[ROI] 从视频DOM元素获取尺寸: ${videoWidth}x${videoHeight}`)
        } else {
          // 备用：尝试从WebSDK获取视频尺寸
          const webVideoCtrl = (window as any).WebVideoCtrl
          if (webVideoCtrl && webVideoCtrl.I_GetWindowStatus) {
            const status = webVideoCtrl.I_GetWindowStatus(0)
            console.log('[ROI] WebSDK窗口状态:', status)
            
            if (status && (status.iPlayStatus === 1 || status.iPlayStatus === 2)) {
              addOperationInfo(`[ROI] WebSDK播放状态: ${status.iPlayStatus === 1 ? '预览中' : '回放中'}`)
            } else {
              addOperationInfo(`[ROI] 视频未处于播放状态，使用默认尺寸 ${videoWidth}x${videoHeight}`)
            }
          }
        }
      } catch (error) {
        console.error('[ROI] 获取视频尺寸失败:', error)
      }
      
      addOperationInfo(`[ROI] 设置Canvas尺寸: ${videoWidth}x${videoHeight}`)
      
      // 更新canvas尺寸
      canvasElement.value.width = videoWidth
      canvasElement.value.height = videoHeight
      
      // 确保样式正确
      canvasElement.value.style.display = 'block'
      canvasElement.value.style.position = 'absolute'
      canvasElement.value.style.top = '0'
      canvasElement.value.style.left = '0'
      canvasElement.value.style.pointerEvents = 'auto'
      canvasElement.value.style.zIndex = '10'
      
      // 初始化绘制器
      initROIDrawer(canvasElement.value)
      
      // 成功初始化后直接返回
      return
    }
    
    // 尝试通过DOM查找canvas
    const foundCanvas = document.querySelector('.roi-display-canvas') as HTMLCanvasElement
    if (foundCanvas) {
      addOperationInfo('[ROI] 通过DOM查询找到canvas元素，正在重新初始化')

      // 尝试获取视频实际尺寸
      let videoWidth = 640
      let videoHeight = 360
      
      try {
        // 优先从video元素获取尺寸
        const videoElement = document.querySelector('#divPlugin video') as HTMLVideoElement
        if (videoElement && videoElement.videoWidth && videoElement.videoHeight) {
          videoWidth = videoElement.videoWidth
          videoHeight = videoElement.videoHeight
          addOperationInfo(`[ROI] 从视频元素获取尺寸: ${videoWidth}x${videoHeight}`)
        }
      } catch (error) {
        console.error('[ROI] 获取视频尺寸失败:', error)
      }
      
      // 更新canvas尺寸和样式
      foundCanvas.width = videoWidth
      foundCanvas.height = videoHeight
      foundCanvas.style.display = 'block'
      foundCanvas.style.position = 'absolute'
      foundCanvas.style.top = '0'
      foundCanvas.style.left = '0'
      foundCanvas.style.pointerEvents = 'auto'
      foundCanvas.style.zIndex = '10'
      
      // 使用找到的canvas重新初始化
      initROIDrawer(foundCanvas)
    } else {
      // 尝试查找可能存在的VideoPlayer组件
      try {
        // 这里假设父组件或其他地方有VideoPlayer的引用
        const videoPlayerElement = document.querySelector('.video-container')
        if (videoPlayerElement) {
          // 如果找到了视频容器，尝试创建一个新的canvas
          addOperationInfo('[ROI] 找到视频容器，尝试创建新的canvas元素')
          
          const newCanvas = document.createElement('canvas')
          newCanvas.className = 'roi-display-canvas'
          
          // 尝试获取视频实际尺寸
          let videoWidth = 640
          let videoHeight = 360
          
          try {
            const videoElement = document.querySelector('#divPlugin video') as HTMLVideoElement
            if (videoElement && videoElement.videoWidth && videoElement.videoHeight) {
              videoWidth = videoElement.videoWidth
              videoHeight = videoElement.videoHeight
              addOperationInfo(`[ROI] 从视频元素获取尺寸: ${videoWidth}x${videoHeight}`)
            }
          } catch (error) {
            console.error('[ROI] 获取视频尺寸失败:', error)
          }
          
          newCanvas.width = videoWidth
          newCanvas.height = videoHeight
          newCanvas.style.display = 'block'
          newCanvas.style.position = 'absolute'
          newCanvas.style.top = '0'
          newCanvas.style.left = '0'
          newCanvas.style.pointerEvents = 'auto'
          newCanvas.style.zIndex = '10'
          
          // 添加到视频容器中
          videoPlayerElement.appendChild(newCanvas)
          
          // 使用新创建的canvas初始化
          initROIDrawer(newCanvas)
          return
        }
      } catch (error) {
        console.error('[ROI] 创建新canvas失败:', error)
      }
      
      // 如果都失败了，通知用户
      ElMessage.warning('找不到或无法创建ROI画布，请确保视频播放器已加载')
      addOperationInfo('[WARNING] 找不到或无法创建ROI画布，无法初始化绘制器')
    }
  }

  /**
   * 导出ROI配置
   */
  const exportROIs = () => {
    try {
      if (roiList.value.length === 0) {
        ElMessage.warning('没有ROI区域可导出')
        return
      }

      const exportData = {
        version: '2.0',
        timestamp: new Date().toISOString(),
        rois: roiList.value,
        summary: {
          total: roiList.value.length,
          pailiao: pailiaoROIs.value.length,
          yazhu: yazhuROIs.value.length
        }
      }

      const dataStr = JSON.stringify(exportData, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })

      const link = document.createElement('a')
      link.href = URL.createObjectURL(dataBlob)
      link.download = `roi_config_${Date.now()}.json`
      link.click()

      addOperationInfo(`[ROI] 已导出 ${roiList.value.length} 个ROI区域配置`)
      ElMessage.success('ROI配置导出成功')
    } catch (error) {
      console.error('导出ROI配置失败:', error)
      addOperationInfo('[ERROR] 导出ROI配置失败')
      ElMessage.error('导出ROI配置失败')
    }
  }

  /**
   * 导入ROI配置
   */
  const importROIs = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'

    input.onchange = (event: any) => {
      const file = event.target.files[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e: any) => {
        try {
          const importData = JSON.parse(e.target.result)

          if (!importData.rois || !Array.isArray(importData.rois)) {
            throw new Error('无效的ROI配置文件格式')
          }

          // 验证和修复ROI数据
          const validROIs = importData.rois.filter((roi: any) => {
            // 确保每个ROI都有必要的属性
            if (!roi.attribute) {
              roi.attribute = 'pailiao' // 默认为排料口
            }
            return roi.points && Array.isArray(roi.points) && roi.points.length > 0
          })

          roiList.value = validROIs

          // 确保ROI绘制器已初始化，然后加载ROI到视频中显示
          if (!roiDrawerInstance.value) {
            console.log('ROI绘制器未初始化，无法导入ROI')
            addOperationInfo('[WARNING] ROI绘制器未初始化，请先初始化ROI绘制器')
          } else {
            const success = roiDrawerInstance.value.loadROIList(validROIs)
            if (success) {
              addOperationInfo('[ROI] ROI已加载到视频显示中')
            } else {
              addOperationInfo('[ROI] ROI加载到视频显示失败，使用备用方法')
              roiDrawerInstance.value.setROIs(validROIs)
            }
          }

          const pailiaoCount = validROIs.filter((roi: any) => roi.attribute === 'pailiao').length
          const yazhuCount = validROIs.filter((roi: any) => roi.attribute === 'yazhu').length

          addOperationInfo(`[ROI] 已导入 ${validROIs.length} 个ROI区域配置 (排料口:${pailiaoCount}, 压铸机:${yazhuCount})`)
          ElMessage.success(`成功导入 ${validROIs.length} 个ROI区域`)
        } catch (error) {
          console.error('导入ROI配置失败:', error)
          addOperationInfo('[ERROR] 导入ROI配置失败')
          ElMessage.error('导入ROI配置失败，请检查文件格式')
        }
      }

      reader.readAsText(file)
    }

    input.click()
  }

  /**
   * 根据视频尺寸更新ROI绘制器
   * @param width 视频宽度
   * @param height 视频高度
   */
  const updateROIDrawerSize = (width: number, height: number) => {
    if (!roiDrawerInstance.value) {
      console.warn('ROI绘制器未初始化，无法更新尺寸')
      return false
    }
    
    try {
      // 检查当前Canvas元素
      if (!canvasElement.value) {
        console.warn('Canvas元素未找到，无法更新尺寸')
        return false
      }
      
      // 更新Canvas尺寸
      canvasElement.value.width = width
      canvasElement.value.height = height
      
      // 如果ROI绘制器有更新尺寸的方法，调用它
      if (typeof roiDrawerInstance.value.updateCanvasSize === 'function') {
        roiDrawerInstance.value.updateCanvasSize(width, height)
        addOperationInfo(`[ROI] 已更新ROI绘制器尺寸为 ${width}x${height}`)
      } 
      // 否则尝试重新初始化绘制器
      else {
        // 备份当前ROI列表
        const currentROIs = [...roiList.value]
        
        // 重新初始化绘制器
        initROIDrawer(canvasElement.value)
        
        // 恢复ROI列表
        if (currentROIs.length > 0 && roiDrawerInstance.value) {
          setTimeout(() => {
            roiDrawerInstance.value.loadROIList(currentROIs)
            addOperationInfo(`[ROI] 尺寸更新后重新加载 ${currentROIs.length} 个ROI`)
          }, 100)
        }
      }
      
      return true
    } catch (error) {
      console.error('更新ROI绘制器尺寸失败:', error)
      addOperationInfo(`[ERROR] 更新ROI绘制器尺寸失败: ${error}`)
      return false
    }
  }

  return {
    roiDrawerInstance,
    roiList,
    roiCount,
    selectedROIAttribute,
    isDrawingEnabled,
    highlightedROIId,
    pailiaoROIs,
    yazhuROIs,
    canvasElement,
    initROIDrawer,
    onROIDrawerReady,
    onROIAttributeChange,
    toggleDrawMode,
    clearROIs,
    clearAttributeROIs,
    saveROIs,
    deleteROI,
    editROI,
    toggleROIHighlight,
    loadROIListToVideo,
    testROILoad,
    manualInitROI,
    exportROIs,
    importROIs,
    getAttributeDisplayName,
    updateROIDrawerSize
  }
} 
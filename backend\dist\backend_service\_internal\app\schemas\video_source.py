from datetime import datetime
from typing import Optional

from pydantic import BaseModel


# 共享属性
class VideoSourceBase(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    source_type: Optional[str] = None  # "local_file", "rtsp_stream", "websdk_device"

    # 通用字段
    path: Optional[str] = None  # 本地文件路径或RTSP URL

    # WebSDK设备专用字段
    device_ip: Optional[str] = None
    device_port: Optional[int] = None
    device_username: Optional[str] = None
    device_password: Optional[str] = None
    device_protocol: Optional[int] = None  # 1-HTTP, 2-HTTPS
    channel_id: Optional[int] = None
    stream_type: Optional[int] = None  # 1-主码流, 2-子码流

    # 设备状态
    device_status: Optional[str] = None
    last_connected_at: Optional[datetime] = None


# 创建时需要的属性
class VideoSourceCreate(VideoSourceBase):
    name: str
    source_type: str  # "local_file", "rtsp_stream", "websdk_device"


# 更新时可以更新的属性
class VideoSourceUpdate(VideoSourceBase):
    pass


# API响应中包含的属性
class VideoSourceInDBBase(VideoSourceBase):
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


# 返回给API的视频源信息
class VideoSource(VideoSourceInDBBase):
    pass


# 测试视频源连接的请求
class VideoSourceTest(BaseModel):
    source_type: str  # "local_file", "rtsp_stream", "websdk_device"

    # 通用字段
    path: Optional[str] = None

    # WebSDK设备测试字段
    device_ip: Optional[str] = None
    device_port: Optional[int] = None
    device_username: Optional[str] = None
    device_password: Optional[str] = None
    device_protocol: Optional[int] = None
    channel_id: Optional[int] = None
    stream_type: Optional[int] = None
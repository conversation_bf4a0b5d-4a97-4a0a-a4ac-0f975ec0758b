<template>
  <div class="video-test-container">
    <div class="page-header">
      <h2>视频测试功能模块</h2>
      <p class="page-description">基于WebSDK_noPlugin的完整视频测试功能，包含登录预览、抓图功能、窗口分割等测试模块</p>
    </div>

    <div class="video-test-content">
      <VideoLoginPreviewTest />
    </div>
  </div>
</template>

<script setup lang="ts">
import VideoLoginPreviewTest from '@/components/video-test/VideoLoginPreviewTest.vue'
</script>

<style scoped>
.video-test-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: var(--text-color);
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: var(--text-color-soft);
  font-size: 14px;
  line-height: 1.5;
}

.video-test-content {
  flex: 1;
  padding: 20px 0;
}
</style>
<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/src/assets/jac.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>压铸件智能检测系统</title>

    <!-- WebSDK核心文件 -->
    <script src="/websdk/codebase/jsPlugin/jquery.min.js"></script>
    <!-- 加密库 - 必须在webVideoCtrl.js之前加载 -->
    <script src="/websdk/codebase/encryption/AES.js"></script>
    <script src="/websdk/codebase/encryption/cryptico.min.js"></script>
    <script src="/websdk/codebase/encryption/crypto-3.1.2.min.js"></script>
    <!-- 暴露MD5函数为全局变量 -->
    <script>
      // 确保MD5函数可以全局访问
      if (typeof CryptoJS !== 'undefined' && CryptoJS.MD5) {
        window.MD5 = function(message) {
          return CryptoJS.MD5(message).toString();
        };
      }
    </script>
    <!-- WebSDK主文件 -->
    <script src="/websdk/codebase/webVideoCtrl.js"></script>
    <script src="/websdk/codebase/jsPlugin/jsPlugin-3.0.0.min.js"></script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>

import platform
import psutil
import sys
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends
from pydantic import BaseModel
from typing import Dict, Any, List
from app.api import deps

router = APIRouter()

class SystemInfoResponse(BaseModel):
    """系统信息响应模型"""
    version: str
    python_version: str
    platform: str
    architecture: str
    uptime: str
    cpu_usage: float
    cpu_count: int
    memory_usage: float
    memory_total: str
    memory_available: str
    disk_usage: float
    disk_total: str
    disk_free: str
    network_info: Dict[str, Any]
    process_count: int
    boot_time: str

def get_uptime() -> str:
    """获取系统运行时间"""
    try:
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time
        days = uptime.days
        hours, remainder = divmod(uptime.seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        return f"{days}天 {hours}小时 {minutes}分钟"
    except Exception:
        return "未知"

def get_memory_info() -> tuple:
    """获取内存信息"""
    try:
        memory = psutil.virtual_memory()
        total_gb = memory.total / (1024**3)
        available_gb = memory.available / (1024**3)
        usage_percent = memory.percent
        return usage_percent, f"{total_gb:.1f} GB", f"{available_gb:.1f} GB"
    except Exception:
        return 0.0, "未知", "未知"

def get_disk_info() -> tuple:
    """获取磁盘信息"""
    try:
        disk = psutil.disk_usage('/')
        total_gb = disk.total / (1024**3)
        free_gb = disk.free / (1024**3)
        usage_percent = (disk.used / disk.total) * 100
        return usage_percent, f"{total_gb:.1f} GB", f"{free_gb:.1f} GB"
    except Exception:
        # Windows系统使用C盘
        try:
            disk = psutil.disk_usage('C:\\')
            total_gb = disk.total / (1024**3)
            free_gb = disk.free / (1024**3)
            usage_percent = (disk.used / disk.total) * 100
            return usage_percent, f"{total_gb:.1f} GB", f"{free_gb:.1f} GB"
        except Exception:
            return 0.0, "未知", "未知"

def get_network_info() -> Dict[str, Any]:
    """获取网络信息"""
    try:
        network_stats = psutil.net_io_counters()
        network_info = {
            "bytes_sent": f"{network_stats.bytes_sent / (1024**2):.1f} MB",
            "bytes_recv": f"{network_stats.bytes_recv / (1024**2):.1f} MB",
            "packets_sent": network_stats.packets_sent,
            "packets_recv": network_stats.packets_recv
        }
        return network_info
    except Exception:
        return {"error": "无法获取网络信息"}

@router.get("/system-info", response_model=SystemInfoResponse)
async def get_system_info():
    """获取系统信息"""
    try:
        # 获取CPU使用率
        cpu_usage = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # 获取内存信息
        memory_usage, memory_total, memory_available = get_memory_info()
        
        # 获取磁盘信息
        disk_usage, disk_total, disk_free = get_disk_info()
        
        # 获取网络信息
        network_info = get_network_info()
        
        # 获取进程数量
        process_count = len(psutil.pids())
        
        # 获取启动时间
        boot_time = datetime.fromtimestamp(psutil.boot_time()).strftime("%Y-%m-%d %H:%M:%S")
        
        return SystemInfoResponse(
            version="1.0.0",
            python_version=f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            platform=platform.system(),
            architecture=platform.architecture()[0],
            uptime=get_uptime(),
            cpu_usage=round(cpu_usage, 1),
            cpu_count=cpu_count,
            memory_usage=round(memory_usage, 1),
            memory_total=memory_total,
            memory_available=memory_available,
            disk_usage=round(disk_usage, 1),
            disk_total=disk_total,
            disk_free=disk_free,
            network_info=network_info,
            process_count=process_count,
            boot_time=boot_time
        )
    except Exception as e:
        # 如果获取系统信息失败，返回基础信息
        return SystemInfoResponse(
            version="1.0.0",
            python_version=f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            platform=platform.system(),
            architecture="未知",
            uptime="未知",
            cpu_usage=0.0,
            cpu_count=1,
            memory_usage=0.0,
            memory_total="未知",
            memory_available="未知",
            disk_usage=0.0,
            disk_total="未知",
            disk_free="未知",
            network_info={"error": "无法获取网络信息"},
            process_count=0,
            boot_time="未知"
        )


class SystemPerformanceResponse(BaseModel):
    """系统性能响应模型"""
    timestamp: str
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    active_processes: int
    load_average: List[float]


@router.get("/system-performance", response_model=SystemPerformanceResponse)
async def get_system_performance():
    """获取系统性能数据"""
    try:
        # 获取CPU使用率
        cpu_usage = psutil.cpu_percent(interval=0.1)
        
        # 获取内存使用率
        memory = psutil.virtual_memory()
        memory_usage = memory.percent
        
        # 获取磁盘使用率
        try:
            disk = psutil.disk_usage('/')
        except:
            disk = psutil.disk_usage('C:\\')
        disk_usage = (disk.used / disk.total) * 100
        
        # 获取网络IO
        network_stats = psutil.net_io_counters()
        network_io = {
            "bytes_sent_mb": round(network_stats.bytes_sent / (1024**2), 2),
            "bytes_recv_mb": round(network_stats.bytes_recv / (1024**2), 2)
        }
        
        # 获取活跃进程数
        active_processes = len([p for p in psutil.process_iter() if p.status() == psutil.STATUS_RUNNING])
        
        # 获取负载平均值（Windows上可能不可用）
        try:
            load_avg = list(psutil.getloadavg())
        except:
            load_avg = [cpu_usage / 100, cpu_usage / 100, cpu_usage / 100]
        
        return SystemPerformanceResponse(
            timestamp=datetime.now().isoformat(),
            cpu_usage=round(cpu_usage, 1),
            memory_usage=round(memory_usage, 1),
            disk_usage=round(disk_usage, 1),
            network_io=network_io,
            active_processes=active_processes,
            load_average=load_avg
        )
    except Exception as e:
        return SystemPerformanceResponse(
            timestamp=datetime.now().isoformat(),
            cpu_usage=0.0,
            memory_usage=0.0,
            disk_usage=0.0,
            network_io={"bytes_sent_mb": 0.0, "bytes_recv_mb": 0.0},
            active_processes=0,
            load_average=[0.0, 0.0, 0.0]
        )
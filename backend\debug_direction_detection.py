#!/usr/bin/env python3
"""
调试方向检测
专门调试方向检测器为什么不能检测到MOVING_DOWN
"""

import numpy as np
import cv2
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.video_detection import DirectionDetector

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def debug_direction_detector():
    """调试方向检测器"""
    print("🧭 调试方向检测器")
    print("=" * 40)
    
    # 创建方向检测器
    direction_params = {
        'detector_type': 'background_subtraction',
        'motion_params': {
            'minArea': 50,
            'detectionThreshold': 20,
            'learningRate': 0.1,
            'shadowsThreshold': 0,
            'enabled': True
        },
        'minDisplacement': 1,  # 非常低的阈值
        'maxPatience': 2,
        'consecutiveThreshold': 1  # 只需要1帧就能确定方向
    }
    
    direction_detector = DirectionDetector(direction_params)
    
    # ROI信息
    roi_info = {
        'type': 'polygon',
        'coords': [
            {"x": 100, "y": 100},
            {"x": 200, "y": 100},
            {"x": 200, "y": 200},
            {"x": 100, "y": 200}
        ]
    }
    
    print(f"参数配置:")
    print(f"  minDisplacement: {direction_params['minDisplacement']}")
    print(f"  consecutiveThreshold: {direction_params['consecutiveThreshold']}")
    print(f"  minArea: {direction_params['motion_params']['minArea']}")
    
    # 创建明显的向下运动序列
    frames = []
    centers = []
    
    for i in range(10):
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        frame[:, :] = (50, 50, 50)  # 灰色背景
        
        # 创建明显的向下运动
        y_pos = 110 + i * 15  # 每帧向下移动15像素
        x_pos = 150
        
        # 添加大的白色矩形
        cv2.rectangle(frame, (x_pos-10, y_pos-10), (x_pos+10, y_pos+10), (255, 255, 255), -1)
        
        frames.append(frame)
        centers.append((x_pos, y_pos))
    
    print(f"\n创建运动序列:")
    print(f"  帧数: {len(frames)}")
    print(f"  起始中心: {centers[0]}")
    print(f"  结束中心: {centers[-1]}")
    print(f"  总位移: {centers[-1][1] - centers[0][1]} 像素")
    print(f"  每帧位移: 15 像素")
    
    # 逐帧检测
    for i, frame in enumerate(frames):
        print(f"\n--- 帧 {i+1} ---")
        print(f"预期中心: {centers[i]}")
        
        try:
            direction, contours, direction_info = direction_detector.detect(frame, roi_info)
            
            print(f"检测结果:")
            print(f"  方向: {direction}")
            print(f"  轮廓数: {len(contours)}")
            
            if isinstance(direction_info, dict):
                detected_center = direction_info.get('center', (0, 0))
                confidence = direction_info.get('confidence', 0)
                print(f"  检测中心: {detected_center}")
                print(f"  置信度: {confidence:.2f}")
                
                # 检查中心点是否合理
                if detected_center != (0, 0):
                    expected_center = centers[i]
                    center_error = abs(detected_center[0] - expected_center[0]) + abs(detected_center[1] - expected_center[1])
                    print(f"  中心误差: {center_error} 像素")
                    
                    if center_error < 50:
                        print(f"  ✅ 中心检测准确")
                    else:
                        print(f"  ❌ 中心检测偏差较大")
            
            # 检查方向历史
            if hasattr(direction_detector, 'direction_history'):
                print(f"  方向历史: {direction_detector.direction_history}")
            
            # 检查前一个中心点
            if hasattr(direction_detector, 'prev_center') and direction_detector.prev_center:
                prev_center = direction_detector.prev_center
                if i > 0:
                    dy = centers[i][1] - prev_center[1]
                    print(f"  实际位移: dy={dy}")
                    print(f"  位移阈值: {direction_params['minDisplacement']}")
                    
                    if abs(dy) >= direction_params['minDisplacement']:
                        expected_direction = 'MOVING_DOWN' if dy > 0 else 'MOVING_UP'
                        print(f"  预期方向: {expected_direction}")
                        
                        if direction == expected_direction:
                            print(f"  ✅ 方向检测正确")
                        else:
                            print(f"  ❌ 方向检测错误")
            
            if direction == 'MOVING_DOWN':
                print(f"  🎉 成功检测到向下运动！")
                break
                
        except Exception as e:
            print(f"❌ 检测失败: {e}")
            import traceback
            traceback.print_exc()

def test_simple_direction():
    """测试简单的方向检测"""
    print(f"\n🔬 测试简单方向检测")
    print("=" * 30)
    
    # 创建极简的方向检测器
    simple_params = {
        'detector_type': 'background_subtraction',
        'motion_params': {
            'minArea': 10,  # 极低阈值
            'detectionThreshold': 10,
            'learningRate': 0.2,
            'shadowsThreshold': 0,
            'enabled': True
        },
        'minDisplacement': 1,
        'maxPatience': 1,
        'consecutiveThreshold': 1
    }
    
    detector = DirectionDetector(simple_params)
    
    roi_info = {
        'type': 'polygon',
        'coords': [
            {"x": 100, "y": 100},
            {"x": 200, "y": 100},
            {"x": 200, "y": 200},
            {"x": 100, "y": 200}
        ]
    }
    
    # 创建两帧：第一帧空，第二帧有物体
    frame1 = np.zeros((480, 640, 3), dtype=np.uint8)
    frame1[:, :] = (50, 50, 50)
    
    frame2 = frame1.copy()
    cv2.rectangle(frame2, (140, 150), (160, 170), (255, 255, 255), -1)  # 在下方位置
    
    print("测试两帧检测:")
    print("  帧1: 空背景")
    print("  帧2: 白色矩形在(140,150)-(160,170)")
    
    # 第一帧
    print(f"\n处理第一帧:")
    direction1, contours1, info1 = detector.detect(frame1, roi_info)
    print(f"  方向: {direction1}, 轮廓: {len(contours1)}")
    
    # 第二帧
    print(f"\n处理第二帧:")
    direction2, contours2, info2 = detector.detect(frame2, roi_info)
    print(f"  方向: {direction2}, 轮廓: {len(contours2)}")
    
    if isinstance(info2, dict):
        center = info2.get('center', (0, 0))
        print(f"  检测中心: {center}")
        
        if center != (0, 0):
            print(f"  ✅ 成功检测到物体中心")
        else:
            print(f"  ❌ 未检测到物体中心")

if __name__ == "__main__":
    try:
        test_simple_direction()
        debug_direction_detector()
        
        print("\n" + "=" * 40)
        print("✅ 方向检测调试完成！")
        
    except Exception as e:
        print(f"\n❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

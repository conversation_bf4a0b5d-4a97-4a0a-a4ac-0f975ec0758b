<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>设备兼容性检测工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .log { height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f8f9fa; font-family: monospace; font-size: 12px; }
        .video-container { width: 640px; height: 360px; border: 2px solid #ccc; background: #000; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .status-unknown { color: #6c757d; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 设备兼容性检测工具</h1>
        <p>专门用于检测海康威视设备的WebSocket取流兼容性</p>
        
        <div class="section info">
            <h3>📋 设备信息</h3>
            <input type="text" id="deviceIp" placeholder="设备IP" value="************" style="margin: 5px; padding: 8px;">
            <input type="text" id="devicePort" placeholder="端口" value="80" style="margin: 5px; padding: 8px;">
            <input type="text" id="username" placeholder="用户名" value="admin" style="margin: 5px; padding: 8px;">
            <input type="password" id="password" placeholder="密码" value="admin123" style="margin: 5px; padding: 8px;">
            <br>
            <button class="btn btn-primary" onclick="startCompatibilityTest()">🚀 开始兼容性检测</button>
            <button class="btn btn-warning" onclick="getDeviceInfo()">📋 获取设备信息</button>
            <button class="btn btn-danger" onclick="clearLog()">🗑️ 清空日志</button>
        </div>
        
        <div class="section">
            <h3>📊 检测结果</h3>
            <table id="resultTable">
                <thead>
                    <tr>
                        <th>检测项目</th>
                        <th>状态</th>
                        <th>详细信息</th>
                        <th>建议</th>
                    </tr>
                </thead>
                <tbody id="resultTableBody">
                    <tr>
                        <td>设备连接</td>
                        <td id="deviceConnection" class="status-unknown">⏳ 未检测</td>
                        <td id="deviceConnectionDetail">-</td>
                        <td id="deviceConnectionSuggestion">-</td>
                    </tr>
                    <tr>
                        <td>设备信息</td>
                        <td id="deviceInfo" class="status-unknown">⏳ 未检测</td>
                        <td id="deviceInfoDetail">-</td>
                        <td id="deviceInfoSuggestion">-</td>
                    </tr>
                    <tr>
                        <td>通道信息</td>
                        <td id="channelInfo" class="status-unknown">⏳ 未检测</td>
                        <td id="channelInfoDetail">-</td>
                        <td id="channelInfoSuggestion">-</td>
                    </tr>
                    <tr>
                        <td>WebSocket直连</td>
                        <td id="websocketDirect" class="status-unknown">⏳ 未检测</td>
                        <td id="websocketDirectDetail">-</td>
                        <td id="websocketDirectSuggestion">-</td>
                    </tr>
                    <tr>
                        <td>WebSocket代理</td>
                        <td id="websocketProxy" class="status-unknown">⏳ 未检测</td>
                        <td id="websocketProxyDetail">-</td>
                        <td id="websocketProxySuggestion">-</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div id="videoContainer" class="video-container"></div>
        
        <div class="section">
            <h3>📝 详细日志</h3>
            <div class="log" id="logContainer"></div>
        </div>
    </div>

    <!-- WebSDK脚本 -->
    <script src="/websdk/codebase/jsPlugin/jquery.min.js"></script>
    <script src="/websdk/codebase/encryption/AES.js"></script>
    <script src="/websdk/codebase/encryption/cryptico.min.js"></script>
    <script src="/websdk/codebase/encryption/crypto-3.1.2.min.js"></script>
    <script>
        if (typeof CryptoJS !== 'undefined' && CryptoJS.MD5) {
            window.MD5 = function(message) { return CryptoJS.MD5(message).toString(); };
        }
    </script>
    <script src="/websdk/codebase/webVideoCtrl.js"></script>
    <script src="/websdk/codebase/jsPlugin/jsPlugin-3.0.0.min.js"></script>

    <script>
        let g_iWndIndex = 0;
        let currentDevice = null;
        let isInitialized = false;
        let testResults = {};

        function addLog(message, type = 'info') {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const logItem = document.createElement('div');
            logItem.style.marginBottom = '3px';
            logItem.innerHTML = `<span style="color: #666;">[${timeStr}]</span> ${message}`;
            logContainer.appendChild(logItem);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }

        function updateResult(testName, status, detail, suggestion) {
            const statusEl = document.getElementById(testName);
            const detailEl = document.getElementById(testName + 'Detail');
            const suggestionEl = document.getElementById(testName + 'Suggestion');
            
            statusEl.className = `status-${status}`;
            switch(status) {
                case 'ok':
                    statusEl.innerHTML = '✅ 成功';
                    break;
                case 'error':
                    statusEl.innerHTML = '❌ 失败';
                    break;
                case 'warning':
                    statusEl.innerHTML = '⚠️ 警告';
                    break;
                default:
                    statusEl.innerHTML = '⏳ 检测中...';
            }
            
            detailEl.textContent = detail || '-';
            suggestionEl.textContent = suggestion || '-';
        }

        function initWebSDK() {
            if (!window.WebVideoCtrl) {
                addLog('❌ WebSDK未加载');
                return false;
            }

            if (!WebVideoCtrl.I_SupportNoPlugin()) {
                addLog('❌ 浏览器不支持无插件模式');
                return false;
            }

            addLog('🔧 初始化WebSDK...');
            
            WebVideoCtrl.I_InitPlugin("100%", "100%", {
                bWndFull: true,
                iPackageType: 2,
                iWndowType: 1,
                bNoPlugin: true,
                cbSelWnd: function(xmlDoc) {
                    g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
                },
                cbInitPluginComplete: function() {
                    addLog('✅ WebSDK插件初始化完成');
                    WebVideoCtrl.I_InsertOBJECTPlugin("videoContainer");
                    isInitialized = true;
                },
                cbPluginErrorHandler: function(iWndIndex, iErrorCode, oError) {
                    addLog(`❌ 插件错误: 窗口${iWndIndex}, 错误码${iErrorCode}`);
                }
            });

            return true;
        }

        function startCompatibilityTest() {
            addLog('🚀 开始设备兼容性检测...');
            clearResults();
            
            if (!isInitialized) {
                if (!initWebSDK()) {
                    return;
                }
                // 等待初始化完成
                setTimeout(startCompatibilityTest, 1000);
                return;
            }
            
            testDeviceConnection();
        }

        function clearResults() {
            const tests = ['deviceConnection', 'deviceInfo', 'channelInfo', 'websocketDirect', 'websocketProxy'];
            tests.forEach(test => {
                updateResult(test, 'unknown', '检测中...', '');
            });
        }

        function testDeviceConnection() {
            const ip = document.getElementById('deviceIp').value;
            const port = document.getElementById('devicePort').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!ip || !port) {
                addLog('❌ 请填写设备IP和端口');
                updateResult('deviceConnection', 'error', '缺少设备信息', '请填写完整的设备信息');
                return;
            }

            const deviceIdentify = `${ip}_${port}`;
            addLog(`🔑 测试设备连接: ${ip}:${port}`);
            
            WebVideoCtrl.I_Login(ip, 1, parseInt(port), username, password, {
                success: function(xmlDoc) {
                    addLog(`✅ 设备连接成功`);
                    currentDevice = { ip, port, username, password, deviceIdentify };
                    updateResult('deviceConnection', 'ok', `${ip}:${port} 连接成功`, '设备网络连接正常');
                    
                    // 继续下一步测试
                    setTimeout(getDeviceInfo, 500);
                },
                error: function(status, xmlDoc) {
                    addLog(`❌ 设备连接失败: ${status}`);
                    let detail = `连接失败，状态码: ${status}`;
                    let suggestion = '检查IP地址、端口、用户名密码';
                    
                    if (status === 401) {
                        suggestion = '用户名或密码错误';
                    } else if (status === 404) {
                        suggestion = '设备不存在或网络不通';
                    }
                    
                    updateResult('deviceConnection', 'error', detail, suggestion);
                }
            });
        }

        function getDeviceInfo() {
            if (!currentDevice) {
                addLog('❌ 设备未连接');
                return;
            }

            addLog('📋 获取设备信息...');
            
            WebVideoCtrl.I_GetDeviceInfo(currentDevice.deviceIdentify, {
                success: function(xmlDoc) {
                    try {
                        const $doc = $(xmlDoc);
                        const deviceName = $doc.find("deviceName").text();
                        const model = $doc.find("model").text();
                        const firmwareVersion = $doc.find("firmwareVersion").text();
                        const encoderVersion = $doc.find("encoderVersion").text();
                        
                        const deviceDetail = `${model} (固件: ${firmwareVersion})`;
                        addLog(`✅ 设备信息: ${deviceDetail}`);
                        
                        updateResult('deviceInfo', 'ok', deviceDetail, '设备信息获取成功');
                        
                        // 检查固件版本是否支持WebSocket
                        const suggestion = checkWebSocketSupport(model, firmwareVersion);
                        if (suggestion) {
                            updateResult('deviceInfo', 'warning', deviceDetail, suggestion);
                        }
                        
                    } catch (e) {
                        addLog('⚠️ 解析设备信息失败');
                        updateResult('deviceInfo', 'warning', '信息解析失败', '设备可能不支持完整的设备信息接口');
                    }
                    
                    // 继续下一步测试
                    setTimeout(testChannelInfo, 500);
                },
                error: function(status, xmlDoc) {
                    addLog(`⚠️ 获取设备信息失败: ${status}`);
                    updateResult('deviceInfo', 'warning', '无法获取设备信息', '设备可能不支持设备信息接口');
                    
                    // 继续下一步测试
                    setTimeout(testChannelInfo, 500);
                }
            });
        }

        function checkWebSocketSupport(model, firmwareVersion) {
            // 根据已知信息检查WebSocket支持
            if (firmwareVersion && firmwareVersion.includes('V5.')) {
                return '固件版本较新，应该支持WebSocket';
            } else if (firmwareVersion && firmwareVersion.includes('V4.')) {
                return '固件版本较老，可能不支持WebSocket，建议升级';
            }
            return null;
        }

        function testChannelInfo() {
            if (!currentDevice) return;

            addLog('📡 测试通道信息获取...');
            let analogChannels = 0;
            let digitalChannels = 0;
            
            // 测试模拟通道
            WebVideoCtrl.I_GetAnalogChannelInfo(currentDevice.deviceIdentify, {
                async: false,
                success: function(xmlDoc) {
                    const channels = $(xmlDoc).find("VideoInputChannel");
                    analogChannels = channels.length;
                    addLog(`✅ 获取到 ${analogChannels} 个模拟通道`);
                },
                error: function(status, xmlDoc) {
                    addLog(`⚠️ 获取模拟通道失败: ${status}`);
                }
            });

            // 测试数字通道
            WebVideoCtrl.I_GetDigitalChannelInfo(currentDevice.deviceIdentify, {
                async: false,
                success: function(xmlDoc) {
                    const channels = $(xmlDoc).find("InputProxyChannelStatus");
                    digitalChannels = channels.length;
                    addLog(`✅ 获取到 ${digitalChannels} 个数字通道`);
                },
                error: function(status, xmlDoc) {
                    addLog(`⚠️ 获取数字通道失败: ${status}`);
                }
            });

            const totalChannels = analogChannels + digitalChannels;
            if (totalChannels > 0) {
                updateResult('channelInfo', 'ok', `模拟通道: ${analogChannels}, 数字通道: ${digitalChannels}`, '通道信息获取正常');
            } else {
                updateResult('channelInfo', 'error', '未获取到任何通道', '检查设备配置或权限设置');
            }

            // 继续WebSocket测试
            setTimeout(testWebSocketDirect, 1000);
        }

        function testWebSocketDirect() {
            if (!currentDevice) return;

            addLog('🌐 测试WebSocket直连模式...');
            
            WebVideoCtrl.I_StartRealPlay(currentDevice.deviceIdentify, {
                iWndIndex: g_iWndIndex,  // 明确指定播放窗口
                iStreamType: 1,          // 主码流
                iChannelID: 1,           // 通道1
                bZeroChannel: false,     // 非零通道
                iPort: 554,             // 修复：使用正确的参数名 iPort
                bProxy: false,
                success: function() {
                    addLog('✅ WebSocket直连模式成功！');
                    updateResult('websocketDirect', 'ok', '直连模式取流成功', '设备完全支持WebSocket直连');
                    
                    // 停止预览并测试代理模式
                    setTimeout(() => {
                        WebVideoCtrl.I_Stop();
                        setTimeout(testWebSocketProxy, 1000);
                    }, 2000);
                },
                error: function(status, xmlDoc) {
                    let detail = `直连失败，状态码: ${status || 'undefined'}`;
                    let suggestion = '';
                    
                    if (status === 403) {
                        suggestion = '设备不支持直连，需要代理模式';
                    } else if (status === undefined) {
                        suggestion = '设备可能不支持WebSocket协议';
                    } else {
                        suggestion = '检查网络连接和设备配置';
                    }
                    
                    addLog(`❌ WebSocket直连失败: ${detail}`);
                    updateResult('websocketDirect', 'error', detail, suggestion);
                    
                    // 测试代理模式
                    setTimeout(testWebSocketProxy, 1000);
                }
            });
        }

        function testWebSocketProxy() {
            if (!currentDevice) return;

            addLog('🔄 测试WebSocket代理模式...');
            
            WebVideoCtrl.I_StartRealPlay(currentDevice.deviceIdentify, {
                iWndIndex: g_iWndIndex,  // 明确指定播放窗口
                iStreamType: 1,          // 主码流
                iChannelID: 1,           // 通道1
                bZeroChannel: false,     // 非零通道
                iPort: 554,             // 修复：使用正确的参数名 iPort
                bProxy: true,
                success: function() {
                    addLog('✅ WebSocket代理模式成功！');
                    updateResult('websocketProxy', 'ok', '代理模式取流成功', '设备支持WebSocket代理模式');
                    
                    // 停止预览
                    setTimeout(() => {
                        WebVideoCtrl.I_Stop();
                        addLog('🎉 兼容性检测完成！');
                    }, 2000);
                },
                error: function(status, xmlDoc) {
                    let detail = `代理模式失败，状态码: ${status || 'undefined'}`;
                    let suggestion = '';
                    
                    if (status === undefined) {
                        suggestion = '设备不支持WebSocket协议，建议升级固件或使用其他取流方式';
                    } else {
                        suggestion = '需要配置nginx代理或检查网络设置';
                    }
                    
                    addLog(`❌ WebSocket代理模式失败: ${detail}`);
                    updateResult('websocketProxy', 'error', detail, suggestion);
                    
                    addLog('❌ 设备不支持WebSocket取流');
                }
            });
        }

        // 页面加载完成后初始化
        window.onload = function() {
            addLog('🌟 设备兼容性检测工具启动');
            if (initWebSDK()) {
                addLog('✅ WebSDK初始化成功');
            } else {
                addLog('❌ WebSDK初始化失败');
            }
        };
    </script>
</body>
</html>

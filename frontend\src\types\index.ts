// 通用分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 用户类型
export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at: string;
}

// 数据库表信息类型
export interface TableInfo {
  name: string;
  rows: number;
  description: string;
}

// 数据库查询结果类型
export interface QueryResult<T = Record<string, any>> {
  columns: string[];
  data: T[];
  rows_affected?: number;
}

// 表数据响应类型
export interface TableDataResponse<T = Record<string, any>> {
  columns: string[];
  data: T[];
  total: number;
  page: number;
  page_size: number;
}

// 视频源类型
export interface VideoSource {
  id: number | string;
  name: string;
  description: string | null;
  source_type: string;
  path?: string;
  created_at?: string;
  updated_at?: string;
  // WebSDK设备特有属性
  device_ip?: string;
  device_port?: number;
  device_username?: string;
  device_password?: string;
  device_protocol?: string;
  channel_id?: number;
  stream_type?: number;
  status?: string;
}

// 压铸机类型
export interface DieCaster {
  id: number;
  name: string;
  description: string | null;
  ip_address: string | null;
  port: number | null;
  status: string;
  created_at: string;
  updated_at: string;
}



// 检测模板类型
export interface DetectionTemplate {
  id: number;
  name: string;
  description: string | null;
  config_json: Record<string, any>;
  status: string;
  created_at: string;
  updated_at: string;
}

// 检测组类型
export interface DetectionGroup {
  id: number;
  name: string;
  template_id: number | null;
  die_caster_id: number;
  video_source_id: number;
  config_json: Record<string, any>;
  status: string;
  created_at: string;
  updated_at: string;

  template?: DetectionTemplate;
  die_caster?: DieCaster;
  video_source?: VideoSource;
}

// ROI类型
export interface ROI {
  id: string;
  detection_group_id?: number;
  name: string;
  type: 'rectangle' | 'polygon' | 'circle';
  coordinates?: number[][];
  points: Point[];
  color: string;
  enabled: boolean;
  parameters?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

// 点坐标类型
export interface Point {
  x: number;
  y: number;
}

// 运动方向类型
export interface Direction {
  angle: number; // 角度 (0-360)
  magnitude: number; // 强度 (0-1)
}

// 轮廓类型
export type Contour = Point[];

// 检测结果类型
export interface DetectionResult {
  motionDetected: boolean;
  contourCount: number;
  direction: Direction | null;
  roiViolations: number;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'frame' | 'detection_result' | 'roi_update' | 'control' | 'error' | 'config_update';
  timestamp: number;
  data: any;
}

// 帧消息类型
export interface FrameMessage {
  type: 'frame';
  data: {
    frame_base64: string;
    frame_id: number;
    width: number;
    height: number;
  };
}

// 检测结果消息类型
export interface DetectionResultMessage {
  type: 'detection_result';
  data: {
    motion_detected: boolean;
    contours: Contour[];
    direction: Direction | null;
    roi_violations: number;
  };
}

// 配置更新消息类型
export interface ConfigUpdateMessage {
  type: 'config_update';
  data: {
    sensitivity: number;
    min_area: number;
    enable_direction: boolean;
    rois: ROI[];
  };
}

// 检测参数类型
export interface DetectionParams {
  sensitivity: number;
  minArea: number;
  learningRate: number;
  noiseFilter: number;
  enableDirection: boolean;
  enableShadowDetection: boolean;
  enableROIOnly: boolean;
}

// 显示选项类型
export interface DisplayOptions {
  showContours: boolean;
  showDirection: boolean;
  showROI: boolean;
  showCoordinates: boolean;
  showStats: boolean;
  overlayOpacity: number;
}

// 预设配置类型
export interface PresetConfig {
  id: string;
  name: string;
  description: string;
  params: DetectionParams;
}

// ROI扩展类型（包含检测类型和统计信息）
export interface ROIExtended extends ROI {
  detectionTypes?: string[];
  sensitivity?: number;
  minArea?: number;
  stats?: {
    detectionCount: number;
    lastDetection?: number;
  };
}

// 帧消息类型（修正）
export interface FrameMessage {
  frame_base64: string;
  frame_id: number;
  width: number;
  height: number;
}

// 检测结果消息类型（修正）
export interface DetectionResultMessage {
  motion_detected: boolean;
  contours?: Contour[];
  direction?: Direction | null;
  roi_violations?: number;
}

// WebSocket连接状态类型
export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

// 视频检测配置类型
export interface VideoDetectionConfig {
  sensitivity: number;
  min_area: number;
  enable_direction: boolean;
  rois: ROI[];
  learning_rate?: number;
  noise_filter?: number;
  enable_shadow_detection?: boolean;
  enable_roi_only?: boolean;
}

// 导出WebSDK相关类型
export * from './websdk.d';

// 通用类型定义

// 点坐标
export interface Point {
  x: number;
  y: number;
}

// 轮廓类型
export interface Contour {
  points: Point[];
}

// 运动方向
export interface Direction {
  direction: string;
  center: [number, number];
  confidence: number;
}

// ROI违规信息
export interface ROIViolation {
  roi_id: string;
  roi_name: string;
  violation_type: string;
}

// 检测结果
export interface DetectionResult {
  motion_detected: boolean;
  contours: Contour[];
  direction?: Direction;
  roi_violations: ROIViolation[];
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string;
  message?: string;
  timestamp?: string;
  [key: string]: any;
}

// 帧消息
export interface FrameMessage extends WebSocketMessage {
  type: 'frame';
  frame: string; // Base64编码的图像
  roi_list: any[];
  detection_mode: 'motion' | 'direction';
  timestamp: number;
}

// 检测结果消息
export interface DetectionResultMessage extends WebSocketMessage {
  type: 'detection_result';
  motion_detected: boolean;
  contours: Contour[];
  direction?: Direction;
  roi_violations: ROIViolation[];
  timestamp?: number;
}

// 配置更新消息
export interface ConfigUpdateMessage extends WebSocketMessage {
  type: 'update_config';
  config: any;
}
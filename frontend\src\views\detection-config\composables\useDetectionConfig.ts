import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import { 
  Plus, 
  Edit, 
  Delete, 
  CopyDocument, 
  MoreFilled, 
  Link, 
  Setting, 
  Close,
  Folder,
  Monitor,
  VideoCamera,
  View,
  Refresh
} from '@element-plus/icons-vue';
import {
  getDetectionTemplates,
  createDetectionTemplate,
  updateDetectionTemplate,
  deleteDetectionTemplate,
  copyDetectionTemplate,
  getDetectionTemplate,
  associateDieCasters,
  removeDieCasterAssociation as removeDieCasterAssociationAPI,
  getAvailableDieCasters,
  type DetectionTemplate,
  type DetectionTemplateWithDieCasters,
  type DetectionTemplateCreate,
  type DetectionTemplateUpdate
} from '@/api/detection-templates';
import { getDieCasters } from '@/api/die-casters';
import { getVideoSources } from '@/api/video-sources';
import { 
  getDetectionGroups, 
  createDetectionGroup, 
  updateDetectionGroup,
  deleteDetectionGroup as deleteDetectionGroupAPI,
} from '@/api/detection-groups';
import type { DieCaster, VideoSource, DetectionGroup } from '@/types';

// Based on createDetectionGroup function signature
export interface DetectionGroupCreate {
  name: string;
  die_caster_id: number;
  video_source_id: number;
  template_id: number;
}


export function useDetectionConfig() {
  // 响应式数据
  const templates = ref<DetectionTemplate[]>([]);
  const selectedTemplate = ref<DetectionTemplateWithDieCasters | null>(null);
  const selectedDetectionGroup = ref<DetectionGroup | null>(null);
  const associatedDieCasters = ref<DieCaster[]>([]);
  const availableDieCasters = ref<DieCaster[]>([]);
  const detectionGroups = ref<DetectionGroup[]>([]);
  const videoSources = ref<VideoSource[]>([]);
  const submitting = ref(false);

  // 对话框状态
  const showCreateTemplateDialog = ref(false);
  const showAssociateDiecastersDialog = ref(false);
  const showCreateDetectionGroupDialog = ref(false);
  const showROIConfigDialog = ref(false);

  // 表单数据
  const templateForm = reactive<DetectionTemplateCreate & { status?: 'enabled' | 'disabled' }>({
    name: '',
    description: ''
  });

  const detectionGroupForm = reactive<DetectionGroupCreate>({
    name: '',
    die_caster_id: 0,
    video_source_id: 0,
    template_id: 0
  });

  const selectedDieCasterIds = ref<number[]>([]);
  const editingTemplate = ref<DetectionTemplate | null>(null);
  const currentDieCaster = ref<DieCaster | null>(null);

  // ROI配置相关数据
  const activeROITab = ref('roi');
  const roiRegions = ref<any[]>([]);
  const globalSettings = reactive({
    detection_mode: 'real_time',
    save_results: true,
    image_preprocessing: {
      brightness: 0,
      contrast: 1.0,
      gamma: 1.0
    }
  });

  // ROI配置上下文
  const roiContext = reactive<{
    detectionGroup?: DetectionGroup;
    dieCaster?: DieCaster;
    videoSource?: VideoSource;
  }>({});

  // 表单引用
  const templateFormRef = ref<FormInstance>();
  const detectionGroupFormRef = ref<FormInstance>();

  // 表单验证规则
  const templateRules: FormRules = {
    name: [
      { required: true, message: '请输入模板名称', trigger: 'blur' },
      { min: 1, max: 50, message: '模板名称长度在 1 到 50 个字符', trigger: 'blur' }
    ]
  };

  const detectionGroupRules: FormRules = {
    name: [
      { required: true, message: '请输入检测组名称', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (!selectedTemplate.value) {
            callback(new Error('请先选择检测模板'));
            return;
          }
          
          const existingGroups = detectionGroups.value.filter(
            (group: DetectionGroup) => group.template_id === selectedTemplate.value!.id && 
                     group.name === value
          );
          
          if (existingGroups.length > 0) {
            callback(new Error('该检测模板下已存在同名的检测组'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    die_caster_id: [
      { required: true, message: '请选择压铸机', trigger: 'change' }
    ],
    video_source_id: [
      { required: true, message: '请选择视频源', trigger: 'change' }
    ]
  };

  // 树形结构配置
  const treeProps = {
    children: 'children',
    label: 'label'
  };

  // 计算属性
  const formattedConfigJson = computed(() => {
    if (!selectedTemplate.value) return '';
    
    const config = {
      template: {
        id: selectedTemplate.value.id,
        name: selectedTemplate.value.name,
        description: selectedTemplate.value.description,
        status: selectedTemplate.value.status,
        created_at: selectedTemplate.value.created_at
      },
      die_casters: associatedDieCasters.value.map((dc: DieCaster) => ({
        id: dc.id,
        name: dc.name,
        description: dc.description,
        status: dc.status
      })),
      detection_groups: detectionGroups.value
        .filter((group: DetectionGroup) => group.template_id === selectedTemplate.value?.id)
        .map((group: DetectionGroup) => {
          const videoSource = videoSources.value.find((vs: VideoSource) => vs.id === group.video_source_id);
          const dieCaster = associatedDieCasters.value.find((dc: DieCaster) => dc.id === group.die_caster_id);
          return {
            id: group.id,
            name: group.name,
            status: group.status,
            die_caster: dieCaster ? { id: dieCaster.id, name: dieCaster.name } : null,
            video_source: videoSource ? { id: videoSource.id, name: videoSource.name } : null,
            config_json: group.config_json || {},
            roi_config: group.config_json?.roi_config || null
          };
        })
    };
    
    return JSON.stringify(config, null, 2);
  });

  const treeData = computed(() => {
    if (!selectedTemplate.value) return [];
    
    const templateNode = {
      id: `template-${selectedTemplate.value.id}`,
      label: `检测模板：${selectedTemplate.value.name}`,
      type: 'template',
      status: selectedTemplate.value.status,
      children: [] as any[]
    };
    
    associatedDieCasters.value.forEach((dieCaster: DieCaster) => {
      const dieCasterNode = {
        id: dieCaster.id,
        label: `压铸机：${dieCaster.name}`,
        type: 'die-caster',
        status: dieCaster.status,
        children: [] as any[]
      };
      
      const groupsForDieCaster = detectionGroups.value.filter(
        (group: DetectionGroup) => group.die_caster_id === dieCaster.id && group.template_id === selectedTemplate.value?.id
      );
      
      groupsForDieCaster.forEach((group: DetectionGroup) => {
        const videoSource = videoSources.value.find((vs: VideoSource) => vs.id === group.video_source_id);
        const groupNode = {
          id: group.id,
          label: `检测组：${group.name}----视频源：${videoSource?.name || '未知视频源'}`,
          type: 'detection-group',
          status: group.status,
          groupData: group,
          videoSourceData: videoSource,
          dieCasterData: dieCaster
        };
        dieCasterNode.children.push(groupNode);
      });
      
      templateNode.children.push(dieCasterNode);
    });
    
    return [templateNode];
  });

  const availableVideoSources = computed(() => {
    if (!selectedTemplate.value) return [];
    
    const usedVideoSourceIds = detectionGroups.value
      .filter((group: DetectionGroup) => group.template_id === selectedTemplate.value?.id)
      .map((group: DetectionGroup) => group.video_source_id);
    
    return videoSources.value.filter((vs: VideoSource) => !usedVideoSourceIds.includes(vs.id));
  });

  // 方法
  const loadTemplates = async () => {
    try {
      templates.value = await getDetectionTemplates();
    } catch (error) {
      ElMessage.error('加载检测模板失败');
    }
  };

  const loadVideoSources = async () => {
    try {
      videoSources.value = await getVideoSources();
    } catch (error) {
      ElMessage.error('加载视频源失败');
    }
  };

  const loadDetectionGroups = async () => {
    try {
      detectionGroups.value = await getDetectionGroups();
    } catch (error) {
      ElMessage.error('加载检测组失败');
    }
  };

  const selectTemplate = async (template: DetectionTemplate) => {
    try {
      selectedTemplate.value = await getDetectionTemplate(template.id);
      await loadAssociatedDieCasters();
    } catch (error) {
      ElMessage.error('加载模板详情失败');
    }
  };

  const loadAssociatedDieCasters = async () => {
    if (!selectedTemplate.value) return;
    
    try {
      const allDieCasters = await getDieCasters();
      associatedDieCasters.value = allDieCasters.filter(
        (dc: DieCaster) => selectedTemplate.value!.die_caster_ids.includes(dc.id)
      );
    } catch (error) {
      ElMessage.error('加载关联压铸机失败');
    }
  };

  const loadAvailableDieCasters = async () => {
    if (!selectedTemplate.value) return;
    
    try {
      availableDieCasters.value = await getAvailableDieCasters(selectedTemplate.value.id);
    } catch (error) {
      ElMessage.error('加载可用压铸机失败');
    }
  };

  const handleTemplateSubmit = async () => {
    if (!templateFormRef.value) return;
    
    try {
      await templateFormRef.value.validate();
      submitting.value = true;
      
      const payload: DetectionTemplateUpdate = {
        name: templateForm.name,
        description: templateForm.description
      };
      if (templateForm.status) {
        payload.status = templateForm.status;
      }
      
      if (editingTemplate.value) {
        const updatedTemplate = await updateDetectionTemplate(editingTemplate.value.id, payload);
        ElMessage.success('模板更新成功');
        
        // 如果当前选中的模板就是被编辑的模板，需要更新selectedTemplate
        if (selectedTemplate.value?.id === editingTemplate.value.id) {
          // 重新获取完整的模板信息
          selectedTemplate.value = await getDetectionTemplate(editingTemplate.value.id);
        }
      } else {
        await createDetectionTemplate(templateForm);
        ElMessage.success('模板创建成功');
      }
      
      showCreateTemplateDialog.value = false;
      resetTemplateForm();
      await loadTemplates();
    } catch (error) {
      ElMessage.error(editingTemplate.value ? '模板更新失败' : '模板创建失败');
    } finally {
      submitting.value = false;
    }
  };

  const handleTemplateAction = async ({ action, template }: { action: string, template: DetectionTemplate }) => {
    switch (action) {
      case 'edit':
        try {
          // 获取完整的模板信息
          const fullTemplate = await getDetectionTemplate(template.id);
          editingTemplate.value = fullTemplate;
          templateForm.name = fullTemplate.name;
          templateForm.description = fullTemplate.description || '';
          templateForm.status = fullTemplate.status;
          showCreateTemplateDialog.value = true;
        } catch (error) {
          ElMessage.error('加载模板详情失败');
        }
        break;
        
      case 'copy':
        try {
          const newName = `${template.name}_副本_${Date.now()}`;
          await copyDetectionTemplate(template.id, newName);
          ElMessage.success('模板复制成功');
          await loadTemplates();
        } catch (error) {
          ElMessage.error('模板复制失败');
        }
        break;
        
      case 'delete':
        try {
          await ElMessageBox.confirm('确定要删除这个检测模板吗？', '确认删除', {
            type: 'warning'
          });
          await deleteDetectionTemplate(template.id);
          ElMessage.success('模板删除成功');
          if (selectedTemplate.value?.id === template.id) {
            selectedTemplate.value = null;
          }
          await loadTemplates();
        } catch (error) {
          if (error !== 'cancel') {
            ElMessage.error('模板删除失败');
          }
        }
        break;
    }
  };

  const handleAssociateDieCasters = async () => {
    if (!selectedTemplate.value) return;
    
    try {
      submitting.value = true;
      const currentIds = selectedTemplate.value.die_caster_ids;
      const newIds = [...currentIds, ...selectedDieCasterIds.value];
      
      const updatedTemplate = await associateDieCasters(selectedTemplate.value.id, { die_caster_ids: newIds });
      ElMessage.success('压铸机关联成功');
      
      selectedTemplate.value = updatedTemplate;
      
      showAssociateDiecastersDialog.value = false;
      selectedDieCasterIds.value = [];
      
      await loadAssociatedDieCasters();
    } catch (error) {
      console.error('压铸机关联失败:', error);
      ElMessage.error('压铸机关联失败，请求的资源不存在');
    } finally {
      submitting.value = false;
    }
  };

  const removeDieCasterAssociation = async (dieCasterId: number) => {
    if (!selectedTemplate.value) return;
    
    try {
      await ElMessageBox.confirm('确定要移除这个压铸机的关联吗？', '确认移除', {
        type: 'warning'
      });
      
      await removeDieCasterAssociationAPI(selectedTemplate.value.id, dieCasterId);
      ElMessage.success('关联移除成功');
      await selectTemplate(selectedTemplate.value);
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('关联移除失败');
      }
    }
  };

  const handleCreateDetectionGroup = async () => {
    if (!detectionGroupFormRef.value || !selectedTemplate.value) return;
    
    try {
      await detectionGroupFormRef.value.validate();
      submitting.value = true;
      
      detectionGroupForm.template_id = selectedTemplate.value.id;
      
      if (currentDieCaster.value) {
        detectionGroupForm.die_caster_id = currentDieCaster.value.id;
      }
      
      await createDetectionGroup(detectionGroupForm);
      ElMessage.success('检测组创建成功');
      
      showCreateDetectionGroupDialog.value = false;
      resetDetectionGroupForm();
      currentDieCaster.value = null;
      
      await loadDetectionGroups();
      if (selectedTemplate.value) {
        await selectTemplate(selectedTemplate.value);
      }
    } catch (error) {
      ElMessage.error('检测组创建失败');
    } finally {
      submitting.value = false;
    }
  };

  const deleteDetectionGroup = async (groupId: number) => {
    try {
      await ElMessageBox.confirm('确定要删除这个检测组吗？', '确认删除', {
        type: 'warning'
      });
      
      await deleteDetectionGroupAPI(groupId);
      ElMessage.success('检测组删除成功');
      await loadDetectionGroups();
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('检测组删除失败');
      }
    }
  };

  const openROIConfig = (nodeData: any) => {
    // 构建配置路径参数
    const configParams = {
      templateId: selectedTemplate.value?.id,
      dieCasterId: nodeData.dieCasterData?.id,
      detectionGroupId: nodeData.groupData?.id,
      videoSourceId: nodeData.videoSourceData?.id,
      videoSourcePath: nodeData.videoSourceData?.path || nodeData.videoSourceData?.url
    };
    
    // 构建URL参数
    const urlParams = new URLSearchParams();
    Object.entries(configParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        urlParams.append(key, String(value));
      }
    });
    
    // 在新窗口中打开video-preview-cur_dection页面
    const baseUrl = window.location.origin;
    const targetUrl = `${baseUrl}/#/video-preview-detection?${urlParams.toString()}`;
    
    // 打开新窗口
    const newWindow = window.open(
      targetUrl,
      'roi-config-window',
      'width=1400,height=900,scrollbars=yes,resizable=yes,status=yes,toolbar=no,menubar=no,location=no'
    );
    
    if (!newWindow) {
      ElMessage.error('无法打开新窗口，请检查浏览器弹窗设置');
      return;
    }
    
    // 监听新窗口关闭事件，用于刷新当前页面数据
    const checkClosed = setInterval(() => {
      if (newWindow.closed) {
        clearInterval(checkClosed);
        // 窗口关闭后刷新当前模板数据
        if (selectedTemplate.value) {
          selectTemplate(selectedTemplate.value);
        }
        ElMessage.success('ROI配置窗口已关闭，数据已刷新');
      }
    }, 1000);
  };

  const loadExistingROIConfig = () => {
    roiRegions.value = [];
    globalSettings.detection_mode = 'real_time';
    globalSettings.save_results = true;
    globalSettings.image_preprocessing.brightness = 0;
    globalSettings.image_preprocessing.contrast = 1.0;
    globalSettings.image_preprocessing.gamma = 1.0;
    
    if (roiContext.detectionGroup?.config_json?.roi_config) {
      const config = roiContext.detectionGroup.config_json.roi_config;
      roiRegions.value = JSON.parse(JSON.stringify(config.rois || []));
      if (config.global_settings) {
        Object.assign(globalSettings, JSON.parse(JSON.stringify(config.global_settings)));
      }
    }
  };

  const addROIRegion = () => {
    const newROI = {
      id: Date.now(),
      name: `ROI区域${roiRegions.value.length + 1}`,
      type: 'rectangle',
      coordinates: { x: 100, y: 100, width: 200, height: 150 },
      algorithm: {
        type: 'defect_detection',
        parameters: {
          threshold: 0.8,
          min_area: 50,
          max_area: 1000
        }
      }
    };
    roiRegions.value.push(newROI);
  };

  const editROIRegion = (index: number) => {
    ElMessage.info('ROI区域编辑功能待实现');
  };

  const removeROIRegion = (index: number) => {
    roiRegions.value.splice(index, 1);
  };

  const getAlgorithmName = (type: string) => {
    const algorithmNames: Record<string, string> = {
      'defect_detection': '缺陷检测',
      'surface_inspection': '表面检查',
      'dimension_measurement': '尺寸测量',
      'color_analysis': '颜色分析'
    };
    return algorithmNames[type] || type;
  };

  const openCreateDetectionGroupDialog = (nodeData: any) => {
    currentDieCaster.value = {
      id: nodeData.id,
      name: nodeData.label.replace('压铸机：', ''),
      description: nodeData.description || '',
      status: nodeData.status
    } as DieCaster;
    
    resetDetectionGroupForm();
    detectionGroupForm.die_caster_id = nodeData.id;
    
    showCreateDetectionGroupDialog.value = true;
  };

  const saveROIConfig = async () => {
    if (!roiContext.detectionGroup) {
      ElMessage.error('请先选择一个检测组');
      return;
    }
    
    try {
      const roiConfig = {
        rois: roiRegions.value,
        global_settings: {
          detection_mode: globalSettings.detection_mode,
          save_results: globalSettings.save_results,
          image_preprocessing: {
            brightness: globalSettings.image_preprocessing.brightness,
            contrast: globalSettings.image_preprocessing.contrast,
            gamma: globalSettings.image_preprocessing.gamma
          }
        }
      };
      
      const updatedConfig = {
        ...roiContext.detectionGroup.config_json,
        roi_config: roiConfig,
        last_updated: new Date().toISOString()
      };
      
      await updateDetectionGroup(roiContext.detectionGroup.id, {
        config_json: updatedConfig
      });
      
      roiContext.detectionGroup.config_json = JSON.parse(JSON.stringify(updatedConfig));
      
      const groupIndex = detectionGroups.value.findIndex((g: DetectionGroup) => g.id === roiContext.detectionGroup!.id);
      if (groupIndex !== -1) {
        detectionGroups.value[groupIndex].config_json = JSON.parse(JSON.stringify(updatedConfig));
      }
      
      ElMessage.success('ROI配置已保存');
      showROIConfigDialog.value = false;
      
      await loadDetectionGroups();
    } catch (error) {
      console.error('保存ROI配置失败:', error);
      ElMessage.error('保存ROI配置失败');
    }
  };

  const resetTemplateForm = () => {
    templateForm.name = '';
    templateForm.description = '';
    templateForm.status = undefined;
    editingTemplate.value = null;
  };

  const handleCancelTemplate = () => {
    showCreateTemplateDialog.value = false;
    resetTemplateForm();
  };

  const resetDetectionGroupForm = () => {
    detectionGroupForm.name = '';
    detectionGroupForm.die_caster_id = 0;
    detectionGroupForm.video_source_id = availableVideoSources.value.length > 0 ? availableVideoSources.value[0].id as number: 0;
    detectionGroupForm.template_id = 0;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getNodeIcon = (type: string) => {
    switch (type) {
      case 'template': return Folder;
      case 'die-caster': return Monitor;
      case 'detection-group': return VideoCamera;
      default: return View;
    }
  };

  const getStatusType = (status: string) => {
    switch (status) {
      case 'enabled': return 'success';
      case 'disabled': return 'danger';
      case 'running': return 'success';
      case 'stopped': return 'info';
      default: return 'info';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'enabled': return '启用';
      case 'disabled': return '禁用';
      case 'running': return '运行中';
      case 'stopped': return '已停止';
      default: return status;
    }
  };

  const refreshPreview = () => {
    ElMessage.success('配置预览已刷新');
  };

  const copyConfigToClipboard = async () => {
    if (!selectedTemplate.value) return;
    
    try {
      await navigator.clipboard.writeText(formattedConfigJson.value);
      ElMessage.success('配置已复制到剪贴板');
    } catch (error) {
      ElMessage.error('复制失败，请手动复制');
    }
  };

  // 监听器
  watch(showAssociateDiecastersDialog, (newVal) => {
    if (newVal) {
      loadAvailableDieCasters();
    }
  });

  watch(showCreateDetectionGroupDialog, (newVal) => {
    if (newVal) {
      resetDetectionGroupForm();
    }
  });

  // 移除自动重置表单的监听器，改为在适当的时机手动重置
  // watch(showCreateTemplateDialog, (newVal) => {
  //   if (!newVal) {
  //     resetTemplateForm();
  //   }
  // });

  // 生命周期
  onMounted(async () => {
    await Promise.all([
      loadTemplates(),
      loadVideoSources(),
      loadDetectionGroups()
    ]);
  });
  
  return {
    templates,
    selectedTemplate,
    selectedDetectionGroup,
    associatedDieCasters,
    availableDieCasters,
    detectionGroups,
    videoSources,
    submitting,
    showCreateTemplateDialog,
    showAssociateDiecastersDialog,
    showCreateDetectionGroupDialog,
    showROIConfigDialog,
    templateForm,
    detectionGroupForm,
    selectedDieCasterIds,
    editingTemplate,
    currentDieCaster,
    activeROITab,
    roiRegions,
    globalSettings,
    roiContext,
    templateFormRef,
    detectionGroupFormRef,
    templateRules,
    detectionGroupRules,
    treeProps,
    formattedConfigJson,
    treeData,
    availableVideoSources,
    loadTemplates,
    loadVideoSources,
    loadDetectionGroups,
    selectTemplate,
    handleTemplateSubmit,
    handleTemplateAction,
    handleCancelTemplate,
    handleAssociateDieCasters,
    removeDieCasterAssociation,
    handleCreateDetectionGroup,
    deleteDetectionGroup,
    openROIConfig,
    addROIRegion,
    editROIRegion,
    removeROIRegion,
    getAlgorithmName,
    openCreateDetectionGroupDialog,
    saveROIConfig,
    formatDate,
    getNodeIcon,
    getStatusType,
    getStatusText,
    refreshPreview,
    copyConfigToClipboard,
    Plus,
    Edit,
    Delete,
    CopyDocument,
    MoreFilled,
    Link,
    Setting,
    Close,
    Folder,
    Monitor,
    VideoCamera,
    View,
    Refresh
  };
}
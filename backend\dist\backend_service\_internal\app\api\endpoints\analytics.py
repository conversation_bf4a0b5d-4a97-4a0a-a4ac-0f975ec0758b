from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from pydantic import BaseModel
from datetime import datetime, timedelta

from app.api import deps
from app.models.models import CardDetectionResult, DetectionGroup, DetectionTemplate

router = APIRouter()


class DetectionEfficiencyResponse(BaseModel):
    """检测效率响应模型"""
    detection_group_id: int
    detection_group_name: str
    template_name: str
    total_detections: int
    normal_detections: int
    abnormal_detections: int
    efficiency_rate: float  # 正常检测率
    avg_detection_time: float  # 平均检测时间（秒）
    detections_per_hour: float  # 每小时检测次数
    last_detection: Optional[str] = None


class EfficiencyTrendPoint(BaseModel):
    """效率趋势点"""
    timestamp: str
    efficiency_rate: float
    detection_count: int


class DetectionEfficiencyTrend(BaseModel):
    """检测效率趋势"""
    detection_group_id: int
    detection_group_name: str
    trend_points: List[EfficiencyTrendPoint]


class SystemEfficiencyOverview(BaseModel):
    """系统效率概览"""
    total_detections: int
    overall_efficiency_rate: float
    active_detection_groups: int
    avg_detections_per_hour: float
    top_performing_groups: List[DetectionEfficiencyResponse]
    efficiency_trends: List[DetectionEfficiencyTrend]


@router.get("/detection-efficiency", response_model=SystemEfficiencyOverview)
def get_detection_efficiency(
    db: Session = Depends(deps.get_db),
    hours: int = Query(24, ge=1, le=168),  # 分析时间范围，最多7天
    limit: int = Query(10, ge=1, le=50)   # 返回的检测组数量限制
) -> Any:
    """
    获取检测效率分析
    """
    since_time = datetime.now() - timedelta(hours=hours)
    
    # 获取所有活跃的检测组
    active_groups = db.query(DetectionGroup).filter(
        DetectionGroup.status == "active"
    ).all()
    
    group_efficiencies = []
    total_system_detections = 0
    total_system_normal = 0
    
    for group in active_groups:
        # 获取该检测组在指定时间范围内的检测结果
        results = db.query(CardDetectionResult).filter(
            and_(
                CardDetectionResult.detection_group_id == group.id,
                CardDetectionResult.timestamp >= since_time
            )
        ).all()
        
        if not results:
            continue
        
        # 计算统计数据
        total_detections = len(results)
        normal_detections = len([r for r in results if r.is_normal])
        abnormal_detections = total_detections - normal_detections
        efficiency_rate = (normal_detections / total_detections * 100) if total_detections > 0 else 0
        
        # 计算平均检测时间
        detection_times = [r.detection_time for r in results if r.detection_time is not None]
        avg_detection_time = sum(detection_times) / len(detection_times) if detection_times else 0
        
        # 计算每小时检测次数
        detections_per_hour = total_detections / hours if hours > 0 else 0
        
        # 获取最后一次检测时间
        last_detection = max([r.timestamp for r in results]).isoformat() if results else None
        
        # 获取模板名称
        template = db.query(DetectionTemplate).filter(
            DetectionTemplate.id == group.template_id
        ).first()
        template_name = template.name if template else "未知模板"
        
        efficiency_data = DetectionEfficiencyResponse(
            detection_group_id=group.id,
            detection_group_name=group.name,
            template_name=template_name,
            total_detections=total_detections,
            normal_detections=normal_detections,
            abnormal_detections=abnormal_detections,
            efficiency_rate=round(efficiency_rate, 2),
            avg_detection_time=round(avg_detection_time, 3),
            detections_per_hour=round(detections_per_hour, 2),
            last_detection=last_detection
        )
        
        group_efficiencies.append(efficiency_data)
        total_system_detections += total_detections
        total_system_normal += normal_detections
    
    # 按效率率排序，获取表现最好的检测组
    top_performing = sorted(group_efficiencies, key=lambda x: x.efficiency_rate, reverse=True)[:limit]
    
    # 计算系统整体效率
    overall_efficiency = (total_system_normal / total_system_detections * 100) if total_system_detections > 0 else 0
    avg_system_detections_per_hour = total_system_detections / hours if hours > 0 else 0
    
    # 生成效率趋势数据（按小时分组）
    efficiency_trends = []
    for group in active_groups[:5]:  # 只为前5个检测组生成趋势
        trend_points = []
        
        # 按小时分组计算趋势
        for i in range(min(hours, 24)):  # 最多显示24小时的趋势
            hour_start = datetime.now() - timedelta(hours=i+1)
            hour_end = datetime.now() - timedelta(hours=i)
            
            hour_results = db.query(CardDetectionResult).filter(
                and_(
                    CardDetectionResult.detection_group_id == group.id,
                    CardDetectionResult.timestamp >= hour_start,
                    CardDetectionResult.timestamp < hour_end
                )
            ).all()
            
            if hour_results:
                hour_total = len(hour_results)
                hour_normal = len([r for r in hour_results if r.is_normal])
                hour_efficiency = (hour_normal / hour_total * 100) if hour_total > 0 else 0
                
                trend_points.append(EfficiencyTrendPoint(
                    timestamp=hour_start.isoformat(),
                    efficiency_rate=round(hour_efficiency, 2),
                    detection_count=hour_total
                ))
        
        if trend_points:
            efficiency_trends.append(DetectionEfficiencyTrend(
                detection_group_id=group.id,
                detection_group_name=group.name,
                trend_points=list(reversed(trend_points))  # 按时间正序排列
            ))
    
    return SystemEfficiencyOverview(
        total_detections=total_system_detections,
        overall_efficiency_rate=round(overall_efficiency, 2),
        active_detection_groups=len(active_groups),
        avg_detections_per_hour=round(avg_system_detections_per_hour, 2),
        top_performing_groups=top_performing,
        efficiency_trends=efficiency_trends
    )


@router.get("/detection-efficiency/{detection_group_id}", response_model=DetectionEfficiencyResponse)
def get_group_detection_efficiency(
    *,
    db: Session = Depends(deps.get_db),
    detection_group_id: int,
    hours: int = Query(24, ge=1, le=168)
) -> Any:
    """
    获取特定检测组的效率分析
    """
    # 检查检测组是否存在
    group = db.query(DetectionGroup).filter(DetectionGroup.id == detection_group_id).first()
    if not group:
        raise HTTPException(status_code=404, detail="检测组不存在")
    
    since_time = datetime.now() - timedelta(hours=hours)
    
    # 获取检测结果
    results = db.query(CardDetectionResult).filter(
        and_(
            CardDetectionResult.detection_group_id == detection_group_id,
            CardDetectionResult.timestamp >= since_time
        )
    ).all()
    
    if not results:
        # 获取模板名称
        template = db.query(DetectionTemplate).filter(
            DetectionTemplate.id == group.template_id
        ).first()
        template_name = template.name if template else "未知模板"
        
        return DetectionEfficiencyResponse(
            detection_group_id=group.id,
            detection_group_name=group.name,
            template_name=template_name,
            total_detections=0,
            normal_detections=0,
            abnormal_detections=0,
            efficiency_rate=0.0,
            avg_detection_time=0.0,
            detections_per_hour=0.0,
            last_detection=None
        )
    
    # 计算统计数据
    total_detections = len(results)
    normal_detections = len([r for r in results if r.is_normal])
    abnormal_detections = total_detections - normal_detections
    efficiency_rate = (normal_detections / total_detections * 100) if total_detections > 0 else 0
    
    # 计算平均检测时间
    detection_times = [r.detection_time for r in results if r.detection_time is not None]
    avg_detection_time = sum(detection_times) / len(detection_times) if detection_times else 0
    
    # 计算每小时检测次数
    detections_per_hour = total_detections / hours if hours > 0 else 0
    
    # 获取最后一次检测时间
    last_detection = max([r.timestamp for r in results]).isoformat() if results else None
    
    # 获取模板名称
    template = db.query(DetectionTemplate).filter(
        DetectionTemplate.id == group.template_id
    ).first()
    template_name = template.name if template else "未知模板"
    
    return DetectionEfficiencyResponse(
        detection_group_id=group.id,
        detection_group_name=group.name,
        template_name=template_name,
        total_detections=total_detections,
        normal_detections=normal_detections,
        abnormal_detections=abnormal_detections,
        efficiency_rate=round(efficiency_rate, 2),
        avg_detection_time=round(avg_detection_time, 3),
        detections_per_hour=round(detections_per_hour, 2),
        last_detection=last_detection
    )
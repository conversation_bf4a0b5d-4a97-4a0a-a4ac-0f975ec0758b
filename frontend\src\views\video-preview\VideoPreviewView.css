/* VideoPreview 页面样式 - 支持主题切换 */
.video-preview-page {
  padding: 20px;
  background-color: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: var(--text-color);
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 600;
  transition: color 0.3s ease;
}

.page-description {
  color: var(--text-color-soft);
  font-size: 16px;
  margin: 0;
  transition: color 0.3s ease;
}

.preview-content {
  max-width: 1600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 视频源选择区域 */
.video-source-section {
  background: var(--bg-color-soft);
  padding: 20px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.source-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.source-controls label {
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
  transition: color 0.3s ease;
}

.source-select {
  flex: 1;
  min-width: 300px;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  background: var(--bg-color);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.source-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.source-select:disabled {
  background-color: var(--bg-color-mute);
  color: var(--text-color-mute);
  cursor: not-allowed;
}

.refresh-btn {
  padding: 8px 16px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.refresh-btn:hover:not(:disabled) {
  background: var(--primary-color-light);
}

.refresh-btn:disabled {
  background: var(--text-color-mute);
  cursor: not-allowed;
}

/* 主要内容区域 - 左右布局 */
.main-content {
  display: flex;
  gap: 20px;
}

/* 左侧视频区域 - 60% */
.video-section {
  flex: 0 0 60%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 右侧面板区域 - 40% */
.right-panel {
  flex: 0 0 40%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* ROI工具栏 - 简洁系统风格 */
.roi-toolbar-section {
  background: var(--bg-color-soft);
  padding: 16px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.roi-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  flex-wrap: wrap;
}

.toolbar-title h4 {
  margin: 0;
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: color 0.3s ease;
}

.toolbar-controls {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tool-btn {
  padding: 6px 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-color);
  color: var(--text-color-soft);
  cursor: pointer;
  font-size: 12px;
  font-weight: 400;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.tool-btn:hover:not(:disabled) {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: var(--bg-color);
}

.tool-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.tool-btn:disabled {
  background: var(--bg-color-mute);
  border-color: var(--border-color);
  color: var(--text-color-mute);
  cursor: not-allowed;
  opacity: 0.5;
}

.tool-btn.toggle-btn.active {
  background: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

/* 简化特殊按钮样式 - 统一hover效果 */
.tool-btn.clear-btn:hover:not(:disabled) {
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.tool-btn.save-btn:hover:not(:disabled) {
  border-color: var(--success-color);
  color: var(--success-color);
}

.tool-btn.export-btn:hover:not(:disabled),
.tool-btn.import-btn:hover:not(:disabled) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.tool-btn.load-btn {
  border-color: var(--success-color);
  color: var(--success-color);
}

.tool-btn.load-btn:hover:not(:disabled) {
  border-color: var(--success-color);
  color: var(--success-color);
  background-color: rgba(103, 194, 58, 0.1);
}

.tool-btn.load-btn:disabled {
  border-color: var(--text-color-mute);
  color: var(--text-color-mute);
  cursor: not-allowed;
}

.tool-btn.test-btn {
  border-color: var(--warning-color);
  color: var(--warning-color);
}

.tool-btn.test-btn:hover:not(:disabled) {
  border-color: var(--warning-color);
  color: var(--warning-color);
  background-color: rgba(230, 162, 60, 0.1);
}

.tool-btn.init-btn {
  border-color: var(--info-color);
  color: var(--info-color);
}

.tool-btn.init-btn:hover:not(:disabled) {
  border-color: var(--info-color);
  color: var(--info-color);
  background-color: rgba(64, 158, 255, 0.1);
}

/* 视频预览区域 */
.video-preview-section {
  background: var(--bg-color-soft);
  padding: 20px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.video-container {
  position: relative;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid var(--border-color);
  transition: border-color 0.3s ease;
}

.video-player {
  width: 100%;
  height: 100%;
  background: #000;
}

/* 运动检测叠加层 */
.motion-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

/* ROI绘制画布 */
.roi-display-canvas {
  position: absolute;
  top: 0;
  left: 0;
  /* 不设置width和height，让JavaScript控制实际像素尺寸 */
  /* width: 100%; height: 100%; */
  max-width: 100%;
  max-height: 100%;
  pointer-events: auto;
  z-index: 20;
  cursor: crosshair;
  /* 确保画布按比例缩放 */
  object-fit: contain;
}

/* 控制按钮区域 - 移到视频下方 */
.control-section {
  background: var(--bg-color-soft);
  padding: 20px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
  transition: all 0.3s ease;
}

.control-btn {
  padding: 10px 20px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  white-space: nowrap;
  background: var(--bg-color);
  color: var(--text-color);
}

.control-btn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.control-btn.primary:hover:not(:disabled) {
  background: var(--primary-color-light);
  border-color: var(--primary-color-light);
  transform: translateY(-1px);
}

.control-btn.danger {
  background: var(--danger-color);
  border-color: var(--danger-color);
  color: white;
}

.control-btn.danger:hover:not(:disabled) {
  background: #f24c4c;
  border-color: #f24c4c;
  transform: translateY(-1px);
}

.control-btn.secondary {
  background: var(--bg-color-soft);
  border-color: var(--border-color);
  color: var(--text-color-soft);
}

.control-btn.secondary:hover:not(:disabled) {
  background: var(--bg-color-mute);
  border-color: var(--border-color-hover);
  color: var(--text-color);
  transform: translateY(-1px);
}

.control-btn.active {
  background: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

.control-btn.active:hover:not(:disabled) {
  background: #5daf34;
  border-color: #5daf34;
  transform: translateY(-1px);
}

.control-btn:disabled {
  background: var(--bg-color-mute);
  color: var(--text-color-mute);
  border-color: var(--border-color);
  cursor: not-allowed;
  transform: none;
  opacity: 0.6;
}

/* ROI管理面板 - 简洁系统风格 */
.roi-management-section {
  background: var(--bg-color-soft);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: background-color 0.3s ease, border-color 0.3s ease;
  height: fit-content;
}

.roi-list-panel {
  padding: 16px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.panel-header h4 {
  margin: 0;
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.panel-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 400;
  background: var(--bg-color-mute);
  color: var(--text-color-soft);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.status-disabled {
  color: var(--text-color-mute);
  background: var(--bg-color-mute);
  border-color: var(--border-color);
}

.status-locked {
  color: var(--warning-color);
  background: rgba(230, 162, 60, 0.1);
  border-color: rgba(230, 162, 60, 0.2);
}

.status-ready {
  color: var(--info-color);
  background: rgba(144, 147, 153, 0.1);
  border-color: rgba(144, 147, 153, 0.2);
}

.status-drawing {
  color: var(--success-color);
  background: rgba(103, 194, 58, 0.1);
  border-color: rgba(103, 194, 58, 0.2);
}

/* 暗色主题下的状态标签优化 */
.dark-theme .status-locked {
  background: rgba(230, 162, 60, 0.15);
  border-color: rgba(230, 162, 60, 0.3);
}

.dark-theme .status-ready {
  background: rgba(144, 147, 153, 0.15);
  border-color: rgba(144, 147, 153, 0.3);
}

.dark-theme .status-drawing {
  background: rgba(103, 194, 58, 0.15);
  border-color: rgba(103, 194, 58, 0.3);
}

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* ROI列表 */
.roi-list {
  max-height: 300px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.roi-list::-webkit-scrollbar {
  width: 6px;
}

.roi-list::-webkit-scrollbar-track {
  background: var(--bg-color-mute);
  border-radius: 3px;
}

.roi-list::-webkit-scrollbar-thumb {
  background: var(--border-color-hover);
  border-radius: 3px;
}

.roi-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-color-mute);
}

.empty-message {
  text-align: center;
  padding: 24px 16px;
  color: var(--text-color-soft);
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.4;
}

.empty-text p {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 400;
  color: var(--text-color-soft);
  transition: color 0.3s ease;
}

.empty-text small {
  font-size: 11px;
  opacity: 0.7;
  color: var(--text-color-mute);
  transition: color 0.3s ease;
}

.roi-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.roi-item:hover {
  border-color: var(--border-color-hover);
}

.roi-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.roi-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid var(--border-color);
  flex-shrink: 0;
  transition: border-color 0.3s ease;
}

.roi-details {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.roi-name {
  font-weight: 400;
  color: var(--text-color);
  font-size: 13px;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.roi-type, .roi-points {
  font-size: 11px;
  color: var(--text-color-soft);
  line-height: 1.2;
  transition: color 0.3s ease;
}

.roi-actions {
  display: flex;
  gap: 2px;
}

.edit-btn, .delete-btn {
  padding: 2px 6px;
  border: 1px solid var(--border-color);
  border-radius: 3px;
  background: var(--bg-color);
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
  color: var(--text-color-soft);
}

.edit-btn:hover {
  border-color: var(--info-color);
  color: var(--info-color);
}

.delete-btn:hover {
  border-color: var(--danger-color);
  color: var(--danger-color);
}

/* 使用说明 - 简洁风格 */
.help-section {
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.help-header {
  padding: 8px 12px;
  background: var(--bg-color-mute);
  border-bottom: 1px solid var(--border-color);
  font-weight: 400;
  color: var(--text-color-soft);
  font-size: 12px;
  transition: all 0.3s ease;
}

.help-content {
  padding: 12px;
}

.help-content ol {
  margin: 0;
  padding-left: 16px;
  color: var(--text-color-soft);
  transition: color 0.3s ease;
}

.help-content li {
  margin-bottom: 3px;
  font-size: 11px;
  line-height: 1.3;
  transition: color 0.3s ease;
}

/* 算法配置参数区域 - 简洁风格 */
.algorithm-config-section {
  background: var(--bg-color-soft);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  padding: 16px;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.algorithm-config-section h4 {
  margin: 0 0 12px 0;
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.config-placeholder {
  text-align: center;
  padding: 24px 16px;
  color: var(--text-color-soft);
  font-size: 12px;
  line-height: 1.4;
  transition: color 0.3s ease;
}

/* 操作信息区域 - 简洁系统风格 */
.operation-info-section {
  background: var(--bg-color-soft);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  transition: border-color 0.3s ease;
}

.info-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.clear-btn {
  padding: 4px 8px;
  background: var(--bg-color);
  color: var(--text-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.info-content {
  max-height: 250px;
  overflow-y: auto;
  padding: 8px 16px 12px;
}

.info-content::-webkit-scrollbar {
  width: 4px;
}

.info-content::-webkit-scrollbar-track {
  background: transparent;
}

.info-content::-webkit-scrollbar-thumb {
  background: var(--border-color-hover);
  border-radius: 2px;
}

.info-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-color-mute);
}

.info-item {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
  padding: 4px 6px;
  border-radius: 3px;
  font-size: 11px;
  line-height: 1.3;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: var(--bg-color-mute);
}

.info-time {
  color: var(--text-color-mute);
  font-family: 'Courier New', monospace;
  white-space: nowrap;
  min-width: 60px;
  font-size: 10px;
  transition: color 0.3s ease;
}

.info-message {
  color: var(--text-color-soft);
  flex: 1;
  word-break: break-word;
  transition: color 0.3s ease;
}

/* 暗色主题下的额外优化 */
.dark-theme .video-container {
  border-color: var(--border-color-hover);
}

.dark-theme .tool-btn:hover:not(:disabled) {
  background: var(--bg-color-mute);
}

.dark-theme .roi-item:hover {
  background: var(--bg-color-mute);
}

.dark-theme .info-item:hover {
  background: var(--bg-color-soft);
}

/* 确保选择框在暗色模式下的正确显示 */
.dark-theme .source-select option {
  background: var(--bg-color);
  color: var(--text-color);
}

/* ROI属性选择器样式 */
.roi-attribute-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: var(--bg-color-mute);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.roi-attribute-selector label {
  font-size: 12px;
  color: var(--text-color-soft);
  margin: 0;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.radio-label:hover {
  background: var(--bg-color);
}

.radio-label input[type="radio"] {
  margin: 0;
}

/* 绘制状态显示 */
.drawing-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 400;
  border: 1px solid var(--border-color);
}

.status-indicator.active {
  background: rgba(103, 194, 58, 0.1);
  color: var(--success-color);
  border-color: rgba(103, 194, 58, 0.2);
}

.status-indicator.inactive {
  background: var(--bg-color-mute);
  color: var(--text-color-mute);
}

/* 管理按钮组 */
.management-buttons {
  display: flex;
  gap: 4px;
}

/* ROI树状列表样式 */
.roi-tree {
  margin-bottom: 16px;
}

.roi-group {
  margin-bottom: 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--bg-color-mute);
  border-bottom: 1px solid var(--border-color);
}

.group-icon {
  font-size: 14px;
  margin-right: 6px;
}

.group-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-color);
  flex: 1;
}

.group-clear-btn {
  padding: 2px 6px;
  background: var(--bg-color);
  color: var(--text-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.3s ease;
}

.group-clear-btn:hover {
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.group-content {
  padding: 8px;
  background: var(--bg-color);
}

.group-content .roi-item {
  margin-bottom: 6px;
}

.group-content .roi-item:last-child {
  margin-bottom: 0;
}

/* ROI列表项高亮样式 */
.roi-item.highlighted {
  background-color: rgba(255, 255, 0, 0.1);
  border: 1px solid #ffff00;
  box-shadow: 0 0 8px rgba(255, 255, 0, 0.3);
}

.roi-item .roi-info {
  cursor: pointer;
  transition: all 0.3s ease;
}

.roi-item .roi-info:hover {
  background-color: var(--bg-color-hover);
}

.roi-item.highlighted .roi-info {
  background-color: rgba(255, 255, 0, 0.05);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .video-section {
    flex: none;
  }

  .right-panel {
    flex: none;
  }

  .roi-toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .roi-attribute-selector,
  .drawing-status,
  .management-buttons {
    margin-top: 8px;
  }
}

@media (max-width: 768px) {
  .video-preview-page {
    padding: 10px;
  }

  .source-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .source-select {
    min-width: auto;
  }

  .control-section {
    flex-direction: column;
  }

  .control-btn {
    min-width: auto;
  }

  .toolbar-controls {
    flex-direction: column;
  }

  .tool-btn {
    text-align: center;
  }
}





/* 响应式设计 */
@media (max-width: 768px) {
  .video-preview-page {
    padding: 16px;
  }
  
  .source-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .source-select {
    min-width: auto;
    width: 100%;
  }
  
  .control-section {
    flex-direction: column;
  }
  
  .control-btn {
    width: 100%;
  }
}
















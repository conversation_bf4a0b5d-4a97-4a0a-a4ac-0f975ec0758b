from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.models import VideoSource
from app.schemas.video_source import VideoSourceCreate, VideoSourceUpdate, VideoSource as VideoSourceSchema

router = APIRouter()


@router.get("/", response_model=List[VideoSourceSchema])
def read_video_sources(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取所有视频源
    """
    video_sources = db.query(VideoSource).offset(skip).limit(limit).all()
    return video_sources


@router.post("/", response_model=VideoSourceSchema)
def create_video_source(
    *,
    db: Session = Depends(deps.get_db),
    video_source_in: VideoSourceCreate,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新视频源
    """
    video_source_data = {
        "name": video_source_in.name,
        "source_type": video_source_in.source_type,
        "description": video_source_in.description,
    }

    # 根据类型设置不同的字段
    if video_source_in.source_type in ["local_file", "rtsp_stream"]:
        video_source_data["path"] = video_source_in.path
    elif video_source_in.source_type == "websdk_device":
        # 对于WebSDK设备，path字段设置为空字符串而不是None
        video_source_data["path"] = ""
        video_source_data.update({
            "device_ip": video_source_in.device_ip,
            "device_port": video_source_in.device_port,
            "device_username": video_source_in.device_username,
            "device_password": video_source_in.device_password,
            "device_protocol": video_source_in.device_protocol,
            "channel_id": video_source_in.channel_id,
            "stream_type": video_source_in.stream_type,
            "device_status": "offline"
        })

    video_source = VideoSource(**video_source_data)
    db.add(video_source)
    db.commit()
    db.refresh(video_source)
    return video_source


@router.put("/{video_source_id}", response_model=VideoSourceSchema)
@router.put("/{video_source_id}/", response_model=VideoSourceSchema)
def update_video_source(
    *,
    db: Session = Depends(deps.get_db),
    video_source_id: int,
    video_source_in: VideoSourceUpdate,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新视频源
    """
    video_source = db.query(VideoSource).filter(VideoSource.id == video_source_id).first()
    if not video_source:
        raise HTTPException(status_code=404, detail="视频源不存在")
    
    update_data = video_source_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(video_source, field, value)
    
    db.add(video_source)
    db.commit()
    db.refresh(video_source)
    return video_source


@router.get("/{video_source_id}", response_model=VideoSourceSchema)
@router.get("/{video_source_id}/", response_model=VideoSourceSchema)
def read_video_source(
    *,
    db: Session = Depends(deps.get_db),
    video_source_id: int,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取特定视频源
    """
    video_source = db.query(VideoSource).filter(VideoSource.id == video_source_id).first()
    if not video_source:
        raise HTTPException(status_code=404, detail="视频源不存在")
    return video_source


@router.delete("/{video_source_id}", response_model=VideoSourceSchema)
@router.delete("/{video_source_id}/", response_model=VideoSourceSchema)
def delete_video_source(
    *,
    db: Session = Depends(deps.get_db),
    video_source_id: int,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除视频源
    """
    video_source = db.query(VideoSource).filter(VideoSource.id == video_source_id).first()
    if not video_source:
        raise HTTPException(status_code=404, detail="视频源不存在")
    db.delete(video_source)
    db.commit()
    return video_source

@router.post("/{video_source_id}/test", response_model=dict)
@router.post("/{video_source_id}/test/", response_model=dict)
def test_video_source(
    *,
    db: Session = Depends(deps.get_db),
    video_source_id: int,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    测试视频源连接
    """
    video_source = db.query(VideoSource).filter(VideoSource.id == video_source_id).first()
    if not video_source:
        raise HTTPException(status_code=404, detail="视频源不存在")
    
    # 根据视频源类型进行不同的测试
    source_type = getattr(video_source, 'source_type', '')

    if source_type == "local_file":
        # 测试本地文件是否存在
        import os
        file_path = getattr(video_source, 'path', '')
        if file_path and os.path.exists(file_path):
            return {"success": True, "message": "本地文件存在"}
        else:
            return {"success": False, "message": "本地文件不存在"}

    elif source_type == "rtsp_stream":
        # 这里可以添加RTSP流测试逻辑
        return {"success": True, "message": "RTSP流测试成功"}

    elif source_type == "websdk_device":
        # 这里可以添加WebSDK设备连接测试逻辑
        return {"success": True, "message": "WebSDK设备连接测试成功"}

    return {"success": False, "message": "未知的视频源类型"}


@router.put("/{video_source_id}/status", response_model=VideoSourceSchema)
@router.put("/{video_source_id}/status/", response_model=VideoSourceSchema)
def update_device_status(
    *,
    db: Session = Depends(deps.get_db),
    video_source_id: int,
    status: str,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新WebSDK设备状态
    """
    video_source = db.query(VideoSource).filter(VideoSource.id == video_source_id).first()
    if not video_source:
        raise HTTPException(status_code=404, detail="视频源不存在")

    if getattr(video_source, 'source_type', '') != "websdk_device":
        raise HTTPException(status_code=400, detail="只能更新WebSDK设备的状态")

    from datetime import datetime, timezone
    setattr(video_source, 'device_status', status)
    if status == "online":
        setattr(video_source, 'last_connected_at', datetime.now(timezone.utc))

    db.add(video_source)
    db.commit()
    db.refresh(video_source)
    return video_source
#!/usr/bin/env python3
"""
创建系统日志表的迁移脚本
"""

import sqlite3
import os
from datetime import datetime

# 数据库文件路径
DB_PATH = "die_casting_detection.db"

def create_system_log_table():
    """创建系统日志表"""
    
    # 检查数据库文件是否存在
    if not os.path.exists(DB_PATH):
        print(f"❌ 数据库文件 {DB_PATH} 不存在")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 检查表是否已存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='system_logs'
        """)
        
        if cursor.fetchone():
            print("✅ system_logs 表已存在")
            conn.close()
            return True
        
        # 创建系统日志表
        cursor.execute("""
            CREATE TABLE system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME NOT NULL,
                level VARCHAR(20) NOT NULL,
                module VARCHAR(100) NOT NULL,
                message TEXT NOT NULL,
                filename VARCHAR(200),
                line_number INTEGER,
                function_name VARCHAR(100),
                extra_data TEXT,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建索引
        cursor.execute("CREATE INDEX idx_timestamp_level ON system_logs(timestamp, level)")
        cursor.execute("CREATE INDEX idx_module_level ON system_logs(module, level)")
        cursor.execute("CREATE INDEX idx_created_at ON system_logs(created_at)")
        cursor.execute("CREATE INDEX idx_timestamp ON system_logs(timestamp)")
        cursor.execute("CREATE INDEX idx_level ON system_logs(level)")
        cursor.execute("CREATE INDEX idx_module ON system_logs(module)")
        
        # 提交更改
        conn.commit()
        
        print("✅ system_logs 表创建成功")
        print("✅ 相关索引创建成功")
        
        # 插入测试数据
        test_logs = [
            (
                datetime.now().isoformat(),
                'info',
                'system',
                '🔧 [SYSTEM] 系统日志表创建成功',
                'create_system_log_table.py',
                50,
                'create_system_log_table',
                '{"event": "table_created"}',
                datetime.now().isoformat()
            ),
            (
                datetime.now().isoformat(),
                'info',
                'database',
                '📊 [DATABASE] 系统日志持久化存储已启用',
                'create_system_log_table.py',
                60,
                'create_system_log_table',
                '{"feature": "persistent_logging"}',
                datetime.now().isoformat()
            )
        ]
        
        cursor.executemany("""
            INSERT INTO system_logs 
            (timestamp, level, module, message, filename, line_number, function_name, extra_data, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, test_logs)
        
        conn.commit()
        print("✅ 测试日志数据插入成功")
        
        # 验证表结构
        cursor.execute("PRAGMA table_info(system_logs)")
        columns = cursor.fetchall()
        
        print("\n📋 表结构:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # 验证索引
        cursor.execute("PRAGMA index_list(system_logs)")
        indexes = cursor.fetchall()
        
        print("\n📋 索引列表:")
        for idx in indexes:
            print(f"  - {idx[1]}")
        
        # 统计记录数
        cursor.execute("SELECT COUNT(*) FROM system_logs")
        count = cursor.fetchone()[0]
        print(f"\n📊 当前日志记录数: {count}")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

def verify_table():
    """验证表是否正确创建"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 查询最新的几条日志
        cursor.execute("""
            SELECT timestamp, level, module, message 
            FROM system_logs 
            ORDER BY timestamp DESC 
            LIMIT 5
        """)
        
        logs = cursor.fetchall()
        
        print("\n📋 最新日志记录:")
        for log in logs:
            print(f"  [{log[0]}] [{log[1].upper()}] [{log[2]}] {log[3]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证表失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始创建系统日志表...")
    print(f"📁 数据库路径: {os.path.abspath(DB_PATH)}")
    
    if create_system_log_table():
        print("\n🎉 系统日志表创建完成！")
        
        if verify_table():
            print("\n✅ 表验证成功！")
            print("\n📝 使用说明:")
            print("1. 重启后端服务器以应用新的日志系统")
            print("2. 访问管理界面的'系统日志'功能")
            print("3. 日志将自动保存到数据库中")
            print("4. 支持按级别、模块、时间范围过滤")
            print("5. 支持导出和复制功能")
        else:
            print("\n⚠️ 表验证失败，请检查数据库")
    else:
        print("\n❌ 系统日志表创建失败！")
        print("请检查数据库文件权限和路径")

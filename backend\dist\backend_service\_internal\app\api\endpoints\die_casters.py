from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.models import DieCaster
from app.schemas.die_caster import DieCasterCreate, DieCasterUpdate, DieCaster as DieCasterSchema

router = APIRouter()


@router.get("/", response_model=List[DieCasterSchema])
def read_die_casters(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取所有压铸机
    """
    die_casters = db.query(DieCaster).offset(skip).limit(limit).all()
    return die_casters


@router.post("/", response_model=DieCasterSchema)
def create_die_caster(
    *,
    db: Session = Depends(deps.get_db),
    die_caster_in: DieCasterCreate,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新压铸机
    """
    die_caster = DieCaster(
        name=die_caster_in.name,
        description=die_caster_in.description,
        ip_address=die_caster_in.ip_address,
        port=die_caster_in.port,
        status=die_caster_in.status,
    )
    db.add(die_caster)
    db.commit()
    db.refresh(die_caster)
    return die_caster


@router.put("/{die_caster_id}", response_model=DieCasterSchema)
@router.put("/{die_caster_id}/", response_model=DieCasterSchema)
def update_die_caster(
    *,
    db: Session = Depends(deps.get_db),
    die_caster_id: int,
    die_caster_in: DieCasterUpdate,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新压铸机
    """
    die_caster = db.query(DieCaster).filter(DieCaster.id == die_caster_id).first()
    if not die_caster:
        raise HTTPException(status_code=404, detail="压铸机不存在")
    
    update_data = die_caster_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(die_caster, field, value)
    
    db.add(die_caster)
    db.commit()
    db.refresh(die_caster)
    return die_caster


@router.get("/{die_caster_id}", response_model=DieCasterSchema)
@router.get("/{die_caster_id}/", response_model=DieCasterSchema)
def read_die_caster(
    *,
    db: Session = Depends(deps.get_db),
    die_caster_id: int,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取特定压铸机
    """
    die_caster = db.query(DieCaster).filter(DieCaster.id == die_caster_id).first()
    if not die_caster:
        raise HTTPException(status_code=404, detail="压铸机不存在")
    return die_caster


@router.delete("/{die_caster_id}", response_model=DieCasterSchema)
@router.delete("/{die_caster_id}/", response_model=DieCasterSchema)
def delete_die_caster(
    *,
    db: Session = Depends(deps.get_db),
    die_caster_id: int,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除压铸机
    """
    die_caster = db.query(DieCaster).filter(DieCaster.id == die_caster_id).first()
    if not die_caster:
        raise HTTPException(status_code=404, detail="压铸机不存在")
    db.delete(die_caster)
    db.commit()
    return die_caster 
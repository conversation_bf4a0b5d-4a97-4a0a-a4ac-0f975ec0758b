import { request } from './request'

// 用户接口
export interface User {
  id: number
  username: string
  email: string
  role: string
  is_active: boolean
  is_superuser?: boolean
  created_at?: string
  updated_at?: string
}

// 创建用户请求
export interface CreateUserRequest {
  username: string
  email: string
  password: string
  role: string
  is_active?: boolean
}

// 更新用户请求
export interface UpdateUserRequest {
  username?: string
  email?: string
  role?: string
  is_active?: boolean
  password?: string
}

// 获取用户列表
export const getUsers = (params?: { skip?: number; limit?: number }) => {
  return request<User[]>({
    url: '/users/',
    method: 'GET',
    params
  })
}

// 创建用户
export const createUser = (data: CreateUserRequest) => {
  return request<User>({
    url: '/users/',
    method: 'POST',
    data
  })
}

// 获取指定用户
export const getUser = (userId: number) => {
  return request<User>({
    url: `/users/${userId}`,
    method: 'GET'
  })
}

// 更新用户
export const updateUser = (userId: number, data: UpdateUserRequest) => {
  return request<User>({
    url: `/users/${userId}`,
    method: 'PUT',
    data
  })
}

// 删除用户
export const deleteUser = (userId: number) => {
  return request<{ message: string }>({
    url: `/users/${userId}`,
    method: 'DELETE'
  })
}
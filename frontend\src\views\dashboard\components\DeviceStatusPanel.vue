<template>
  <div class="device-status-panel">
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon><monitor /></el-icon>
        设备状态监控
      </h3>
      <div class="panel-actions">
        <el-button size="small" @click="refreshDevices">
          <el-icon><refresh /></el-icon>
        </el-button>
      </div>
    </div>

    <div class="panel-content">
      <div class="device-list">
        <div 
          v-for="device in devices" 
          :key="device.id"
          class="device-item"
          :class="{ 'device-offline': device.status === 'offline' }"
          @click="handleDeviceClick(device)"
        >
          <div class="device-info">
            <div class="device-header">
              <div class="device-name">{{ device.name }}</div>
              <div class="device-status">
                <el-tag 
                  :type="getStatusType(device.status)" 
                  size="small"
                  effect="dark"
                >
                  {{ getStatusText(device.status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="device-details">
              <div class="detail-item">
                <el-icon><connection /></el-icon>
                <span>{{ device.ip }}</span>
              </div>
              <div class="detail-item">
                <el-icon><clock /></el-icon>
                <span>{{ formatTime(device.lastUpdate) }}</span>
              </div>
            </div>

            <!-- 设备性能指标 -->
            <div class="device-metrics" v-if="device.status === 'online'">
              <div class="metric-item">
                <div class="metric-label">检测组</div>
                <div class="metric-value">{{ getDeviceGroupCount(device.id) }}</div>
              </div>
              <div class="metric-item">
                <div class="metric-label">ROI数量</div>
                <div class="metric-value">{{ getDeviceROICount(device.id) }}</div>
              </div>
              <div class="metric-item">
                <div class="metric-label">今日报警</div>
                <div class="metric-value alarm-count">{{ getDeviceAlarmCount(device.id) }}</div>
              </div>
            </div>

            <!-- 算法状态指示器 -->
            <div class="algorithm-status" v-if="device.status === 'online'">
              <div class="algorithm-item">
                <div class="algorithm-icon motion-detection">
                  <el-icon><view /></el-icon>
                </div>
                <span class="algorithm-name">运动检测</span>
                <div class="algorithm-indicator active"></div>
              </div>
              <div class="algorithm-item">
                <div class="algorithm-icon direction-detection">
                  <el-icon><sort /></el-icon>
                </div>
                <span class="algorithm-name">方向检测</span>
                <div class="algorithm-indicator active"></div>
              </div>
              <div class="algorithm-item">
                <div class="algorithm-icon jam-detection">
                  <el-icon><warning /></el-icon>
                </div>
                <span class="algorithm-name">卡料检测</span>
                <div class="algorithm-indicator active"></div>
              </div>
            </div>
          </div>

          <!-- 设备状态指示灯 -->
          <div class="device-indicator">
            <div class="status-light" :class="device.status"></div>
          </div>
        </div>
      </div>

      <!-- 设备统计 -->
      <div class="device-summary">
        <div class="summary-item">
          <div class="summary-label">总设备数</div>
          <div class="summary-value">{{ devices.length }}</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">在线设备</div>
          <div class="summary-value online">{{ onlineDeviceCount }}</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">离线设备</div>
          <div class="summary-value offline">{{ offlineDeviceCount }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Monitor, Refresh, Connection, Clock, View, Sort, Warning
} from '@element-plus/icons-vue'

// Props
interface Device {
  id: number
  name: string
  status: 'online' | 'offline' | 'error'
  ip: string
  lastUpdate: Date
}

const props = defineProps<{
  devices: Device[]
}>()

// Emits
const emit = defineEmits<{
  deviceClick: [device: Device]
}>()

// 计算属性
const onlineDeviceCount = computed(() => 
  props.devices.filter(d => d.status === 'online').length
)

const offlineDeviceCount = computed(() => 
  props.devices.filter(d => d.status === 'offline').length
)

// 方法
const getStatusType = (status: string) => {
  switch (status) {
    case 'online':
      return 'success'
    case 'offline':
      return 'info'
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'online':
      return '在线'
    case 'offline':
      return '离线'
    case 'error':
      return '错误'
    default:
      return '未知'
  }
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`
  
  const days = Math.floor(hours / 24)
  return `${days}天前`
}

// 模拟数据获取方法
const getDeviceGroupCount = (deviceId: number) => {
  // 这里应该从实际数据中获取
  const counts = { 1: 2, 2: 2, 3: 2, 4: 3 }
  return counts[deviceId] || 0
}

const getDeviceROICount = (deviceId: number) => {
  // 这里应该从实际数据中获取
  const counts = { 1: 4, 2: 2, 3: 8, 4: 6 }
  return counts[deviceId] || 0
}

const getDeviceAlarmCount = (deviceId: number) => {
  // 这里应该从实际数据中获取
  const counts = { 1: 1, 2: 2, 3: 0, 4: 0 }
  return counts[deviceId] || 0
}

const refreshDevices = () => {
  ElMessage.success('设备状态已刷新')
  // 这里添加实际的刷新逻辑
}

const handleDeviceClick = (device: Device) => {
  emit('deviceClick', device)
}
</script>

<style scoped>
.device-status-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-color);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.panel-content {
  flex: 1;
  padding: 16px 20px;
  overflow-y: auto;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.device-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.1);
  border-color: var(--primary-color);
}

.device-item.device-offline {
  opacity: 0.7;
}

.device-info {
  flex: 1;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.device-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
}

.device-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--text-color-soft);
}

.device-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.metric-item {
  text-align: center;
}

.metric-label {
  font-size: 11px;
  color: var(--text-color-mute);
  margin-bottom: 2px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
}

.metric-value.alarm-count {
  color: var(--danger-color);
}

.algorithm-status {
  display: flex;
  gap: 8px;
}

.algorithm-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: var(--bg-color-mute);
  border-radius: 4px;
  font-size: 11px;
}

.algorithm-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
}

.algorithm-icon.motion-detection {
  background-color: #3498db;
}

.algorithm-icon.direction-detection {
  background-color: #e74c3c;
}

.algorithm-icon.jam-detection {
  background-color: #f39c12;
}

.algorithm-name {
  color: var(--text-color-soft);
}

.algorithm-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--success-color);
}

.algorithm-indicator.active {
  animation: pulse 2s infinite;
}

.device-indicator {
  margin-left: 12px;
}

.status-light {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: relative;
}

.status-light.online {
  background-color: var(--success-color);
  box-shadow: 0 0 8px rgba(var(--success-color), 0.5);
}

.status-light.offline {
  background-color: var(--info-color);
}

.status-light.error {
  background-color: var(--danger-color);
  box-shadow: 0 0 8px rgba(var(--danger-color), 0.5);
}

.device-summary {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.summary-item {
  text-align: center;
}

.summary-label {
  font-size: 12px;
  color: var(--text-color-mute);
  margin-bottom: 4px;
}

.summary-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-color);
}

.summary-value.online {
  color: var(--success-color);
}

.summary-value.offline {
  color: var(--info-color);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>

import { get, post, del } from '@/utils/api'
import type { TableInfo, QueryResult, TableDataResponse } from '@/types'

/**
 * 获取数据库表列表
 */
export const getTables = (): Promise<TableInfo[]> => {
  return get<TableInfo[]>('/database/tables')
}

/**
 * 获取指定表的数据
 */
export const getTableData = (
  tableName: string, 
  page: number = 1, 
  pageSize: number = 10
): Promise<TableDataResponse> => {
  return get<TableDataResponse>(`/database/table-data/${tableName}`, { page, page_size: pageSize })
}

/**
 * 执行SQL查询
 */
export const executeQuery = (query: string): Promise<QueryResult> => {
  return post<QueryResult>('/database/query', { query })
}

/**
 * 创建数据库备份
 */
export const createBackup = (): Promise<any> => {
  return post('/database/backup')
}

/**
 * 获取备份列表
 */
export const getBackups = (): Promise<any[]> => {
  return get('/database/backups')
}

/**
 * 恢复备份
 */
export const restoreBackup = (backupFilename: string): Promise<any> => {
  return post(`/database/restore/${backupFilename}`)
}

/**
 * 删除备份
 */
export const deleteBackup = (backupFilename: string): Promise<any> => {
  return del(`/database/backups/${backupFilename}`)
}

/**
 * 导出表数据
 */
export const exportTableData = (tableName: string, format: string = 'csv'): string => {
  return `/api/database/export/${tableName}?format=${format}`
}

/**
 * 导入表数据
 */
export const importTableData = (tableName: string, file: File): Promise<any> => {
  const formData = new FormData()
  formData.append('file', file)
  return post(`/database/import/${tableName}`, formData)
}

/**
 * 获取数据库维护信息
 */
export const getDatabaseInfo = (): Promise<any> => {
  return get('/database/maintenance/info')
}

/**
 * 执行数据库VACUUM操作
 */
export const vacuumDatabase = (): Promise<any> => {
  return post('/database/maintenance/vacuum')
}

/**
 * 执行数据库ANALYZE操作
 */
export const analyzeDatabase = (): Promise<any> => {
  return post('/database/maintenance/analyze')
}

/**
 * 检查数据库完整性
 */
export const checkDatabaseIntegrity = (): Promise<any> => {
  return get('/database/maintenance/integrity-check')
}
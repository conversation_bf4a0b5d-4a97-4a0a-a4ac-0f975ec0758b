from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
import logging
import base64
import cv2
import numpy as np
import time
from ..services.rtsp_service import RTSPManager, RTSPConfig, rtsp_manager

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/rtsp", tags=["RTSP"])

class RTSPConnectionRequest(BaseModel):
    """RTSP连接请求模型"""
    stream_id: str = Field(..., description="流ID")
    url: str = Field(..., description="RTSP URL")
    buffer_size: int = Field(default=1, ge=1, le=10, description="缓冲区大小")
    timeout: int = Field(default=5000, ge=1000, le=30000, description="超时时间(ms)")
    reconnect_interval: int = Field(default=5, ge=1, le=60, description="重连间隔(s)")
    enable_multi_thread: bool = Field(default=True, description="启用多线程处理")
    max_retry_count: int = Field(default=3, ge=1, le=10, description="最大重试次数")

class RTSPConnectionResponse(BaseModel):
    """RTSP连接响应模型"""
    success: bool
    message: str
    stream_info: Optional[Dict[str, Any]] = None

class RTSPStreamInfo(BaseModel):
    """RTSP流信息模型"""
    stream_id: str
    is_connected: bool
    stream_info: Dict[str, Any]
    config: Dict[str, Any]

@router.post("/connect", response_model=RTSPConnectionResponse)
async def connect_rtsp_stream(request: RTSPConnectionRequest, background_tasks: BackgroundTasks):
    """连接RTSP视频流"""
    try:
        logger.info(f"连接RTSP流: {request.stream_id} -> {request.url}")
        
        # 创建RTSP配置
        config = RTSPConfig(
            url=request.url,
            buffer_size=request.buffer_size,
            timeout=request.timeout,
            reconnect_interval=request.reconnect_interval,
            enable_multi_thread=request.enable_multi_thread,
            max_retry_count=request.max_retry_count
        )
        
        # 创建RTSP流处理器
        processor = rtsp_manager.create_stream(request.stream_id, config)
        
        # 设置帧回调函数
        def frame_callback(frame):
            try:
                # 将帧转换为base64
                _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
                frame_base64 = base64.b64encode(buffer).decode('utf-8')
                
                # 这里可以添加WebSocket广播逻辑
                # 暂时只记录日志
                logger.debug(f"处理RTSP帧: {request.stream_id}, 帧大小: {frame.shape}")
                
            except Exception as e:
                logger.error(f"帧回调处理错误: {e}")
        
        processor.set_frame_callback(frame_callback)
        
        # 启动RTSP流处理
        success = rtsp_manager.start_stream(request.stream_id)
        
        if success:
            # 等待连接建立
            import time
            time.sleep(1)
            
            stream_info = processor.get_stream_info()
            
            return RTSPConnectionResponse(
                success=True,
                message=f"RTSP流 {request.stream_id} 连接成功",
                stream_info=stream_info
            )
        else:
            raise HTTPException(status_code=500, detail="RTSP流启动失败")
            
    except Exception as e:
        logger.error(f"连接RTSP流失败: {e}")
        return RTSPConnectionResponse(
            success=False,
            message=f"连接失败: {str(e)}"
        )

@router.delete("/disconnect/{stream_id}")
async def disconnect_rtsp_stream(stream_id: str):
    """断开RTSP视频流"""
    try:
        logger.info(f"断开RTSP流: {stream_id}")
        
        success = rtsp_manager.stop_stream(stream_id)
        
        if success:
            return {"success": True, "message": f"RTSP流 {stream_id} 已断开"}
        else:
            return {"success": False, "message": f"RTSP流 {stream_id} 不存在"}
            
    except Exception as e:
        logger.error(f"断开RTSP流失败: {e}")
        raise HTTPException(status_code=500, detail=f"断开失败: {str(e)}")

@router.get("/streams", response_model=Dict[str, RTSPStreamInfo])
async def get_all_rtsp_streams():
    """获取所有RTSP流信息"""
    try:
        streams_info = {}
        
        for stream_id, processor in rtsp_manager.processors.items():
            streams_info[stream_id] = RTSPStreamInfo(
                stream_id=stream_id,
                is_connected=processor.is_connected(),
                stream_info=processor.get_stream_info(),
                config={
                    'url': processor.config.url,
                    'buffer_size': processor.config.buffer_size,
                    'timeout': processor.config.timeout,
                    'reconnect_interval': processor.config.reconnect_interval,
                    'enable_multi_thread': processor.config.enable_multi_thread,
                    'max_retry_count': processor.config.max_retry_count
                }
            )
        
        return streams_info
        
    except Exception as e:
        logger.error(f"获取RTSP流信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取流信息失败: {str(e)}")

@router.get("/stream/{stream_id}", response_model=RTSPStreamInfo)
async def get_rtsp_stream_info(stream_id: str):
    """获取指定RTSP流信息"""
    try:
        processor = rtsp_manager.get_processor(stream_id)
        
        if not processor:
            raise HTTPException(status_code=404, detail=f"RTSP流 {stream_id} 不存在")
        
        return RTSPStreamInfo(
            stream_id=stream_id,
            is_connected=processor.is_connected(),
            stream_info=processor.get_stream_info(),
            config={
                'url': processor.config.url,
                'buffer_size': processor.config.buffer_size,
                'timeout': processor.config.timeout,
                'reconnect_interval': processor.config.reconnect_interval,
                'enable_multi_thread': processor.config.enable_multi_thread,
                'max_retry_count': processor.config.max_retry_count
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取RTSP流信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取流信息失败: {str(e)}")

@router.post("/test-connection")
async def test_rtsp_connection(url: str, timeout: int = 5000):
    """测试RTSP连接"""
    try:
        logger.info(f"测试RTSP连接: {url}")
        
        # 创建临时的VideoCapture进行测试
        cap = cv2.VideoCapture(url)
        
        # 设置超时
        if hasattr(cv2, 'CAP_PROP_OPEN_TIMEOUT_MSEC'):
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout)
        
        if cap.isOpened():
            # 尝试读取一帧
            ret, frame = cap.read()
            cap.release()
            
            if ret and frame is not None:
                return {
                    "success": True,
                    "message": "RTSP连接测试成功",
                    "frame_size": frame.shape[:2] if frame is not None else None
                }
            else:
                return {
                    "success": False,
                    "message": "RTSP连接成功但无法读取帧"
                }
        else:
            cap.release()
            return {
                "success": False,
                "message": "无法建立RTSP连接"
            }
            
    except Exception as e:
        logger.error(f"RTSP连接测试失败: {e}")
        return {
            "success": False,
            "message": f"连接测试失败: {str(e)}"
        }

@router.delete("/disconnect-all")
async def disconnect_all_rtsp_streams():
    """断开所有RTSP流"""
    try:
        logger.info("断开所有RTSP流")
        
        rtsp_manager.stop_all()
        
        return {"success": True, "message": "所有RTSP流已断开"}
        
    except Exception as e:
        logger.error(f"断开所有RTSP流失败: {e}")
        raise HTTPException(status_code=500, detail=f"断开失败: {str(e)}")

@router.get("/health")
async def rtsp_health_check():
    """RTSP服务健康检查"""
    try:
        active_streams = len(rtsp_manager.processors)
        connected_streams = sum(
            1 for processor in rtsp_manager.processors.values()
            if processor.is_connected()
        )
        
        return {
            "status": "healthy",
            "active_streams": active_streams,
            "connected_streams": connected_streams,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        logger.error(f"RTSP健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": int(time.time() * 1000)
        }
// 预设检测计划组合式函数

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { useDetectionStateStore } from '@/stores/detection-state'
import type { 
  PresetSchedule, 
  PresetScheduleCreate, 
  PresetScheduleUpdate,
  ScheduleExecutionStatus
} from '@/types/preset-schedule'
import {
  getPresetSchedules,
  createPresetSchedule,
  updatePresetSchedule,
  deletePresetSchedule,
  togglePresetSchedule,
  executePresetSchedule,
  stopPresetSchedule,
  getScheduleExecutionStatus,
  checkScheduleConflict
} from '@/api/preset-schedules'

export function usePresetSchedule() {
  // 路由实例
  const router = useRouter()

  // 全局检测状态管理
  const detectionStateStore = useDetectionStateStore()

  // 响应式数据
  const schedules = ref<PresetSchedule[]>([])
  const loading = ref(false)
  const executingSchedules = ref<Map<string, ScheduleExecutionStatus>>(new Map())
  const autoCheckTimer = ref<NodeJS.Timeout | null>(null)

  // 计算属性
  const todaySchedules = computed(() => {
    const today = new Date().toISOString().split('T')[0]
    return schedules.value.filter(schedule => 
      schedule.date === today && schedule.isEnabled
    ).sort((a, b) => a.startTime.localeCompare(b.startTime))
  })

  const activeSchedules = computed(() => {
    return schedules.value.filter(schedule => 
      schedule.status === 'running' || schedule.status === 'active' || schedule.status === 'pending'
    )
  })

  const upcomingSchedules = computed(() => {
    const now = new Date()
    const today = now.toISOString().split('T')[0]
    const currentTime = now.toTimeString().slice(0, 5)
    
    return schedules.value.filter(schedule => {
      if (!schedule.isEnabled) return false
      if (schedule.date > today) return true
      if (schedule.date === today && schedule.startTime > currentTime) return true
      return false
    }).sort((a, b) => {
      const dateCompare = a.date.localeCompare(b.date)
      if (dateCompare !== 0) return dateCompare
      return a.startTime.localeCompare(b.startTime)
    })
  })

  // 加载预设计划列表
  const loadSchedules = async () => {
    try {
      loading.value = true
      const data = await getPresetSchedules()
      schedules.value = Array.isArray(data) ? data : []
    } catch (error) {
      console.error('加载预设计划失败:', error)
      schedules.value = []
      ElMessage.error('加载预设计划失败')
    } finally {
      loading.value = false
    }
  }

  // 创建预设计划
  const createSchedule = async (data: PresetScheduleCreate): Promise<boolean> => {
    try {
      // 检查时间冲突
      const conflictCheck = await checkScheduleConflict({
        date: data.date,
        startTime: data.startTime,
        endTime: data.endTime
      })

      if (conflictCheck.hasConflict) {
        ElMessage.warning(`时间冲突: ${conflictCheck.message}`)
        return false
      }

      const newSchedule = await createPresetSchedule(data)
      schedules.value.push(newSchedule)
      ElMessage.success('预设计划创建成功')
      return true
    } catch (error) {
      console.error('创建预设计划失败:', error)
      ElMessage.error('创建预设计划失败')
      return false
    }
  }

  // 更新预设计划
  const updateSchedule = async (id: string, data: PresetScheduleUpdate): Promise<boolean> => {
    try {
      // 如果更新了时间相关字段，检查冲突
      if (data.date || data.startTime || data.endTime) {
        const schedule = schedules.value.find(s => s.id === id)
        if (schedule) {
          const conflictCheck = await checkScheduleConflict({
            date: data.date || schedule.date,
            startTime: data.startTime || schedule.startTime,
            endTime: data.endTime || schedule.endTime,
            excludeId: id
          })

          if (conflictCheck.hasConflict) {
            ElMessage.warning(`时间冲突: ${conflictCheck.message}`)
            return false
          }
        }
      }

      const updatedSchedule = await updatePresetSchedule(id, data)
      const updateIndex = schedules.value.findIndex(s => s.id === id)
      if (updateIndex !== -1) {
        schedules.value[updateIndex] = updatedSchedule
      }
      ElMessage.success('预设计划更新成功')
      return true
    } catch (error) {
      console.error('更新预设计划失败:', error)
      ElMessage.error('更新预设计划失败')
      return false
    }
  }

  // 删除预设计划
  const deleteSchedule = async (id: string): Promise<boolean> => {
    try {
      await ElMessageBox.confirm(
        '确定要删除这个预设计划吗？此操作不可恢复。',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await deletePresetSchedule(id)
      schedules.value = schedules.value.filter(s => s.id !== id)
      ElMessage.success('预设计划删除成功')
      return true
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除预设计划失败:', error)
        ElMessage.error('删除预设计划失败')
      }
      return false
    }
  }

  // 切换预设计划启用状态
  const toggleSchedule = async (id: string, enabled: boolean): Promise<boolean> => {
    try {
      const updatedSchedule = await togglePresetSchedule(id, enabled)
      const toggleIndex = schedules.value.findIndex(s => s.id === id)
      if (toggleIndex !== -1) {
        schedules.value[toggleIndex] = updatedSchedule
      }
      ElMessage.success(`预设计划已${enabled ? '启用' : '禁用'}`)
      return true
    } catch (error) {
      console.error('切换预设计划状态失败:', error)
      ElMessage.error('切换预设计划状态失败')
      return false
    }
  }

  // 手动执行预设计划
  const executeSchedule = async (id: string): Promise<boolean> => {
    try {
      // 检查是否已有检测在运行
      if (detectionStateStore.isDetectionRunning) {
        const currentTemplate = detectionStateStore.currentRunningDetection?.templateName
        const result = await ElMessageBox.confirm(
          `检测模板 "${currentTemplate}" 正在运行中，是否停止当前检测并启动新的预设计划？`,
          '检测冲突',
          {
            confirmButtonText: '停止并启动',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).catch(() => false)

        if (!result) {
          return false
        }

        // 停止当前检测
        await detectionStateStore.stopDetection()
      }

      const response = await executePresetSchedule(id)
      executingSchedules.value.set(id, response)

      // 更新计划状态 - 使用后端返回的完整计划数据
      const executeIndex = schedules.value.findIndex(s => s.id === id)
      if (executeIndex !== -1 && response.schedule) {
        schedules.value[executeIndex] = response.schedule
      }

      // 使用全局状态管理启动检测
      if (response.template && response.template.id) {
        const success = await detectionStateStore.startDetection(
          response.template.id,
          response.template.name,
          id
        )

        if (success) {
          ElMessage.success(`预设计划 "${response.schedule?.name}" 开始执行，检测看板已启动`)
        } else {
          ElMessage.error('启动检测看板失败')
          return false
        }
      } else {
        ElMessage.success('预设计划开始执行')
      }

      return true
    } catch (error) {
      console.error('执行预设计划失败:', error)
      ElMessage.error('执行预设计划失败')
      return false
    }
  }

  // 停止预设计划
  const stopSchedule = async (id: string): Promise<boolean> => {
    try {
      // 检查是否是当前运行的计划
      const currentDetection = detectionStateStore.currentRunningDetection
      if (currentDetection?.scheduleId === id) {
        // 停止检测
        await detectionStateStore.stopDetection()
      }

      const response = await stopPresetSchedule(id)
      executingSchedules.value.set(id, response)
      ElMessage.success('预设计划已停止')

      // 更新计划状态 - 使用后端返回的完整计划数据
      const stopIndex = schedules.value.findIndex(s => s.id === id)
      if (stopIndex !== -1 && response.schedule) {
        schedules.value[stopIndex] = response.schedule
      }

      return true
    } catch (error) {
      console.error('停止预设计划失败:', error)
      ElMessage.error('停止预设计划失败')
      return false
    }
  }

  // 检查并自动执行到期的预设计划
  const checkAndExecuteSchedules = async () => {
    const now = new Date()
    const today = now.toISOString().split('T')[0]
    const currentTime = now.toTimeString().slice(0, 5)
    
    console.log(`[预设计划检查] 当前时间: ${today} ${currentTime}`)
    
    // 获取今日的启用计划
    const todayEnabledSchedules = schedules.value.filter(schedule => 
      schedule.date === today && schedule.isEnabled
    )
    
    console.log(`[预设计划检查] 今日启用计划数量: ${todayEnabledSchedules.length}`)
    
    for (const schedule of todayEnabledSchedules) {
      console.log(`[预设计划检查] 检查计划: ${schedule.name}, 状态: ${schedule.status}, 时间: ${schedule.startTime}-${schedule.endTime}`)
      
      // 检查是否需要启动
      if (
        (schedule.status === 'pending' || schedule.status === 'stopped') &&
        schedule.startTime <= currentTime &&
        schedule.endTime > currentTime
      ) {
        console.log(`[预设计划执行] 自动启动预设计划: ${schedule.name}`)
        ElMessage.info(`预设计划 "${schedule.name}" 开始执行`)
        await executeSchedule(schedule.id)
      } 
      // 检查是否需要停止
      else if (
        (schedule.status === 'running' || schedule.status === 'active') &&
        schedule.endTime <= currentTime
      ) {
        console.log(`[预设计划停止] 自动停止预设计划: ${schedule.name}`)
        ElMessage.info(`预设计划 "${schedule.name}" 执行完成`)
        await stopSchedule(schedule.id)
      }
    }
  }

  // 启动自动检查定时器
  const startAutoCheck = () => {
    if (autoCheckTimer.value) {
      clearInterval(autoCheckTimer.value)
    }
    
    // 每分钟检查一次
    autoCheckTimer.value = setInterval(() => {
      checkAndExecuteSchedules()
    }, 60000)
  }

  // 停止自动检查定时器
  const stopAutoCheck = () => {
    if (autoCheckTimer.value) {
      clearInterval(autoCheckTimer.value)
      autoCheckTimer.value = null
    }
  }

  // 获取执行状态
  const getExecutionStatus = (id: string) => {
    return executingSchedules.value.get(id)
  }

  // 组件挂载时初始化
  onMounted(() => {
    loadSchedules()
    startAutoCheck()
  })

  // 组件卸载时清理
  onUnmounted(() => {
    stopAutoCheck()
  })

  return {
    // 响应式数据
    schedules,
    loading,
    executingSchedules,
    
    // 计算属性
    todaySchedules,
    activeSchedules,
    upcomingSchedules,
    
    // 方法
    loadSchedules,
    createSchedule,
    updateSchedule,
    deleteSchedule,
    toggleSchedule,
    executeSchedule,
    stopSchedule,
    checkAndExecuteSchedules,
    startAutoCheck,
    stopAutoCheck,
    getExecutionStatus
  }
}
import cv2
import threading
import queue
import time
import logging
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class RTSPConfig:
    """RTSP配置类 - 参考video_worker.py的简单配置"""
    url: str
    fps: float = 25.0
    timeout: int = 10000
    max_retries: int = 3
    reconnect_interval: float = 3.0

class RTSPStreamProcessor:
    """RTSP视频流处理器 - 解决解码错误和内存溢出问题"""
    
    def __init__(self, config: RTSPConfig):
        self.config = config
        self.frame_queue = queue.Queue(maxsize=config.buffer_size)
        self.capture_thread: Optional[threading.Thread] = None
        self.is_running = False
        self.cap: Optional[cv2.VideoCapture] = None
        self.frame_callback: Optional[Callable] = None
        self.retry_count = 0
        
    def set_frame_callback(self, callback: Callable[[Any], None]):
        """设置帧回调函数"""
        self.frame_callback = callback
        
    def _capture_frames(self):
        """帧捕获线程 - 优化实时性能"""
        logger.info(f"开始RTSP流捕获: {self.config.url}")
        
        consecutive_failures = 0
        max_consecutive_failures = 5
        frame_skip_count = 0
        
        while self.is_running:
            try:
                # 初始化视频捕获
                if self.cap is None or not self.cap.isOpened():
                    self._initialize_capture()
                    
                ret, frame = self.cap.read()
                
                if not ret or frame is None:
                    consecutive_failures += 1
                    logger.warning(f"读取帧失败，连续失败次数: {consecutive_failures}")
                    
                    if consecutive_failures >= max_consecutive_failures:
                        logger.error(f"连续失败{consecutive_failures}次，尝试重连")
                        self._force_reconnect()
                        consecutive_failures = 0
                    
                    time.sleep(0.1)
                    continue
                    
                # 重置失败计数器
                consecutive_failures = 0
                self.retry_count = 0
                
                # 实时性优化：如果队列有积压，跳过一些帧
                queue_size = self.frame_queue.qsize()
                if queue_size > 1:
                    frame_skip_count += 1
                    # 每3帧跳过2帧以保持实时性
                    if frame_skip_count % 3 != 0:
                        continue
                
                # 清理旧帧，保持队列最新
                while self.frame_queue.qsize() >= 2:
                    try:
                        self.frame_queue.get_nowait()
                    except queue.Empty:
                        break
                        
                # 将帧放入队列
                try:
                    self.frame_queue.put_nowait(frame)
                except queue.Full:
                    # 如果队列满了，移除最旧的帧
                    try:
                        self.frame_queue.get_nowait()
                        self.frame_queue.put_nowait(frame)
                    except queue.Empty:
                        pass
                    
                # 减少延迟 - 使用更小的睡眠时间
                time.sleep(0.01)  # 10ms，允许更高的帧率
                
            except Exception as e:
                consecutive_failures += 1
                logger.warning(f"捕获帧时发生错误: {e}")
                
                if consecutive_failures >= max_consecutive_failures:
                    logger.error(f"连续失败{consecutive_failures}次，尝试重连")
                    self._force_reconnect()
                    consecutive_failures = 0
                    
                time.sleep(0.1)
                
        # 清理资源
        self._cleanup_capture()
        
    def _test_network_connection(self) -> bool:
        """测试网络连接 - 参考video_worker.py的网络预检测"""
        try:
            import socket
            from urllib.parse import urlparse
            
            parsed = urlparse(self.config.url)
            host = parsed.hostname
            port = parsed.port or 554  # RTSP默认端口
            
            if not host:
                return False
                
            # 创建套接字连接测试
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)  # 5秒超时
            
            result = sock.connect_ex((host, port))
            sock.close()
            
            return result == 0
        except Exception as e:
            logger.warning(f"网络连接测试失败: {e}")
            return False
    
    def _initialize_capture(self):
        """初始化视频捕获 - 优化H.264解码和实时性能"""
        try:
            if self.cap:
                self.cap.release()
                
            # 先进行网络连接测试
            if not self._test_network_connection():
                raise Exception(f"网络连接测试失败: {self.config.url}")
            
            # 尝试使用FFMPEG后端以获得更好的RTSP支持
            try:
                self.cap = cv2.VideoCapture(self.config.url, cv2.CAP_FFMPEG)
                logger.info("使用FFMPEG后端")
            except:
                # 如果FFMPEG失败，回退到默认后端
                self.cap = cv2.VideoCapture(self.config.url)
                logger.info("使用默认后端")
            
            # 优化缓冲区设置 - 使用最小缓冲区以减少延迟
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            
            # 设置超时参数
            if hasattr(cv2, 'CAP_PROP_OPEN_TIMEOUT_MSEC'):
                self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, self.config.timeout)
            if hasattr(cv2, 'CAP_PROP_READ_TIMEOUT_MSEC'):
                self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)  # 5秒读取超时
            
            # 设置FPS以匹配配置
            self.cap.set(cv2.CAP_PROP_FPS, self.config.fps)
            
            if not self.cap.isOpened():
                raise Exception(f"无法打开RTSP流: {self.config.url}")
                
            # 读取一帧测试连接
            ret, frame = self.cap.read()
            if not ret or frame is None:
                raise Exception(f"无法读取初始帧: {self.config.url}")
                
            # 获取并记录流信息
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = self.cap.get(cv2.CAP_PROP_FPS)
            buffer_size = int(self.cap.get(cv2.CAP_PROP_BUFFERSIZE))
            
            logger.info(f"RTSP初始化成功 - 分辨率: {width}x{height}, FPS: {fps}, 缓冲区: {buffer_size}")
            
        except Exception as e:
            logger.error(f"RTSP初始化失败: {e}")
            raise

            
    def _reconnect(self):
        """重新连接RTSP流 - 参考video_worker.py的重连策略"""
        try:
            self._cleanup_capture()
            time.sleep(self.config.reconnect_interval)
            self.retry_count = 0
            logger.info("尝试重新连接RTSP流")
            self._initialize_capture()
        except Exception as e:
            logger.error(f"重新连接失败: {e}")
    
    def _force_reconnect(self):
        """强制重连 - 参考video_worker.py的重连策略"""
        logger.info("执行强制重连...")
        self._reconnect()
    
    def _cleanup_capture(self):
        """清理捕获资源"""
        if self.cap:
            try:
                self.cap.release()
            except Exception as e:
                logger.debug(f"释放捕获资源时出错: {e}")
            finally:
                self.cap = None
            
    def _process_frames(self):
        """帧处理线程 - 从队列中获取帧并处理"""
        logger.info("开始帧处理线程")
        
        while self.is_running:
            try:
                # 从队列获取帧，设置超时避免阻塞
                frame = self.frame_queue.get(timeout=1.0)
                
                if self.frame_callback and frame is not None:
                    # 调用回调函数处理帧
                    self.frame_callback(frame)
                    
            except queue.Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                logger.error(f"帧处理错误: {e}")
                
    def start(self):
        """启动RTSP流处理"""
        if self.is_running:
            logger.warning("RTSP流处理已在运行")
            return
            
        self.is_running = True
        
        # 启动帧捕获线程
        self.capture_thread = threading.Thread(
            target=self._capture_frames,
            name="RTSP-Capture",
            daemon=True
        )
        self.capture_thread.start()
        
        # 启动帧处理线程（简化为总是启用）
        self.process_thread = threading.Thread(
            target=self._process_frames,
            name="RTSP-Process",
            daemon=True
        )
        self.process_thread.start()
        logger.info("RTSP流处理已启动")
            
    def stop(self):
        """停止RTSP流处理"""
        logger.info("停止RTSP流处理")
        self.is_running = False
        
        # 等待线程结束
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=5)
            
        if hasattr(self, 'process_thread') and self.process_thread.is_alive():
            self.process_thread.join(timeout=5)
            
        # 清理资源
        self._cleanup_capture()
        
        # 清空队列
        while not self.frame_queue.empty():
            try:
                self.frame_queue.get_nowait()
            except queue.Empty:
                break
                
        logger.info("RTSP流处理已停止")
        
    def get_frame(self) -> Optional[Any]:
        """获取最新帧"""
        try:
            return self.frame_queue.get_nowait()
        except queue.Empty:
            return None
        
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.cap is not None and self.cap.isOpened()
        
    def get_stream_info(self) -> Dict[str, Any]:
        """获取流信息"""
        if not self.is_connected():
            return {}
            
        try:
            return {
                'width': int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                'height': int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                'fps': self.cap.get(cv2.CAP_PROP_FPS),
                'frame_count': int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT)),
                'buffer_size': self.config.buffer_size,
                'queue_size': self.frame_queue.qsize()
            }
        except Exception as e:
            logger.error(f"获取流信息失败: {e}")
            return {}

class RTSPManager:
    """RTSP管理器 - 管理多个RTSP流"""
    
    def __init__(self):
        self.processors: Dict[str, RTSPStreamProcessor] = {}
        
    def create_stream(self, stream_id: str, config: RTSPConfig) -> RTSPStreamProcessor:
        """创建RTSP流处理器"""
        if stream_id in self.processors:
            self.stop_stream(stream_id)
            
        processor = RTSPStreamProcessor(config)
        self.processors[stream_id] = processor
        return processor
        
    def start_stream(self, stream_id: str) -> bool:
        """启动指定的RTSP流"""
        if stream_id in self.processors:
            try:
                self.processors[stream_id].start()
                return True
            except Exception as e:
                logger.error(f"启动RTSP流失败 {stream_id}: {e}")
                return False
        return False
        
    def stop_stream(self, stream_id: str) -> bool:
        """停止指定的RTSP流"""
        if stream_id in self.processors:
            try:
                self.processors[stream_id].stop()
                del self.processors[stream_id]
                return True
            except Exception as e:
                logger.error(f"停止RTSP流失败 {stream_id}: {e}")
                return False
        return False
        
    def get_processor(self, stream_id: str) -> Optional[RTSPStreamProcessor]:
        """获取RTSP流处理器"""
        return self.processors.get(stream_id)
        
    def stop_all(self):
        """停止所有RTSP流"""
        for stream_id in list(self.processors.keys()):
            self.stop_stream(stream_id)
            
    def get_all_stream_info(self) -> Dict[str, Dict[str, Any]]:
        """获取所有流的信息"""
        return {
            stream_id: processor.get_stream_info()
            for stream_id, processor in self.processors.items()
        }

# 全局RTSP管理器实例
rtsp_manager = RTSPManager()
<template>
  <div class="roi-preview">
    <el-card shadow="never">
      <template #header>
        <div class="preview-header">
          <el-icon><View /></el-icon>
          <span>ROI预览</span>
          <el-tag
            :type="roiData.attribute === 'yazhu' ? 'danger' : 'primary'"
            size="small"
          >
            {{ getAttributeLabel(roiData.attribute) }}
          </el-tag>
        </div>
      </template>

      <div class="preview-content">
        <!-- 基本信息 -->
        <div class="info-section">
          <h4>基本信息</h4>
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="ROI ID">
              <el-text class="roi-id">{{ roiData.roi_id }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="名称">
              {{ roiData.name || '未命名' }}
            </el-descriptions-item>
            <el-descriptions-item label="属性">
              <el-tag
                :type="roiData.attribute === 'yazhu' ? 'danger' : 'primary'"
                size="small"
              >
                {{ getAttributeLabel(roiData.attribute) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="形状">
              {{ getShapeLabel(roiData.roi_type) }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag 
                :type="roiData.is_active ? 'success' : 'info'"
                size="small"
              >
                {{ roiData.is_active ? '启用' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">
              {{ formatDate(roiData.created_at) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 坐标信息 -->
        <div class="coordinates-section">
          <h4>坐标信息</h4>
          <div class="coordinates-grid">
            <div 
              v-for="(coord, index) in roiData.coordinates" 
              :key="index"
              class="coordinate-item"
            >
              <el-tag size="small" type="info">
                点{{ index + 1 }}: ({{ coord.x }}, {{ coord.y }})
              </el-tag>
            </div>
          </div>
          <el-text type="info" size="small">
            共 {{ roiData.coordinates?.length || 0 }} 个坐标点
          </el-text>
        </div>

        <!-- 算法参数 -->
        <div class="algorithm-section">
          <h4>算法参数</h4>
          <div v-if="roiData.algorithm_type || roiData.algorithm_params">
            <el-tag
              :type="getAlgorithmTagType(roiData.algorithm_type || roiData.algorithm_params?.type)"
              size="small"
              class="algorithm-tag"
            >
              {{ getAlgorithmLabel(roiData.algorithm_type || roiData.algorithm_params?.type) }}
            </el-tag>
            
            <!-- 方向检测参数 -->
            <div v-if="roiData.algorithm_params.type === 'direction'" class="params-detail">
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item label="前置背景检测">
                  <div v-if="roiData.algorithm_params['前置背景检测']">
                    <p>启用: {{ roiData.algorithm_params['前置背景检测'].enabled ? '是' : '否' }}</p>
                    <p>背景更新率: {{ roiData.algorithm_params['前置背景检测'].backgroundUpdateRate }}</p>
                    <p>运动阈值: {{ roiData.algorithm_params['前置背景检测'].motionThreshold }}</p>
                    <p>最小区域: {{ roiData.algorithm_params['前置背景检测'].minArea }}</p>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="后置方向检测">
                  <div v-if="roiData.algorithm_params['后置方向检测']">
                    <p>连续检测阈值: {{ roiData.algorithm_params['后置方向检测'].consecutiveDetectionThreshold }}</p>
                    <p>最小位移: {{ roiData.algorithm_params['后置方向检测'].minDisplacement }}</p>
                    <p>最大耐心值: {{ roiData.algorithm_params['后置方向检测'].maxPatience }}</p>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 运动检测参数 -->
            <div v-else-if="roiData.algorithm_params.type === 'motion'" class="params-detail">
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item label="运动检测参数">
                  <div v-if="roiData.algorithm_params['运动检测']">
                    <p>算法: {{ roiData.algorithm_params['运动检测'].algorithm }}</p>
                    <p>学习率: {{ roiData.algorithm_params['运动检测'].learningRate }}</p>
                    <p>检测阈值: {{ roiData.algorithm_params['运动检测'].detectionThreshold }}</p>
                    <p>阴影移除: {{ roiData.algorithm_params['运动检测'].shadowRemoval }}</p>
                    <p>阈值: {{ roiData.algorithm_params['运动检测'].threshold }}</p>
                    <p>帧间隔: {{ roiData.algorithm_params['运动检测'].frameInterval }}</p>
                    <p>最小区域: {{ roiData.algorithm_params['运动检测'].minArea }}</p>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 其他算法参数 -->
            <div v-else class="params-detail">
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item label="参数详情">
                  <pre class="params-json">{{ JSON.stringify(roiData.algorithm_params, null, 2) }}</pre>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
          <el-text v-else type="warning" size="small">
            暂无算法参数
          </el-text>
        </div>

        <!-- 视频源信息 -->
        <div class="video-source-section">
          <h4>视频源信息</h4>
          <el-descriptions :column="1" size="small" border>
            <el-descriptions-item label="视频源ID">
              {{ roiData.video_source_id || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="视频源路径">
              {{ roiData.video_source_path || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { View } from '@element-plus/icons-vue'

// Props
interface Props {
  roiData: any
}

defineProps<Props>()

// 获取属性标签
const getAttributeLabel = (attribute: string) => {
  switch (attribute) {
    case 'yazhu':
      return '压铸机'
    case 'pailiao':
      return '排料口'
    default:
      return attribute || '未知'
  }
}

// 获取形状标签
const getShapeLabel = (shape: string) => {
  switch (shape) {
    case 'polygon':
      return '多边形'
    case 'rectangle':
      return '矩形'
    case 'circle':
      return '圆形'
    default:
      return shape || '多边形'
  }
}

// 获取算法标签
const getAlgorithmLabel = (algorithm: string) => {
  switch (algorithm) {
    case 'direction':
      return '方向检测'
    case 'motion':
      return '运动检测'
    case 'background_subtraction':
      return '背景减除'
    case 'frame_difference':
      return '帧差法'
    default:
      return algorithm || '未知'
  }
}

// 获取算法标签类型
const getAlgorithmTagType = (algorithm: string) => {
  switch (algorithm) {
    case 'direction':
      return 'warning'
    case 'motion':
    case 'background_subtraction':
    case 'frame_difference':
      return 'success'
    default:
      return 'info'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    return dateString
  }
}
</script>

<style scoped>
.roi-preview {
  width: 100%;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.preview-content {
  max-height: 400px;
  overflow-y: auto;
}

.info-section,
.coordinates-section,
.algorithm-section,
.video-source-section {
  margin-bottom: 20px;
}

.info-section h4,
.coordinates-section h4,
.algorithm-section h4,
.video-source-section h4 {
  margin: 0 0 12px 0;
  color: var(--text-color, #303133);
  font-size: 14px;
  font-weight: 600;
  transition: color 0.3s ease;
}

.roi-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--primary-color, #409eff);
  transition: color 0.3s ease;
}

.coordinates-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.coordinate-item {
  flex: 0 0 auto;
}

.algorithm-tag {
  margin-bottom: 12px;
}

.params-detail {
  margin-top: 8px;
}

.params-detail p {
  margin: 4px 0;
  font-size: 13px;
  color: var(--text-color-soft, #606266);
  transition: color 0.3s ease;
}

.params-json {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--text-color-soft, #606266);
  background-color: var(--bg-color-soft, #f5f7fa);
  padding: 8px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
  transition: color 0.3s ease, background-color 0.3s ease;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: var(--text-color, #303133) !important;
  transition: color 0.3s ease;
}

:deep(.el-descriptions__content) {
  color: var(--text-color-soft, #606266) !important;
  transition: color 0.3s ease;
}

/* 暗色模式增强支持 */
.dark-theme .params-json {
  background-color: var(--bg-color-mute);
  border: 1px solid var(--border-color);
}

.dark-theme :deep(.el-descriptions) {
  border-color: var(--border-color) !important;
}

.dark-theme :deep(.el-descriptions__cell) {
  border-color: var(--border-color) !important;
}
</style>

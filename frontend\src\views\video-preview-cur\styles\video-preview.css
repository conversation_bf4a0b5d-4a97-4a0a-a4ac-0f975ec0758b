/* Video Preview 样式文件 */

.video-preview-page {
  padding: 20px;
  background-color: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 页面标题区域 */
.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: var(--text-color);
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 600;
  transition: color 0.3s ease;
}

.page-description {
  color: var(--text-color-soft);
  font-size: 16px;
  margin: 0;
  transition: color 0.3s ease;
}

/* 内容区域 */
.preview-content {
  max-width: 1600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 视频源选择区域 */
.video-source-section {
  background: var(--bg-color-soft);
  padding: 20px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.source-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.source-controls label {
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
  transition: color 0.3s ease;
}

.source-select {
  flex: 1;
  min-width: 300px;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  background: var(--bg-color);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.source-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.source-select:disabled {
  background-color: var(--bg-color-mute);
  color: var(--text-color-mute);
  cursor: not-allowed;
}

.refresh-btn {
  padding: 8px 16px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.refresh-btn:hover:not(:disabled) {
  background: var(--primary-color-light);
}

.refresh-btn:disabled {
  background: var(--text-color-mute);
  cursor: not-allowed;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  gap: 20px;
}

/* 左侧视频区域 */
.video-section {
  flex: 0 0 60%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* ROI工具栏区域 */
.roi-toolbar-section {
  background: var(--bg-color-soft);
  padding: 16px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.roi-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  flex-wrap: wrap;
}

.toolbar-title h4 {
  margin: 0;
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: color 0.3s ease;
}

.toolbar-controls {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

/* ROI属性选择器 */
.roi-attribute-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px 10px;
  background: var(--bg-color);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.roi-attribute-selector label {
  color: var(--text-color);
  font-size: 12px;
  white-space: nowrap;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-color-soft);
  cursor: pointer;
  transition: color 0.3s ease;
}

.radio-label:hover {
  color: var(--primary-color);
}

.radio-label input[type="radio"] {
  margin: 0;
}

/* 绘制状态显示 */
.drawing-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.status-indicator.active {
  background-color: var(--success-color);
  color: white;
}

.status-indicator.inactive {
  background-color: var(--info-color);
  color: white;
}

/* 管理按钮 */
.management-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tool-btn {
  padding: 6px 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-color);
  color: var(--text-color-soft);
  cursor: pointer;
  font-size: 12px;
  font-weight: 400;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.tool-btn:hover:not(:disabled) {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: var(--bg-color);
}

.tool-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.tool-btn:disabled {
  background: var(--bg-color-mute);
  border-color: var(--border-color);
  color: var(--text-color-mute);
  cursor: not-allowed;
  opacity: 0.5;
}

.tool-btn.toggle-btn.active {
  background: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

/* 简化特殊按钮样式 - 统一hover效果 */
.tool-btn.clear-btn:hover:not(:disabled) {
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.tool-btn.save-btn:hover:not(:disabled) {
  border-color: var(--success-color);
  color: var(--success-color);
}

.tool-btn.export-btn:hover:not(:disabled),
.tool-btn.import-btn:hover:not(:disabled) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.tool-btn.load-btn {
  border-color: var(--success-color);
  color: var(--success-color);
}

.tool-btn.load-btn:hover:not(:disabled) {
  border-color: var(--success-color);
  color: var(--success-color);
  background-color: rgba(103, 194, 58, 0.1);
}

.tool-btn.load-btn:disabled {
  border-color: var(--text-color-mute);
  color: var(--text-color-mute);
  cursor: not-allowed;
}

.tool-btn.test-btn {
  border-color: var(--warning-color);
  color: var(--warning-color);
}

.tool-btn.test-btn:hover:not(:disabled) {
  border-color: var(--warning-color);
  color: var(--warning-color);
  background-color: rgba(230, 162, 60, 0.1);
}

/* 视频预览区域 */
.video-preview-section {
  background: var(--bg-color);
  border-radius: 6px;
  overflow: hidden;
}

.video-container {
  position: relative;
  background-color: #000;
  border: 1px solid var(--border-color);
  width: 100%;
  aspect-ratio: 16/9;
}

.video-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.motion-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.roi-display-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 20;
}

/* 控制按钮区域 */
.control-section {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.control-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  min-width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.control-btn.primary {
  background: var(--primary-color);
  color: white;
}

.control-btn.primary:hover:not(:disabled) {
  background: var(--primary-color-light);
}

.control-btn.danger {
  background: var(--danger-color);
  color: white;
}

.control-btn.danger:hover:not(:disabled) {
  background: var(--danger-color-light);
}

.control-btn.secondary {
  background: var(--text-color-soft);
  color: white;
}

.control-btn.secondary:hover:not(:disabled) {
  background: var(--border-color);
}

.control-btn.active {
  background: var(--success-color);
  color: white;
}

.control-btn.active:hover:not(:disabled) {
  background: var(--success-color-light);
}

.control-btn:disabled {
  background: var(--bg-color-mute);
  color: var(--text-color-mute);
  cursor: not-allowed;
  opacity: 0.7;
}

/* 右侧面板区域 */
.right-panel {
  flex: 0 0 40%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* ROI管理区域 */
.roi-management-section {
  background: var(--bg-color-soft);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  flex: 1;
}

.roi-list-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: rgba(var(--bg-color-rgb), 0.05);
}

.panel-header h4 {
  margin: 0;
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.panel-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.status-disabled {
  background-color: var(--info-color);
  color: white;
}

.status-locked {
  background-color: var(--info-color);
  color: white;
}

.status-ready {
  background-color: var(--warning-color);
  color: white;
}

.status-drawing {
  background-color: var(--success-color);
  color: white;
}

.dark-theme .status-locked {
  background-color: #909399;
}

.dark-theme .status-ready {
  background-color: #E6A23C;
}

.dark-theme .status-drawing {
  background-color: #67C23A;
}

.panel-content {
  padding: 16px;
  flex-grow: 1;
  overflow-y: auto;
}

/* ROI树状列表 */
.roi-tree {
  margin-bottom: 20px;
}

.empty-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 24px 16px;
  background: var(--bg-color);
  border-radius: 6px;
  border: 1px dashed var(--border-color);
}

.empty-icon {
  font-size: 40px;
  color: var(--text-color-mute);
}

.empty-text p {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: var(--text-color);
  font-weight: 500;
}

.empty-text small {
  color: var(--text-color-soft);
  font-size: 12px;
}

/* ROI分组 */
.roi-group {
  margin-bottom: 12px;
  background: var(--bg-color);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.group-header {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  background: rgba(var(--bg-color-rgb), 0.05);
  border-bottom: 1px solid var(--border-color);
}

.group-icon {
  margin-right: 6px;
}

.group-title {
  flex: 1;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-color);
}

.group-clear-btn {
  background: none;
  border: none;
  color: var(--danger-color);
  cursor: pointer;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.group-clear-btn:hover {
  background: rgba(var(--danger-color-rgb), 0.1);
}

.group-content {
  padding: 8px;
}

.group-content .roi-item {
  margin-bottom: 4px;
}

.group-content .roi-item:last-child {
  margin-bottom: 0;
}

.roi-item.highlighted {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-color: var(--primary-color);
}

.roi-item .roi-info {
  padding: 4px;
}

.roi-item .roi-info:hover {
  background-color: rgba(var(--bg-color-rgb), 0.05);
}

.roi-item.highlighted .roi-info {
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

/* 紧凑型ROI项目样式 */
.roi-item-compact {
  margin-bottom: 4px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-color);
  transition: all 0.2s ease;
}

.roi-item-compact.highlighted {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-color: var(--primary-color);
}

.roi-compact-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.roi-compact-content:hover {
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.roi-color-compact {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.roi-info-compact {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
}

.roi-name-compact {
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.roi-meta-compact {
  font-size: 12px;
  color: var(--text-color-soft);
  white-space: nowrap;
}

.roi-actions-compact {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.edit-btn-compact,
.delete-btn-compact {
  padding: 2px 6px;
  font-size: 11px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.edit-btn-compact {
  background: var(--primary-color);
  color: white;
}

.edit-btn-compact:hover {
  background: var(--primary-color-light);
}

.delete-btn-compact {
  background: var(--danger-color);
  color: white;
}

.delete-btn-compact:hover {
  background: var(--danger-color-light);
}

/* 使用说明 */
.help-section {
  margin-top: 20px;
  background: var(--bg-color);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.help-header {
  padding: 12px 16px;
  background: rgba(var(--bg-color-rgb), 0.05);
  border-bottom: 1px solid var(--border-color);
  font-weight: 500;
  font-size: 13px;
  color: var(--text-color);
}

.help-content {
  padding: 16px;
}

.help-content ol {
  margin: 0;
  padding-left: 18px;
}

.help-content li {
  color: var(--text-color-soft);
  font-size: 12px;
  margin-bottom: 6px;
  line-height: 1.5;
}

/* 算法配置参数区域 */
.algorithm-config-section {
  background: var(--bg-color-soft);
  padding: 16px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.algorithm-config-section h4 {
  margin: 0 0 16px 0;
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.config-placeholder {
  padding: 24px;
  text-align: center;
  background: var(--bg-color);
  border-radius: 4px;
  border: 1px dashed var(--border-color);
  color: var(--text-color-soft);
}

/* 操作信息区域 */
.operation-info-section {
  background: var(--bg-color-soft);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: rgba(var(--bg-color-rgb), 0.05);
}

.info-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.clear-btn {
  background: none;
  border: none;
  color: var(--danger-color);
  cursor: pointer;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: rgba(var(--danger-color-rgb), 0.1);
}

.info-content {
  padding: 0;
  height: 150px;
  overflow-y: auto;
  background: var(--bg-color);
}

.info-content::-webkit-scrollbar {
  width: 6px;
}

.info-content::-webkit-scrollbar-track {
  background: var(--bg-color);
}

.info-content::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 6px;
}

.info-content::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-color-mute);
}

.info-item {
  display: flex;
  gap: 8px;
  padding: 6px 16px;
  border-bottom: 1px solid var(--border-color);
  font-size: 12px;
  transition: background-color 0.3s ease;
}

.info-item:hover {
  background-color: var(--bg-color-hover);
}

.info-time {
  flex-shrink: 0;
  color: var(--text-color-mute);
  font-family: monospace;
  transition: color 0.3s ease;
  padding-right: 8px;
  border-right: 1px solid var(--border-color);
}

.info-message {
  color: var(--text-color-soft);
  word-break: break-word;
  transition: color 0.3s ease;
  line-height: 1.5;
}

/* Dark theme specific overrides */
.dark-theme .video-container {
  border-color: #444;
}

.dark-theme .tool-btn:hover:not(:disabled) {
  border-color: var(--primary-color);
}

.dark-theme .roi-item:hover {
  background: #2a2a2a;
}

.dark-theme .info-item:hover {
  background-color: #2a2a2a;
}

.dark-theme .source-select option {
  background-color: #333;
  color: #fff;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }
  
  .video-section {
    flex: none;
  }
  
  .right-panel {
    flex: none;
  }
  
  .roi-toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toolbar-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .roi-attribute-selector,
  .drawing-status,
  .management-buttons {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .video-preview-page {
    padding: 10px;
  }
  
  .source-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .source-select {
    min-width: auto;
  }
  
  .control-section {
    flex-direction: column;
  }
  
  .control-btn {
    width: 100%;
  }
  
  .toolbar-controls {
    gap: 10px;
  }
  
  .tool-btn {
    width: 100%;
  }
}
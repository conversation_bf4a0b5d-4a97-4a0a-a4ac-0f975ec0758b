from datetime import datetime
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Table, JSON, Text, Index, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
import json

Base = declarative_base()


class DetectionTemplate(Base):
    __tablename__ = "detection_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, nullable=False)
    description = Column(String, nullable=True)
    status = Column(String, default="enabled")  # enabled, disabled
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联的压铸机
    template_die_casters = relationship("TemplateDieCaster", back_populates="template", cascade="all, delete-orphan")


class TemplateDieCaster(Base):
    __tablename__ = "template_die_casters"
    
    id = Column(Integer, primary_key=True, index=True)
    template_id = Column(Integer, ForeignKey("detection_templates.id", ondelete="CASCADE"), nullable=False)
    die_caster_id = Column(Integer, ForeignKey("die_casters.id", ondelete="CASCADE"), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    
    template = relationship("DetectionTemplate", back_populates="template_die_casters")
    die_caster = relationship("DieCaster", back_populates="template_associations")



class VideoSource(Base):
    __tablename__ = "video_sources"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, nullable=False)
    description = Column(String, nullable=True)
    source_type = Column(String, nullable=False)  # "local_file", "rtsp_stream", "websdk_device"

    # 通用字段
    path = Column(String, nullable=True)  # 本地文件路径或RTSP URL

    # WebSDK设备专用字段
    device_ip = Column(String, nullable=True)  # 设备IP地址
    device_port = Column(Integer, nullable=True)  # 设备端口
    device_username = Column(String, nullable=True)  # 设备用户名
    device_password = Column(String, nullable=True)  # 设备密码
    device_protocol = Column(Integer, nullable=True)  # 协议类型：1-HTTP, 2-HTTPS
    channel_id = Column(Integer, nullable=True)  # 通道号
    stream_type = Column(Integer, nullable=True)  # 码流类型：1-主码流, 2-子码流

    # 设备状态
    device_status = Column(String, default="offline")  # offline, online, error
    last_connected_at = Column(DateTime, nullable=True)  # 最后连接时间

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    detection_group = relationship("DetectionGroup", back_populates="video_source", uselist=False, cascade="all, delete-orphan")

class DieCaster(Base):
    __tablename__ = "die_casters"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, nullable=False)
    description = Column(String, nullable=True)
    ip_address = Column(String, nullable=True)
    port = Column(Integer, nullable=True)
    status = Column(String, default="offline")  # offline, online, error
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    detection_groups = relationship("DetectionGroup", back_populates="die_caster", cascade="all, delete-orphan")
    template_associations = relationship("TemplateDieCaster", back_populates="die_caster", cascade="all, delete-orphan")



class DetectionGroup(Base):
    __tablename__ = "detection_groups"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    template_id = Column(Integer, ForeignKey("detection_templates.id", ondelete="CASCADE"), nullable=True)
    die_caster_id = Column(Integer, ForeignKey("die_casters.id", ondelete="CASCADE"), nullable=False)
    video_source_id = Column(Integer, ForeignKey("video_sources.id", ondelete="CASCADE"), nullable=False)
    config_json = Column(JSON, nullable=False, default="{}")
    status = Column(String, default="inactive")  # inactive, active, error
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    template = relationship("DetectionTemplate")
    die_caster = relationship("DieCaster", back_populates="detection_groups")
    video_source = relationship("VideoSource", back_populates="detection_group")



class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    email = Column(String, unique=True, nullable=False)
    role = Column(String, default="operator")  # admin, manager, operator
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class SystemSettings(Base):
    __tablename__ = "system_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    setting_key = Column(String, unique=True, nullable=False)  # 设置项的键名
    setting_value = Column(String, nullable=True)  # 设置项的值
    setting_type = Column(String, default="string")  # 值类型: string, integer, boolean, json
    description = Column(String, nullable=True)  # 设置项描述
    category = Column(String, default="basic")  # 设置分类: basic, storage, notification
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class ROIConfig(Base):
    """ROI配置数据模型"""
    __tablename__ = "roi_configs"

    id = Column(Integer, primary_key=True, index=True)
    roi_id = Column(String(255), unique=True, index=True)  # ROI唯一标识
    name = Column(String(255))  # ROI名称
    attribute = Column(String(50))  # ROI属性：yazhu/pailiao
    roi_type = Column(String(50))  # ROI类型：rectangle/polygon
    color = Column(String(20), default='#ff0000')  # ROI颜色，十六进制格式

    # ROI几何信息
    coordinates = Column(Text)  # ROI坐标点，JSON格式

    # 检测算法配置
    algorithm_type = Column(String(50))  # 算法类型：direction/motion
    algorithm_params = Column(Text)  # 算法参数，JSON格式

    # 视频源信息
    video_source_id = Column(String(255))  # 视频源ID
    video_source_path = Column(String(500))  # 视频源路径

    # 状态信息
    is_active = Column(Boolean, default=True)  # 是否激活

    # 时间戳
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'roi_id': self.roi_id,
            'name': self.name,
            'attribute': self.attribute,
            'roi_type': self.roi_type,
            'color': self.color,
            'coordinates': json.loads(self.coordinates) if self.coordinates else [],
            'algorithm_type': self.algorithm_type,
            'algorithm_params': json.loads(self.algorithm_params) if self.algorithm_params else {},
            'video_source_id': self.video_source_id,
            'video_source_path': self.video_source_path,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建实例"""
        return cls(
            roi_id=data.get('roi_id'),
            name=data.get('name'),
            attribute=data.get('attribute'),
            roi_type=data.get('roi_type'),
            color=data.get('color', '#ff0000'),
            coordinates=json.dumps(data.get('coordinates', [])),
            algorithm_type=data.get('algorithm_type'),
            algorithm_params=json.dumps(data.get('algorithm_params', {})),
            video_source_id=data.get('video_source_id'),
            video_source_path=data.get('video_source_path'),
            is_active=data.get('is_active', True)
        )


class SystemLog(Base):
    """系统日志表"""
    __tablename__ = "system_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(DateTime, default=datetime.now, nullable=False, index=True)
    level = Column(String(20), nullable=False, index=True)  # debug, info, warning, error
    module = Column(String(100), nullable=False, index=True)  # 模块名
    message = Column(Text, nullable=False)  # 日志消息
    filename = Column(String(200))  # 文件名
    line_number = Column(Integer)  # 行号
    function_name = Column(String(100))  # 函数名
    extra_data = Column(Text)  # 额外数据，JSON格式
    created_at = Column(DateTime, default=datetime.now, nullable=False)

    # 创建复合索引以提高查询性能
    __table_args__ = (
        Index('idx_timestamp_level', 'timestamp', 'level'),
        Index('idx_module_level', 'module', 'level'),
        Index('idx_created_at', 'created_at'),
    )

    def to_dict(self):
        """转换为字典格式"""
        extra_data = None
        if self.extra_data is not None:
            try:
                extra_data = json.loads(str(self.extra_data))
            except (json.JSONDecodeError, TypeError):
                extra_data = None

        return {
            'id': self.id,
            'timestamp': self.timestamp.isoformat() if self.timestamp is not None else None,
            'level': self.level,
            'module': self.module,
            'message': self.message,
            'filename': self.filename,
            'line_number': self.line_number,
            'function_name': self.function_name,
            'extra_data': extra_data,
            'created_at': self.created_at.isoformat() if self.created_at is not None else None
        }

    @classmethod
    def from_log_record(cls, record):
        """从日志记录创建实例"""
        extra_data = {}

        # 提取额外信息
        if hasattr(record, 'extra'):
            extra_data.update(record.extra)

        # 添加进程和线程信息
        extra_data.update({
            'process_id': record.process,
            'thread_id': record.thread,
            'thread_name': record.threadName if hasattr(record, 'threadName') else None
        })

        return cls(
            timestamp=datetime.now(),  # 使用服务器本地时间而不是record.created
            level=record.levelname.lower(),
            module=record.name,
            message=record.getMessage(),
            filename=record.filename,
            line_number=record.lineno,
            function_name=record.funcName,
            extra_data=json.dumps(extra_data, ensure_ascii=False) if extra_data else None
        )

    def __repr__(self):
        return f"<SystemLog(id={self.id}, level={self.level}, module={self.module}, timestamp={self.timestamp})>"


class CardDetectionResult(Base):
    """卡料检测结果表"""
    __tablename__ = "card_detection_results"

    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 关联信息
    detection_group_id = Column(Integer, ForeignKey("detection_groups.id", ondelete="CASCADE"), nullable=False, index=True)
    
    # 检测结果核心字段
    timestamp = Column(DateTime, nullable=False, index=True)  # 检测时间戳
    is_normal = Column(Boolean, nullable=False, index=True)  # 是否正常（True=正常，False=卡料）
    detection_time = Column(Float, nullable=False)  # 检测耗时（秒）
    
    # ROI相关字段
    undetected_rois = Column(JSON, nullable=True)  # 未检测到的ROI列表，JSON格式
    detected_rois = Column(JSON, nullable=True)  # 已检测到的ROI列表，JSON格式
    trigger_roi_id = Column(String(255), nullable=True)  # 触发检测的ROI ID
    
    # 详细信息
    result_details = Column(JSON, nullable=True)  # 详细检测信息，JSON格式
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, nullable=False, index=True)
    
    # 关联关系
    detection_group = relationship("DetectionGroup")
    
    # 创建复合索引以提高查询性能
    __table_args__ = (
        Index('idx_detection_group_timestamp', 'detection_group_id', 'timestamp'),
        Index('idx_detection_group_is_normal', 'detection_group_id', 'is_normal'),
        Index('idx_timestamp_is_normal', 'timestamp', 'is_normal'),
        Index('idx_created_at_detection_group', 'created_at', 'detection_group_id'),
    )

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'detection_group_id': self.detection_group_id,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'is_normal': self.is_normal,
            'detection_time': self.detection_time,
            'undetected_rois': self.undetected_rois,
            'detected_rois': self.detected_rois,
            'trigger_roi_id': self.trigger_roi_id,
            'result_details': self.result_details,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    @classmethod
    def from_detection_data(cls, detection_group_id, detection_result, result_details=None):
        """从检测数据创建实例"""
        # 处理时间戳 - 统一使用本地时间
        timestamp_value = detection_result['timestamp']
        if isinstance(timestamp_value, str):
            # 如果是字符串，直接解析为本地时间（不进行时区转换）
            timestamp_value = datetime.fromisoformat(timestamp_value.replace('Z', ''))

        return cls(
            detection_group_id=detection_group_id,
            timestamp=timestamp_value,
            is_normal=detection_result['isNormal'],
            detection_time=detection_result['detectionTime'],
            undetected_rois=detection_result.get('undetectedROIs', []),
            detected_rois=detection_result.get('detectedROIs', []),
            trigger_roi_id=detection_result.get('triggerROIId'),
            result_details=result_details
        )

    def __repr__(self):
        return f"<CardDetectionResult(id={self.id}, detection_group_id={self.detection_group_id}, is_normal={self.is_normal}, timestamp={self.timestamp})>"
<template>
  <div class="roi-management-section">
    <div class="roi-list-panel">
      <div class="panel-header">
        <h4>ROI区域列表 ({{ roiCount }})</h4>
        <div class="panel-status">
          <span v-if="!selectedAttribute" class="status-ready">请选择ROI属性</span>
          <span v-else-if="!isDrawingEnabled" class="status-locked">绘制已暂停</span>
          <span v-else class="status-drawing">多边形绘制模式 - {{ getAttributeDisplayName(selectedAttribute) }}</span>
        </div>
      </div>

      <div class="panel-content">
        <!-- ROI树状列表 -->
        <div class="roi-tree">
          <div v-if="roiCount === 0" class="empty-message">
            <div class="empty-icon">·</div>
            <div class="empty-text">
              <p>暂无ROI区域</p>
              <small v-if="!selectedAttribute">请选择ROI属性后开始绘制</small>
              <small v-else>请在视频画面上绘制ROI区域</small>
            </div>
          </div>

          <!-- 排料口ROI组 -->
          <div v-if="pailiaoROIs.length > 0" class="roi-group">
            <div class="group-header">
              <span class="group-icon">📦</span>
              <span class="group-title">排料口 ({{ pailiaoROIs.length }})</span>
              <button @click="$emit('clear-attribute', 'pailiao')" class="group-clear-btn" title="清空排料口ROI">清空</button>
            </div>
            <div class="group-content">
              <div 
                v-for="roi in pailiaoROIs" 
                :key="roi.id" 
                class="roi-item" 
                :class="{ 'highlighted': highlightedROIId === roi.id }"
              >
                <div class="roi-info" @click="$emit('highlight', roi.id)" title="点击在视频中高亮显示">
                  <div class="roi-color" :style="{ backgroundColor: roi.color }"></div>
                  <div class="roi-details">
                    <span class="roi-name">{{ roi.name }}</span>
                    <span class="roi-points">{{ roi.points?.length || 0 }} 个点</span>
                  </div>
                </div>
                <div class="roi-actions">
                  <button @click="$emit('edit', roi.id)" class="edit-btn" title="编辑ROI">编辑</button>
                  <button @click="$emit('delete', roi.id)" class="delete-btn" title="删除ROI">删除</button>
                </div>
              </div>
            </div>
          </div>

          <!-- 压铸机ROI组 -->
          <div v-if="yazhuROIs.length > 0" class="roi-group">
            <div class="group-header">
              <span class="group-icon">🏭</span>
              <span class="group-title">压铸机 ({{ yazhuROIs.length }})</span>
              <button @click="$emit('clear-attribute', 'yazhu')" class="group-clear-btn" title="清空压铸机ROI">清空</button>
            </div>
            <div class="group-content">
              <div 
                v-for="roi in yazhuROIs" 
                :key="roi.id" 
                class="roi-item" 
                :class="{ 'highlighted': highlightedROIId === roi.id }"
              >
                <div class="roi-info" @click="$emit('highlight', roi.id)" title="点击在视频中高亮显示">
                  <div class="roi-color" :style="{ backgroundColor: roi.color }"></div>
                  <div class="roi-details">
                    <span class="roi-name">{{ roi.name }}</span>
                    <span class="roi-points">{{ roi.points?.length || 0 }} 个点</span>
                  </div>
                </div>
                <div class="roi-actions">
                  <button @click="$emit('edit', roi.id)" class="edit-btn" title="编辑ROI">编辑</button>
                  <button @click="$emit('delete', roi.id)" class="delete-btn" title="删除ROI">删除</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 使用说明 -->
        <div class="help-section">
          <div class="help-header">
            <span>使用说明</span>
          </div>
          <div class="help-content">
            <ol>
              <li>选择ROI属性（排料口/压铸机）</li>
              <li>在视频画面上绘制多边形ROI</li>
              <li>左键点击添加顶点，右键完成绘制</li>
              <li>可连续绘制多个ROI区域</li>
              <li>完成后可暂停绘制或继续添加</li>
              <li>保存按钮将ROI数据保存到后端</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
// defineProps 和 defineEmits 在 Vue 3 中是编译器宏，无需导入

const props = defineProps({
  roiList: {
    type: Array as () => any[],
    default: () => []
  },
  selectedAttribute: {
    type: String as () => 'pailiao' | 'yazhu' | null,
    default: null
  },
  isDrawingEnabled: {
    type: Boolean,
    default: false
  },
  highlightedROIId: {
    type: String,
    default: null
  }
})

// 确保highlighedRoiId和highlightedROIId兼容性
console.log('ROI管理组件初始化，highlightedROIId状态:', props.highlightedROIId)

// 计算ROI数量
const roiCount = computed(() => props.roiList.length)

// 按属性分组ROI
const pailiaoROIs = computed(() => 
  props.roiList.filter((roi: any) => roi.attribute === 'pailiao')
)

const yazhuROIs = computed(() => 
  props.roiList.filter((roi: any) => roi.attribute === 'yazhu')
)

// ROI属性显示名称
const getAttributeDisplayName = (attribute: string | null) => {
  if (!attribute) return ''
  return attribute === 'pailiao' ? '排料口' : '压铸机'
}

const emit = defineEmits(['edit', 'delete', 'highlight', 'clear-attribute'])
</script>
#!/usr/bin/env python3
"""
创建测试用的活跃检测组
"""

import requests
import json
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8000/api"

def get_detection_groups():
    """获取所有检测组"""
    try:
        response = requests.get(f"{BASE_URL}/detection-groups/", timeout=10)
        if response.status_code == 200:
            return response.json()
        return []
    except Exception as e:
        print(f"获取检测组失败: {e}")
        return []

def update_group_status(group_id, status):
    """更新检测组状态"""
    try:
        data = {"status": status}
        response = requests.put(f"{BASE_URL}/detection-groups/{group_id}", json=data, timeout=10)
        return response.status_code == 200
    except Exception as e:
        print(f"更新检测组状态失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 创建测试用的活跃检测组...")
    
    # 获取所有检测组
    groups = get_detection_groups()
    if not groups:
        print("❌ 没有找到检测组")
        return
    
    print(f"找到 {len(groups)} 个检测组")
    
    # 找到有模板关联的检测组
    groups_with_template = [g for g in groups if g.get('template_id')]
    
    if not groups_with_template:
        print("❌ 没有找到关联模板的检测组")
        return
    
    print(f"找到 {len(groups_with_template)} 个关联模板的检测组")
    
    # 激活前2个检测组作为测试
    activated_count = 0
    for group in groups_with_template[:2]:
        group_id = group['id']
        group_name = group['name']
        template_id = group.get('template_id')
        
        print(f"正在激活检测组: {group_name} (ID: {group_id}, 模板ID: {template_id})")
        
        if update_group_status(group_id, "active"):
            print(f"✅ 成功激活检测组: {group_name}")
            activated_count += 1
        else:
            print(f"❌ 激活检测组失败: {group_name}")
    
    print(f"\n🎉 成功激活 {activated_count} 个检测组")
    
    # 验证结果
    print("\n验证运行模板...")
    try:
        response = requests.get(f"{BASE_URL}/detection-templates/running/current", timeout=10)
        if response.status_code == 200:
            templates = response.json()
            print(f"✅ 当前运行模板数量: {len(templates)}")
            for template in templates:
                print(f"  - {template['template_name']} (活跃组数: {template['active_groups_count']})")
        else:
            print("❌ 验证失败")
    except Exception as e:
        print(f"❌ 验证异常: {e}")

if __name__ == "__main__":
    main()

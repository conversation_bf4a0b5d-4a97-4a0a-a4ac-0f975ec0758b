#!/usr/bin/env python3
import sqlite3

def add_color_field():
    try:
        conn = sqlite3.connect('die_casting_detection.db')
        cursor = conn.cursor()
        
        # 检查color字段是否已存在
        cursor.execute("PRAGMA table_info(roi_configs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'color' in columns:
            print("✅ color字段已存在")
            conn.close()
            return
        
        print("🔧 添加color字段...")
        
        # 添加color字段
        cursor.execute("ALTER TABLE roi_configs ADD COLUMN color VARCHAR(20) DEFAULT '#ff0000'")
        
        # 为现有记录设置默认颜色
        cursor.execute("""
            UPDATE roi_configs 
            SET color = CASE 
                WHEN attribute = 'yazhu' THEN '#ff0000'
                WHEN attribute = 'pailiao' THEN '#00ff00'
                ELSE '#ff0000'
            END
        """)
        
        conn.commit()
        print("✅ color字段添加成功")
        
        # 验证
        cursor.execute("SELECT roi_id, name, attribute, color FROM roi_configs LIMIT 3")
        records = cursor.fetchall()
        print("示例记录:")
        for record in records:
            print(f"  {record}")
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    add_color_field()

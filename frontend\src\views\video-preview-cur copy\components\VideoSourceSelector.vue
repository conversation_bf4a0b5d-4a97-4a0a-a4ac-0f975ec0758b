<template>
  <div class="video-source-section">
    <div class="source-controls">
      <label>选择视频流（自动预览）:</label>
      <select 
        :value="selectedSource" 
        @change="onSelectChange"
        class="source-select"
        :disabled="isConnecting || isPreviewActive"
      >
        <option value="">请选择视频源</option>
        <option 
          v-for="source in sources" 
          :key="source.id" 
          :value="source.id"
        >
          {{ source.name }}
        </option>
      </select>
      <button 
        @click="$emit('refresh')" 
        class="refresh-btn"
        :disabled="isConnecting"
      >
        刷新
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// defineProps 和 defineEmits 在 Vue 3 中是编译器宏，无需导入

const props = defineProps({
  sources: {
    type: Array as () => any[],
    default: () => []
  },
  selectedSource: {
    type: String,
    default: ''
  },
  isConnecting: {
    type: Boolean,
    default: false
  },
  isPreviewActive: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['source-change', 'refresh'])

const onSelectChange = (event: Event) => {
  const value = (event.target as HTMLSelectElement).value
  emit('source-change', value)
}
</script>
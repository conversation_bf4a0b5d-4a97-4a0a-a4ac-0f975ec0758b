from typing import Any, Dict, List, Optional, cast
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from fastapi.responses import FileResponse
from sqlalchemy import text, inspect, Row
from sqlalchemy.orm import Session
from sqlalchemy.engine import Inspector, Result, Connection
import sqlite3
import os
import shutil
import json
from datetime import datetime
from pathlib import Path
import tempfile
import csv
import io

from app.db.session import get_db
from app.api.deps import get_current_active_superuser
from app.models.models import User

router = APIRouter()


@router.get("/tables", response_model=List[Dict[str, Any]])
def get_tables(
    current_user: User = Depends(get_current_active_superuser),
    db: Session = Depends(get_db),
) -> List[Dict[str, Any]]:
    """
    获取所有数据库表信息
    """
    # 使用SQLAlchemy inspector获取表信息
    inspector = cast(Inspector, inspect(db.bind))
    tables_info = []
    
    # 获取所有表名
    table_names = inspector.get_table_names()
    
    for table_name in table_names:
        # 获取表的行数
        result = db.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
        row_count = result.scalar()
        
        # 表的描述信息 - 通过获取列信息来提供基本描述
        columns = inspector.get_columns(table_name)
        column_names = [col['name'] for col in columns]
        
        tables_info.append({
            "name": table_name,
            "rows": row_count,
            "description": f"包含 {len(columns)} 列: {', '.join(column_names[:5])}{'...' if len(column_names) > 5 else ''}"
        })
    
    return tables_info


@router.get("/table-data/{table_name}")
def get_table_data(
    table_name: str,
    page: int = 1,
    page_size: int = 10,
    current_user: User = Depends(get_current_active_superuser),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    获取指定表的数据
    """
    try:
        # 获取表的列信息
        inspector = cast(Inspector, inspect(db.bind))
        if table_name not in inspector.get_table_names():
            raise HTTPException(status_code=404, detail=f"表 {table_name} 不存在")
            
        columns = inspector.get_columns(table_name)
        column_names = [col['name'] for col in columns]
        
        # 计算总行数
        result = db.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
        total_rows = result.scalar()
        
        # 分页查询数据
        offset = (page - 1) * page_size
        query = text(f"SELECT * FROM {table_name} LIMIT :limit OFFSET :offset")
        result = db.execute(query, {"limit": page_size, "offset": offset})
        
        # 构建返回数据
        rows = []
        for row in result:
            row_dict = {}
            for idx, col_name in enumerate(column_names):
                row_dict[col_name] = row[idx] if idx < len(row) else None
            rows.append(row_dict)
        
        return {
            "columns": column_names,
            "data": rows,
            "total": total_rows,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询表数据失败: {str(e)}")


@router.post("/query")
def execute_query(
    query_data: Dict[str, str],
    current_user: User = Depends(get_current_active_superuser),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    执行SQL查询
    """
    query_str = query_data.get("query")
    if not query_str:
        raise HTTPException(status_code=400, detail="查询语句不能为空")
    
    # 安全检查，允许SELECT和DELETE语句
    query_lower = query_str.lower().strip()
    allowed_operations = ["select", "delete"]
    if not any(query_lower.startswith(op) for op in allowed_operations):
        raise HTTPException(status_code=403, detail="只允许执行SELECT和DELETE查询")
    
    try:
        result = db.execute(text(query_str))
        
        # 对于DELETE操作，提交事务并返回影响的行数
        if query_lower.startswith("delete"):
            db.commit()
            return {
                "columns": [],
                "data": [],
                "rows_affected": result.rowcount,
                "message": f"成功删除 {result.rowcount} 行数据"
            }
        
        # 对于SELECT操作，获取列名和数据
        columns = []
        if hasattr(result, 'keys'):
            columns = result.keys()
        
        # 构建返回数据
        rows = []
        for row in result:
            row_dict = {}
            for idx, col_name in enumerate(columns):
                row_dict[col_name] = row[idx] if idx < len(row) else None
            rows.append(row_dict)
        
        return {
            "columns": columns,
            "data": rows,
            "rows_affected": len(rows)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行查询失败: {str(e)}")


@router.post("/backup")
def create_backup(
    current_user: User = Depends(get_current_active_superuser),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    创建数据库备份
    """
    try:
        # 获取数据库文件路径
        db_url = str(db.bind.url)
        if "sqlite" not in db_url:
            raise HTTPException(status_code=400, detail="仅支持SQLite数据库备份")
        
        # 提取数据库文件路径
        db_path = db_url.replace("sqlite:///", "")
        if not os.path.exists(db_path):
            raise HTTPException(status_code=404, detail="数据库文件不存在")
        
        # 创建备份目录
        backup_dir = Path("backups")
        backup_dir.mkdir(exist_ok=True)
        
        # 生成备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"database_backup_{timestamp}.db"
        backup_path = backup_dir / backup_filename
        
        # 执行备份
        shutil.copy2(db_path, backup_path)
        
        # 获取备份文件信息
        backup_size = os.path.getsize(backup_path)
        
        return {
            "success": True,
            "backup_file": backup_filename,
            "backup_path": str(backup_path),
            "backup_size": backup_size,
            "created_at": timestamp
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建备份失败: {str(e)}")


@router.get("/backups")
def list_backups(
    current_user: User = Depends(get_current_active_superuser),
) -> List[Dict[str, Any]]:
    """
    获取备份文件列表
    """
    try:
        backup_dir = Path("backups")
        if not backup_dir.exists():
            return []
        
        backups = []
        for backup_file in backup_dir.glob("*.db"):
            stat = backup_file.stat()
            backups.append({
                "filename": backup_file.name,
                "size": stat.st_size,
                "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat()
            })
        
        # 按创建时间倒序排列
        backups.sort(key=lambda x: x["created_at"], reverse=True)
        return backups
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取备份列表失败: {str(e)}")


@router.post("/restore/{backup_filename}")
def restore_backup(
    backup_filename: str,
    current_user: User = Depends(get_current_active_superuser),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    从备份恢复数据库
    """
    try:
        # 验证备份文件
        backup_path = Path("backups") / backup_filename
        if not backup_path.exists():
            raise HTTPException(status_code=404, detail="备份文件不存在")
        
        # 获取当前数据库路径
        db_url = str(db.bind.url)
        if "sqlite" not in db_url:
            raise HTTPException(status_code=400, detail="仅支持SQLite数据库恢复")
        
        db_path = db_url.replace("sqlite:///", "")
        
        # 创建当前数据库的备份
        current_backup = f"database_before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        current_backup_path = Path("backups") / current_backup
        shutil.copy2(db_path, current_backup_path)
        
        # 关闭当前数据库连接
        db.close()
        
        # 恢复备份
        shutil.copy2(backup_path, db_path)
        
        return {
            "success": True,
            "restored_from": backup_filename,
            "current_backup": current_backup,
            "message": "数据库恢复成功，请重启应用以生效"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"恢复备份失败: {str(e)}")


@router.delete("/backups/{backup_filename}")
def delete_backup(
    backup_filename: str,
    current_user: User = Depends(get_current_active_superuser),
) -> Dict[str, Any]:
    """
    删除备份文件
    """
    try:
        backup_path = Path("backups") / backup_filename
        if not backup_path.exists():
            raise HTTPException(status_code=404, detail="备份文件不存在")
        
        backup_path.unlink()
        
        return {
            "success": True,
            "message": f"备份文件 {backup_filename} 已删除"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除备份失败: {str(e)}")


@router.get("/export/{table_name}")
def export_table_data(
    table_name: str,
    format: str = "csv",
    current_user: User = Depends(get_current_active_superuser),
    db: Session = Depends(get_db),
) -> FileResponse:
    """
    导出表数据为CSV或JSON格式
    """
    try:
        # 验证表是否存在
        inspector = cast(Inspector, inspect(db.bind))
        if table_name not in inspector.get_table_names():
            raise HTTPException(status_code=404, detail=f"表 {table_name} 不存在")
        
        # 查询所有数据
        result = db.execute(text(f"SELECT * FROM {table_name}"))
        columns = result.keys()
        rows = result.fetchall()
        
        # 创建临时文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format.lower() == "csv":
            filename = f"{table_name}_export_{timestamp}.csv"
            temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8')
            
            writer = csv.writer(temp_file)
            writer.writerow(columns)
            for row in rows:
                writer.writerow(row)
            
            temp_file.close()
            
            return FileResponse(
                path=temp_file.name,
                filename=filename,
                media_type='text/csv'
            )
        
        elif format.lower() == "json":
            filename = f"{table_name}_export_{timestamp}.json"
            temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json', encoding='utf-8')
            
            data = []
            for row in rows:
                row_dict = {}
                for idx, col_name in enumerate(columns):
                    row_dict[col_name] = row[idx]
                data.append(row_dict)
            
            json.dump(data, temp_file, ensure_ascii=False, indent=2, default=str)
            temp_file.close()
            
            return FileResponse(
                path=temp_file.name,
                filename=filename,
                media_type='application/json'
            )
        
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式，仅支持csv和json")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出数据失败: {str(e)}")


@router.post("/import/{table_name}")
def import_table_data(
    table_name: str,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_superuser),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    导入数据到指定表
    """
    try:
        # 验证表是否存在
        inspector = cast(Inspector, inspect(db.bind))
        if table_name not in inspector.get_table_names():
            raise HTTPException(status_code=404, detail=f"表 {table_name} 不存在")
        
        # 获取表结构
        columns = inspector.get_columns(table_name)
        column_names = [col['name'] for col in columns]
        
        # 读取文件内容
        content = file.file.read()
        
        imported_rows = 0
        errors = []
        
        if file.filename.endswith('.csv'):
            # 处理CSV文件
            csv_content = content.decode('utf-8')
            csv_reader = csv.DictReader(io.StringIO(csv_content))
            
            for row_num, row in enumerate(csv_reader, 1):
                try:
                    # 构建插入语句
                    valid_data = {k: v for k, v in row.items() if k in column_names}
                    if valid_data:
                        placeholders = ', '.join([f':{k}' for k in valid_data.keys()])
                        columns_str = ', '.join(valid_data.keys())
                        query = text(f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})")
                        db.execute(query, valid_data)
                        imported_rows += 1
                except Exception as e:
                    errors.append(f"第{row_num}行: {str(e)}")
        
        elif file.filename.endswith('.json'):
            # 处理JSON文件
            json_data = json.loads(content.decode('utf-8'))
            
            if not isinstance(json_data, list):
                raise HTTPException(status_code=400, detail="JSON文件必须包含数组格式的数据")
            
            for row_num, row in enumerate(json_data, 1):
                try:
                    # 构建插入语句
                    valid_data = {k: v for k, v in row.items() if k in column_names}
                    if valid_data:
                        placeholders = ', '.join([f':{k}' for k in valid_data.keys()])
                        columns_str = ', '.join(valid_data.keys())
                        query = text(f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})")
                        db.execute(query, valid_data)
                        imported_rows += 1
                except Exception as e:
                    errors.append(f"第{row_num}行: {str(e)}")
        
        else:
            raise HTTPException(status_code=400, detail="不支持的文件格式，仅支持CSV和JSON")
        
        # 提交事务
        db.commit()
        
        return {
            "success": True,
            "imported_rows": imported_rows,
            "errors": errors,
            "message": f"成功导入 {imported_rows} 行数据"
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"导入数据失败: {str(e)}")


@router.get("/maintenance/info")
def get_database_info(
    current_user: User = Depends(get_current_active_superuser),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    获取数据库维护信息
    """
    try:
        # 获取数据库文件信息
        db_url = str(db.bind.url)
        db_path = db_url.replace("sqlite:///", "")
        
        db_info = {}
        if os.path.exists(db_path):
            stat = os.stat(db_path)
            db_info = {
                "file_path": db_path,
                "file_size": stat.st_size,
                "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat()
            }
        
        # 获取表统计信息
        inspector = cast(Inspector, inspect(db.bind))
        table_stats = []
        total_rows = 0
        
        for table_name in inspector.get_table_names():
            result = db.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            row_count = result.scalar()
            total_rows += row_count
            
            table_stats.append({
                "table_name": table_name,
                "row_count": row_count
            })
        
        # 获取数据库版本信息
        version_result = db.execute(text("SELECT sqlite_version()"))
        sqlite_version = version_result.scalar()
        
        return {
            "database_info": db_info,
            "sqlite_version": sqlite_version,
            "total_tables": len(table_stats),
            "total_rows": total_rows,
            "table_statistics": table_stats
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库信息失败: {str(e)}")


@router.post("/maintenance/vacuum")
def vacuum_database(
    current_user: User = Depends(get_current_active_superuser),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    执行数据库VACUUM操作，优化数据库性能
    """
    try:
        # 获取VACUUM前的数据库大小
        db_url = str(db.bind.url)
        db_path = db_url.replace("sqlite:///", "")
        size_before = os.path.getsize(db_path) if os.path.exists(db_path) else 0
        
        # 执行VACUUM
        db.execute(text("VACUUM"))
        db.commit()
        
        # 获取VACUUM后的数据库大小
        size_after = os.path.getsize(db_path) if os.path.exists(db_path) else 0
        space_saved = size_before - size_after
        
        return {
            "success": True,
            "size_before": size_before,
            "size_after": size_after,
            "space_saved": space_saved,
            "message": "数据库优化完成"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库优化失败: {str(e)}")


@router.post("/maintenance/analyze")
def analyze_database(
    current_user: User = Depends(get_current_active_superuser),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    执行数据库ANALYZE操作，更新查询优化器统计信息
    """
    try:
        # 执行ANALYZE
        db.execute(text("ANALYZE"))
        db.commit()
        
        return {
            "success": True,
            "message": "数据库统计信息已更新"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新统计信息失败: {str(e)}")


@router.get("/maintenance/integrity-check")
def check_database_integrity(
    current_user: User = Depends(get_current_active_superuser),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    检查数据库完整性
    """
    try:
        # 执行完整性检查
        result = db.execute(text("PRAGMA integrity_check"))
        check_results = [row[0] for row in result.fetchall()]
        
        is_ok = len(check_results) == 1 and check_results[0] == "ok"
        
        return {
            "success": True,
            "is_healthy": is_ok,
            "check_results": check_results,
            "message": "数据库完整性检查完成"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"完整性检查失败: {str(e)}")
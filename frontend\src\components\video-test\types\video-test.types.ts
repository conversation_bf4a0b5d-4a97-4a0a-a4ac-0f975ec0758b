// 设备配置接口
export interface DeviceConfig {
  ip: string
  port: number
  username: string
  password: string
  channel: number
  streamType: string
}

// 设备信息接口
export interface DeviceInfo {
  deviceName: string
  deviceModel: string
  firmwareVersion: string
  channelNum: number
}

// 操作日志接口
export interface OperationLog {
  time: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
}

// WebSDK登录参数接口
export interface LoginParams {
  szIP: string
  iPort: number
  szUserName: string
  szPassword: string
}

// WebSDK预览参数接口
export interface PreviewParams {
  iWndIndex: number
  szIP: string
  iChannelID: number
  iStreamType: number
  bZeroChannel: boolean
}

// WebSDK初始化配置接口
export interface WebSDKInitConfig {
  bWndFull: boolean
  iPackageType: number
  iWndowType: number
  bNoPlugin: boolean
  cbSelWnd?: (xmlDoc: any) => void
  cbDoubleClickWnd?: (xmlDoc: any) => void
  cbEvent?: (xmlDoc: any) => void
  cbRemoteConfig?: () => void
  cbInitPluginComplete?: () => void
}

// 抓图格式枚举
export enum CaptureFormat {
  JPG = 'JPG',
  BMP = 'BMP'
}

// 抓图参数接口
export interface CaptureParams {
  format: CaptureFormat
  quality?: number // JPG质量 1-100
  filename?: string
}

// 抓图结果接口
export interface CaptureResult {
  success: boolean
  data?: string // Base64数据
  filename?: string
  size?: number
  error?: string
}

// 窗口分割模式枚举
export enum WindowSplitMode {
  SINGLE = 1,
  FOUR = 4,
  NINE = 9,
  SIXTEEN = 16
}

// 窗口信息接口
export interface WindowInfo {
  index: number
  isActive: boolean
  isPlaying: boolean
  deviceIP?: string
  channel?: number
}

// PTZ控制方向枚举
export enum PTZDirection {
  UP = 'UP',
  DOWN = 'DOWN',
  LEFT = 'LEFT',
  RIGHT = 'RIGHT',
  UP_LEFT = 'UP_LEFT',
  UP_RIGHT = 'UP_RIGHT',
  DOWN_LEFT = 'DOWN_LEFT',
  DOWN_RIGHT = 'DOWN_RIGHT',
  ZOOM_IN = 'ZOOM_IN',
  ZOOM_OUT = 'ZOOM_OUT',
  FOCUS_NEAR = 'FOCUS_NEAR',
  FOCUS_FAR = 'FOCUS_FAR',
  IRIS_OPEN = 'IRIS_OPEN',
  IRIS_CLOSE = 'IRIS_CLOSE'
}

// PTZ控制参数接口
export interface PTZControlParams {
  direction: PTZDirection
  speed: number // 1-7
  isStart: boolean // true开始，false停止
}

// 预置点接口
export interface PresetPoint {
  id: number
  name: string
  description?: string
}

// 录像参数接口
export interface RecordParams {
  filename?: string
  duration?: number // 录像时长（秒）
  format?: 'mp4' | 'avi'
}

// 录像状态接口
export interface RecordStatus {
  isRecording: boolean
  filename?: string
  startTime?: Date
  duration?: number
  fileSize?: number
}

// 音频控制参数接口
export interface AudioControlParams {
  volume: number // 0-100
  enabled: boolean
}

// 视频参数接口
export interface VideoParams {
  brightness: number // 0-255
  contrast: number // 0-255
  saturation: number // 0-255
  hue: number // 0-255
}

// 网络状态接口
export interface NetworkStatus {
  isConnected: boolean
  latency?: number // 延迟（毫秒）
  bandwidth?: number // 带宽（kbps）
  packetLoss?: number // 丢包率（%）
}

// 错误信息接口
export interface ErrorInfo {
  code: number
  message: string
  timestamp: Date
  context?: any
}

// 统计信息接口
export interface Statistics {
  frameRate: number // 帧率
  bitRate: number // 码率（kbps）
  resolution: string // 分辨率
  codecType: string // 编码类型
  connectionTime: number // 连接时长（秒）
}

// 事件类型枚举
export enum EventType {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILED = 'LOGIN_FAILED',
  PREVIEW_START = 'PREVIEW_START',
  PREVIEW_STOP = 'PREVIEW_STOP',
  CAPTURE_SUCCESS = 'CAPTURE_SUCCESS',
  CAPTURE_FAILED = 'CAPTURE_FAILED',
  RECORD_START = 'RECORD_START',
  RECORD_STOP = 'RECORD_STOP',
  NETWORK_ERROR = 'NETWORK_ERROR',
  DEVICE_DISCONNECT = 'DEVICE_DISCONNECT'
}

// 事件数据接口
export interface EventData {
  type: EventType
  timestamp: Date
  data?: any
  message?: string
}

// 抓图历史记录接口
export interface CaptureHistory {
  id: string
  timestamp: Date
  filename: string
  format: CaptureFormat
  size: number
  quality?: number
  deviceIP: string
  channel: number
  success: boolean
  error?: string
}

// 抓图统计信息接口
export interface CaptureStatistics {
  total: number
  success: number
  failed: number
  successRate: number
  totalSize: number
  averageSize: number
}

// 综合测试模式枚举
export enum TestMode {
  QUICK = 'quick',
  STANDARD = 'standard',
  COMPREHENSIVE = 'comprehensive',
  CUSTOM = 'custom'
}

// 测试项目枚举
export enum TestItem {
  LOGIN = 'login',
  PREVIEW = 'preview',
  CAPTURE = 'capture',
  PTZ = 'ptz',
  RECORD = 'record',
  AUDIO = 'audio'
}

// 综合测试配置接口
export interface ComprehensiveTestConfig {
  mode: TestMode
  rounds: number
  interval: number // 间隔时间（秒）
  testItems: TestItem[]
  devices: DeviceConfig[]
  captureConfig?: CaptureParams
  recordConfig?: RecordParams
}

// 测试进度接口
export interface TestProgress {
  current: number
  total: number
  percentage: number
  currentItem?: TestItem
  currentDevice?: string
}

// 测试结果接口
export interface TestResult {
  id: string
  testItem: TestItem
  deviceIP: string
  startTime: Date
  endTime: Date
  duration: number // 耗时（毫秒）
  success: boolean
  error?: string
  details?: any
}

// 综合测试统计接口
export interface ComprehensiveTestStatistics {
  total: number
  success: number
  failed: number
  successRate: number
  averageDuration: number // 平均耗时（毫秒）
  totalDuration: number // 总耗时（毫秒）
  itemStatistics: Record<TestItem, {
    total: number
    success: number
    failed: number
    successRate: number
    averageDuration: number
  }>
}

// 测试状态枚举
export enum TestStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  STOPPED = 'stopped',
  ERROR = 'error'
}
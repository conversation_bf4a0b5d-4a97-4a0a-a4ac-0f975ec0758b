from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.models import DetectionTemplate, TemplateDieCaster, DieCaster, DetectionGroup
from app.schemas.detection_template import (
    DetectionTemplateCreate,
    DetectionTemplateUpdate,
    DetectionTemplate as DetectionTemplateSchema,
    DetectionTemplateWithDieCasters,
    BatchAssociateDieCasters,
    TemplateDieCasterCreate
)

router = APIRouter()


@router.get("/", response_model=List[DetectionTemplateSchema])
def read_detection_templates(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取所有检测模板
    """
    templates = db.query(DetectionTemplate).offset(skip).limit(limit).all()
    return templates


@router.post("/", response_model=DetectionTemplateSchema)
def create_detection_template(
    *,
    db: Session = Depends(deps.get_db),
    template_in: DetectionTemplateCreate,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新检测模板
    """
    # 检查模板名称是否已存在
    existing_template = db.query(DetectionTemplate).filter(DetectionTemplate.name == template_in.name).first()
    if existing_template:
        raise HTTPException(status_code=400, detail="模板名称已存在")
    
    template = DetectionTemplate(**template_in.dict())
    db.add(template)
    db.commit()
    db.refresh(template)
    return template


@router.get("/{template_id}", response_model=DetectionTemplateWithDieCasters)
@router.get("/{template_id}/", response_model=DetectionTemplateWithDieCasters)
def read_detection_template(
    *,
    db: Session = Depends(deps.get_db),
    template_id: int,
) -> Any:
    """
    获取特定检测模板及其关联的压铸机
    """
    template = db.query(DetectionTemplate).filter(DetectionTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="检测模板不存在")
    
    # 获取关联的压铸机ID列表
    die_caster_ids = db.query(TemplateDieCaster.die_caster_id).filter(
        TemplateDieCaster.template_id == template_id
    ).all()
    die_caster_ids = [item[0] for item in die_caster_ids]
    
    # 构造响应数据
    template_dict = {
        "id": template.id,
        "name": template.name,
        "description": template.description,
        "status": template.status,
        "created_at": template.created_at,
        "updated_at": template.updated_at,
        "die_caster_ids": die_caster_ids
    }
    
    return template_dict


@router.put("/{template_id}", response_model=DetectionTemplateSchema)
@router.put("/{template_id}/", response_model=DetectionTemplateSchema)
def update_detection_template(
    *,
    db: Session = Depends(deps.get_db),
    template_id: int,
    template_in: DetectionTemplateUpdate,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新检测模板
    """
    template = db.query(DetectionTemplate).filter(DetectionTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="检测模板不存在")
    
    # 检查名称是否与其他模板冲突
    if template_in.name and template_in.name != template.name:
        existing_template = db.query(DetectionTemplate).filter(
            DetectionTemplate.name == template_in.name,
            DetectionTemplate.id != template_id
        ).first()
        if existing_template:
            raise HTTPException(status_code=400, detail="模板名称已存在")
    
    update_data = template_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(template, field, value)
    
    db.commit()
    db.refresh(template)
    return template


@router.delete("/{template_id}", response_model=DetectionTemplateSchema)
@router.delete("/{template_id}/", response_model=DetectionTemplateSchema)
def delete_detection_template(
    *,
    db: Session = Depends(deps.get_db),
    template_id: int,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除检测模板
    """
    template = db.query(DetectionTemplate).filter(DetectionTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="检测模板不存在")
    
    db.delete(template)
    db.commit()
    return template


@router.post("/{template_id}/die-casters", response_model=DetectionTemplateWithDieCasters)
@router.post("/{template_id}/die-casters/", response_model=DetectionTemplateWithDieCasters)
def associate_die_casters(
    *,
    db: Session = Depends(deps.get_db),
    template_id: int,
    die_caster_data: BatchAssociateDieCasters,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    批量关联压铸机到检测模板
    """
    template = db.query(DetectionTemplate).filter(DetectionTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="检测模板不存在")
    
    # 验证压铸机是否存在
    existing_die_casters = db.query(DieCaster.id).filter(
        DieCaster.id.in_(die_caster_data.die_caster_ids)
    ).all()
    existing_ids = [item[0] for item in existing_die_casters]
    
    invalid_ids = set(die_caster_data.die_caster_ids) - set(existing_ids)
    if invalid_ids:
        raise HTTPException(status_code=400, detail=f"压铸机ID不存在: {list(invalid_ids)}")
    
    # 删除现有关联
    db.query(TemplateDieCaster).filter(TemplateDieCaster.template_id == template_id).delete()
    
    # 创建新关联
    for die_caster_id in die_caster_data.die_caster_ids:
        association = TemplateDieCaster(
            template_id=template_id,
            die_caster_id=die_caster_id
        )
        db.add(association)
    
    db.commit()
    
    # 返回更新后的模板信息
    return read_detection_template(db=db, template_id=template_id)


@router.delete("/{template_id}/die-casters/{die_caster_id}")
@router.delete("/{template_id}/die-casters/{die_caster_id}/")
def remove_die_caster_association(
    *,
    db: Session = Depends(deps.get_db),
    template_id: int,
    die_caster_id: int,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    移除模板与压铸机的关联
    """
    association = db.query(TemplateDieCaster).filter(
        TemplateDieCaster.template_id == template_id,
        TemplateDieCaster.die_caster_id == die_caster_id
    ).first()
    
    if not association:
        raise HTTPException(status_code=404, detail="关联不存在")
    
    db.delete(association)
    db.commit()
    
    return {"message": "关联已删除"}


@router.get("/{template_id}/available-die-casters", response_model=List[dict])
@router.get("/{template_id}/available-die-casters/", response_model=List[dict])
def get_available_die_casters(
    *,
    db: Session = Depends(deps.get_db),
    template_id: int,
    current_user: Any = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取可关联到模板的压铸机列表（未关联的压铸机）
    """
    # 获取已关联的压铸机ID
    associated_ids = db.query(TemplateDieCaster.die_caster_id).filter(
        TemplateDieCaster.template_id == template_id
    ).all()
    associated_ids = [item[0] for item in associated_ids]
    
    # 获取未关联的压铸机
    available_die_casters = db.query(DieCaster).filter(
        ~DieCaster.id.in_(associated_ids)
    ).all()
    
    return [{
        "id": dc.id,
        "name": dc.name,
        "description": dc.description,
        "status": dc.status
    } for dc in available_die_casters]


@router.get("/running/current", response_model=List[dict])
def get_current_running_templates(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取当前运行的监测模板名称
    """
    # 查询状态为active的检测组及其关联的模板
    active_groups = db.query(DetectionGroup, DetectionTemplate).join(
        DetectionTemplate, DetectionGroup.template_id == DetectionTemplate.id
    ).filter(DetectionGroup.status == "active").all()
    
    # 构造返回数据
    running_templates = []
    template_names = set()  # 用于去重
    
    for group, template in active_groups:
        if template.name not in template_names:
            running_templates.append({
                "template_id": template.id,
                "template_name": template.name,
                "description": template.description,
                "active_groups_count": len([g for g, t in active_groups if t.id == template.id])
            })
            template_names.add(template.name)
    
    return running_templates
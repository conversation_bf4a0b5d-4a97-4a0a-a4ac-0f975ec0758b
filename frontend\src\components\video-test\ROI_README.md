# ROI配置页面使用说明

## 概述

ROI（Region of Interest）配置页面基于现有的VideoLoginPreviewTest.vue开发，支持在视频画面上绘制和管理感兴趣区域，用于压铸机和排料口的检测配置。

## 功能特性

### 1. 设备连接
- 支持设备IP、端口、用户名、密码配置
- 支持HTTP/HTTPS协议选择
- 支持多通道和码流类型选择
- 实时显示连接状态

### 2. ROI绘制功能
- **矩形绘制**：点击两个点完成矩形ROI绘制
- **多边形绘制**：点击多个点绘制多边形，右键或按Enter完成绘制
- **实时预览**：绘制过程中实时显示临时ROI
- **键盘快捷键**：
  - `Enter`：完成绘制
  - `Esc`：取消绘制
  - `Backspace`：撤销最后一个点（多边形）

### 3. ROI类型配置
- **压铸机（yazhu）**：红色显示 (#ff4444)
- **排料口（pailiao）**：青色显示 (#00cccc)
- 支持实时切换ROI类型

### 4. ROI管理功能
- **列表显示**：显示所有已配置的ROI
- **选择高亮**：点击ROI列表项或画面中的ROI进行选择
- **编辑功能**：支持修改ROI名称、类型、检测参数
- **删除功能**：支持删除不需要的ROI
- **统计信息**：显示ROI总数、各类型数量

### 5. 数据管理
- **导出功能**：将ROI配置导出为JSON文件
- **导入功能**：从JSON文件导入ROI配置
- **数据验证**：确保ROI数据的完整性和正确性

## 数据结构

### ROI数据字典
```typescript
interface ROIData {
  id: string              // 唯一标识符（UUID）
  name: string           // ROI名称（类型_ID前4位）
  roi_type: 'yazhu' | 'pailiao'  // ROI类型（'yazhu'压铸 或 'pailiao'排料）
  shape: 'Rectangle' | 'Polygon'  // 形状类型（'Rectangle'矩形、'Polygon'多边形）
  coordinates: number[][]         // 坐标点列表
  params: ROIParams              // 检测参数配置
}

interface ROIParams {
  threshold?: number     // 检测阈值
  sensitivity?: number   // 敏感度
  [key: string]: any    // 其他参数
}
```

## 使用步骤

### 1. 设备连接
1. 填写设备IP地址、端口、用户名、密码
2. 选择协议类型（HTTP/HTTPS）
3. 设置通道号和码流类型
4. 点击"登录设备"按钮

### 2. 开始预览
1. 设备连接成功后，点击"开始预览"按钮
2. 等待视频画面显示

### 3. 配置ROI
1. 选择ROI类型（压铸机/排料口）
2. 选择绘制形状（矩形/多边形）
3. 在视频画面上点击绘制ROI
4. 完成绘制后ROI会自动添加到列表

### 4. 管理ROI
1. 在ROI列表中查看所有配置的ROI
2. 点击"编辑"按钮修改ROI属性
3. 点击"删除"按钮移除不需要的ROI
4. 使用"导出"/"导入"功能管理ROI配置

## 文件结构

```
frontend/src/components/video-test/
├── VideoROIConfigTest.vue          # ROI配置页面主组件
├── composables/
│   └── useROI.ts                   # ROI管理逻辑
├── styles/
│   └── video-roi-config.css        # ROI配置页面样式
└── ROI_README.md                   # 使用说明文档
```

## 技术特点

1. **响应式设计**：支持不同屏幕尺寸的自适应显示
2. **TypeScript支持**：完整的类型定义和类型检查
3. **组合式API**：使用Vue 3 Composition API开发
4. **模块化设计**：ROI逻辑独立封装在useROI composable中
5. **用户体验优化**：支持键盘快捷键、实时预览、数据验证等

## 注意事项

1. 确保设备网络连接正常
2. ROI坐标基于视频分辨率，建议在稳定的视频画面上进行配置
3. 多边形ROI至少需要3个点
4. ROI名称不能重复
5. 导入ROI数据时请确保JSON格式正确

## 扩展功能

可以根据需要扩展以下功能：
- ROI模板保存和加载
- 批量ROI操作
- ROI区域统计分析
- 与后端API的数据同步
- ROI配置的版本管理

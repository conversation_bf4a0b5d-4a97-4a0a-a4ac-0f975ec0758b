/* 窗口分割测试页面样式 */
.window-split-test {
  padding: 20px;
  background-color: var(--bg-color);
  min-height: 100vh;
}

/* 配置区域 */
.config-section {
  margin-bottom: 20px;
}

.config-card {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-color-mute);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-card .el-card__header {
  background-color: var(--bg-color-soft);
  border-bottom: 1px solid var(--border-color);
  padding: 16px 20px;
}

.config-card .el-card__body {
  padding: 20px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--text-color);
}

.split-controls {
  display: flex;
  gap: 8px;
}

.split-controls .el-button {
  border-color: var(--border-color);
  color: var(--text-color);
  background-color: var(--bg-color);
  transition: all 0.3s ease;
}

.split-controls .el-button:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background-color: var(--bg-color-soft);
}

.split-controls .el-button.el-button--primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

/* 窗口操作按钮 */
.window-actions {
  display: flex;
  gap: 8px;
}

.window-actions .el-button {
  border-color: var(--border-color);
  transition: all 0.3s ease;
}

.window-actions .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 分割显示区域 */
.split-section {
  margin-bottom: 20px;
}

.split-card {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-color-mute);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.split-card .el-card__header {
  background-color: var(--bg-color-soft);
  border-bottom: 1px solid var(--border-color);
  padding: 16px 20px;
}

.split-card .el-card__body {
  padding: 20px;
}

.split-actions {
  display: flex;
  gap: 8px;
}

.split-actions .el-button {
  border-color: var(--border-color);
  transition: all 0.3s ease;
}

.split-actions .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 分割容器 */
.split-container {
  display: grid;
  gap: 10px;
  background-color: var(--bg-color);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 10px;
  min-height: 400px;
}

/* 不同分割模式的网格布局 */
.split-container.split-1 {
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
}

.split-container.split-4 {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.split-container.split-9 {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
}

.split-container.split-16 {
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
}

/* 分割窗口 */
.split-window {
  border: 2px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--bg-color-mute);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  min-height: 150px;
  position: relative;
  overflow: hidden;
}

.split-window:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.split-window.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.split-window.playing {
  border-color: var(--success-color);
  background-color: var(--bg-color);
}

.split-window.playing.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 窗口头部 */
.window-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--bg-color-soft);
  border-bottom: 1px solid var(--border-color);
  font-size: 12px;
}

.window-title {
  font-weight: 600;
  color: var(--text-color);
}

.window-status .el-tag {
  font-size: 10px;
  height: 20px;
  line-height: 18px;
}

/* 窗口内容 */
.window-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.window-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-color-soft);
  text-align: center;
  padding: 20px;
}

.window-placeholder p {
  margin: 8px 0 0 0;
  font-size: 12px;
}

.window-player {
  width: 100%;
  height: 100%;
  background-color: #000;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.mock-video {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #1a1a1a 25%, transparent 25%), 
              linear-gradient(-45deg, #1a1a1a 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #1a1a1a 75%), 
              linear-gradient(-45deg, transparent 75%, #1a1a1a 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: mockVideoPlay 2s linear infinite;
}

@keyframes mockVideoPlay {
  0% { background-position: 0 0, 0 10px, 10px -10px, -10px 0px; }
  100% { background-position: 20px 20px, 20px 30px, 30px 10px, 10px 20px; }
}

.video-info {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  border-radius: 4px;
  font-size: 11px;
  line-height: 1.4;
}

.video-info p {
  margin: 2px 0;
}

/* 窗口控制按钮 */
.window-controls {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 4px 8px;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.split-window:hover .window-controls {
  opacity: 1;
}

.window-controls .el-button {
  font-size: 10px;
  padding: 4px 8px;
  height: auto;
  min-height: auto;
}

/* 信息区域 */
.info-section {
  margin-top: 20px;
}

.info-card,
.log-card {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-color-mute);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 400px;
}

.info-card .el-card__header,
.log-card .el-card__header {
  background-color: var(--bg-color-soft);
  border-bottom: 1px solid var(--border-color);
  padding: 16px 20px;
}

.info-card .el-card__body,
.log-card .el-card__body {
  padding: 20px;
  height: calc(100% - 60px);
  overflow: hidden;
}

/* 窗口信息列表 */
.window-info-list {
  height: 100%;
  overflow-y: auto;
}

.window-info-item {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  background-color: var(--bg-color);
  transition: all 0.3s ease;
}

.window-info-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.window-info-item.active {
  border-color: var(--primary-color);
  background-color: var(--bg-color-soft);
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.window-name {
  font-weight: 600;
  color: var(--text-color);
  font-size: 14px;
}

.info-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-color-soft);
}

.info-item .label {
  font-weight: 500;
  color: var(--text-color);
}

.info-empty {
  color: var(--text-color-soft);
  font-size: 12px;
  text-align: center;
  padding: 8px 0;
}

/* 日志容器 */
.log-container {
  height: 100%;
  overflow-y: auto;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
}

.no-logs {
  text-align: center;
  color: var(--text-color-soft);
  padding: 40px 20px;
  font-size: 14px;
}

.log-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color-light);
  font-size: 12px;
  line-height: 1.4;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: var(--text-color-soft);
  font-family: 'Courier New', monospace;
  white-space: nowrap;
  min-width: 80px;
}

.log-message {
  color: var(--text-color);
  flex: 1;
  word-break: break-word;
}

/* 不同类型的日志颜色 */
.log-item.success .log-message {
  color: var(--success-color);
}

.log-item.warning .log-message {
  color: var(--warning-color);
}

.log-item.error .log-message {
  color: var(--error-color);
}

.log-item.info .log-message {
  color: var(--text-color);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .split-container.split-16 {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }
  
  .split-container.split-9 {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .window-split-test {
    padding: 10px;
  }
  
  .split-container {
    min-height: 300px;
    gap: 8px;
    padding: 8px;
  }
  
  .split-container.split-16,
  .split-container.split-9,
  .split-container.split-4 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: repeat(2, 1fr);
  }
  
  .split-window {
    min-height: 120px;
  }
  
  .window-header {
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .video-info {
    padding: 6px;
    font-size: 10px;
  }
  
  .info-card,
  .log-card {
    height: 300px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .split-controls,
  .split-actions {
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .split-controls .el-button,
  .split-actions .el-button {
    font-size: 12px;
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  .split-container.split-16,
  .split-container.split-9,
  .split-container.split-4 {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(2, 1fr);
  }
  
  .window-info-item {
    padding: 8px;
  }
  
  .info-item {
    font-size: 11px;
  }
  
  .log-item {
    font-size: 11px;
  }
  
  .log-time {
    min-width: 70px;
    font-size: 10px;
  }
}

/* 动画效果 */
.split-window {
  animation: windowFadeIn 0.3s ease-out;
}

@keyframes windowFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 平滑过渡效果 */
.split-container,
.split-window,
.window-info-item,
.log-item {
  transition: all 0.3s ease;
}

.el-button {
  transition: all 0.3s ease;
}

.el-card {
  transition: box-shadow 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}
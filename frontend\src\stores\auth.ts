import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/api/users'
import { login as loginApi, getCurrentUser } from '@/api/auth'

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  user: User
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const user = ref<User | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isSuperuser = computed(() => user.value?.is_superuser === true)

  // 登录
  const login = async (credentials: LoginRequest): Promise<void> => {
    try {
      isLoading.value = true
      
      // 调用实际的登录API
      const response = await loginApi(credentials)
      
      // 保存token
      token.value = response.access_token
      localStorage.setItem('token', response.access_token)
      
      // 等待一个微任务，确保token更新被应用
      await new Promise(resolve => setTimeout(resolve, 0))
      
      // 获取用户信息
      const userInfo = await getCurrentUser()
      user.value = userInfo
      localStorage.setItem('user', JSON.stringify(userInfo))
      
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = (): void => {
    token.value = null
    user.value = null
    
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 初始化用户信息
  const initializeAuth = (): void => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (savedToken && savedUser) {
      token.value = savedToken
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  // 更新用户信息
  const updateUser = (newUser: User): void => {
    user.value = newUser
    localStorage.setItem('user', JSON.stringify(newUser))
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (!user.value) return false
    
    // 超级管理员拥有所有权限
    if (user.value.is_superuser) return true
    
    // 管理员权限检查
    if (user.value.role === 'admin') {
      return ['read', 'write', 'delete', 'manage_users', 'manage_settings'].includes(permission)
    }
    
    // 普通用户权限检查
    if (user.value.role === 'user') {
      return ['read'].includes(permission)
    }
    
    return false
  }

  // 初始化
  initializeAuth()

  return {
    // 状态
    token,
    user,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    isSuperuser,
    
    // 方法
    login,
    logout,
    updateUser,
    hasPermission,
    initializeAuth
  }
})
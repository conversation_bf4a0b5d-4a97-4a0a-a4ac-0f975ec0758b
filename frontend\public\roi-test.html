<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>ROI绘制测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .video-container { position: relative; width: 640px; height: 360px; background: #000; margin: 20px auto; border: 2px solid #ddd; }
        .video-placeholder { width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; }
        .roi-canvas { position: absolute; top: 0; left: 0; z-index: 10; cursor: crosshair; }
        .toolbar { display: flex; gap: 10px; margin: 20px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; }
        .btn { padding: 8px 16px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer; }
        .btn.active { background: #007bff; color: white; }
        .btn:hover { background: #e9ecef; }
        .btn.active:hover { background: #0056b3; }
        .roi-list { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        .roi-item { display: flex; justify-content: space-between; align-items: center; padding: 8px; margin: 5px 0; background: white; border-radius: 3px; }
        .roi-color { width: 16px; height: 16px; border-radius: 50%; margin-right: 8px; }
        .delete-btn { padding: 4px 8px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 ROI绘制功能测试</h1>
        <p>测试在视频区域上方进行ROI绘制的功能</p>
        
        <!-- 工具栏 -->
        <div class="toolbar">
            <button id="toggleDraw" class="btn">✏️ 启用绘制</button>
            <button id="rectMode" class="btn">📐 矩形</button>
            <button id="polygonMode" class="btn">🔷 多边形</button>
            <button id="clearAll" class="btn">🗑️ 清空</button>
            <button id="saveROIs" class="btn">💾 保存</button>
        </div>
        
        <!-- 视频容器 -->
        <div class="video-container" id="videoContainer">
            <div class="video-placeholder">
                📹 模拟视频画面<br>
                <small>在此区域上方可以绘制ROI</small>
            </div>
            <canvas id="roiCanvas" class="roi-canvas" width="640" height="360"></canvas>
        </div>
        
        <!-- ROI列表 -->
        <div class="roi-list">
            <h4>ROI区域列表 (<span id="roiCount">0</span>)</h4>
            <div id="roiListContainer">
                <p style="color: #666; text-align: center;">暂无ROI区域</p>
            </div>
        </div>
        
        <!-- 使用说明 -->
        <div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 5px; border-left: 4px solid #007bff;">
            <h4>📖 使用说明</h4>
            <ol>
                <li>点击"✏️ 启用绘制"按钮开启绘制模式</li>
                <li>选择"📐 矩形"或"🔷 多边形"绘制工具</li>
                <li><strong>矩形绘制</strong>：在视频区域内按住鼠标拖拽</li>
                <li><strong>多边形绘制</strong>：点击设置顶点，双击完成绘制</li>
                <li>绘制完成后可以查看ROI列表</li>
            </ol>
        </div>
    </div>

    <script>
        // ROI绘制功能
        class ROIDrawer {
            constructor() {
                this.canvas = document.getElementById('roiCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.isDrawingEnabled = false;
                this.drawMode = null; // 'rectangle' | 'polygon'
                this.isDrawing = false;
                this.rois = [];
                this.currentROI = null;
                this.startPoint = null;
                this.polygonPoints = [];
                this.colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
                this.colorIndex = 0;
                
                this.initEvents();
                this.updateUI();
            }
            
            initEvents() {
                // 工具栏事件
                document.getElementById('toggleDraw').addEventListener('click', () => this.toggleDrawing());
                document.getElementById('rectMode').addEventListener('click', () => this.setDrawMode('rectangle'));
                document.getElementById('polygonMode').addEventListener('click', () => this.setDrawMode('polygon'));
                document.getElementById('clearAll').addEventListener('click', () => this.clearAll());
                document.getElementById('saveROIs').addEventListener('click', () => this.saveROIs());
                
                // 画布事件
                this.canvas.addEventListener('mousedown', (e) => this.onMouseDown(e));
                this.canvas.addEventListener('mousemove', (e) => this.onMouseMove(e));
                this.canvas.addEventListener('mouseup', (e) => this.onMouseUp(e));
                this.canvas.addEventListener('dblclick', (e) => this.onDoubleClick(e));
            }
            
            toggleDrawing() {
                this.isDrawingEnabled = !this.isDrawingEnabled;
                const btn = document.getElementById('toggleDraw');
                
                if (this.isDrawingEnabled) {
                    btn.textContent = '🔒 锁定绘制';
                    btn.classList.add('active');
                    this.canvas.style.pointerEvents = 'auto';
                } else {
                    btn.textContent = '✏️ 启用绘制';
                    btn.classList.remove('active');
                    this.canvas.style.pointerEvents = 'none';
                    this.drawMode = null;
                    this.updateToolbar();
                }
            }
            
            setDrawMode(mode) {
                if (!this.isDrawingEnabled) {
                    alert('请先启用绘制模式');
                    return;
                }
                
                this.drawMode = mode;
                this.isDrawing = false;
                this.currentROI = null;
                this.startPoint = null;
                this.polygonPoints = [];
                this.updateToolbar();
                this.redraw();
            }
            
            updateToolbar() {
                document.getElementById('rectMode').classList.toggle('active', this.drawMode === 'rectangle');
                document.getElementById('polygonMode').classList.toggle('active', this.drawMode === 'polygon');
            }
            
            getMousePos(event) {
                const rect = this.canvas.getBoundingClientRect();
                return {
                    x: event.clientX - rect.left,
                    y: event.clientY - rect.top
                };
            }
            
            onMouseDown(event) {
                if (!this.isDrawingEnabled || !this.drawMode) return;
                
                event.preventDefault();
                const pos = this.getMousePos(event);
                
                if (this.drawMode === 'rectangle') {
                    this.isDrawing = true;
                    this.startPoint = pos;
                    this.currentROI = {
                        id: Date.now().toString(),
                        name: `矩形ROI ${this.rois.length + 1}`,
                        type: 'rectangle',
                        points: [pos],
                        color: this.colors[this.colorIndex % this.colors.length]
                    };
                    this.colorIndex++;
                } else if (this.drawMode === 'polygon') {
                    if (!this.isDrawing) {
                        this.isDrawing = true;
                        this.polygonPoints = [pos];
                        this.currentROI = {
                            id: Date.now().toString(),
                            name: `多边形ROI ${this.rois.length + 1}`,
                            type: 'polygon',
                            points: [pos],
                            color: this.colors[this.colorIndex % this.colors.length]
                        };
                        this.colorIndex++;
                    } else {
                        this.polygonPoints.push(pos);
                        this.currentROI.points = [...this.polygonPoints];
                    }
                }
                
                this.redraw();
            }
            
            onMouseMove(event) {
                if (!this.isDrawing || !this.currentROI) return;
                
                const pos = this.getMousePos(event);
                
                if (this.drawMode === 'rectangle' && this.startPoint) {
                    this.currentROI.points = [this.startPoint, pos];
                    this.redraw();
                } else if (this.drawMode === 'polygon') {
                    this.redraw();
                    this.drawPreviewLine(pos);
                }
            }
            
            onMouseUp(event) {
                if (this.drawMode === 'rectangle' && this.isDrawing && this.currentROI) {
                    this.rois.push(this.currentROI);
                    this.isDrawing = false;
                    this.currentROI = null;
                    this.startPoint = null;
                    this.updateUI();
                }
            }
            
            onDoubleClick(event) {
                if (this.drawMode === 'polygon' && this.isDrawing && this.currentROI) {
                    if (this.polygonPoints.length >= 3) {
                        this.rois.push(this.currentROI);
                        this.updateUI();
                    }
                    
                    this.isDrawing = false;
                    this.currentROI = null;
                    this.polygonPoints = [];
                    this.redraw();
                }
            }
            
            redraw() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 绘制已保存的ROI
                this.rois.forEach(roi => this.drawROI(roi));
                
                // 绘制当前正在绘制的ROI
                if (this.currentROI) {
                    this.drawROI(this.currentROI, true);
                }
            }
            
            drawROI(roi, isPreview = false) {
                this.ctx.strokeStyle = roi.color;
                this.ctx.fillStyle = roi.color + '20';
                this.ctx.lineWidth = 2;
                
                if (roi.type === 'rectangle' && roi.points.length >= 2) {
                    const [start, end] = roi.points;
                    const width = end.x - start.x;
                    const height = end.y - start.y;
                    
                    this.ctx.strokeRect(start.x, start.y, width, height);
                    if (!isPreview) {
                        this.ctx.fillRect(start.x, start.y, width, height);
                    }
                } else if (roi.type === 'polygon' && roi.points.length >= 2) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(roi.points[0].x, roi.points[0].y);
                    
                    for (let i = 1; i < roi.points.length; i++) {
                        this.ctx.lineTo(roi.points[i].x, roi.points[i].y);
                    }
                    
                    if (!isPreview && roi.points.length >= 3) {
                        this.ctx.closePath();
                        this.ctx.fill();
                    }
                    
                    this.ctx.stroke();
                }
                
                // 绘制标签
                if (!isPreview && roi.points.length > 0) {
                    this.ctx.fillStyle = roi.color;
                    this.ctx.font = '12px Arial';
                    this.ctx.fillText(roi.name, roi.points[0].x, roi.points[0].y - 5);
                }
            }
            
            drawPreviewLine(currentPos) {
                if (!this.currentROI || this.polygonPoints.length === 0) return;
                
                this.ctx.strokeStyle = this.currentROI.color;
                this.ctx.lineWidth = 1;
                this.ctx.setLineDash([5, 5]);
                
                const lastPoint = this.polygonPoints[this.polygonPoints.length - 1];
                this.ctx.beginPath();
                this.ctx.moveTo(lastPoint.x, lastPoint.y);
                this.ctx.lineTo(currentPos.x, currentPos.y);
                this.ctx.stroke();
                
                this.ctx.setLineDash([]);
            }
            
            clearAll() {
                this.rois = [];
                this.isDrawing = false;
                this.currentROI = null;
                this.polygonPoints = [];
                this.redraw();
                this.updateUI();
            }
            
            deleteROI(index) {
                this.rois.splice(index, 1);
                this.redraw();
                this.updateUI();
            }
            
            saveROIs() {
                console.log('保存ROI配置:', this.rois);
                alert(`已保存 ${this.rois.length} 个ROI区域配置`);
            }
            
            updateUI() {
                const count = document.getElementById('roiCount');
                const container = document.getElementById('roiListContainer');
                
                count.textContent = this.rois.length;
                
                if (this.rois.length === 0) {
                    container.innerHTML = '<p style="color: #666; text-align: center;">暂无ROI区域</p>';
                } else {
                    container.innerHTML = this.rois.map((roi, index) => `
                        <div class="roi-item">
                            <div style="display: flex; align-items: center;">
                                <div class="roi-color" style="background-color: ${roi.color}"></div>
                                <span><strong>${roi.name}</strong> (${roi.roi_type === 'rectangle' ? '矩形' : '多边形'})</span>
                            </div>
                            <button class="delete-btn" onclick="roiDrawer.deleteROI(${index})">删除</button>
                        </div>
                    `).join('');
                }
            }
        }
        
        // 初始化ROI绘制器
        const roiDrawer = new ROIDrawer();
    </script>
</body>
</html>

import uuid
import copy

class ROI:
    def __init__(self, roi_type, shape, coordinates):
        self.id = str(uuid.uuid4())
        self.name = f"{roi_type}_{self.id[:4]}"
        self.roi_type = roi_type  # 'yazhu' or 'pailiao'
        self.shape = shape      # 'Rectangle', 'Polygon'
        self.coordinates = coordinates
        self.params = ROI.get_default_params(roi_type)

    def get_scaled_roi(self, scale_x, scale_y):
        """
        Returns a new ROI instance with coordinates scaled by the given factors.
        This is used for processing on a resized frame.
        """
        if scale_x == 1.0 and scale_y == 1.0:
            return self

        scaled_roi = copy.deepcopy(self)

        # Scale coordinates
        scaled_coords = []
        for point in self.coordinates:
            scaled_x = int(point[0] / scale_x)
            scaled_y = int(point[1] / scale_y)
            scaled_coords.append((scaled_x, scaled_y))

        scaled_roi.coordinates = scaled_coords
        return scaled_roi

    @staticmethod
    def get_default_params(roi_type):
        if roi_type == 'yazhu':
            return {
                "前置背景检测": {
                    "enabled": True,
                    "backgroundUpdateRate": 0.01,
                    "motionThreshold": 50,
                    "minArea": 500
                },
                "后置方向检测": {
                    "consecutiveDetectionThreshold": 3,
                    "minDisplacement": 2,
                    "maxPatience": 3
                }
            }
        elif roi_type == 'pailiao':
            return {
                "运动检测": {
                    "algorithm": "帧差法",
                    "learningRate": 0.01,
                    "detectionThreshold": 50,
                    "shadowRemoval": 0.5,
                    "threshold": 30,
                    "frameInterval": 2,
                    "minArea": 300
                }
            }
        return {}

    def to_dict(self):
        """将ROI对象序列化为字典。"""
        return {
            'id': self.id,
            'name': self.name,
            'roi_type': self.roi_type,
            'shape': self.shape,
            'coordinates': self.coordinates,
            'params': self.params
        }

    @classmethod
    def from_dict(cls, data):
        """从字典反序列化创建ROI对象。"""
        # 使用字典中的数据创建一个新的ROI实例
        roi_instance = cls(
            roi_type=data.get('roi_type', 'yazhu'),
            shape=data.get('shape', 'Rectangle'),
            coordinates=data.get('coordinates', [])
        )
        # 手动设置其他属性以确保一致性
        roi_instance.id = data.get('id', roi_instance.id)
        roi_instance.name = data.get('name', roi_instance.name)
        # 合并默认参数和加载的参数，以确保兼容旧配置
        default_params = cls.get_default_params(roi_instance.roi_type)
        loaded_params = data.get('params', {})

        # 深层合并
        merged_params = copy.deepcopy(default_params)
        for group, params in loaded_params.items():
            if group in merged_params:
                merged_params[group].update(params)
            else:
                merged_params[group] = params

        roi_instance.params = merged_params

        return roi_instance

    def __repr__(self):
        return f"ROI(id={self.id}, name='{self.name}', type='{self.roi_type}')"

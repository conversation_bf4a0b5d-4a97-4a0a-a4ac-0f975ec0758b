/* 算法调试日志组件样式 */
.algorithm-debug-log {
  border: 1px solid var(--el-border-color-light, #e4e7ed);
  border-radius: 8px;
  margin-top: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color, #ffffff);
  transition: all 0.3s;
}

.algorithm-debug-log .log-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-light, #e4e7ed);
  background-color: var(--el-bg-color-overlay, #f5f7fa);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s;
}

.algorithm-debug-log .log-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary, #303133);
}

.algorithm-debug-log .header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.algorithm-debug-log .log-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
  background-color: var(--el-bg-color, #ffffff);
  height: 350px;
  transition: all 0.3s;
}

.algorithm-debug-log .filter-bar {
  padding: 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--el-border-color-extra-light, #ebeef5);
  margin-bottom: 12px;
  padding-bottom: 12px;
}

.algorithm-debug-log .log-item {
  margin-bottom: 12px;
  border-radius: 4px;
  padding: 12px;
  border-left: 4px solid var(--el-color-info, #909399);
  background-color: var(--el-bg-color-overlay, #f7f8fa);
  position: relative;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.algorithm-debug-log .log-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.algorithm-debug-log .log-item.motion {
  border-left-color: var(--el-color-primary, #409EFF);
}

.algorithm-debug-log .log-item.direction {
  border-left-color: var(--el-color-success, #67C23A);
}

.algorithm-debug-log .log-timestamp {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--el-text-color-secondary, #909399);
  margin-bottom: 6px;
}

.algorithm-debug-log .log-roi-info {
  margin-bottom: 8px;
}

.algorithm-debug-log .roi-id {
  font-weight: bold;
  margin-right: 6px;
  color: var(--el-text-color-primary, #303133);
}

.algorithm-debug-log .roi-attribute {
  color: var(--el-text-color-secondary, #909399);
  background-color: var(--el-bg-color-overlay, #f0f2f5);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.algorithm-debug-log .log-algorithm {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.algorithm-debug-log .disabled-tag {
  background-color: var(--el-color-danger, #f56c6c);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 3px;
}

.algorithm-debug-log .enabled-tag {
  background-color: var(--el-color-success, #67C23A);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 3px;
}

.algorithm-debug-log .log-params, 
.algorithm-debug-log .log-results {
  margin-top: 8px;
}

.algorithm-debug-log .section-title {
  font-size: 12px;
  color: var(--el-text-color-secondary, #606266);
  margin-bottom: 4px;
  font-weight: 600;
}

.algorithm-debug-log pre {
  margin: 0;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  background-color: var(--el-bg-color-overlay, #f0f0f0);
  padding: 8px;
  border-radius: 4px;
  max-height: 150px;
  overflow-y: auto;
  color: var(--el-text-color-primary, #303133);
  border: 1px solid var(--el-border-color-lighter, #ebeef5);
}

.algorithm-debug-log .no-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: var(--el-text-color-secondary, #909399);
  font-size: 14px;
}

.algorithm-debug-log .logs-container {
  padding-bottom: 16px;
}

/* 暗色主题适配 */
.dark .algorithm-debug-log {
  border-color: var(--el-border-color-light, #4c4d4f);
  background-color: var(--el-bg-color, #1d1e1f);
}

.dark .algorithm-debug-log .log-header {
  background-color: var(--el-bg-color-overlay, #2d2e2f);
  border-bottom-color: var(--el-border-color-light, #4c4d4f);
}

.dark .algorithm-debug-log .log-header h3 {
  color: var(--el-text-color-primary, #e0e0e0);
}

.dark .algorithm-debug-log .log-content {
  background-color: var(--el-bg-color, #1d1e1f);
}

.dark .algorithm-debug-log .filter-bar {
  border-bottom-color: var(--el-border-color-extra-light, #4c4d4f);
}

.dark .algorithm-debug-log .log-item {
  background-color: var(--el-bg-color-overlay, #2d2e2f);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark .algorithm-debug-log .log-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.dark .algorithm-debug-log .roi-id {
  color: var(--el-text-color-primary, #e0e0e0);
}

.dark .algorithm-debug-log .roi-attribute {
  background-color: var(--el-bg-color-overlay, #3d3e3f);
}

.dark .algorithm-debug-log pre {
  background-color: var(--el-bg-color-overlay, #2d2e2f);
  border-color: var(--el-border-color-lighter, #4c4d4f);
  color: var(--el-text-color-primary, #e0e0e0);
} 
<template>
  <el-dialog
    v-model="visible"
    title="执行确认"
    width="500px"
    :before-close="handleClose"
  >
    <div class="execute-confirm-content">
      <div class="icon-wrapper">
        <el-icon :size="48" color="#409EFF">
          <Warning />
        </el-icon>
      </div>
      <div class="content">
        <h3>确认执行预设计划</h3>
        <p v-if="schedule">您确定要执行计划 <strong>"{{ schedule.name }}"</strong> 吗？</p>
        <div v-if="schedule" class="schedule-info">
          <div class="info-item">
            <span class="label">执行日期：</span>
            <span class="value">{{ schedule.date }}</span>
          </div>
          <div class="info-item">
            <span class="label">执行时间：</span>
            <span class="value">{{ schedule.startTime }} - {{ schedule.endTime }}</span>
          </div>
          <div class="info-item">
            <span class="label">检测模板：</span>
            <span class="value">{{ schedule.templateName }}</span>
          </div>
        </div>
        <p class="warning-text">执行后将开始检测任务，请确认相关设备已准备就绪。</p>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :loading="loading"
        >
          确认执行
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import type { PresetSchedule } from '@/types/preset-schedule'

interface Props {
  modelValue: boolean
  schedule?: PresetSchedule | null
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', scheduleId: number): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  schedule: null,
  loading: false
})

const emit = defineEmits<Emits>()

const visible = ref(props.modelValue)

// 监听外部传入的显示状态
watch(
  () => props.modelValue,
  (newValue) => {
    visible.value = newValue
  }
)

// 监听内部显示状态变化
watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理关闭
const handleClose = () => {
  visible.value = false
  emit('cancel')
}

// 处理确认
const handleConfirm = () => {
  if (props.schedule) {
    emit('confirm', props.schedule.id)
  }
}
</script>

<style scoped>
.execute-confirm-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px 0;
}

.icon-wrapper {
  flex-shrink: 0;
}

.content {
  flex: 1;
}

.content h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.content p {
  margin: 0 0 16px 0;
  color: #606266;
  line-height: 1.5;
}

.schedule-info {
  background: #f5f7fa;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  width: 80px;
  color: #909399;
  font-size: 14px;
}

.value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.warning-text {
  color: #e6a23c;
  font-size: 14px;
  margin: 16px 0 0 0 !important;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
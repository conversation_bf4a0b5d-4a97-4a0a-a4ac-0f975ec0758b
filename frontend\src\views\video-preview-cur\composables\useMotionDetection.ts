import { ref } from 'vue'
import { ElMessage } from 'element-plus'

/**
 * 运动检测功能Hook
 * 负责处理视频运动检测相关功能
 * @param selectedVideoSource 当前选择的视频源ID
 * @param videoSources 视频源列表
 * @param addOperationInfo 操作日志记录函数
 */
export function useMotionDetection(
  selectedVideoSource: any,
  videoSources: any,
  addOperationInfo: (message: string) => void
) {
  // 运动检测相关
  const showMotionDetection = ref(false)
  const detectionResult = ref<any>(null)
  let motionDetectionWS: WebSocket | null = null

  /**
   * 切换运动检测状态
   */
  const toggleMotionDetection = () => {
    if (showMotionDetection.value) {
      stopMotionDetection()
    } else {
      startMotionDetection()
    }
  }

  /**
   * 启动运动检测
   */
  const startMotionDetection = async () => {
    if (!selectedVideoSource.value) {
      ElMessage.warning('请先选择视频源')
      return
    }

    try {
      addOperationInfo('[MOTION] 启动运动检测...')

      // 获取当前视频源信息
      const source = videoSources.value.find((s: any) => s.id === selectedVideoSource.value)
      if (!source) {
        ElMessage.error('未找到视频源信息')
        return
      }

      // 构建RTSP URL
      const rtspUrl = `rtsp://${source.username}:${source.password}@${source.ip}:${source.rtspPort || 554}/Streaming/Channels/101`

      // 启动后端运动检测
      const response = await fetch('/api/rtsp/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stream_id: `motion_${source.id}`,
          url: rtspUrl,
          buffer_size: 3,
          timeout: 5000,
          enable_multi_thread: true
        })
      })

      if (response.ok) {
        // 连接WebSocket接收检测结果
        connectMotionDetectionWS()
        showMotionDetection.value = true
        addOperationInfo('[MOTION] 运动检测已启动')
        ElMessage.success('运动检测已启动')
      } else {
        throw new Error('启动运动检测失败')
      }
    } catch (error) {
      console.error('启动运动检测失败:', error)
      addOperationInfo('[ERROR] 启动运动检测失败')
      ElMessage.error('启动运动检测失败')
    }
  }

  /**
   * 停止运动检测
   */
  const stopMotionDetection = async () => {
    try {
      addOperationInfo('[MOTION] 停止运动检测...')

      // 断开WebSocket连接
      if (motionDetectionWS) {
        motionDetectionWS.close()
        motionDetectionWS = null
      }

      // 停止后端运动检测
      const source = videoSources.value.find((s: any) => s.id === selectedVideoSource.value)
      if (source) {
        await fetch(`/api/rtsp/disconnect/${source.id}`, {
          method: 'POST'
        })
      }

      showMotionDetection.value = false
      detectionResult.value = null
      addOperationInfo('[MOTION] 运动检测已停止')
      ElMessage.success('运动检测已停止')
    } catch (error) {
      console.error('停止运动检测失败:', error)
      addOperationInfo('[ERROR] 停止运动检测失败')
    }
  }

  /**
   * 连接WebSocket接收运动检测结果
   */
  const connectMotionDetectionWS = () => {
    const wsUrl = `ws://localhost:8000/ws/video-detection`
    motionDetectionWS = new WebSocket(wsUrl)

    motionDetectionWS.onopen = () => {
      addOperationInfo('[MOTION] WebSocket连接已建立')
    }

    motionDetectionWS.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        if (data.type === 'detection_result') {
          detectionResult.value = data.data
        }
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    motionDetectionWS.onclose = () => {
      addOperationInfo('[MOTION] WebSocket连接已断开')
    }

    motionDetectionWS.onerror = (error) => {
      console.error('WebSocket错误:', error)
      addOperationInfo('[ERROR] WebSocket连接错误')
    }
  }

  return {
    showMotionDetection,
    detectionResult,
    toggleMotionDetection,
    startMotionDetection,
    stopMotionDetection
  }
} 
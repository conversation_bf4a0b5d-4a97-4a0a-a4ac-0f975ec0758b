import requests
import json

# 获取认证token
token_response = requests.post('http://localhost:8000/api/auth/login/json/', json={'username': 'admin', 'password': '123456'})
if token_response.status_code == 200:
    token = token_response.json()['access_token']
    print(f"✅ 登录成功，获取到token")
    
    # 获取模板列表
    templates_response = requests.get('http://localhost:8000/api/detection-templates/', headers={'Authorization': f'Bearer {token}'})
    if templates_response.status_code == 200:
        templates = templates_response.json()
        print(f"\n📋 共找到 {len(templates)} 个模板")
        print("\n模板状态详情:")
        for i, template in enumerate(templates[:5]):  # 只显示前5个
            print(f"  {i+1}. ID: {template['id']}")
            print(f"     名称: {template['name']}")
            print(f"     状态: \"{template['status']}\" (类型: {type(template['status'])})")
            print(f"     原始数据: {json.dumps(template, ensure_ascii=False, indent=6)}")
            print()
    else:
        print(f"❌ 获取模板列表失败: {templates_response.status_code} - {templates_response.text}")
else:
    print(f"❌ 登录失败: {token_response.status_code} - {token_response.text}")
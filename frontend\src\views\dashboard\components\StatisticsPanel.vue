<template>
  <div class="statistics-panel">
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon><data-analysis /></el-icon>
        统计数据
      </h3>
      <div class="panel-actions">
        <el-select v-model="timeRange" size="small" style="width: 100px">
          <el-option label="今日" value="today" />
          <el-option label="本周" value="week" />
          <el-option label="本月" value="month" />
        </el-select>
      </div>
    </div>

    <div class="panel-content">
      <div class="statistics-grid">
        <!-- 检测统计 -->
        <div class="stat-card">
          <div class="stat-icon detection">
            <el-icon><view /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.todayDetections }}</div>
            <div class="stat-label">检测次数</div>
            <div class="stat-trend">
              <el-icon class="trend-up"><arrow-up /></el-icon>
              <span>+12.5%</span>
            </div>
          </div>
        </div>

        <!-- 报警统计 -->
        <div class="stat-card">
          <div class="stat-icon alarm">
            <el-icon><warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.todayAlarms }}</div>
            <div class="stat-label">报警次数</div>
            <div class="stat-trend">
              <el-icon class="trend-down"><arrow-down /></el-icon>
              <span>-8.3%</span>
            </div>
          </div>
        </div>

        <!-- 卡料率 -->
        <div class="stat-card">
          <div class="stat-icon jam">
            <el-icon><warning-filled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.jamRate }}%</div>
            <div class="stat-label">卡料率</div>
            <div class="stat-trend">
              <el-icon class="trend-down"><arrow-down /></el-icon>
              <span>-2.1%</span>
            </div>
          </div>
        </div>

        <!-- 处理时间 -->
        <div class="stat-card">
          <div class="stat-icon processing">
            <el-icon><timer /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.avgProcessingTime }}ms</div>
            <div class="stat-label">平均处理时间</div>
            <div class="stat-trend">
              <el-icon class="trend-up"><arrow-up /></el-icon>
              <span>+5.2%</span>
            </div>
          </div>
        </div>

        <!-- 系统运行时间 -->
        <div class="stat-card wide">
          <div class="stat-icon uptime">
            <el-icon><clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.systemUptime }}</div>
            <div class="stat-label">系统运行时间</div>
            <div class="stat-trend">
              <el-icon class="trend-normal"><check /></el-icon>
              <span>稳定运行</span>
            </div>
          </div>
        </div>

        <!-- 检测准确率 -->
        <div class="stat-card wide">
          <div class="stat-icon accuracy">
            <el-icon><aim /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ statistics.detectionAccuracy }}%</div>
            <div class="stat-label">检测准确率</div>
            <div class="stat-progress">
              <el-progress 
                :percentage="statistics.detectionAccuracy" 
                :show-text="false"
                color="#67c23a"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 算法性能对比 -->
      <div class="algorithm-comparison">
        <h4 class="section-title">算法性能对比</h4>
        <div class="algorithm-stats">
          <div class="algorithm-item">
            <div class="algorithm-name">运动检测</div>
            <div class="algorithm-metrics">
              <div class="metric">
                <span class="metric-label">准确率</span>
                <span class="metric-value">98.2%</span>
              </div>
              <div class="metric">
                <span class="metric-label">速度</span>
                <span class="metric-value">25 FPS</span>
              </div>
            </div>
          </div>
          
          <div class="algorithm-item">
            <div class="algorithm-name">方向检测</div>
            <div class="algorithm-metrics">
              <div class="metric">
                <span class="metric-label">准确率</span>
                <span class="metric-value">96.8%</span>
              </div>
              <div class="metric">
                <span class="metric-label">速度</span>
                <span class="metric-value">23 FPS</span>
              </div>
            </div>
          </div>
          
          <div class="algorithm-item">
            <div class="algorithm-name">卡料检测</div>
            <div class="algorithm-metrics">
              <div class="metric">
                <span class="metric-label">准确率</span>
                <span class="metric-value">99.1%</span>
              </div>
              <div class="metric">
                <span class="metric-label">响应时间</span>
                <span class="metric-value">120ms</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  DataAnalysis, View, Warning, WarningFilled, Timer, Clock, 
  Aim, ArrowUp, ArrowDown, Check
} from '@element-plus/icons-vue'

// Props
interface Statistics {
  todayDetections: number
  todayAlarms: number
  jamRate: number
  avgProcessingTime: number
  systemUptime: string
  detectionAccuracy: number
}

const props = defineProps<{
  statistics: Statistics
}>()

// 响应式数据
const timeRange = ref('today')
</script>

<style scoped>
.statistics-panel {
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-color);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.panel-content {
  padding: 20px;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.1);
}

.stat-card.wide {
  grid-column: span 2;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-icon.detection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.alarm {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.jam {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #d4691a;
}

.stat-icon.processing {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #2d5aa0;
}

.stat-icon.uptime {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #8e44ad;
}

.stat-icon.accuracy {
  background: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%);
  color: #3498db;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-color-soft);
  margin-bottom: 8px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.trend-up {
  color: var(--success-color);
}

.trend-down {
  color: var(--danger-color);
}

.trend-normal {
  color: var(--info-color);
}

.stat-progress {
  margin-top: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 16px 0;
}

.algorithm-comparison {
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
}

.algorithm-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.algorithm-item {
  padding: 16px;
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.algorithm-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 12px;
}

.algorithm-metrics {
  display: flex;
  justify-content: space-between;
}

.metric {
  text-align: center;
}

.metric-label {
  display: block;
  font-size: 12px;
  color: var(--text-color-mute);
  margin-bottom: 4px;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .statistics-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
  
  .stat-card.wide {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .statistics-grid {
    grid-template-columns: 1fr;
  }
  
  .algorithm-stats {
    grid-template-columns: 1fr;
  }
}
</style>

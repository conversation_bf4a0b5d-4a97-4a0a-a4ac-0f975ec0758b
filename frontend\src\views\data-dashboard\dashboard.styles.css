/* DataDashboard.vue Global Styles */
.data-dashboard {
  padding: 20px;
  background-color: var(--bg-color-mute);
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
}

.dashboard-grid {
  flex-grow: 1;
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: min-content min-content 1fr 1fr min-content;
  gap: 20px;
  overflow: hidden;
}

.dashboard-card {
  background-color: var(--bg-color-soft);
  color: var(--text-color);
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px var(--shadow-color);
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(.25,.8,.25,1);
  display: flex;
  flex-direction: column;
}

.dashboard-card:hover {
  box-shadow: 0 3px 6px var(--shadow-color-hover);
  border-color: var(--border-color-hover);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-color-soft);
}

.card-content {
  flex-grow: 1;
  overflow: hidden;
}

/* Icon Styles */
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
} 
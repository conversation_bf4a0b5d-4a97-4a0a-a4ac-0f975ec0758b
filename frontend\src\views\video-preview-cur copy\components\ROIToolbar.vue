<template>
  <div class="roi-toolbar-section">
    <div class="roi-toolbar">
      <div class="toolbar-title">
        <h4>ROI区域绘制工具</h4>
      </div>
      <div class="toolbar-controls">
        <!-- ROI属性选择 -->
        <div class="roi-attribute-selector">
          <label>ROI属性:</label>
          <label class="radio-label">
            <input
              type="radio"
              value="pailiao"
              name="roi-attribute"
              v-model="localSelectedAttribute"
              @change="onAttributeChange('pailiao')"
            />
            排料口
          </label>
          <label class="radio-label">
            <input
              type="radio"
              value="yazhu"
              name="roi-attribute"
              v-model="localSelectedAttribute"
              @change="onAttributeChange('yazhu')"
            />
            压铸机
          </label>
        </div>

        <!-- 绘制状态显示 -->
        <div v-if="selectedAttribute" class="drawing-status">
          <span :class="['status-indicator', isDrawingEnabled ? 'active' : 'inactive']">
            {{ isDrawingEnabled ? '绘制中' : '已暂停' }}
          </span>
          <button
            @click="$emit('toggle-draw')"
            :class="{ active: isDrawingEnabled }"
            class="tool-btn toggle-btn"
          >
            {{ isDrawingEnabled ? '暂停绘制' : '开始绘制' }}
          </button>
        </div>

        <!-- 管理按钮 -->
        <div class="management-buttons">
          <button
            @click="$emit('clear')"
            class="tool-btn clear-btn"
            :disabled="roiCount === 0"
          >
            清空
          </button>
          <button
            @click="$emit('save')"
            class="tool-btn save-btn"
            :disabled="roiCount === 0"
          >
            保存
          </button>
          <button
            @click="$emit('export')"
            class="tool-btn export-btn"
            :disabled="roiCount === 0"
          >
            导出
          </button>
          <button
            @click="$emit('import')"
            class="tool-btn import-btn"
          >
            导入
          </button>
          <button
            @click="$emit('load')"
            class="tool-btn load-btn"
            :disabled="roiCount === 0"
            title="将ROI列表加载到视频中显示"
          >
            加载到视频
          </button>
          <button
            @click="$emit('test')"
            class="tool-btn test-btn"
            title="测试ROI加载功能"
          >
            测试加载
          </button>
          <button
            @click="$emit('init')"
            class="tool-btn init-btn"
            title="手动初始化ROI绘制器"
          >
            初始化ROI
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue'
// defineProps 和 defineEmits 在 Vue 3 中是编译器宏，无需导入

const props = defineProps({
  selectedAttribute: {
    type: String as () => 'pailiao' | 'yazhu' | null,
    default: null
  },
  isDrawingEnabled: {
    type: Boolean,
    default: false
  },
  roiCount: {
    type: Number,
    default: 0
  }
})

// 本地状态，用于v-model双向绑定
const localSelectedAttribute = ref(props.selectedAttribute)

const emit = defineEmits([
  'attribute-change', 
  'toggle-draw', 
  'clear', 
  'save', 
  'export', 
  'import', 
  'load', 
  'test', 
  'init'
])

// 监听绘制状态变化
watch(() => props.isDrawingEnabled, (newValue) => {
  console.log('绘制状态变化:', newValue)
})

// 监听选中属性变化
watch(() => props.selectedAttribute, (newValue) => {
  console.log('选中属性变化:', newValue)
})

const onAttributeChange = (value: 'pailiao' | 'yazhu') => {
  console.log('用户选择属性:', value)
  // 更新本地状态
  localSelectedAttribute.value = value
  // 发送事件到父组件
  emit('attribute-change', value)
}
</script>
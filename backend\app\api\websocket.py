# from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
# from typing import Dict, Any, List, Optional
# import json
# import asyncio
# import base64
# import io
# import os
# import copy
# import numpy as np
# import cv2
# import uuid
# from PIL import Image
# from datetime import datetime

# from app.services.websocket_manager import WebSocketConnectionManager, WebSocketData
# from app.core.config import settings
# from app.api import deps

# router = APIRouter()

# # WebSocket连接管理器
# connection_manager = WebSocketConnectionManager()

# # 检测实例存储
# detection_instances: Dict[str, Any] = {}

# @router.websocket("/ws/video-detection/{client_id}")
# async def websocket_detection_endpoint(websocket: WebSocket, client_id: str):
#     """处理视频检测WebSocket连接"""
#     await connection_manager.connect(websocket, client_id)
    
#     # 创建检测器实例
#     from app.services.video_detection import MotionDete<PERSON>, DirectionDetector, FrameDifferencer, VideoDetectionManager
    
#     # 创建视频检测管理器
#     detection_manager = VideoDetectionManager()
    
#     # 初始化检测器
#     motion_detector = detection_manager.get_detector(f"{client_id}_motion", "motion", {
#         'minArea': 100, 
#         'detectionThreshold': 40,
#         'learningRate': 0.005,
#         'shadowsThreshold': 0.5
#     })
    
#     frame_differencer = detection_manager.get_detector(f"{client_id}_frame_diff", "frame_difference", {
#         'threshold': 30,
#         'minArea': 100,
#         'frameInterval': 2
#     })
    
#     direction_detector = detection_manager.get_detector(f"{client_id}_direction", "direction", {
#         'detector_type': 'background_subtraction',
#         'motion_params': {
#             'minArea': 100, 
#             'detectionThreshold': 30,
#             'learningRate': 0.003,
#             'shadowsThreshold': 0.5
#         },
#         'minDisplacement': 2,
#         'maxPatience': 3,
#         'consecutiveThreshold': 3
#     })
    
#     # 保存检测实例
#     detection_instances[client_id] = {
#         'detection_manager': detection_manager,
#         'motion_detector': motion_detector,
#         'frame_differencer': frame_differencer,
#         'direction_detector': direction_detector,
#         'active': False,
#         'mode': 'motion',
#         'detection_algorithm': 'background_subtraction',  # 默认使用背景减除法
#         'last_frame_time': datetime.now(),
#         'roi_detectors': {}  # 每个ROI的单独检测器
#     }
    
#     try:
#         # 发送连接建立消息
#         await connection_manager.send_personal_message(
#             json.dumps({
#                 "type": "connection_established",
#                 "client_id": client_id,
#                 "message": "Connected to detection server"
#             }),
#             websocket
#         )
        
#         # 处理消息
#         while True:
#             data = await websocket.receive_text()
            
#             try:
#                 message = json.loads(data)
#                 await process_detection_message(message, websocket, client_id)
                
#             except json.JSONDecodeError:
#                 await connection_manager.send_personal_message(
#                     json.dumps({"type": "error", "message": "Invalid JSON"}),
#                     websocket
#                 )
                
#             except Exception as e:
#                 await connection_manager.send_personal_message(
#                     json.dumps({"type": "error", "message": str(e)}),
#                     websocket
#                 )
    
#     except WebSocketDisconnect:
#         connection_manager.disconnect(websocket)
        
#         # 清理资源
#         if client_id in detection_instances:
#             del detection_instances[client_id]

# async def process_detection_message(message: Dict[str, Any], websocket: WebSocket, client_id: str):
#     """处理检测相关的消息"""
#     message_type = message.get("type")

#     if message_type == "frame":
#         # 处理视频帧
#         await process_detection_frame(message, websocket, client_id)
    
#     elif message_type == "roi_detection_control":
#         # ROI检测控制命令
#         await handle_roi_detection_control(websocket, client_id, message)

#     elif message_type == "reset_stuck_detection":
#         # 重置卡料检测状态
#         if client_id in detection_instances:
#             detection_manager = detection_instances[client_id].get("detection_manager")
#             # 检测状态重置功能已移除

#     elif message_type == "update_config":
#         # 更新检测配置
#         config = message.get("config", {})

#         if client_id in detection_instances:
#             detection_instance = detection_instances[client_id]
#             detection_manager = detection_instance["detection_manager"]

#             # 更新配置
#             if "detection_mode" in config:
#                 detection_instance["mode"] = config["detection_mode"]

#             # 更新检测算法
#             if "detection_algorithm" in config:
#                 detection_instance["detection_algorithm"] = config["detection_algorithm"]
            
#             # 更新检测器参数
#             if "motion_params" in config:
#                 motion_detector = detection_instance["motion_detector"]
#                 motion_detector.update_params(config["motion_params"])

#             # 更新帧差法参数
#             if "frame_difference_params" in config:
#                 frame_differencer = detection_instance["frame_differencer"]
#                 frame_differencer.update_params(config["frame_difference_params"])

#             # 更新方向检测器参数
#             if "direction_params" in config:
#                 direction_detector = detection_instance["direction_detector"]
#                 direction_detector.update_params(config["direction_params"])
            
#             # 更新ROI检测器配置
#             if "roi_detectors" in config:
#                 for roi_id, detector_config in config["roi_detectors"].items():
#                     detector_type = detector_config.get("type", "background_subtraction")

#                     # 根据检测器类型准备参数
#                     detector_params = {}

#                     if detector_type == "direction":
#                         # 方向检测器参数 - 处理前端发送的参数格式
#                         # 前端可能发送两种格式：标准化格式或原始格式
#                         if 'motion_params' in detector_config:
#                             # 标准化格式（前端setRoiDetector处理后的格式）
#                             motion_params = detector_config.get('motion_params', {})

#                             detector_params = {
#                                 'detector_type': detector_config.get('detector_type', 'background_subtraction'),
#                                 'motion_params': motion_params,
#                                 'minDisplacement': detector_config.get('minDisplacement', 2),
#                                 'maxPatience': detector_config.get('maxPatience', 3),
#                                 'consecutiveThreshold': detector_config.get('consecutiveThreshold', 3)
#                             }
#                         else:
#                             # 原始格式（支持新格式和旧格式）
#                             # 新格式
#                             motion_detection = detector_config.get('motion_detection', {})
#                             direction_detection = detector_config.get('direction_detection', {})

#                             # 旧格式兼容
#                             if not motion_detection:
#                                 前置背景检测 = detector_config.get('前置背景检测', {})
#                                 motion_detection = {
#                                     'enabled': 前置背景检测.get('enabled', True),
#                                     'minArea': 前置背景检测.get('minArea', 500),
#                                     'motionThreshold': 前置背景检测.get('motionThreshold', 50),
#                                     'backgroundUpdateRate': 前置背景检测.get('backgroundUpdateRate', 0.01)
#                                 }

#                             if not direction_detection:
#                                 后置方向检测 = detector_config.get('后置方向检测', {})
#                                 direction_detection = {
#                                     'minDisplacement': 后置方向检测.get('minDisplacement', 2),
#                                     'maxPatience': 后置方向检测.get('maxPatience', 3),
#                                     'consecutiveDetectionThreshold': 后置方向检测.get('consecutiveDetectionThreshold', 3)
#                                 }

#                             detector_params = {
#                                 'detector_type': 'background_subtraction',
#                                 'motion_params': {
#                                     'enabled': motion_detection.get('enabled', True),
#                                     'minArea': motion_detection.get('minArea', 500),
#                                     'detectionThreshold': motion_detection.get('motionThreshold', 50),
#                                     'learningRate': motion_detection.get('backgroundUpdateRate', 0.01),
#                                     'shadowsThreshold': 0.5
#                                 },
#                                 'minDisplacement': direction_detection.get('minDisplacement', 2),
#                                 'maxPatience': direction_detection.get('maxPatience', 3),
#                                 'consecutiveThreshold': direction_detection.get('consecutiveDetectionThreshold', 3)
#                             }

#                         # 使用方向检测器
#                         roi_detector = detection_manager.get_detector(
#                             f"{client_id}_roi_{roi_id}",
#                             "direction",
#                             detector_params
#                         )

#                     elif detector_type == "frame_difference":
#                         # 帧差法参数 - 支持新格式和旧格式
#                         motion_detection = detector_config.get('motion_detection', {})
#                         运动检测 = detector_config.get('运动检测', {})

#                         detector_params = {
#                             'minArea': motion_detection.get('minArea') or 运动检测.get('minArea') or detector_config.get('minArea', 100),
#                             'threshold': motion_detection.get('threshold') or 运动检测.get('threshold') or detector_config.get('threshold', 30),
#                             'frameInterval': motion_detection.get('frameInterval') or 运动检测.get('frameInterval') or detector_config.get('frameInterval', 2)
#                         }

#                         # 使用帧差检测器
#                         roi_detector = detection_manager.get_detector(
#                             f"{client_id}_roi_{roi_id}",
#                             "frame_difference",
#                             detector_params
#                         )

#                     else:
#                         # 背景减除法参数 - 支持新格式和旧格式
#                         motion_detection = detector_config.get('motion_detection', {})
#                         运动检测 = detector_config.get('运动检测', {})

#                         detector_params = {
#                             'minArea': motion_detection.get('minArea') or 运动检测.get('minArea') or detector_config.get('minArea', 100),
#                             'detectionThreshold': motion_detection.get('detectionThreshold') or 运动检测.get('detectionThreshold') or detector_config.get('detectionThreshold', 40),
#                             'learningRate': motion_detection.get('learningRate') or 运动检测.get('learningRate') or detector_config.get('learningRate', 0.005),
#                             'shadowsThreshold': motion_detection.get('shadowRemoval') or 运动检测.get('shadowRemoval') or detector_config.get('shadowsThreshold', 0.5)
#                         }

#                         # 使用运动检测器
#                         roi_detector = detection_manager.get_detector(
#                             f"{client_id}_roi_{roi_id}",
#                             "motion",
#                             detector_params
#                         )

#                     # 保存到ROI检测器映射
#                     detection_instance["roi_detectors"][roi_id] = roi_detector
            
#             # 更新全局设置（支持动态更新）
#             if "global_settings" in config:
#                 old_settings = detection_manager.get_global_settings()
#                 detection_manager.update_global_settings(config["global_settings"])
#                 new_settings = detection_manager.get_global_settings()

#                 # 卡料检测器参数更新功能已移除

#             # 请求获取全局设置
#             if config.get("request_global_settings", False):
#                 global_settings = detection_manager.get_global_settings()
#                 await connection_manager.send_personal_message(
#                     json.dumps({
#                         "type": "global_settings",
#                         "settings": global_settings
#                     }),
#                     websocket
#                 )
            
#             await connection_manager.send_personal_message(
#                 json.dumps({
#                     "type": "config_updated",
#                     "message": "Configuration updated"
#                 }),
#                 websocket
#             )
    
#     elif message_type == "ping":
#         # 心跳检测
#         await connection_manager.send_personal_message(
#             json.dumps({
#                 "type": "pong",
#                 "timestamp": datetime.now().isoformat()
#             }),
#             websocket
#         )

# async def process_detection_frame(message: Dict[str, Any], websocket: WebSocket, client_id: str):
#     """处理检测帧"""
#     if client_id not in detection_instances:
#         return

#     detection_instance = detection_instances[client_id]
#     detection_mode = message.get("detection_mode", detection_instance["mode"])
#     detection_algorithm = message.get("detection_algorithm", detection_instance["detection_algorithm"])
#     roi_list = message.get("roi_list", [])

#     # 获取检测管理器
#     detection_manager = detection_instance.get("detection_manager")
    
#     try:
#         # 解码Base64图像
#         frame_data = message.get("frame", "")
        
#         # 移除可能的前缀
#         if "," in frame_data:
#             frame_data = frame_data.split(",", 1)[1]
        
#         # 解码
#         frame_bytes = base64.b64decode(frame_data)
#         frame_pil = Image.open(io.BytesIO(frame_bytes))
        
#         # 转换为OpenCV格式
#         frame = np.array(frame_pil)
#         frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        
#         # 检查图像是否有效
#         if frame.size == 0 or frame.shape[0] == 0 or frame.shape[1] == 0:
#             return
        
#         # 创建结果容器
#         all_results = {
#             "type": "detection_result",
#             "motion_detected": False,
#             "contours": [],
#             "roi_results": [],
#             "timestamp": datetime.now().isoformat()
#         }
        
#         # 处理每个ROI
#         for roi in roi_list:
#             roi_id = roi.get("id", "")
#             roi_name = roi.get("name", f"ROI-{roi_id}")
#             roi_params = roi.get("params", {})
#             roi_detection_type = roi_params.get("type", "motion")  # 默认为运动检测

#             # 确定使用哪个检测器
#             detector = None
#             detector_source = ""
#             current_roi_mode = roi_detection_type  # 每个ROI独立的检测模式

#             # 检查是否有ROI专用检测器
#             if roi_id in detection_instance["roi_detectors"]:
#                 detector = detection_instance["roi_detectors"][roi_id]
#                 detector_source = f"专用检测器({type(detector).__name__})"
#             else:
#                 # 根据ROI的检测类型选择检测器
#                 if roi_detection_type == "direction":
#                     # 方向检测使用方向检测器
#                     detector = detection_instance["direction_detector"]
#                     detector_source = f"方向检测器({type(detector).__name__})"
#                 elif detection_algorithm == "frame_difference":
#                     detector = detection_instance["frame_differencer"]
#                     detector_source = f"全局帧差检测器({type(detector).__name__})"
#                 else:
#                     detector = detection_instance["motion_detector"]
#                     detector_source = f"全局运动检测器({type(detector).__name__})"
            
#             # 创建ROI掩码
#             roi_mask = None
#             if roi.get('type') == 'polygon' and 'points' in roi:
#                 points = np.array([[p.get('x', 0), p.get('y', 0)] for p in roi['points']], np.int32)
#                 points = points.reshape((-1, 1, 2))
#                 roi_mask = np.zeros(frame.shape[:2], dtype=np.uint8)
#                 cv2.fillPoly(roi_mask, [points], (255,))
            
#             elif roi.get('type') == 'rectangle' and 'points' in roi:
#                 points = roi['points']
#                 if len(points) >= 2:
#                     x1, y1 = int(points[0].get('x', 0)), int(points[0].get('y', 0))
#                     x2, y2 = int(points[1].get('x', 0)), int(points[1].get('y', 0))
#                     roi_mask = np.zeros(frame.shape[:2], dtype=np.uint8)
#                     cv2.rectangle(roi_mask, (x1, y1), (x2, y2), 255, -1)
            
#             if roi_mask is None:
#                 continue
            
#             # 执行检测
#             roi_result = {
#                 "roi_id": roi_id,
#                 "roi_name": roi_name,
#                 "motion_detected": False,
#                 "contours": [],
#                 # 增加调试信息
#                 "attribute": roi.get('attribute', 'unknown'),
#                 "detector_type": current_roi_mode,
#                 "detector_source": detector_source,
#                 "roi_params": roi_params
#             }
            
#             # 根据ROI的检测类型执行相应的检测算法
#             if current_roi_mode == 'motion':
#                 # 运动检测
#                 motion_detected, contours = detector.detect(frame, roi_mask)
                
#                 # 将轮廓转换为前端可用的格式
#                 contours_json = []
#                 for contour in contours:
#                     points = []
#                     for point in contour:
#                         try:
#                             # 🔥 修复：安全的轮廓点格式转换
#                             if hasattr(point, '__len__') and len(point) > 0:
#                                 if hasattr(point[0], '__len__') and len(point[0]) >= 2:
#                                     # 标准OpenCV格式 [[x, y]]
#                                     x, y = int(point[0][0]), int(point[0][1])
#                                 elif len(point) >= 2:
#                                     # 简化格式 [x, y]
#                                     x, y = int(point[0]), int(point[1])
#                                 else:
#                                     continue  # 跳过无效点
#                             else:
#                                 continue  # 跳过无效点

#                             points.append({"x": x, "y": y})
#                         except (IndexError, TypeError, ValueError):
#                             # 跳过无效的轮廓点
#                             continue

#                     if points:  # 只添加有效的轮廓
#                         contours_json.append({"points": points})
                
#                 roi_result["motion_detected"] = motion_detected
#                 roi_result["contours"] = contours_json

#                 # 添加检测器参数信息用于调试
#                 if hasattr(detector, 'params'):
#                     roi_result["detector_params"] = detector.params

#                 # 更新全局检测结果
#                 if motion_detected:
#                     all_results["motion_detected"] = True
#                     all_results["contours"].extend(contours_json)
                
#             elif current_roi_mode == 'direction':
#                 # 方向检测
#                 # 提取ROI区域
#                 roi_frame = cv2.bitwise_and(frame, frame, mask=roi_mask)
                
#                 # 方向检测
#                 direction_state, contours, direction_info = detection_instance["direction_detector"].detect_roi(roi_frame)
                
#                 # 将轮廓转换为前端可用的格式
#                 contours_json = []

#                 for i, contour in enumerate(contours):
#                     try:
#                         # 处理OpenCV轮廓格式
#                         points = []

#                         # 检查轮廓是否为空或格式错误
#                         if contour is None or len(contour) == 0:
#                             continue

#                         # 判断是否是箭头轮廓（通过轮廓点数和形状特征）
#                         is_arrow = False
#                         if len(contour) == 7:  # 箭头轮廓有7个点
#                             is_arrow = True

#                         for j, point in enumerate(contour):
#                             try:
#                                 # 检查点的格式
#                                 if point is None:
#                                     continue

#                                 # 🔥 修复：安全的OpenCV轮廓点格式处理
#                                 if hasattr(point, '__len__') and len(point) > 0:
#                                     try:
#                                         if hasattr(point[0], '__len__') and len(point[0]) >= 2:
#                                             # 标准OpenCV格式 [[x, y]]
#                                             x, y = int(point[0][0]), int(point[0][1])
#                                         elif len(point) >= 2:
#                                             # 简化格式 [x, y]
#                                             x, y = int(point[0]), int(point[1])
#                                         else:
#                                             continue

#                                         points.append({"x": x, "y": y})

#                                     except (IndexError, TypeError, ValueError):
#                                         continue
#                                 else:
#                                     continue

#                             except (ValueError, TypeError, IndexError):
#                                 continue

#                         if points:
#                             contour_data = {
#                                 "points": points,
#                                 "type": "direction_arrow" if is_arrow else "normal",
#                                 "color": "red" if is_arrow else "green"
#                             }

#                             # 如果是箭头，添加方向信息
#                             if is_arrow and direction_info:
#                                 contour_data["direction"] = direction_info.get('direction', '')

#                             contours_json.append(contour_data)

#                     except Exception:
#                         continue
                
#                 roi_result["motion_detected"] = direction_state != 'STATIONARY'
#                 roi_result["contours"] = contours_json
#                 roi_result["direction"] = direction_info

#                 # 添加检测器参数信息用于调试
#                 if hasattr(detector, 'params'):
#                     roi_result["detector_params"] = detector.params
#                 elif hasattr(detector, 'motion_detect_params'):
#                     roi_result["detector_params"] = detector.motion_detect_params

#                 # 更新全局检测结果
#                 if direction_state != 'STATIONARY':
#                     all_results["motion_detected"] = True
#                     all_results["contours"].extend(contours_json)
#                     if direction_info:
#                         all_results["direction"] = direction_info
            
#             # 添加到ROI结果列表
#             all_results["roi_results"].append(roi_result)

#         # 多ROI检测处理已移除（原卡料检测功能）
#         # 如果需要多ROI协同检测功能，可在此处添加新的实现

#         # 将检测结果写入文件（排除contours字段）
#         try:
#             import os
#             import copy
#             log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "logs")
#             os.makedirs(log_dir, exist_ok=True)
#             log_file = os.path.join(log_dir, "检测结果.txt")
            
#             # 创建结果的副本，移除contours字段
#             log_results = copy.deepcopy(all_results)
            
#             # 处理全局contours
#             if "contours" in log_results and log_results["contours"]:
#                 log_results["contours"] = f"[已省略 {len(log_results['contours'])} 个轮廓]"
            
#             # 处理各ROI结果中的contours
#             for roi_result in log_results.get("roi_results", []):
#                 if "contours" in roi_result and roi_result["contours"]:
#                     roi_result["contours"] = f"[已省略 {len(roi_result['contours'])} 个轮廓]"
#                 # 如果contours为空，保留原始空列表
            
#             with open(log_file, "a", encoding="utf-8") as f:
#                 timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
#                 f.write(f"[{timestamp}] {json.dumps(log_results, ensure_ascii=False)}\n")
#         except Exception as write_err:
#             print(f"写入检测结果到文件失败: {write_err}")
            
#         # 返回所有结果
#         await connection_manager.send_personal_message(
#             json.dumps(all_results),
#             websocket
#         )
                
#     except Exception as e:
#         await connection_manager.send_personal_message(
#             json.dumps({
#                 "type": "detection_error",
#                 "message": f"Detection error: {str(e)}"
#             }),
#             websocket
#         )


# async def handle_roi_detection_control(websocket: WebSocket, client_id: str, message: dict):
#     """
#     处理ROI检测控制命令
#     """
#     try:
#         command = message.get("command", {})
#         action = command.get("action", "")
#         roi_ids = command.get("roi_ids", [])
#         reason = command.get("reason", "")

#         if client_id not in detection_instances:
#             return

#         detection_instance = detection_instances[client_id]

#         # 初始化ROI控制状态（如果不存在）
#         if "roi_control_states" not in detection_instance:
#             detection_instance["roi_control_states"] = {}

#         roi_control_states = detection_instance["roi_control_states"]

#         # 处理不同的控制命令
#         if action == "start":
#             for roi_id in roi_ids:
#                 roi_control_states[roi_id] = {
#                     "is_active": True,
#                     "activated_at": datetime.now().isoformat(),
#                     "reason": reason
#                 }

#         elif action == "stop":
#             for roi_id in roi_ids:
#                 roi_control_states[roi_id] = {
#                     "is_active": False,
#                     "deactivated_at": datetime.now().isoformat(),
#                     "reason": reason
#                 }

#         elif action == "start_batch":
#             for roi_id in roi_ids:
#                 roi_control_states[roi_id] = {
#                     "is_active": True,
#                     "activated_at": datetime.now().isoformat(),
#                     "reason": reason
#                 }

#         elif action == "stop_batch":
#             for roi_id in roi_ids:
#                 roi_control_states[roi_id] = {
#                     "is_active": False,
#                     "deactivated_at": datetime.now().isoformat(),
#                     "reason": reason
#                 }

#         # 发送确认消息
#         await connection_manager.send_personal_message(
#             json.dumps({
#                 "type": "roi_control_response",
#                 "success": True,
#                 "action": action,
#                 "roi_ids": roi_ids,
#                 "message": f"ROI检测控制命令执行成功: {action}"
#             }),
#             websocket
#         )

#     except Exception as e:
#         await connection_manager.send_personal_message(
#             json.dumps({
#                 "type": "roi_control_response",
#                 "success": False,
#                 "error": str(e),
#                 "message": "ROI检测控制命令执行失败"
#             }),
#             websocket
#         )
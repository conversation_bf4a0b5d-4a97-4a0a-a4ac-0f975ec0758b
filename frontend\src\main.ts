import './assets/main.css'
import './assets/element-theme.css'
import './assets/global-override.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// ElementPlus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 导入用户Store
import { useUserStore } from './stores/user'

// 创建应用实例
const app = createApp(App)
const pinia = createPinia()
app.use(pinia)

// 全局配置Element Plus主题
const setElementPlusTheme = () => {
  const primaryColor = 'rgb(191, 46, 41)'
  const style = document.createElement('style')
  style.innerHTML = `
    :root {
      --el-color-primary: ${primaryColor} !important;
      --el-color-primary-light-3: rgb(217, 83, 79) !important;
      --el-color-primary-light-5: rgb(230, 121, 118) !important;
      --el-color-primary-light-7: rgb(242, 160, 157) !important;
      --el-color-primary-light-8: rgb(248, 180, 177) !important;
      --el-color-primary-light-9: rgb(253, 217, 216) !important;
      --el-color-primary-dark-2: rgb(165, 40, 35) !important;
    }
  `
  document.head.appendChild(style)
}

setElementPlusTheme()

// 注册所有Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 初始化用户信息
const userStore = useUserStore()
await userStore.init()

// 注册路由
app.use(router)

// 注册Element Plus
app.use(ElementPlus, {
  locale: zhCn
})

app.mount('#app')

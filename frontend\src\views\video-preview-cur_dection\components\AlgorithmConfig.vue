<template>
  <div class="algorithm-config-section">
    <h4>算法配置参数</h4>
    
    <div class="config-content">
      <div class="config-group">
        <h5>检测算法</h5>
        <div class="config-item">
          <label>
            <input 
              type="radio" 
              name="detection-algorithm" 
              value="background_subtraction" 
              v-model="algorithmType"
              @change="updateAlgorithm" 
            > 
            背景减除法
          </label>
          <label>
            <input 
              type="radio" 
              name="detection-algorithm" 
              value="frame_difference" 
              v-model="algorithmType"
              @change="updateAlgorithm" 
            > 
            帧差法
          </label>
        </div>
      </div>
      
      <!-- 背景减除法参数 -->
      <div class="config-group" v-if="algorithmType === 'background_subtraction'">
        <h5>背景减除法参数</h5>
        <div class="config-item">
          <label>最小检测面积</label>
          <div class="slider-container">
            <input 
              type="range" 
              min="50" 
              max="500" 
              v-model.number="bgSubParams.minArea" 
              class="slider"
              @change="updateParams"
            >
            <span>{{ bgSubParams.minArea }}px</span>
          </div>
        </div>
        <div class="config-item">
          <label>检测阈值</label>
          <div class="slider-container">
            <input 
              type="range" 
              min="10" 
              max="100" 
              v-model.number="bgSubParams.detectionThreshold" 
              class="slider"
              @change="updateParams"
            >
            <span>{{ bgSubParams.detectionThreshold }}</span>
          </div>
        </div>
        <div class="config-item">
          <label>学习速率</label>
          <div class="slider-container">
            <input 
              type="range" 
              min="1" 
              max="20" 
              v-model.number="bgSubParams.learningRateScale" 
              class="slider"
              @change="updateParams"
            >
            <span>{{ (bgSubParams.learningRate).toFixed(4) }}</span>
          </div>
        </div>
        <div class="config-item">
          <label>
            <input 
              type="checkbox" 
              v-model="bgSubParams.detectShadows"
              @change="updateParams"
            > 
            检测阴影
          </label>
        </div>
      </div>
      
      <!-- 帧差法参数 -->
      <div class="config-group" v-if="algorithmType === 'frame_difference'">
        <h5>帧差法参数</h5>
        <div class="config-item">
          <label>最小检测面积</label>
          <div class="slider-container">
            <input 
              type="range" 
              min="50" 
              max="500" 
              v-model.number="frameDiffParams.minArea" 
              class="slider"
              @change="updateParams"
            >
            <span>{{ frameDiffParams.minArea }}px</span>
          </div>
        </div>
        <div class="config-item">
          <label>差异阈值</label>
          <div class="slider-container">
            <input 
              type="range" 
              min="10" 
              max="100" 
              v-model.number="frameDiffParams.threshold" 
              class="slider"
              @change="updateParams"
            >
            <span>{{ frameDiffParams.threshold }}</span>
          </div>
        </div>
        <div class="config-item">
          <label>帧间隔</label>
          <div class="slider-container">
            <input 
              type="range" 
              min="1" 
              max="5" 
              v-model.number="frameDiffParams.frameInterval" 
              class="slider"
              @change="updateParams"
            >
            <span>{{ frameDiffParams.frameInterval }}</span>
          </div>
        </div>
      </div>
      
      <!-- 方向检测参数 -->
      <div class="config-group">
        <h5>方向检测参数</h5>
        <div class="config-item">
          <label>最小位移</label>
          <div class="slider-container">
            <input 
              type="range" 
              min="1" 
              max="10" 
              v-model.number="directionParams.minDisplacement" 
              class="slider"
              @change="updateParams"
            >
            <span>{{ directionParams.minDisplacement }}px</span>
          </div>
        </div>
        <div class="config-item">
          <label>最大耐心值</label>
          <div class="slider-container">
            <input 
              type="range" 
              min="1" 
              max="10" 
              v-model.number="directionParams.maxPatience" 
              class="slider"
              @change="updateParams"
            >
            <span>{{ directionParams.maxPatience }}</span>
          </div>
        </div>
        <div class="config-item">
          <label>连续检测阈值</label>
          <div class="slider-container">
            <input 
              type="range" 
              min="1" 
              max="10" 
              v-model.number="directionParams.consecutiveThreshold" 
              class="slider"
              @change="updateParams"
            >
            <span>{{ directionParams.consecutiveThreshold }}</span>
          </div>
        </div>
      </div>
      
      <div class="config-group">
        <h5>ROI配置</h5>
        <div class="config-item">
          <label>
            <input 
              type="checkbox" 
              v-model="enableRoiSpecificAlgorithm"
              @change="updateParams"
            > 
            每个ROI使用单独算法
          </label>
        </div>
      </div>
      
      <div class="config-actions">
        <button class="btn-apply" @click="applySettings">应用设置</button>
        <button class="btn-reset" @click="resetDefaults">重置默认</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 定义组件属性
const props = defineProps({
  // 是否可以编辑
  isEditable: {
    type: Boolean,
    default: true
  },
  // 当前检测算法
  currentAlgorithm: {
    type: String,
    default: 'background_subtraction'
  }
})

// 定义事件
const emit = defineEmits(['update-config', 'apply-settings', 'reset-defaults'])

// 算法类型
const algorithmType = ref(props.currentAlgorithm)

// 背景减除法参数
const bgSubParams = ref({
  minArea: 100,
  detectionThreshold: 40,
  learningRateScale: 5,  // 0.001 - 0.02
  detectShadows: true,
  get learningRate() {
    return this.learningRateScale * 0.001
  }
})

// 帧差法参数
const frameDiffParams = ref({
  minArea: 100,
  threshold: 30,
  frameInterval: 2
})

// 方向检测参数
const directionParams = ref({
  minDisplacement: 2,
  maxPatience: 3,
  consecutiveThreshold: 3
})

// 是否为每个ROI启用单独算法
const enableRoiSpecificAlgorithm = ref(false)

// 更新算法类型
const updateAlgorithm = () => {
  emit('update-config', {
    detection_algorithm: algorithmType.value
  })
}

// 更新参数
const updateParams = () => {
  const config: any = {}
  
  if (algorithmType.value === 'background_subtraction') {
    config.motion_params = {
      minArea: bgSubParams.value.minArea,
      detectionThreshold: bgSubParams.value.detectionThreshold,
      learningRate: bgSubParams.value.learningRate,
      shadowsThreshold: bgSubParams.value.detectShadows ? 0.5 : 0
    }
  } else {
    config.frame_difference_params = {
      minArea: frameDiffParams.value.minArea,
      threshold: frameDiffParams.value.threshold,
      frameInterval: frameDiffParams.value.frameInterval
    }
  }
  
  config.direction_params = {
    detector_type: algorithmType.value,
    minDisplacement: directionParams.value.minDisplacement,
    maxPatience: directionParams.value.maxPatience,
    consecutiveThreshold: directionParams.value.consecutiveThreshold,
    motion_params: algorithmType.value === 'background_subtraction' 
      ? config.motion_params 
      : config.frame_difference_params
  }
  
  emit('update-config', config)
}

// 应用设置
const applySettings = () => {
  updateParams()
  emit('apply-settings')
}

// 重置默认值
const resetDefaults = () => {
  algorithmType.value = 'background_subtraction'
  
  bgSubParams.value = {
    minArea: 100,
    detectionThreshold: 40,
    learningRateScale: 5,
    detectShadows: true,
    get learningRate() {
      return this.learningRateScale * 0.001
    }
  }
  
  frameDiffParams.value = {
    minArea: 100,
    threshold: 30,
    frameInterval: 2
  }
  
  directionParams.value = {
    minDisplacement: 2,
    maxPatience: 3,
    consecutiveThreshold: 3
  }
  
  enableRoiSpecificAlgorithm.value = false
  
  emit('reset-defaults')
}

// 监听属性变化
watch(() => props.currentAlgorithm, (newVal) => {
  algorithmType.value = newVal
})
</script>

<style scoped>
.algorithm-config-section {
  margin-top: 15px;
  padding: 10px;
  background-color: var(--bg-color-soft);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.algorithm-config-section h4 {
  margin: 0 0 10px 0;
  color: var(--text-color);
  font-size: 16px;
}

.config-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.config-group {
  padding: 8px;
  background-color: var(--bg-color);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.config-group h5 {
  margin: 0 0 8px 0;
  color: var(--text-color-soft);
  font-size: 14px;
}

.config-item {
  margin-bottom: 8px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.config-item label {
  font-size: 13px;
  color: var(--text-color-soft);
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.slider {
  flex: 1;
  height: 6px;
}

.slider-container span {
  min-width: 45px;
  text-align: right;
  font-size: 13px;
  color: var(--text-color-soft);
}

.config-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
}

.btn-apply {
  background-color: var(--primary-color);
  color: white;
}

.btn-reset {
  background-color: var(--text-color-mute);
  color: white;
}

button:hover {
  opacity: 0.8;
}
</style>


import requests
import json

# 模拟前端API调用
print("=== 调试前端API调用 ===")

# 1. 登录获取token
print("\n1. 登录获取token...")
login_response = requests.post('http://localhost:8000/api/auth/login/json/', json={'username': 'admin', 'password': '123456'})
if login_response.status_code == 200:
    token = login_response.json()['access_token']
    print(f"✅ 登录成功")
else:
    print(f"❌ 登录失败: {login_response.status_code} - {login_response.text}")
    exit(1)

# 2. 测试前端使用的API端点
headers = {'Authorization': f'Bearer {token}'}

print("\n2. 测试前端API端点...")
api_endpoints = [
    '/api/detection-templates/',
    '/detection-templates/',
    '/api/detection-templates',
    '/detection-templates'
]

for endpoint in api_endpoints:
    print(f"\n测试端点: {endpoint}")
    try:
        response = requests.get(f'http://localhost:8000{endpoint}', headers=headers)
        print(f"  状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  数据长度: {len(data)}")
            if data:
                print(f"  第一个模板状态: {data[0].get('status', 'NO_STATUS')}")
        else:
            print(f"  错误: {response.text[:100]}")
    except Exception as e:
        print(f"  异常: {e}")

# 3. 检查前端utils/api.js的get函数行为
print("\n3. 模拟前端get函数调用...")
try:
    # 模拟前端的get函数调用
    response = requests.get('http://localhost:8000/api/detection-templates/', headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"返回数据类型: {type(data)}")
        print(f"数据内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
except Exception as e:
    print(f"异常: {e}")
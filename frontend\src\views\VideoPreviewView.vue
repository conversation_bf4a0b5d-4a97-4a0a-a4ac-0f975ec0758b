<template>
  <div class="video-preview-page">
    <div class="page-header">
        <div>
          <h1>视频预览（自动化）</h1>
          <p class="page-description">选择视频源后将自动登录设备、选择模拟通道、使用主码流并开始预览</p>
        </div>
    </div>
    
    <div class="preview-content">
      <!-- 视频源选择区域 -->
      <div class="video-source-section">
        <div class="source-controls">
          <label>选择视频流（自动预览）:</label>
          <select 
            v-model="selectedVideoSource" 
            @change="onVideoSourceChange"
            class="source-select"
            :disabled="isConnecting || isPreviewActive"
          >
            <option value="">请选择视频源</option>
            <option 
              v-for="source in videoSources" 
              :key="source.id" 
              :value="source.id"
            >
              {{ source.name }}
            </option>
          </select>
          <button 
            @click="refreshVideoSources" 
            class="refresh-btn"
            :disabled="isConnecting"
          >
            刷新
          </button>
        </div>
      </div>

      <!-- 主要内容区域 - 左右布局 -->
      <div class="main-content">
        <!-- 左侧视频区域 - 60% -->
        <div class="video-section">
          <!-- ROI绘制工具栏 - 移到视频上方 -->
          <div class="roi-toolbar-section">
            <div class="roi-toolbar">
              <div class="toolbar-title">
                <h4>ROI区域绘制工具</h4>
              </div>
              <div class="toolbar-controls">
                <!-- ROI绘制工具栏 - 默认始终可用 -->

                <!-- ROI属性选择 -->
                <div class="roi-attribute-selector">
                  <label>ROI属性:</label>
                  <label class="radio-label">
                    <input
                      type="radio"
                      v-model="selectedROIAttribute"
                      value="pailiao"
                      @change="onROIAttributeChange"
                    />
                    排料口
                  </label>
                  <label class="radio-label">
                    <input
                      type="radio"
                      v-model="selectedROIAttribute"
                      value="yazhu"
                      @change="onROIAttributeChange"
                    />
                    压铸机
                  </label>
                </div>

                <!-- 绘制状态显示 -->
                <div v-if="selectedROIAttribute" class="drawing-status">
                  <span :class="['status-indicator', isDrawingEnabled ? 'active' : 'inactive']">
                    {{ isDrawingEnabled ? '绘制中' : '已暂停' }}
                  </span>
                  <button
                    @click="toggleDrawMode"
                    :class="{ active: isDrawingEnabled }"
                    class="tool-btn toggle-btn"
                  >
                    {{ isDrawingEnabled ? '暂停绘制' : '开始绘制' }}
                  </button>
                </div>

                <!-- 管理按钮 -->
                <div class="management-buttons">
                  <button
                    @click="clearROIs"
                    class="tool-btn clear-btn"
                    :disabled="roiCount === 0"
                  >
                    清空
                  </button>
                  <button
                    @click="saveROIs"
                    class="tool-btn save-btn"
                    :disabled="roiCount === 0"
                  >
                    保存
                  </button>
                  <button
                    @click="exportROIs"
                    class="tool-btn export-btn"
                    :disabled="roiCount === 0"
                  >
                    导出
                  </button>
                  <button
                    @click="importROIs"
                    class="tool-btn import-btn"
                  >
                    导入
                  </button>
                  <button
                    @click="loadROIListToVideo(roiList)"
                    class="tool-btn load-btn"
                    :disabled="roiCount === 0"
                    title="将ROI列表加载到视频中显示"
                  >
                    加载到视频
                  </button>
                  <button
                    @click="testROILoad"
                    class="tool-btn test-btn"
                    title="测试ROI加载功能"
                  >
                    测试加载
                  </button>
                  <button
                    @click="manualInitROI"
                    class="tool-btn init-btn"
                    title="手动初始化ROI绘制器"
                  >
                    初始化ROI
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 视频预览区域 -->
          <div class="video-preview-section">
            <div
              ref="videoContainer"
              class="video-container"
              :style="videoContainerStyle"

            >
              <div
                id="divPlugin"
                class="video-player"
              ></div>

              <!-- 运动检测叠加层 -->
              <MotionOverlay
                v-if="showMotionDetection && isPreviewActive"
                :video-width="640"
                :video-height="360"
                :detection-result="detectionResult"
                :show-status="true"
                class="motion-overlay"
              />

              <!-- ROI绘制结果显示层 - 只显示已绘制的ROI，不处理交互 -->
              <canvas
                ref="roiDisplayCanvas"
                class="roi-display-canvas"
                :width="640"
                :height="360"
              ></canvas>
            </div>
          </div>

          <!-- 控制按钮区域 - 移到视频下方 -->
          <div class="control-section">
            <button
              @click="startPreview"
              :disabled="!canStartPreview || isConnecting"
              class="control-btn primary"
            >
              {{ isConnecting ? '连接中...' : '开启预览' }}
            </button>
            <button
              @click="stopPreview"
              :disabled="!isPreviewActive"
              class="control-btn danger"
            >
              停止预览
            </button>
            <button
              @click="toggleMotionDetection"
              :disabled="!isPreviewActive"
              :class="['control-btn', showMotionDetection ? 'active' : 'secondary']"
            >
              {{ showMotionDetection ? '关闭运动检测' : '开启运动检测' }}
            </button>
          </div>
        </div>

        <!-- 右侧面板区域 - 40% -->
        <div class="right-panel">
          <!-- ROI列表和管理面板 -->
          <div class="roi-management-section">
            <div class="roi-list-panel">
              <div class="panel-header">
                <h4>ROI区域列表 ({{ roiCount }})</h4>
                <div class="panel-status">
                  <span v-if="!selectedROIAttribute" class="status-ready">请选择ROI属性</span>
                  <span v-else-if="!isDrawingEnabled" class="status-locked">绘制已暂停</span>
                  <span v-else class="status-drawing">多边形绘制模式 - {{ getAttributeDisplayName(selectedROIAttribute) }}</span>
                </div>
              </div>

              <div class="panel-content">
                <!-- ROI树状列表 -->
                <div class="roi-tree">
                  <div v-if="roiCount === 0" class="empty-message">
                    <div class="empty-icon">·</div>
                    <div class="empty-text">
                      <p>暂无ROI区域</p>
                      <small v-if="!selectedROIAttribute">请选择ROI属性后开始绘制</small>
                      <small v-else>请在视频画面上绘制ROI区域</small>
                    </div>
                  </div>

                  <!-- 排料口ROI组 -->
                  <div v-if="pailiaoROIs.length > 0" class="roi-group">
                    <div class="group-header">
                      <span class="group-icon">📦</span>
                      <span class="group-title">排料口 ({{ pailiaoROIs.length }})</span>
                      <button @click="clearAttributeROIs('pailiao')" class="group-clear-btn" title="清空排料口ROI">清空</button>
                    </div>
                    <div class="group-content">
                      <div v-for="(roi, index) in pailiaoROIs" :key="roi.roi_id" class="roi-item" :class="{ 'highlighted': highlightedROIId === roi.roi_id }">
                        <div class="roi-info" @click="toggleROIHighlight(roi.roi_id)" title="点击在视频中高亮显示">
                          <div class="roi-color" :style="{ backgroundColor: roi.color }"></div>
                          <div class="roi-details">
                            <span class="roi-name">{{ roi.name }}</span>
                            <span class="roi-points">{{ roi.coordinates?.length || roi.points?.length || 0 }} 个点</span>
                          </div>
                        </div>
                        <div class="roi-actions">
                          <button @click="editROI(roi.roi_id)" class="edit-btn" title="编辑ROI">编辑</button>
                          <button @click="deleteROI(roi.roi_id)" class="delete-btn" title="删除ROI">删除</button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 压铸机ROI组 -->
                  <div v-if="yazhuROIs.length > 0" class="roi-group">
                    <div class="group-header">
                      <span class="group-icon">🏭</span>
                      <span class="group-title">压铸机 ({{ yazhuROIs.length }})</span>
                      <button @click="clearAttributeROIs('yazhu')" class="group-clear-btn" title="清空压铸机ROI">清空</button>
                    </div>
                    <div class="group-content">
                      <div v-for="(roi, index) in yazhuROIs" :key="roi.roi_id" class="roi-item" :class="{ 'highlighted': highlightedROIId === roi.roi_id }">
                        <div class="roi-info" @click="toggleROIHighlight(roi.roi_id)" title="点击在视频中高亮显示">
                          <div class="roi-color" :style="{ backgroundColor: roi.color }"></div>
                          <div class="roi-details">
                            <span class="roi-name">{{ roi.name }}</span>
                            <span class="roi-points">{{ roi.coordinates?.length || roi.points?.length || 0 }} 个点</span>
                          </div>
                        </div>
                        <div class="roi-actions">
                          <button @click="editROI(roi.roi_id)" class="edit-btn" title="编辑ROI">编辑</button>
                          <button @click="deleteROI(roi.roi_id)" class="delete-btn" title="删除ROI">删除</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 使用说明 -->
                <div class="help-section">
                  <div class="help-header">
                    <span>使用说明</span>
                  </div>
                  <div class="help-content">
                    <ol>
                      <li>点击"开启ROI绘制"按钮</li>
                      <li>选择ROI属性（排料口/压铸机）</li>
                      <li>在视频画面上绘制多边形ROI</li>
                      <li>左键点击添加顶点，右键完成绘制</li>
                      <li>可连续绘制多个ROI区域</li>
                      <li>完成后可暂停绘制或继续添加</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 算法配置参数区域 - 预留 -->
          <div class="algorithm-config-section">
            <h4>算法配置参数</h4>
            <div class="config-placeholder">
              算法配置参数区域<br>
              <small>预留给未来的算法参数配置</small>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作信息框 -->
      <div class="operation-info-section">
        <div class="info-header">
          <h3>操作信息</h3>
          <button @click="clearOperationInfo" class="clear-btn">清空</button>
        </div>
        <div class="info-content" ref="infoContent">
          <div 
            v-for="(info, index) in operationInfos" 
            :key="index" 
            class="info-item"
          >
            <span class="info-time">{{ info.time }}</span>
            <span class="info-message">{{ info.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import MotionOverlay from '@/components/MotionOverlay.vue'
import { ROIDrawer } from '@/utils/roiDrawer.js'

// 状态管理
const videoSources = ref<any[]>([])
const selectedVideoSource = ref('')
const isConnecting = ref(false)
const isPreviewActive = ref(false)
const operationInfos = ref<Array<{ time: string; message: string }>>([])
const videoContainer = ref<HTMLElement>()
const infoContent = ref<HTMLElement>()

// WebSDK相关
let webVideoCtrl: any = null
let g_iWndIndex = 0 // 当前窗口索引
let currentDevice: any = null
let currentChannel: any = null
let isWebSDKInitialized = false

// 运动检测相关
const showMotionDetection = ref(false)
const detectionResult = ref<any>(null)
const roiDrawer = ref<any>(null)
let motionDetectionWS: WebSocket | null = null

// ROI绘制相关
const isDrawingEnabled = ref(false)
const selectedROIAttribute = ref<'pailiao' | 'yazhu' | null>(null)
const roiList = ref<any[]>([])
const roiCount = computed(() => roiList.value.length)
const roiDisplayCanvas = ref<HTMLCanvasElement>()
const highlightedROIId = ref<string | null>(null)

// ROI绘制器实例
let roiDrawerInstance: any = null

// ROI分组计算属性
const pailiaoROIs = computed(() => roiList.value.filter(roi => roi.attribute === 'pailiao'))
const yazhuROIs = computed(() => roiList.value.filter(roi => roi.attribute === 'yazhu'))

// ROI属性显示名称
const getAttributeDisplayName = (attribute: string | null) => {
  if (!attribute) return ''
  return attribute === 'pailiao' ? '排料口' : '压铸机'
}

// 获取指定属性的ROI数量
const getAttributeROICount = (attribute: string | null) => {
  if (!attribute) return 0
  return roiList.value.filter(roi => roi.attribute === attribute).length
}

// 根据属性获取ROI颜色
const getROIColorByAttribute = (attribute: string | null) => {
  if (attribute === 'pailiao') return '#00ffff'  // 青色
  if (attribute === 'yazhu') return '#ff0000'    // 红色
  return '#00ff00'  // 默认绿色
}

// 初始化ROI绘制器
const initROIDrawer = () => {
  console.log('开始初始化ROI绘制器...')
  console.log('roiDisplayCanvas ref状态:', {
    ref: !!roiDisplayCanvas,
    value: !!roiDisplayCanvas.value,
    element: roiDisplayCanvas.value
  })

  const canvas = roiDisplayCanvas.value
  if (!canvas) {
    console.error('ROI画布未找到，可能的原因:')
    console.error('1. 画布元素还未渲染到DOM')
    console.error('2. ref绑定失败')
    console.error('3. 组件还未完全挂载')
    addOperationInfo('[ERROR] ROI画布未找到，初始化失败')
    return
  }

  console.log('找到ROI画布:', {
    tagName: canvas.tagName,
    width: canvas.width,
    height: canvas.height,
    offsetWidth: canvas.offsetWidth,
    offsetHeight: canvas.offsetHeight
  })

  try {
    roiDrawerInstance = new ROIDrawer(canvas, {
      videoWidth: 640,
      videoHeight: 360,
      colors: {
        pailiao: '#00ffff',  // 青色
        yazhu: '#ff0000',    // 红色
        default: ['#00ff00', '#ffff00', '#ff00ff', '#0066ff']  // 备用颜色
      },
      debugMode: true  // 启用调试模式
    })

    // 设置事件回调
    roiDrawerInstance.onROIAdded = (roi: any) => {
      // 添加属性信息
      roi.attribute = selectedROIAttribute.value
      roi.name = `${getAttributeDisplayName(selectedROIAttribute.value)}${getAttributeROICount(selectedROIAttribute.value) + 1}`
      // 根据属性设置颜色
      roi.color = getROIColorByAttribute(selectedROIAttribute.value)

      roiList.value.push(roi)
      addOperationInfo(`[ROI] 添加${getAttributeDisplayName(selectedROIAttribute.value)}ROI: ${roi.name}`)
    }

    roiDrawerInstance.onROIDeleted = (roi: any, index: number) => {
      addOperationInfo(`[ROI] 删除ROI: ${roi.name}`)
    }

    // 注意：事件绑定现在由ROIDrawer内部管理，无需手动绑定

    console.log('ROI绘制器初始化成功')
    addOperationInfo('[ROI] 绘制器初始化成功')
  } catch (error) {
    console.error('ROI绘制器初始化失败:', error)
    addOperationInfo(`[ERROR] ROI绘制器初始化失败: ${error}`)
  }
}

// 计算属性
const canStartPreview = computed(() => {
  return selectedVideoSource.value && !isPreviewActive.value && isWebSDKInitialized
})

// 视频容器样式 - 16:9比例自适应
const videoContainerStyle = computed(() => {
  const style: any = {
    aspectRatio: '16/9',
    width: '100%',
    maxWidth: '1200px'
  }

  // 在绘制模式下添加鼠标样式
  if (isDrawingEnabled.value && selectedROIAttribute.value) {
    style.cursor = 'crosshair'
  }

  return style
})

// 添加操作信息
const addOperationInfo = (message: string) => {
  const now = new Date()
  const timeStr = now.getHours().toString().padStart(2, '0') + ':' + now.getMinutes().toString().padStart(2, '0') + ':' + now.getSeconds().toString().padStart(2, '0')
  
  operationInfos.value.push({
    time: timeStr,
    message
  })
  
  // 自动滚动到底部
  nextTick(() => {
    if (infoContent.value) {
      infoContent.value.scrollTop = infoContent.value.scrollHeight
    }
  })
}

// 清空操作信息
const clearOperationInfo = () => {
  operationInfos.value = []
}

// 获取WebSDK实例
const getWebVideoCtrl = () => {
  if (!webVideoCtrl) {
    webVideoCtrl = (window as any).WebVideoCtrl
  }
  return webVideoCtrl
}

// 初始化WebSDK - 完全基于WebSDKPlayer组件的正确实现
const initWebSDK = async () => {
  try {
    // 检查WebSDK是否已加载
    if (!(window as any).WebVideoCtrl) {
      throw new Error('WebSDK未加载，请检查脚本引用')
    }

    webVideoCtrl = (window as any).WebVideoCtrl

    // 检查浏览器支持 - 对应demo.js第33行
    const iRet = webVideoCtrl.I_SupportNoPlugin()
    if (!iRet) {
      throw new Error('当前浏览器版本过低，不支持无插件，请升级后再试！')
    }

    addOperationInfo('浏览器支持检查通过')

    // 初始化插件参数及插入无插件 - 对应demo.js第40行
    webVideoCtrl.I_InitPlugin("100%", "100%", {
      bWndFull: true,     // 是否支持单窗口双击全屏，默认支持 true:支持 false:不支持
      iPackageType: 2,    // 2:PS 11:MP4
      iWndowType: 1,
      bNoPlugin: true,

      // 窗口选择回调 - 对应demo.js第46行
      cbSelWnd: (xmlDoc: any) => {
        console.log('cbSelWnd回调被触发，xmlDoc:', xmlDoc)
        try {
          // 检查jQuery是否可用
          if (typeof $ === 'undefined') {
            console.error('jQuery未加载，无法解析XML')
            addOperationInfo('[ERROR] jQuery未加载，无法解析XML')
            return
          }

          // 使用jQuery方式解析，对应demo.js第47行
          const windowText = $(xmlDoc).find("SelectWnd").eq(0).text()
          console.log('SelectWnd文本内容:', windowText)
          g_iWndIndex = parseInt(windowText, 10)
          console.log('当前选择的窗口编号：', g_iWndIndex)
          addOperationInfo('当前选择的窗口编号：' + g_iWndIndex)
        } catch (error) {
          console.error('解析窗口选择XML失败:', error)
          addOperationInfo('[ERROR] 解析窗口选择XML失败')
        }
      },

      // 双击窗口回调 - 对应demo.js第51行
      cbDoubleClickWnd: (iWndIndex: number, bFullScreen: boolean) => {
        const szInfo = bFullScreen
          ? `当前放大的窗口编号：${iWndIndex}`
          : `当前还原的窗口编号：${iWndIndex}`
        console.log(szInfo)
        addOperationInfo(szInfo)
      },

      // 事件回调 - 对应demo.js第58行
      cbEvent: (iEventType: number, iParam1: number, iParam2: number) => {
        let eventMsg = ''
        if (2 == iEventType) { // 回放正常结束
          eventMsg = `窗口${iParam1}回放结束！`
          console.log(eventMsg)
          addOperationInfo(eventMsg)
        } else if (-1 == iEventType) {
          eventMsg = `设备${iParam1}网络错误！`
          console.log(eventMsg)
          addOperationInfo(eventMsg)
        } else if (3001 == iEventType) {
          eventMsg = `录像结束事件：窗口${iParam1}`
          console.log(eventMsg)
          addOperationInfo(eventMsg)
        } else {
          eventMsg = `事件回调：类型${iEventType}，参数1：${iParam1}，参数2：${iParam2}`
          console.log(eventMsg)
          addOperationInfo(eventMsg)
        }
      },

      // 远程配置回调 - 对应demo.js第67行
      cbRemoteConfig: () => {
        console.log('关闭远程配置库！')
      },

      // 插件初始化完成回调 - 对应demo.js第70行
      cbInitPluginComplete: () => {
        console.log('WebSDK插件初始化完成')
        isWebSDKInitialized = true
        addOperationInfo('[PLUGIN] WebSDK插件初始化完成')

        // 嵌入插件到指定容器 - 必须在初始化完成后调用，与demo.js保持一致
        nextTick(() => {
          const container = document.getElementById('divPlugin')
          if (container) {
            try {
              webVideoCtrl.I_InsertOBJECTPlugin('divPlugin')
              console.log('WebSDK插件嵌入完成')
              addOperationInfo('[PLUGIN] WebSDK插件嵌入完成')
            } catch (error) {
              console.error('插件嵌入失败:', error)
              addOperationInfo('[ERROR] 插件嵌入失败')
            }
          } else {
            console.error('未找到播放器容器元素 divPlugin')
            addOperationInfo('[ERROR] 未找到播放器容器元素 divPlugin')
          }
        })
      },

       // 插件错误处理回调 - 对应demo.js第73行
       cbPluginErrorHandler: (iWndIndex: number, iErrorCode: number, oError: any) => {
         const ErrorCodes: Record<number, string> = {
           1001: "码流传输过程异常",
           1002: "回放结束",
           1003: "取流失败，连接被动断开",
           1004: "对讲连接被动断开",
           1005: "广播连接被动断开",
           1006: "视频编码格式不支持 目前只支持h264 和 h265",
           1007: "网络异常导致websocket断开",
           1008: "首帧回调超时",
           1009: "对讲码流传输过程异常",
           1010: "广播码流传输过程异常",
           1011: "数据接收异常，请检查是否修改了视频格式",
           1012: "播放资源不足",
           1013: "当前环境不支持该鱼眼展开模式",
           1014: "外部强制关闭了",
           1015: "获取播放url失败",
           1016: "文件下载完成",
           1017: "密码错误",
           1018: "链接到萤石平台失败",
           1019: "未找到录像片段",
           1020: "水印模式等场景，当前通道需要重新播放",
           1021: "缓存溢出",
           1022: "采集音频失败，可能是在非https/localhost域下使用对讲导致,或者没有插耳机等"
         }
         const errorMsg = ErrorCodes[iErrorCode] || `未知错误(${iErrorCode})`
         console.error(`窗口${iWndIndex}：${errorMsg}`, oError)
         addOperationInfo(`[ERROR] 窗口${iWndIndex}：${errorMsg}`)

         // 获取窗口状态并停止播放 - 对应demo.js第75行
         const oWndInfo = webVideoCtrl.I_GetWindowStatus(iWndIndex)
         if (oWndInfo != null) {
           webVideoCtrl.I_Stop({
             success: () => {
               console.log(`窗口${iWndIndex}停止预览成功`)
               addOperationInfo(`[STOP] 窗口${iWndIndex}停止预览成功`)
             },
             error: () => {
               console.log(`窗口${iWndIndex}停止预览失败`)
               addOperationInfo(`[ERROR] 窗口${iWndIndex}停止预览失败`)
             }
           })
         }
       },

       // 性能不足回调 - 对应demo.js第91行
       cbPerformanceLack: () => {
         console.log('性能不足！')
         addOperationInfo('[WARNING] 性能不足！')
       },

       // 码流加密秘钥错误回调 - 对应demo.js第94行
       cbSecretKeyError: (iWndIndex: number) => {
         console.error(`窗口${iWndIndex}：码流加密秘钥错误！`)
         addOperationInfo(`[ERROR] 窗口${iWndIndex}：码流加密秘钥错误！`)

         const oWndInfo = webVideoCtrl.I_GetWindowStatus(iWndIndex)
         if (oWndInfo != null) {
           webVideoCtrl.I_Stop({
             success: () => {
               console.log(`窗口${iWndIndex}停止预览成功`)
               addOperationInfo(`[STOP] 窗口${iWndIndex}停止预览成功`)
             },
             error: () => {
               console.log(`窗口${iWndIndex}停止预览失败`)
               addOperationInfo(`[ERROR] 窗口${iWndIndex}停止预览失败`)
             }
           })
         }
       }
     })

  } catch (error) {
    console.error('WebSDK初始化失败:', error)
    addOperationInfo(`[ERROR] WebSDK初始化失败: ${error instanceof Error ? error.message : String(error)}`)
    throw error
  }
}

// 刷新视频源列表 - 完全基于WebSDKLogin组件的正确实现
const refreshVideoSources = async () => {
  try {
    addOperationInfo('[API] 开始获取视频源列表...')
    // 调用后端API获取视频源列表
    // 从localStorage获取认证token
    const token = localStorage.getItem('token')

    const response = await fetch('/api/video-sources/', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      }
    })

    if (response.ok) {
      const data = await response.json()
      // 只显示WebSDK设备类型的视频源
      const websdkSources = data.filter((source: any) => source.source_type === 'websdk_device')

      // 转换数据格式以适配WebSDK登录需求
      videoSources.value = websdkSources.map((source: any) => ({
        id: source.id.toString(),
        name: source.name || (source.description + ' - ' + source.device_ip),
        description: source.description || '',
        ip: source.device_ip,
        port: source.device_port || 80,
        username: source.device_username || 'admin',
        password: source.device_password || '',
        protocol: source.device_protocol || 'HTTP',
        channelId: source.channel_id || 1,
        streamType: source.stream_type || 1
      }))

      console.log('获取到WebSDK视频源列表:', videoSources.value)
      ElMessage.success(`已加载 ${videoSources.value.length} 个WebSDK设备`)
      addOperationInfo(`[API] 成功加载 ${videoSources.value.length} 个WebSDK设备`)
    } else {
      console.error('获取视频源失败:', response.status, response.statusText)

      if (response.status === 401) {
        ElMessage.warning('请先登录系统后再获取视频源列表')
        addOperationInfo('[WARNING] 请先登录系统后再获取视频源列表')
      } else {
        ElMessage.error('获取视频源列表失败')
        addOperationInfo('[ERROR] 获取视频源列表失败')
        videoSources.value = []
      }
    }
  } catch (error) {
    console.error('刷新视频源失败:', error)
    ElMessage.error('网络错误，无法获取视频源列表')
    addOperationInfo('[ERROR] 网络错误，无法获取视频源列表')

    addOperationInfo('[INFO] 网络错误，已提供临时测试设备')
  }
}

// 视频源选择变化处理 - 自动执行完整流程
const onVideoSourceChange = async () => {
  if (!selectedVideoSource.value) {
    addOperationInfo('[SOURCE] 清空视频源选择')
    return
  }

  const source = videoSources.value.find(s => s.id === selectedVideoSource.value)
  if (!source) {
    addOperationInfo('[ERROR] 未找到选择的视频源')
    return
  }

  console.log('选择视频源:', selectedVideoSource.value)
  addOperationInfo(`[SOURCE] 选择视频源: ${source.name} (${source.ip}:${source.port})`)
  addOperationInfo('[AUTO] 开始自动化流程: 登录设备 → 获取通道 → 开始预览')

  // 如果当前正在预览，先停止
  if (isPreviewActive.value) {
    addOperationInfo('[AUTO] 检测到正在预览，先停止当前预览')
    stopPreview()
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  // 自动开始预览
  setTimeout(() => {
    startPreview()
  }, 100)
}

// 设备登录 - 完全基于WebSDKLogin组件的正确实现
const loginDevice = async (source: any): Promise<boolean> => {
  return new Promise((resolve) => {
    const ctrl = getWebVideoCtrl()
    if (!ctrl) {
      addOperationInfo('[ERROR] WebSDK未初始化，无法登录设备')
      resolve(false)
      return
    }

    const szIP = source.ip
    const szPort = source.port
    const szUsername = source.username
    const szPassword = source.password
    const szDeviceIdentify = `${szIP}_${szPort}`
    const protocol = source.protocol === 'HTTPS' ? 2 : 1

    addOperationInfo(`[LOGIN] 正在连接设备: ${szIP}:${szPort} (${source.protocol})`)

    // 保存登录信息
    sessionStorage.setItem('loginip', szIP)
    sessionStorage.setItem('password', szPassword)
    localStorage.setItem('currentWebSDKDevice', JSON.stringify({
      ip: szIP,
      port: szPort,
      username: szUsername,
      password: szPassword
    }))

    // 调用WebSDK登录API - 完全基于demo.js的实现
    const iRet = ctrl.I_Login(szIP, protocol, parseInt(szPort, 10), szUsername, szPassword, {
      success: (xmlDoc: any) => {
        console.log('设备登录成功:', xmlDoc)
        addOperationInfo(`[LOGIN] 设备登录成功: ${szIP}`)

        // 解析登录成功的XML响应
        try {
          const parser = new DOMParser()
          const doc = parser.parseFromString(xmlDoc, 'text/xml')
          const sessionID = doc.getElementsByTagName('sessionID')[0]?.textContent
          if (sessionID) {
            addOperationInfo(`[LOGIN] 获得会话ID: ${sessionID}`)
          }
        } catch (parseError) {
          console.warn('解析登录响应XML失败:', parseError)
        }

        currentDevice = {
          id: szDeviceIdentify,
          ip: szIP,
          port: szPort,
          username: szUsername,
          password: szPassword,
          protocol: source.protocol,
          xmlDoc
        }

        // 登录成功后获取通道信息和设备端口
        addOperationInfo('[LOGIN] 登录成功，正在获取通道信息...')
        setTimeout(() => {
          getChannelInfo(szDeviceIdentify, source.channelId)
        }, 100) // 增加延迟确保登录完全完成

        addOperationInfo('[LOGIN] 设备连接完成，可以开始预览')
        resolve(true)
      },
      error: (status: any, xmlDoc: any) => {
        console.error('设备登录失败:', status, xmlDoc)

        // 解析错误信息 - 基于demo.js的错误处理
        let errorMsg = `登录失败 (状态码: ${status})`
        try {
          if (xmlDoc) {
            const parser = new DOMParser()
            const doc = parser.parseFromString(xmlDoc, 'text/xml')
            const statusString = doc.getElementsByTagName('statusString')[0]?.textContent
            const subStatusCode = doc.getElementsByTagName('subStatusCode')[0]?.textContent

            if (statusString) {
              errorMsg = `登录失败: ${statusString}`
            }
            if (subStatusCode) {
              errorMsg += ` (子状态码: ${subStatusCode})`
            }
          }
        } catch (parseError) {
          console.warn('解析错误响应XML失败:', parseError)
        }

        addOperationInfo(`[ERROR] ${errorMsg}`)
        resolve(false)
      }
    })

    if (iRet === -1) {
      addOperationInfo(`[LOGIN] 设备 ${szDeviceIdentify} 已登录过！`)
      resolve(true)
    }
  })
}

// 获取通道信息 - 完全基于WebSDKLogin组件的正确实现
const getChannelInfo = (deviceIdentify: string, targetChannelId: number) => {
  const ctrl = getWebVideoCtrl()
  if (!ctrl) {
    addOperationInfo('[ERROR] WebSDK不可用，无法获取通道信息')
    return
  }

  let hasFoundChannel = false
  addOperationInfo('[CHANNEL] 正在获取通道信息（优先选择模拟通道）...')

  // 获取模拟通道信息 - 基于demo.js的实现
  ctrl.I_GetAnalogChannelInfo(deviceIdentify, {
    async: false,
    success: (xmlDoc: any) => {
      console.log('获取模拟通道信息成功:', xmlDoc)
      addOperationInfo('[CHANNEL] 成功获取模拟通道信息')

      if (!hasFoundChannel) {
        try {
          // 使用jQuery解析XML，与demo.js保持一致
          if (typeof $ !== 'undefined') {
            const $doc = $(xmlDoc)
            const channels = $doc.find('VideoInputChannel')

            addOperationInfo(`[CHANNEL] 找到 ${channels.length} 个模拟通道`)

            if (channels.length > 0) {
              // 查找指定通道或使用第一个通道
              let targetChannel = null
              channels.each(function(index) {
                const channelId = $(this).find('id').text()
                if (channelId && parseInt(channelId) === targetChannelId) {
                  targetChannel = $(this)
                  return false // 跳出循环
                }
              })

              if (!targetChannel) {
                targetChannel = channels.eq(0) // 使用第一个通道
              }

              if (targetChannel && targetChannel.length > 0) {
                const channelId = targetChannel.find('id').text()
                let channelName = targetChannel.find('name').text()

                // 如果名称为空，使用默认名称格式
                if (!channelName) {
                  const index = parseInt(channelId || '1') - 1
                  channelName = 'Camera ' + (index < 9 ? '0' + (index + 1) : (index + 1))
                }

                if (channelId) {
                  currentChannel = {
                    id: channelId,
                    name: channelName,
                    channelNo: channelId,
                    type: 'analog',
                    deviceId: deviceIdentify
                  }
                  hasFoundChannel = true
                  addOperationInfo(`[CHANNEL] 自动选择模拟通道: ${channelName} (ID: ${channelId})`)
                  console.log('自动选择模拟通道:', currentChannel)
                }
              }
            }
          } else {
            // 如果jQuery不可用，使用原生DOM解析
            const parser = new DOMParser()
            const doc = parser.parseFromString(xmlDoc, 'text/xml')
            const channels = doc.querySelectorAll('VideoInputChannel')

            addOperationInfo(`[CHANNEL] 找到 ${channels.length} 个模拟通道`)

            if (channels.length > 0) {
              let targetChannel = null
              for (const channel of channels) {
                const channelId = channel.querySelector('id')?.textContent
                if (channelId && parseInt(channelId) === targetChannelId) {
                  targetChannel = channel
                  break
                }
              }

              if (!targetChannel) {
                targetChannel = channels[0]
              }

              if (targetChannel) {
                const channelId = targetChannel.querySelector('id')?.textContent
                let channelName = targetChannel.querySelector('name')?.textContent

                if (!channelName) {
                  const index = parseInt(channelId || '1') - 1
                  channelName = 'Camera ' + (index < 9 ? '0' + (index + 1) : (index + 1))
                }

                if (channelId) {
                  currentChannel = {
                    id: channelId,
                    name: channelName,
                    channelNo: channelId,
                    type: 'analog',
                    deviceId: deviceIdentify
                  }
                  hasFoundChannel = true
                  addOperationInfo(`[CHANNEL] 自动选择模拟通道: ${channelName} (ID: ${channelId})`)
                  console.log('自动选择模拟通道:', currentChannel)
                }
              }
            }
          }
        } catch (error) {
          console.error('解析模拟通道信息失败:', error)
          addOperationInfo('[ERROR] 解析模拟通道信息失败')
        }
      }
    },
    error: (status: any, xmlDoc: any) => {
      console.warn('获取模拟通道信息失败:', { status, xmlDoc })
      addOperationInfo('[WARNING] 获取模拟通道信息失败')
    }
  })

  // 获取数字通道信息 - 基于demo.js的实现
  ctrl.I_GetDigitalChannelInfo(deviceIdentify, {
    async: false,
    success: (xmlDoc: any) => {
      console.log('获取数字通道信息成功:', xmlDoc)
      addOperationInfo('[CHANNEL] 成功获取数字通道信息')

      if (!hasFoundChannel) {
        try {
          // 使用jQuery解析XML，与demo.js保持一致 - 修复：使用正确的XML节点名称
          if (typeof $ !== 'undefined') {
            const $doc = $(xmlDoc)
            // 修复：数字通道的正确XML节点名称是 InputProxyChannelStatus
            const channels = $doc.find('InputProxyChannelStatus')

            addOperationInfo(`[CHANNEL] 找到 ${channels.length} 个数字通道`)

            if (channels.length > 0) {
              // 查找指定通道或使用第一个通道
              let targetChannel = null
              channels.each(function(index) {
                const channelId = $(this).find('id').text()
                const online = $(this).find('online').text() // 检查通道是否在线

                // 过滤离线的数字通道，与demo.js保持一致
                if (online === "false") {
                  return true // 继续下一个
                }

                if (channelId && parseInt(channelId) === targetChannelId) {
                  targetChannel = $(this)
                  return false // 跳出循环
                }
              })

              if (!targetChannel) {
                // 如果没找到指定通道，使用第一个在线通道
                channels.each(function(index) {
                  const online = $(this).find('online').text()
                  if (online !== "false") {
                    targetChannel = $(this)
                    return false // 跳出循环
                  }
                })
              }

              if (targetChannel && targetChannel.length > 0) {
                const channelId = targetChannel.find('id').text()
                let channelName = targetChannel.find('name').text()

                // 如果名称为空，使用默认名称格式，与demo.js保持一致
                if (!channelName) {
                  const index = parseInt(channelId || '1') - 1
                  channelName = 'IPCamera ' + (index < 9 ? '0' + (index + 1) : (index + 1))
                }

                if (channelId) {
                  currentChannel = {
                    id: channelId,
                    name: channelName,
                    channelNo: channelId,
                    type: 'digital',
                    deviceId: deviceIdentify
                  }
                  hasFoundChannel = true
                  addOperationInfo(`[CHANNEL] 自动选择数字通道: ${channelName} (ID: ${channelId})`)
                  console.log('自动选择数字通道:', currentChannel)
                }
              }
            }
          } else {
            // 如果jQuery不可用，使用原生DOM解析
            const parser = new DOMParser()
            const doc = parser.parseFromString(xmlDoc, 'text/xml')
            // 修复：使用正确的XML节点名称
            const channels = doc.querySelectorAll('InputProxyChannelStatus')

            addOperationInfo(`[CHANNEL] 找到 ${channels.length} 个数字通道`)

            if (channels.length > 0) {
              let targetChannel = null
              for (const channel of channels) {
                const channelId = channel.querySelector('id')?.textContent
                const online = channel.querySelector('online')?.textContent

                // 过滤离线的数字通道
                if (online === "false") {
                  continue
                }

                if (channelId && parseInt(channelId) === targetChannelId) {
                  targetChannel = channel
                  break
                }
              }

              if (!targetChannel) {
                // 使用第一个在线通道
                for (const channel of channels) {
                  const online = channel.querySelector('online')?.textContent
                  if (online !== "false") {
                    targetChannel = channel
                    break
                  }
                }
              }

              if (targetChannel) {
                const channelId = targetChannel.querySelector('id')?.textContent
                let channelName = targetChannel.querySelector('name')?.textContent

                if (!channelName) {
                  const index = parseInt(channelId || '1') - 1
                  channelName = 'IPCamera ' + (index < 9 ? '0' + (index + 1) : (index + 1))
                }

                if (channelId) {
                  currentChannel = {
                    id: channelId,
                    name: channelName,
                    channelNo: channelId,
                    type: 'digital',
                    deviceId: deviceIdentify
                  }
                  hasFoundChannel = true
                  addOperationInfo(`[CHANNEL] 自动选择数字通道: ${channelName} (ID: ${channelId})`)
                  console.log('自动选择数字通道:', currentChannel)
                }
              }
            }
          }
        } catch (error) {
          console.error('解析数字通道信息失败:', error)
          addOperationInfo('[ERROR] 解析数字通道信息失败')
        }
      }
    },
    error: (status: any, xmlDoc: any) => {
      console.warn('获取数字通道信息失败:', { status, xmlDoc })
      addOperationInfo('[WARNING] 获取数字通道信息失败')
    }
  })

  // 获取零通道信息（如果需要）
  ctrl.I_GetZeroChannelInfo(deviceIdentify, {
    async: false,
    success: (xmlDoc: any) => {
      console.log('获取零通道信息成功:', xmlDoc)
      addOperationInfo('[CHANNEL] 成功获取零通道信息')
    },
    error: (status: any, xmlDoc: any) => {
      console.warn('获取零通道信息失败:', { status, xmlDoc })
    }
  })

  // 如果没有找到任何通道，使用默认值
  setTimeout(() => {
    if (!hasFoundChannel) {
      const defaultChannelId = targetChannelId || 1
      currentChannel = {
        id: defaultChannelId.toString(),
        name: '默认通道' + defaultChannelId,
        channelNo: defaultChannelId.toString(),
        type: 'default',
        deviceId: deviceIdentify
      }
      addOperationInfo('[CHANNEL] 未找到通道信息，使用默认通道: ' + defaultChannelId)
      console.log('使用默认通道:', currentChannel)
    }
    addOperationInfo('[CHANNEL] 通道配置完成，当前选择通道: ' + (currentChannel?.name || '未知'))
  }, 300) // 增加延迟确保所有通道查询完成
}

// 开始预览 - 完全基于WebSDKPlayer组件的正确实现
const startPreview = async () => {
  if (!selectedVideoSource.value) {
    ElMessage.warning('请先选择视频源')
    addOperationInfo('[WARNING] 请先选择视频源')
    return
  }

  const source = videoSources.value.find(s => s.id === selectedVideoSource.value)
  if (!source) {
    ElMessage.warning('视频源不存在')
    addOperationInfo('[ERROR] 视频源不存在')
    return
  }

  isConnecting.value = true
  addOperationInfo('[PREVIEW] 开始连接设备...')

  try {
    // 1. 登录设备
    addOperationInfo('[PREVIEW] 正在登录设备...')
    const loginSuccess = await loginDevice(source)
    if (!loginSuccess) {
      isConnecting.value = false
      addOperationInfo('[ERROR] 设备登录失败，无法开始预览')
      return
    }

    // 等待通道信息获取完成 - 增加等待时间确保通道信息完全获取
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (!currentDevice || !currentChannel) {
      addOperationInfo('[ERROR] 获取设备通道信息失败，无法开始预览')
      isConnecting.value = false
      return
    }

    // 2. 开始预览
    const ctrl = getWebVideoCtrl()
    if (!ctrl) {
      addOperationInfo('[ERROR] WebSDK不可用，无法开始预览')
      isConnecting.value = false
      return
    }

    // 2. 开始预览
    const szDeviceIdentify = currentDevice.id
    const iChannelID = parseInt(currentChannel.channelNo || currentChannel.id)
    const iStreamType = 1 // 自动选择主码流
    const bZeroChannel = currentChannel.type === 'zero'

    // 关键修复：先获取设备端口信息，与demo.js保持一致
    addOperationInfo('[PREVIEW] 正在获取设备端口信息...')
    const oPort = ctrl.I_GetDevicePort(szDeviceIdentify)
    let iRtspPort = parseInt(source.rtspPort || '554', 10) // 默认值

    if (oPort != null) {
      iRtspPort = oPort.iRtspPort || iRtspPort
      addOperationInfo(`[PREVIEW] 获取设备端口成功: RTSP端口=${iRtspPort}, 设备端口=${oPort.iDevicePort}`)
    } else {
      addOperationInfo(`[PREVIEW] 获取设备端口失败，使用默认RTSP端口: ${iRtspPort}`)
    }

    addOperationInfo(`[PREVIEW] 开始播放通道 ${iChannelID} (${currentChannel.name}) - 主码流`)

    // 定义预览函数，严格按照编程指南的参数结构
    const startRealPlay = () => {
      // 根据编程指南：明确传递所有参数，使用正确的参数名称
      addOperationInfo('[PREVIEW] 尝试直连模式 (bProxy: false)...')

      ctrl.I_StartRealPlay(szDeviceIdentify, {
        iWndIndex: g_iWndIndex,    // 明确指定播放窗口
        iStreamType: iStreamType,   // 码流类型
        iChannelID: iChannelID,     // 播放通道号
        bZeroChannel: bZeroChannel, // 是否播放零通道
        iRtspPort: iRtspPort,      // 修复：使用demo.js中的正确参数名 iRtspPort
        bProxy: false,             // 先尝试直连模式
        success: () => {
          console.log('直连模式预览成功')
          addOperationInfo('[PREVIEW] ✅ 直连模式预览成功')
          isPreviewActive.value = true
          isConnecting.value = false
          ElMessage.success('预览成功（直连模式）')
        },
        error: (status: any, xmlDoc: any) => {
          console.error('直连模式失败:', { status, xmlDoc })

          // 详细的错误信息解析
          let errorDetail = `状态码: ${status || 'undefined'}`
          if (xmlDoc) {
            try {
              const parser = new DOMParser()
              const doc = parser.parseFromString(xmlDoc, 'text/xml')
              const statusString = doc.getElementsByTagName('statusString')[0]?.textContent
              const subStatusCode = doc.getElementsByTagName('subStatusCode')[0]?.textContent
              if (statusString) errorDetail += `, 错误: ${statusString}`
              if (subStatusCode) errorDetail += `, 子状态: ${subStatusCode}`
            } catch (e) {
              console.warn('解析错误XML失败:', e)
            }
          }

          addOperationInfo(`[PREVIEW] ❌ 直连模式失败: ${errorDetail}`)

          // 特殊处理undefined状态码
          if (status === undefined || status === null) {
            addOperationInfo('[ERROR] 状态码为undefined，可能的原因：')
            addOperationInfo('[ERROR] 1. 设备不支持WebSocket协议')
            addOperationInfo('[ERROR] 2. 设备固件版本过低')
            addOperationInfo('[ERROR] 3. 网络连接问题或防火墙阻止')
            addOperationInfo('[ERROR] 4. 设备WebSocket服务未启用')
            addOperationInfo('[INFO] 建议：检查设备型号和固件版本，确认是否支持WebSocket')
          }

          if (status === 403 || status === undefined) {
            // 根据编程指南：某些设备在HTTP下也需要代理模式
            addOperationInfo('[PREVIEW] 设备不支持直连，尝试代理模式 (bProxy: true)...')
            addOperationInfo('[INFO] 根据编程指南：某些设备在HTTP环境下需要代理模式')

            ctrl.I_StartRealPlay(szDeviceIdentify, {
              iWndIndex: g_iWndIndex,    // 明确指定播放窗口
              iStreamType: iStreamType,   // 码流类型
              iChannelID: iChannelID,     // 播放通道号
              bZeroChannel: bZeroChannel, // 是否播放零通道
              iRtspPort: iRtspPort,      // 修复：使用demo.js中的正确参数名 iRtspPort
              bProxy: true, // 使用代理模式
              success: () => {
                console.log('代理模式预览成功')
                addOperationInfo('[PREVIEW] ✅ 代理模式预览成功')
                addOperationInfo('[INFO] 建议在nginx配置中添加WebSocket代理规则')
                isPreviewActive.value = true
                isConnecting.value = false
                ElMessage.success('预览成功（代理模式）')
              },
              error: (status2: any, xmlDoc2: any) => {
                console.error('代理模式也失败:', { status2, xmlDoc2 })

                // 解析代理模式错误
                let proxyErrorDetail = `状态码: ${status2}`
                if (xmlDoc2) {
                  try {
                    const parser = new DOMParser()
                    const doc = parser.parseFromString(xmlDoc2, 'text/xml')
                    const statusString = doc.getElementsByTagName('statusString')[0]?.textContent
                    if (statusString) proxyErrorDetail += `, 错误: ${statusString}`
                  } catch (e) {
                    console.warn('解析代理错误XML失败:', e)
                  }
                }

                addOperationInfo(`[ERROR] 代理模式也失败: ${proxyErrorDetail}`)
                addOperationInfo('[ERROR] 设备不支持WebSocket取流，请检查以下项目：')
                addOperationInfo('[ERROR] 1. 设备固件版本是否支持WebSocket')
                addOperationInfo('[ERROR] 2. 设备网络配置是否正确')
                addOperationInfo('[ERROR] 3. 是否需要nginx代理配置')
                addOperationInfo('[ERROR] 4. 防火墙是否阻止了WebSocket连接')
                ElMessage.error('设备不支持WebSocket取流')
                isConnecting.value = false
              }
            })
          } else {
            addOperationInfo(`[ERROR] 预览失败: ${errorDetail}`)
            addOperationInfo('[ERROR] 请检查设备连接和网络配置')
            ElMessage.error(`预览失败: ${status}`)
            isConnecting.value = false
          }
        }
      })
    }

    // 检查当前窗口状态，如果正在播放则先停止，与demo.js保持一致
    const currentWndInfo = ctrl.I_GetWindowStatus(g_iWndIndex)
    if (currentWndInfo && currentWndInfo.bPlaying) {
      addOperationInfo('[PREVIEW] 当前窗口正在播放，先停止...')
      ctrl.I_Stop({
        success: () => {
          addOperationInfo('[PREVIEW] 停止成功，开始新的预览')
          startRealPlay()
        },
        error: () => {
          addOperationInfo('[ERROR] 停止当前播放失败')
          isConnecting.value = false
        }
      })
    } else {
      startRealPlay()
    }



  } catch (error) {
    console.error('开始预览异常:', error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    addOperationInfo(`[ERROR] 开始预览异常: ${errorMessage}`)
    ElMessage.error(`开始预览失败: ${errorMessage}`)
    isConnecting.value = false
  }
}

// 停止预览 - 完全基于WebSDKPlayer组件的正确实现
const stopPreview = () => {
  const ctrl = getWebVideoCtrl()
  if (!ctrl) {
    ElMessage.error('WebSDK未初始化')
    addOperationInfo('[ERROR] WebSDK未初始化，无法停止预览')
    return
  }

  try {
    addOperationInfo('[PREVIEW] 正在停止预览...')

    // 检查当前窗口状态
    const currentWndInfo = ctrl.I_GetWindowStatus(g_iWndIndex)
    if (currentWndInfo && currentWndInfo.bPlaying) {
      addOperationInfo('[PREVIEW] 检测到正在播放的视频，正在停止...')

      // 停止预览 - 基于demo.js的实现
      const iRet = ctrl.I_Stop()

      if (iRet === 0) {
        addOperationInfo('[PREVIEW] 视频预览已停止')
        ElMessage.success('视频预览已停止')
      } else {
        addOperationInfo(`[WARNING] 停止预览返回码: ${iRet}`)
        ElMessage.success('视频预览已停止')
      }
    } else {
      addOperationInfo('[PREVIEW] 当前没有正在播放的视频')
      ElMessage.info('当前没有正在播放的视频')
    }

    isPreviewActive.value = false
    addOperationInfo('[PREVIEW] 预览停止完成')
  } catch (error) {
    console.error('停止预览失败:', error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    ElMessage.error(`停止预览失败: ${errorMessage}`)
    addOperationInfo(`[ERROR] 停止预览失败: ${errorMessage}`)

    // 强制重置状态
    isPreviewActive.value = false
  }
}


// 运动检测相关方法
const toggleMotionDetection = () => {
  if (showMotionDetection.value) {
    stopMotionDetection()
  } else {
    startMotionDetection()
  }
}

const startMotionDetection = async () => {
  if (!selectedVideoSource.value) {
    ElMessage.warning('请先选择视频源')
    return
  }

  try {
    addOperationInfo('[MOTION] 启动运动检测...')

    // 获取当前视频源信息
    const source = videoSources.value.find(s => s.id === selectedVideoSource.value)
    if (!source) {
      ElMessage.error('未找到视频源信息')
      return
    }

    // 构建RTSP URL
    const rtspUrl = `rtsp://${source.username}:${source.password}@${source.ip}:${source.rtspPort || 554}/Streaming/Channels/101`

    // 启动后端运动检测
    const response = await fetch('/api/rtsp/connect', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        stream_id: `motion_${source.id}`,
        url: rtspUrl,
        buffer_size: 3,
        timeout: 5000,
        enable_multi_thread: true
      })
    })

    if (response.ok) {
      // 连接WebSocket接收检测结果
      connectMotionDetectionWS()
      showMotionDetection.value = true
      addOperationInfo('[MOTION] 运动检测已启动')
      ElMessage.success('运动检测已启动')
    } else {
      throw new Error('启动运动检测失败')
    }
  } catch (error) {
    console.error('启动运动检测失败:', error)
    addOperationInfo('[ERROR] 启动运动检测失败')
    ElMessage.error('启动运动检测失败')
  }
}

const stopMotionDetection = async () => {
  try {
    addOperationInfo('[MOTION] 停止运动检测...')

    // 断开WebSocket连接
    if (motionDetectionWS) {
      motionDetectionWS.close()
      motionDetectionWS = null
    }

    // 停止后端运动检测
    const source = videoSources.value.find(s => s.id === selectedVideoSource.value)
    if (source) {
      await fetch(`/api/rtsp/disconnect/${source.id}`, {
        method: 'POST'
      })
    }

    showMotionDetection.value = false
    detectionResult.value = null
    addOperationInfo('[MOTION] 运动检测已停止')
    ElMessage.success('运动检测已停止')
  } catch (error) {
    console.error('停止运动检测失败:', error)
    addOperationInfo('[ERROR] 停止运动检测失败')
  }
}

const connectMotionDetectionWS = () => {
  const wsUrl = `ws://localhost:8000/ws/video-detection`
  motionDetectionWS = new WebSocket(wsUrl)

  motionDetectionWS.onopen = () => {
    addOperationInfo('[MOTION] WebSocket连接已建立')
  }

  motionDetectionWS.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      if (data.type === 'detection_result') {
        detectionResult.value = data.data
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
    }
  }

  motionDetectionWS.onclose = () => {
    addOperationInfo('[MOTION] WebSocket连接已断开')
  }

  motionDetectionWS.onerror = (error) => {
    console.error('WebSocket错误:', error)
    addOperationInfo('[ERROR] WebSocket连接错误')
  }
}



// ROI属性选择变化
const onROIAttributeChange = () => {
  if (selectedROIAttribute.value) {
    // 选择属性后自动启用多边形绘制
    isDrawingEnabled.value = true
    if (roiDrawerInstance) {
      roiDrawerInstance.setCurrentAttribute(selectedROIAttribute.value)
      roiDrawerInstance.setDrawMode('polygon')
      roiDrawerInstance.setEnabled(true)
    }
    addOperationInfo(`[ROI] 选择${getAttributeDisplayName(selectedROIAttribute.value)}属性，启用多边形绘制`)
  } else {
    isDrawingEnabled.value = false
    if (roiDrawerInstance) {
      roiDrawerInstance.setCurrentAttribute(null)
      roiDrawerInstance.setEnabled(false)
    }
  }
}

// ROI绘制控制方法
const toggleDrawMode = () => {
  if (!selectedROIAttribute.value) {
    ElMessage.warning('请先选择ROI属性')
    return
  }

  isDrawingEnabled.value = !isDrawingEnabled.value
  if (roiDrawerInstance) {
    roiDrawerInstance.setEnabled(isDrawingEnabled.value)
    if (isDrawingEnabled.value) {
      roiDrawerInstance.setDrawMode('polygon')
    }
  }

  if (isDrawingEnabled.value) {
    addOperationInfo(`[ROI] 开始绘制${getAttributeDisplayName(selectedROIAttribute.value)}`)
  } else {
    addOperationInfo('[ROI] 绘制已暂停')
  }
}

const clearROIs = () => {
  roiList.value = []
  highlightedROIId.value = null
  if (roiDrawerInstance) {
    roiDrawerInstance.clearROIs()
    roiDrawerInstance.clearHighlight()
  }
  addOperationInfo('[ROI] 已清空所有ROI区域')
  ElMessage.success('已清空所有ROI区域')
}

// 清空指定属性的ROI
const clearAttributeROIs = (attribute: 'pailiao' | 'yazhu') => {
  const beforeCount = roiList.value.length

  // 检查是否要删除当前高亮的ROI
  const highlightedROI = roiList.value.find(roi => roi.roi_id === highlightedROIId.value)
  if (highlightedROI && highlightedROI.attribute === attribute) {
    highlightedROIId.value = null
    if (roiDrawerInstance) {
      roiDrawerInstance.clearHighlight()
    }
  }

  roiList.value = roiList.value.filter(roi => roi.attribute !== attribute)
  const afterCount = roiList.value.length
  const deletedCount = beforeCount - afterCount

  if (roiDrawerInstance) {
    roiDrawerInstance.setROIs(roiList.value)
  }

  const attributeName = getAttributeDisplayName(attribute)
  addOperationInfo(`[ROI] 已清空${deletedCount}个${attributeName}ROI区域`)
  ElMessage.success(`已清空${deletedCount}个${attributeName}ROI区域`)
}

const saveROIs = async () => {
  if (roiList.value.length === 0) {
    ElMessage.warning('没有ROI区域可保存')
    return
  }

  await saveROIConfigToBackend()
  addOperationInfo(`[ROI] 保存 ${roiList.value.length} 个ROI区域`)
  ElMessage.success(`已保存 ${roiList.value.length} 个ROI区域`)
}



const deleteROI = (roiId: string) => {
  const index = roiList.value.findIndex(roi => roi.roi_id === roiId)
  if (index >= 0) {
    const roi = roiList.value[index]

    // 如果删除的是当前高亮的ROI，清除高亮状态
    if (highlightedROIId.value === roiId) {
      highlightedROIId.value = null
      if (roiDrawerInstance) {
        roiDrawerInstance.clearHighlight()
      }
    }

    roiList.value.splice(index, 1)
    addOperationInfo(`[ROI] 删除ROI区域: ${roi.name}`)

    // 使用ROI绘制器更新
    if (roiDrawerInstance) {
      roiDrawerInstance.setROIs(roiList.value)
    }
  }
}

const editROI = (roiId: string) => {
  const roi = roiList.value.find(roi => roi.roi_id === roiId)
  if (roi) {
    // TODO: 实现ROI编辑功能
    ElMessage.info('ROI编辑功能待实现')
    addOperationInfo(`[ROI] 编辑ROI: ${roi.name}`)
  }
}

// ROI高亮切换
const toggleROIHighlight = (roiId: string) => {
  if (!roiDrawerInstance) {
    console.warn('ROI绘制器未初始化')
    return
  }

  // 切换高亮状态
  if (highlightedROIId.value === roiId) {
    // 如果当前ROI已高亮，则清除高亮
    highlightedROIId.value = null
    roiDrawerInstance.clearHighlight()
    addOperationInfo('[ROI] 清除高亮显示')
  } else {
    // 高亮新的ROI
    highlightedROIId.value = roiId
    roiDrawerInstance.highlightROI(roiId)
    const roi = roiList.value.find(r => r.roi_id === roiId)
    addOperationInfo(`[ROI] 高亮显示: ${roi?.name || roiId}`)
  }
}

// 加载ROI列表到视频中显示
const loadROIListToVideo = (roiData: any[]) => {
  if (!roiDrawerInstance) {
    console.warn('ROI绘制器未初始化，无法加载ROI')
    return false
  }

  try {
    const success = roiDrawerInstance.loadROIList(roiData)
    if (success) {
      // 同步到组件的ROI列表
      roiList.value = [...roiDrawerInstance.rois]
      addOperationInfo(`[ROI] 成功加载 ${roiData.length} 个ROI到视频中`)
      ElMessage.success(`成功加载 ${roiData.length} 个ROI区域`)
      return true
    } else {
      addOperationInfo('[ROI] ROI加载失败')
      ElMessage.error('ROI加载失败')
      return false
    }
  } catch (error) {
    console.error('加载ROI到视频失败:', error)
    addOperationInfo(`[ERROR] 加载ROI失败: ${error}`)
    ElMessage.error('加载ROI失败')
    return false
  }
}

// 同步ROI列表到视频显示
const syncROIListToVideo = () => {
  if (!roiDrawerInstance) {
    console.warn('ROI绘制器未初始化，无法同步ROI')
    return false
  }

  try {
    const success = roiDrawerInstance.syncROIList(roiList.value)
    if (success) {
      addOperationInfo(`[ROI] 成功同步 ${roiList.value.length} 个ROI到视频显示`)
      return true
    } else {
      addOperationInfo('[ROI] ROI同步失败')
      return false
    }
  } catch (error) {
    console.error('同步ROI到视频失败:', error)
    addOperationInfo(`[ERROR] 同步ROI失败: ${error}`)
    return false
  }
}

// 添加单个ROI到视频显示
const addROIToVideo = (roi: any) => {
  if (!roiDrawerInstance) {
    console.warn('ROI绘制器未初始化，无法添加ROI')
    return false
  }

  try {
    const success = roiDrawerInstance.addROIToVideo(roi)
    if (success) {
      // 检查是否需要添加到组件列表
      const existingIndex = roiList.value.findIndex(r => r.roi_id === roi.roi_id)
      if (existingIndex >= 0) {
        roiList.value[existingIndex] = roi
      } else {
        roiList.value.push(roi)
      }
      addOperationInfo(`[ROI] 添加ROI到视频: ${roi.name || roi.roi_id}`)
      return true
    }
    return false
  } catch (error) {
    console.error('添加ROI到视频失败:', error)
    addOperationInfo(`[ERROR] 添加ROI失败: ${error}`)
    return false
  }
}

// 从视频中移除ROI
const removeROIFromVideo = (roiId: string) => {
  if (!roiDrawerInstance) {
    console.warn('ROI绘制器未初始化，无法移除ROI')
    return false
  }

  try {
    const success = roiDrawerInstance.removeROIFromVideo(roiId)
    if (success) {
      // 从组件列表中移除
      const index = roiList.value.findIndex(r => r.roi_id === roiId)
      if (index >= 0) {
        const removedROI = roiList.value.splice(index, 1)[0]
        addOperationInfo(`[ROI] 从视频中移除ROI: ${removedROI.name || roiId}`)
      }

      // 清除高亮状态
      if (highlightedROIId.value === roiId) {
        highlightedROIId.value = null
      }

      return true
    }
    return false
  } catch (error) {
    console.error('从视频中移除ROI失败:', error)
    addOperationInfo(`[ERROR] 移除ROI失败: ${error}`)
    return false
  }
}

// 测试ROI加载功能
const testROILoad = () => {
  console.log('开始测试ROI加载功能')
  addOperationInfo('[TEST] 开始测试ROI加载功能')

  // 创建测试ROI数据
  const testROIs = [
    {
      id: 'test_pailiao_1',
      name: '测试排料口1',
      type: 'polygon',
      attribute: 'pailiao',
      color: '#00ffff',
      points: [
        { x: 100, y: 100 },
        { x: 200, y: 100 },
        { x: 200, y: 150 },
        { x: 100, y: 150 }
      ]
    },
    {
      id: 'test_yazhu_1',
      name: '测试压铸机1',
      type: 'polygon',
      attribute: 'yazhu',
      color: '#ff0000',
      points: [
        { x: 300, y: 200 },
        { x: 400, y: 200 },
        { x: 400, y: 280 },
        { x: 300, y: 280 }
      ]
    }
  ]

  console.log('测试ROI数据:', testROIs)

  // 确保ROI绘制器已初始化
  if (!roiDrawerInstance) {
    console.log('ROI绘制器未初始化，先初始化')
    addOperationInfo('[TEST] 初始化ROI绘制器')

    nextTick(() => {
      if (!roiDrawerInstance) {
        initROIDrawer()
      }

      nextTick(() => {
        if (roiDrawerInstance) {
          performTestLoad(testROIs)
        } else {
          console.error('ROI绘制器初始化失败')
          addOperationInfo('[ERROR] ROI绘制器初始化失败')
        }
      })
    })
  } else {
    performTestLoad(testROIs)
  }

  function performTestLoad(rois: any[]) {
    console.log('执行测试加载，绘制器状态:', {
      instance: !!roiDrawerInstance,
      canvas: !!roiDrawerInstance?.canvas,
      ctx: !!roiDrawerInstance?.ctx,
      canvasSize: {
        width: roiDrawerInstance?.canvas?.width,
        height: roiDrawerInstance?.canvas?.height
      }
    })

    // 先清空现有ROI
    roiList.value = []
    if (roiDrawerInstance) {
      roiDrawerInstance.clearROIs()
    }

    // 加载测试ROI
    const success = loadROIListToVideo(rois)
    if (success) {
      addOperationInfo('[TEST] 测试ROI加载成功')
      ElMessage.success('测试ROI加载成功')
    } else {
      addOperationInfo('[TEST] 测试ROI加载失败')
      ElMessage.error('测试ROI加载失败')
    }
  }
}

// 手动初始化ROI绘制器
const manualInitROI = () => {
  console.log('手动初始化ROI绘制器')
  addOperationInfo('[ROI] 手动初始化ROI绘制器')

  // 检查DOM状态
  console.log('DOM状态检查:', {
    roiDisplayCanvas: !!roiDisplayCanvas,
    roiDisplayCanvasValue: !!roiDisplayCanvas.value,
    roiDrawerInstance: !!roiDrawerInstance
  })

  if (roiDrawerInstance) {
    console.log('ROI绘制器已存在，重新初始化')
    roiDrawerInstance.destroy()
    roiDrawerInstance = null
  }

  // 强制等待DOM更新
  nextTick(() => {
    setTimeout(() => {
      initROIDrawer()
      if (roiDrawerInstance) {
        addOperationInfo('[ROI] 手动初始化成功')
        ElMessage.success('ROI绘制器初始化成功')
      } else {
        addOperationInfo('[ERROR] 手动初始化失败')
        ElMessage.error('ROI绘制器初始化失败')
      }
    }, 100)
  })
}

const saveROIConfigToBackend = async () => {
  try {
    if (roiList.value.length === 0) return

    // 保存ROI配置到后端
    const source = videoSources.value.find(s => s.id === selectedVideoSource.value)
    if (source) {
      const response = await fetch(`/api/detection-groups/1/rois`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rois: roiList.value.map(roi => ({
            name: roi.name,
            roi_type: roi.roi_type,
            coordinates: roi.coordinates || roi.points, // 🔥 修复：使用数据库字段名
            color: roi.color
          }))
        })
      })

      if (response.ok) {
        addOperationInfo('[ROI] ROI配置已保存到后端')
      }
    }
  } catch (error) {
    console.error('保存ROI配置失败:', error)
    addOperationInfo('[ERROR] 保存ROI配置失败')
  }
}

// ROI导入导出功能
const exportROIs = () => {
  try {
    if (roiList.value.length === 0) {
      ElMessage.warning('没有ROI区域可导出')
      return
    }

    const exportData = {
      version: '2.0',
      timestamp: new Date().toISOString(),
      video_source_id: selectedVideoSource.value,
      video_resolution: { width: 640, height: 360 },
      rois: roiList.value,
      summary: {
        total: roiList.value.length,
        pailiao: pailiaoROIs.value.length,
        yazhu: yazhuROIs.value.length
      }
    }

    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })

    const link = document.createElement('a')
    link.href = URL.createObjectURL(dataBlob)
    link.download = `roi_config_${selectedVideoSource.value}_${Date.now()}.json`
    link.click()

    addOperationInfo(`[ROI] 已导出 ${roiList.value.length} 个ROI区域配置`)
    ElMessage.success('ROI配置导出成功')
  } catch (error) {
    console.error('导出ROI配置失败:', error)
    addOperationInfo('[ERROR] 导出ROI配置失败')
    ElMessage.error('导出ROI配置失败')
  }
}

const importROIs = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'

  input.onchange = (event: any) => {
    const file = event.target.files[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e: any) => {
      try {
        const importData = JSON.parse(e.target.result)

        if (!importData.rois || !Array.isArray(importData.rois)) {
          throw new Error('无效的ROI配置文件格式')
        }

        // 验证和修复ROI数据
        const validROIs = importData.rois.filter((roi: any) => {
          // 确保每个ROI都有必要的属性
          if (!roi.attribute) {
            roi.attribute = 'pailiao' // 默认为排料口
          }
          return roi.points && Array.isArray(roi.points) && roi.points.length > 0
        })

        roiList.value = validROIs

        // 确保ROI绘制器已初始化，然后加载ROI到视频中显示
        if (!roiDrawerInstance) {
          console.log('ROI绘制器未初始化，先初始化绘制器')
          addOperationInfo('[ROI] 初始化ROI绘制器以显示导入的ROI')

          // 延迟初始化，确保画布已渲染
          nextTick(() => {
            if (!roiDrawerInstance) {
              initROIDrawer()
            }

            // 再次延迟，确保初始化完成
            nextTick(() => {
              if (roiDrawerInstance) {
                loadROIToDrawer(validROIs)
              }
            })
          })
        } else {
          loadROIToDrawer(validROIs)
        }

        // 加载ROI到绘制器的辅助函数
        function loadROIToDrawer(rois: any[]) {
          console.log('开始加载ROI到视频，ROI绘制器状态:', {
            instance: !!roiDrawerInstance,
            canvas: !!roiDrawerInstance?.canvas,
            ctx: !!roiDrawerInstance?.ctx
          })
          console.log('要加载的ROI数据:', rois)

          const success = roiDrawerInstance!.loadROIList(rois)
          if (success) {
            addOperationInfo('[ROI] ROI已加载到视频显示中')
            console.log('ROI加载成功')
          } else {
            addOperationInfo('[ROI] ROI加载到视频显示失败，使用备用方法')
            console.log('ROI加载失败，尝试备用方法')
            roiDrawerInstance!.setROIs(rois)
          }
        }

        const pailiaoCount = validROIs.filter((roi: any) => roi.attribute === 'pailiao').length
        const yazhuCount = validROIs.filter((roi: any) => roi.attribute === 'yazhu').length

        addOperationInfo(`[ROI] 已导入 ${validROIs.length} 个ROI区域配置 (排料口:${pailiaoCount}, 压铸机:${yazhuCount})`)
        ElMessage.success(`成功导入 ${validROIs.length} 个ROI区域`)
      } catch (error) {
        console.error('导入ROI配置失败:', error)
        addOperationInfo('[ERROR] 导入ROI配置失败')
        ElMessage.error('导入ROI配置失败，请检查文件格式')
      }
    }

    reader.readAsText(file)
  }

  input.click()
}





// 组件挂载
onMounted(async () => {
  try {
    // 检查WebSDK脚本是否已加载
    if (!(window as any).WebVideoCtrl) {
      addOperationInfo('[ERROR] WebSDK脚本未加载，请检查index.html中的脚本引用')
      ElMessage.error('WebSDK脚本未加载，请检查配置')
      return
    }

    // 检查jQuery是否已加载
    if (typeof $ === 'undefined') {
      addOperationInfo('[WARNING] jQuery未加载，将使用原生DOM解析XML')
    } else {
      addOperationInfo('[INFO] jQuery已加载，将使用jQuery解析XML')
    }

    await initWebSDK()
    await refreshVideoSources()

    // 延迟初始化ROI绘制器，确保DOM已渲染
    nextTick(() => {
      setTimeout(() => {
        console.log('尝试初始化ROI绘制器...')
        initROIDrawer()
      }, 1000) // 延迟1秒确保视频容器已完全渲染
    })
  } catch (error) {
    console.error('初始化失败:', error)
    addOperationInfo(`[ERROR] 初始化失败: ${error instanceof Error ? error.message : String(error)}`)
    ElMessage.error('初始化失败，请检查WebSDK配置')
  }
})

// 监听ROI列表变化，自动同步到视频显示
watch(roiList, (newROIList) => {
  if (roiDrawerInstance && newROIList.length > 0) {
    // 延迟同步，避免频繁更新
    nextTick(() => {
      syncROIListToVideo()
    })
  }
}, { deep: true })

// 组件卸载
onUnmounted(() => {
  // 清理ROI绘制器
  if (roiDrawerInstance) {
    roiDrawerInstance.destroy()
    roiDrawerInstance = null
  }

  // 清理WebSDK
  if (webVideoCtrl && isWebSDKInitialized) {
    try {
      webVideoCtrl.I_DestroyPlugin()
    } catch (error) {
      console.error('销毁WebSDK失败:', error)
    }
  }
})
</script>

<style>
/* 导入分离的CSS文件 - 移除scoped以确保CSS变量正确继承 */
@import './video-preview/VideoPreviewView.css';
</style>
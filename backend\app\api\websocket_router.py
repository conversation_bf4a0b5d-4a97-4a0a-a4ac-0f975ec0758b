from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from typing import Dict, Any
import json
from datetime import datetime

from app.services.websocket_manager import WebSocketConnectionManager
from app.services.video_detection import VideoDetectionManager
from .message_handlers import MessageHandler

router = APIRouter()

# WebSocket连接管理器
connection_manager = WebSocketConnectionManager()

# 检测实例存储
detection_instances: Dict[str, Any] = {}

# 消息处理器
message_handler = MessageHandler(connection_manager, detection_instances)

@router.websocket("/ws/video-detection/{client_id}")
async def websocket_detection_endpoint(websocket: WebSocket, client_id: str):
    """处理视频检测WebSocket连接"""
    await connection_manager.connect(websocket, client_id)
    
    # 创建检测器实例
    from app.services.video_detection import MotionDetector, DirectionDetector, FrameDifferencer, VideoDetectionManager
    
    # 创建视频检测管理器
    detection_manager = VideoDetectionManager()
    
    # 初始化检测器
    motion_detector = detection_manager.get_detector(f"{client_id}_motion", "motion", {
        'minArea': 100, 
        'detectionThreshold': 40,
        'learningRate': 0.005,
        'shadowsThreshold': 0.5
    })
    
    frame_differencer = detection_manager.get_detector(f"{client_id}_frame_diff", "frame_difference", {
        'threshold': 30,
        'minArea': 100,
        'frameInterval': 2
    })
    
    direction_detector = detection_manager.get_detector(f"{client_id}_direction", "direction", {
        'detector_type': 'background_subtraction',
        'motion_params': {
            'minArea': 100, 
            'detectionThreshold': 30,
            'learningRate': 0.003,
            'shadowsThreshold': 0.5
        },
        'minDisplacement': 2,
        'maxPatience': 3,
        'consecutiveThreshold': 3
    })
    
    # 保存检测实例
    detection_instances[client_id] = {
        'detection_manager': detection_manager,
        'motion_detector': motion_detector,
        'frame_differencer': frame_differencer,
        'direction_detector': direction_detector,
        'active': False,
        'mode': 'motion',
        'detection_algorithm': 'background_subtraction',  # 默认使用背景减除法
        'last_frame_time': datetime.now(),
        'roi_detectors': {}  # 每个ROI的单独检测器
    }
    
    try:
        # 发送连接建立消息
        await connection_manager.send_personal_message(
            json.dumps({
                "type": "connection_established",
                "client_id": client_id,
                "message": "Connected to detection server"
            }),
            websocket
        )
        
        # 处理消息
        while True:
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                await message_handler.process_detection_message(message, websocket, client_id)
                
            except json.JSONDecodeError:
                await connection_manager.send_personal_message(
                    json.dumps({"type": "error", "message": "Invalid JSON"}),
                    websocket
                )
                
            except Exception as e:
                await connection_manager.send_personal_message(
                    json.dumps({"type": "error", "message": str(e)}),
                    websocket
                )
    
    except WebSocketDisconnect:
        connection_manager.disconnect(websocket)
        
        # 清理资源
        if client_id in detection_instances:
            del detection_instances[client_id]
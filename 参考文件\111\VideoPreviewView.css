/* VideoPreviewView 组件样式文件 */

.video-preview-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 视频源选择区域 */
.video-source-section {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
}

.source-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.source-controls label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.source-select {
  min-width: 300px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.source-select:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.refresh-btn {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.refresh-btn:hover:not(:disabled) {
  background: #337ecc;
}

.refresh-btn:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

/* 视频预览区域 */
.video-preview-section {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: center;
}

.video-container {
  border: 2px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
  position: relative; /* 为叠加层提供定位基准 */
}

.video-player {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

/* 控制按钮区域 */
.control-section {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 16px;
}

.control-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.control-btn.primary {
  background: #409eff;
  color: white;
}

.control-btn.primary:hover:not(:disabled) {
  background: #337ecc;
  transform: translateY(-1px);
}

.control-btn.danger {
  background: #f56c6c;
  color: white;
}

.control-btn.danger:hover:not(:disabled) {
  background: #f24c4c;
  transform: translateY(-1px);
}

.control-btn.secondary {
  background: #909399;
  color: white;
}

.control-btn.secondary:hover:not(:disabled) {
  background: #767a82;
  transform: translateY(-1px);
}

.control-btn:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
  transform: none;
}

/* 操作信息区域 */
.operation-info-section {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.info-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.clear-btn {
  padding: 6px 12px;
  background: #909399;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-btn:hover {
  background: #767a82;
}

.info-content {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.info-item {
  margin-bottom: 4px;
  display: flex;
  gap: 8px;
}

.info-time {
  color: #666;
  white-space: nowrap;
}

.info-message {
  color: #333;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-preview-page {
    padding: 16px;
  }
  
  .source-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .source-select {
    min-width: auto;
    width: 100%;
  }
  
  .control-section {
    flex-direction: column;
  }
  
  .control-btn {
    width: 100%;
  }
}

/* 运动检测相关样式 */
.control-btn.active {
  background: #28a745;
  border-color: #28a745;
  color: white;
}

.control-btn.active:hover {
  background: #218838;
  border-color: #1e7e34;
}

.motion-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

/* ROI相关样式 */
.roi-toolbar-section {
  margin-bottom: 20px;
}

.roi-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  color: white;
}

.toolbar-title h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.toolbar-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.tool-btn {
  padding: 8px 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  backdrop-filter: blur(10px);
}

.tool-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.tool-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border-color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.tool-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.clear-btn {
  background: rgba(220, 53, 69, 0.8);
  border-color: rgba(220, 53, 69, 0.8);
}

.clear-btn:hover:not(:disabled) {
  background: rgba(220, 53, 69, 1);
}

.save-btn {
  background: rgba(40, 167, 69, 0.8);
  border-color: rgba(40, 167, 69, 0.8);
}

.save-btn:hover:not(:disabled) {
  background: rgba(40, 167, 69, 1);
}

.export-btn {
  background: rgba(23, 162, 184, 0.8);
  border-color: rgba(23, 162, 184, 0.8);
}

.export-btn:hover:not(:disabled) {
  background: rgba(23, 162, 184, 1);
}

.import-btn {
  background: rgba(108, 117, 125, 0.8);
  border-color: rgba(108, 117, 125, 0.8);
}

.import-btn:hover:not(:disabled) {
  background: rgba(108, 117, 125, 1);
}

.toggle-btn.active {
  background: rgba(255, 193, 7, 0.9);
  color: #000;
  border-color: rgba(255, 193, 7, 0.9);
}

/* ROI显示画布 */
.roi-display-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 15;
  pointer-events: none;
}

/* 视频容器在绘制模式下的样式 */
.video-container[style*="cursor"] {
  cursor: crosshair !important;
}

/* ROI管理面板 */
.roi-management-section {
  margin-top: 20px;
}

.roi-list-panel {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.panel-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.panel-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.status-disabled {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

.status-locked {
  background: rgba(255, 193, 7, 0.1);
  color: #856404;
}

.status-ready {
  background: rgba(23, 162, 184, 0.1);
  color: #0c5460;
}

.status-drawing {
  background: rgba(40, 167, 69, 0.1);
  color: #155724;
}

.panel-content {
  padding: 20px;
}

.roi-list {
  margin-bottom: 20px;
  max-height: 300px;
  overflow-y: auto;
}

.empty-message {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 10px;
  opacity: 0.5;
}

.empty-text p {
  margin: 0 0 5px 0;
  color: #6c757d;
  font-size: 16px;
  font-weight: 500;
}

.empty-text small {
  color: #adb5bd;
  font-size: 12px;
}

.roi-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin: 8px 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.roi-item:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.roi-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.roi-color {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 3px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.roi-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.roi-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.roi-type {
  color: #7f8c8d;
  font-size: 11px;
  padding: 2px 6px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 10px;
  display: inline-block;
}

.roi-points {
  color: #95a5a6;
  font-size: 10px;
}

.roi-actions {
  display: flex;
  gap: 6px;
}

.edit-btn, .delete-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.edit-btn {
  background: rgba(52, 152, 219, 0.8);
  color: white;
}

.edit-btn:hover {
  background: rgba(52, 152, 219, 1);
  transform: scale(1.1);
}

.delete-btn {
  background: rgba(231, 76, 60, 0.8);
  color: white;
}

.delete-btn:hover {
  background: rgba(231, 76, 60, 1);
  transform: scale(1.1);
}

.help-section {
  margin-top: 20px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  overflow: hidden;
}

.help-header {
  padding: 12px 16px;
  background: rgba(52, 152, 219, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.help-content {
  padding: 16px;
}

.help-content ol {
  margin: 0;
  padding-left: 20px;
  color: #7f8c8d;
  font-size: 13px;
  line-height: 1.6;
}

.help-content li {
  margin-bottom: 4px;
}
<template>
  <div class="performance-panel">
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon><odometer /></el-icon>
        性能监控
      </h3>
    </div>

    <div class="panel-content">
      <!-- 系统资源监控 -->
      <div class="resource-section">
        <h4 class="section-title">系统资源</h4>
        <div class="resource-grid">
          <div class="resource-item">
            <div class="resource-header">
              <el-icon><cpu /></el-icon>
              <span>CPU</span>
            </div>
            <div class="resource-progress">
              <el-progress 
                :percentage="performanceData.cpuUsage" 
                :color="getProgressColor(performanceData.cpuUsage)"
                :show-text="false"
              />
              <span class="progress-text">{{ performanceData.cpuUsage }}%</span>
            </div>
          </div>

          <div class="resource-item">
            <div class="resource-header">
              <el-icon><memory-card /></el-icon>
              <span>内存</span>
            </div>
            <div class="resource-progress">
              <el-progress 
                :percentage="performanceData.memoryUsage" 
                :color="getProgressColor(performanceData.memoryUsage)"
                :show-text="false"
              />
              <span class="progress-text">{{ performanceData.memoryUsage }}%</span>
            </div>
          </div>

          <div class="resource-item">
            <div class="resource-header">
              <el-icon><lightning /></el-icon>
              <span>GPU</span>
            </div>
            <div class="resource-progress">
              <el-progress 
                :percentage="performanceData.gpuUsage" 
                :color="getProgressColor(performanceData.gpuUsage)"
                :show-text="false"
              />
              <span class="progress-text">{{ performanceData.gpuUsage }}%</span>
            </div>
          </div>

          <div class="resource-item">
            <div class="resource-header">
              <el-icon><folder /></el-icon>
              <span>磁盘</span>
            </div>
            <div class="resource-progress">
              <el-progress 
                :percentage="performanceData.diskUsage" 
                :color="getProgressColor(performanceData.diskUsage)"
                :show-text="false"
              />
              <span class="progress-text">{{ performanceData.diskUsage }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 网络监控 -->
      <div class="network-section">
        <h4 class="section-title">网络状态</h4>
        <div class="network-stats">
          <div class="network-item">
            <div class="network-icon upload">
              <el-icon><upload /></el-icon>
            </div>
            <div class="network-info">
              <div class="network-label">上传</div>
              <div class="network-value">{{ formatBytes(performanceData.networkOut) }}/s</div>
            </div>
          </div>
          <div class="network-item">
            <div class="network-icon download">
              <el-icon><download /></el-icon>
            </div>
            <div class="network-info">
              <div class="network-label">下载</div>
              <div class="network-value">{{ formatBytes(performanceData.networkIn) }}/s</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 检测性能 -->
      <div class="detection-section">
        <h4 class="section-title">检测性能</h4>
        <div class="detection-metrics">
          <div class="metric-item">
            <div class="metric-icon">
              <el-icon><timer /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-label">处理速度</div>
              <div class="metric-value">{{ performanceData.detectionFps }} FPS</div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-icon">
              <el-icon><clock /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-label">平均延迟</div>
              <div class="metric-value">{{ performanceData.processingLatency }} ms</div>
            </div>
          </div>
        </div>
      </div>

      <!-- GPU加速状态 -->
      <div class="gpu-section">
        <h4 class="section-title">GPU加速</h4>
        <div class="gpu-status">
          <div class="gpu-info">
            <div class="gpu-indicator" :class="{ active: gpuAcceleration.enabled }"></div>
            <div class="gpu-details">
              <div class="gpu-name">{{ gpuAcceleration.deviceName }}</div>
              <div class="gpu-memory">
                {{ formatBytes(gpuAcceleration.memoryUsed * 1024 * 1024) }} / 
                {{ formatBytes(gpuAcceleration.totalMemory * 1024 * 1024) }}
              </div>
            </div>
          </div>
          <div class="gpu-usage">
            <el-progress 
              type="circle" 
              :percentage="gpuAcceleration.usage"
              :width="60"
              :color="getProgressColor(gpuAcceleration.usage)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Odometer, Cpu, Lightning, Folder, Upload, Download,
  Timer, Clock, MemoryCard
} from '@element-plus/icons-vue'

// Props
interface PerformanceData {
  cpuUsage: number
  memoryUsage: number
  gpuUsage: number
  diskUsage: number
  networkIn: number
  networkOut: number
  detectionFps: number
  processingLatency: number
}

const props = defineProps<{
  performanceData: PerformanceData
}>()

// 模拟GPU加速数据
const gpuAcceleration = ref({
  enabled: true,
  deviceName: 'NVIDIA GeForce RTX 3080',
  usage: 78,
  memoryUsed: 6144,
  totalMemory: 8192
})

// 方法
const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}
</script>

<style scoped>
.performance-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-color);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.panel-content {
  flex: 1;
  padding: 16px 20px;
  overflow-y: auto;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 12px 0;
}

.resource-section {
  margin-bottom: 24px;
}

.resource-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.resource-item {
  padding: 12px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

.resource-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--text-color-soft);
}

.resource-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.resource-progress .el-progress {
  flex: 1;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-color);
  min-width: 35px;
  text-align: right;
}

.network-section {
  margin-bottom: 24px;
}

.network-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.network-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

.network-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

.network-icon.upload {
  background-color: #e74c3c;
}

.network-icon.download {
  background-color: #3498db;
}

.network-info {
  flex: 1;
}

.network-label {
  font-size: 12px;
  color: var(--text-color-mute);
  margin-bottom: 2px;
}

.network-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
}

.detection-section {
  margin-bottom: 24px;
}

.detection-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

.metric-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 12px;
  color: var(--text-color-mute);
  margin-bottom: 2px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
}

.gpu-section {
  margin-bottom: 24px;
}

.gpu-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

.gpu-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.gpu-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--danger-color);
}

.gpu-indicator.active {
  background-color: var(--success-color);
  animation: pulse 2s infinite;
}

.gpu-details {
  flex: 1;
}

.gpu-name {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 2px;
}

.gpu-memory {
  font-size: 11px;
  color: var(--text-color-mute);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>

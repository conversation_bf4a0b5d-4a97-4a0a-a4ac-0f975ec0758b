"""
WebSDK代理端点
用于代理前端到设备的ISAPI请求，解决跨域问题
"""
from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import Response
import httpx
import asyncio
from typing import Any, Dict
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# 创建HTTP客户端
http_client = httpx.AsyncClient(
    timeout=30.0,
    verify=False,  # 忽略SSL证书验证
    follow_redirects=True
)

@router.api_route(
    "/websdk-proxy/{device_ip}/{device_port:int}/{path:path}",
    methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
)
async def websdk_proxy(
    device_ip: str,
    device_port: int,
    path: str,
    request: Request
):
    """
    WebSDK代理端点
    将前端的ISAPI请求代理到实际的设备
    """
    try:
        # 构建目标URL
        protocol = "https" if device_port == 443 else "http"
        target_url = f"{protocol}://{device_ip}:{device_port}/{path}"
        
        # 获取请求参数
        query_params = dict(request.query_params)
        
        # 获取请求体
        body = None
        if request.method in ["POST", "PUT"]:
            body = await request.body()
        
        # 获取请求头，过滤掉一些不需要的头
        headers = {}
        for key, value in request.headers.items():
            if key.lower() not in [
                'host', 'content-length', 'connection', 
                'upgrade-insecure-requests', 'user-agent',
                'accept-encoding', 'accept-language'
            ]:
                headers[key] = value
        
        # 添加必要的头
        headers.update({
            'User-Agent': 'WebSDK-Proxy/1.0',
            'Accept': '*/*',
        })
        
        logger.info(f"代理请求: {request.method} {target_url}")
        logger.debug(f"请求头: {headers}")
        logger.debug(f"查询参数: {query_params}")
        
        # 发送请求到设备
        response = await http_client.request(
            method=request.method,
            url=target_url,
            params=query_params,
            content=body,
            headers=headers
        )
        
        logger.info(f"设备响应: {response.status_code}")
        
        # 构建响应头
        response_headers = {}
        for key, value in response.headers.items():
            if key.lower() not in ['content-encoding', 'transfer-encoding', 'connection']:
                response_headers[key] = value
        
        # 添加CORS头
        response_headers.update({
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': '*',
            'Access-Control-Expose-Headers': '*'
        })
        
        # 返回响应
        return Response(
            content=response.content,
            status_code=response.status_code,
            headers=response_headers,
            media_type=response.headers.get('content-type', 'application/octet-stream')
        )
        
    except httpx.TimeoutException:
        logger.error(f"请求超时: {target_url}")
        raise HTTPException(status_code=408, detail="设备请求超时")
    except httpx.ConnectError:
        logger.error(f"连接失败: {target_url}")
        raise HTTPException(status_code=503, detail="无法连接到设备")
    except Exception as e:
        logger.error(f"代理请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"代理请求失败: {str(e)}")

@router.options("/websdk-proxy/{device_ip}/{device_port:int}/{path:path}")
async def websdk_proxy_options(device_ip: str, device_port: int, path: str):
    """
    处理CORS预检请求
    """
    return Response(
        status_code=200,
        headers={
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': '*',
            'Access-Control-Max-Age': '86400'
        }
    )

# 清理资源
@router.on_event("shutdown")
async def shutdown_event():
    await http_client.aclose()

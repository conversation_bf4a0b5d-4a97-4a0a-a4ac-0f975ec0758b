<!DOCTYPE html>
<html>
<head>
    <title>网络访问测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .info {
            color: blue;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>压铸件智能检测系统 - 网络访问测试</h1>
    
    <div class="test-section">
        <h2>当前访问信息</h2>
        <div id="access-info"></div>
    </div>
    
    <div class="test-section">
        <h2>API连接测试</h2>
        <button onclick="testAPI()">测试API连接</button>
        <div id="api-results"></div>
    </div>
    
    <div class="test-section">
        <h2>WebSocket连接测试</h2>
        <button onclick="testWebSocket()">测试WebSocket连接</button>
        <div id="ws-results"></div>
    </div>
    
    <div class="test-section">
        <h2>登录功能测试</h2>
        <button onclick="testLogin()">测试登录API</button>
        <div id="login-results"></div>
    </div>
    
    <div class="test-section">
        <h2>系统状态</h2>
        <button onclick="runAllTests()">运行所有测试</button>
        <div id="system-status"></div>
    </div>
    
    <script>
        // 显示当前访问信息
        function showAccessInfo() {
            const accessInfo = document.getElementById('access-info');
            const hostname = window.location.hostname;
            const port = window.location.port;
            const protocol = window.location.protocol;
            
            accessInfo.innerHTML = `
                <p><strong>当前访问地址:</strong> ${window.location.href}</p>
                <p><strong>主机名:</strong> ${hostname}</p>
                <p><strong>端口:</strong> ${port}</p>
                <p><strong>协议:</strong> ${protocol}</p>
                <p class="info"><strong>访问类型:</strong> ${hostname === 'localhost' || hostname === '127.0.0.1' ? '本地访问' : '网络访问'}</p>
            `;
        }
        
        // 获取动态API地址
        function getApiUrl() {
            const hostname = window.location.hostname;
            return hostname === 'localhost' || hostname === '127.0.0.1' 
                ? 'http://localhost:8000/api' 
                : `http://${hostname}:8000/api`;
        }
        
        // 获取动态WebSocket地址
        function getWebSocketUrl() {
            const hostname = window.location.hostname;
            return hostname === 'localhost' || hostname === '127.0.0.1' 
                ? 'ws://localhost:8000/ws/video-detection/test-client' 
                : `ws://${hostname}:8000/ws/video-detection/test-client`;
        }
        
        // 测试API连接
        async function testAPI() {
            const results = document.getElementById('api-results');
            const apiUrl = getApiUrl();
            
            results.innerHTML = `<p class="info">正在测试API地址: ${apiUrl}</p>`;
            
            try {
                const response = await fetch(`${apiUrl}/`);
                if (response.ok) {
                    const data = await response.json();
                    results.innerHTML += `<p class="success">✅ API连接成功</p>`;
                    results.innerHTML += `<p>响应消息: ${data.message}</p>`;
                } else {
                    results.innerHTML += `<p class="error">❌ API响应错误: ${response.status} ${response.statusText}</p>`;
                }
            } catch (error) {
                results.innerHTML += `<p class="error">❌ API连接失败: ${error.message}</p>`;
                results.innerHTML += `<p class="info">可能原因: 后端服务未启动或网络连接问题</p>`;
            }
        }
        
        // 测试WebSocket连接
        function testWebSocket() {
            const results = document.getElementById('ws-results');
            const wsUrl = getWebSocketUrl();
            
            results.innerHTML = `<p class="info">正在测试WebSocket地址: ${wsUrl}</p>`;
            
            const ws = new WebSocket(wsUrl);
            let connected = false;
            
            // 设置超时
            const timeout = setTimeout(() => {
                if (!connected) {
                    ws.close();
                    results.innerHTML += `<p class="error">❌ WebSocket连接超时</p>`;
                }
            }, 5000);
            
            ws.onopen = () => {
                connected = true;
                clearTimeout(timeout);
                results.innerHTML += `<p class="success">✅ WebSocket连接成功</p>`;
                ws.close();
            };
            
            ws.onclose = (event) => {
                if (connected) {
                    results.innerHTML += `<p class="info">WebSocket连接已关闭 (代码: ${event.code})</p>`;
                }
            };
            
            ws.onerror = (error) => {
                clearTimeout(timeout);
                results.innerHTML += `<p class="error">❌ WebSocket连接失败</p>`;
                results.innerHTML += `<p class="info">可能原因: 后端WebSocket服务未启动或端口被阻止</p>`;
            };
        }
        
        // 测试登录API
        async function testLogin() {
            const results = document.getElementById('login-results');
            const apiUrl = getApiUrl();
            
            results.innerHTML = `<p class="info">正在测试登录API...</p>`;
            
            try {
                const response = await fetch(`${apiUrl}/auth/login/json/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'test',
                        password: 'test'
                    })
                });
                
                if (response.ok) {
                    results.innerHTML += `<p class="success">✅ 登录API端点可访问</p>`;
                    results.innerHTML += `<p class="info">注意: 这是测试连接，不是真实登录</p>`;
                } else {
                    results.innerHTML += `<p class="success">✅ 登录API端点可访问 (状态码: ${response.status})</p>`;
                    results.innerHTML += `<p class="info">API正常响应，可以进行登录操作</p>`;
                }
            } catch (error) {
                results.innerHTML += `<p class="error">❌ 登录API连接失败: ${error.message}</p>`;
            }
        }
        
        // 运行所有测试
        async function runAllTests() {
            const statusDiv = document.getElementById('system-status');
            statusDiv.innerHTML = '<p class="info">正在运行所有测试...</p>';
            
            // 清空之前的结果
            document.getElementById('api-results').innerHTML = '';
            document.getElementById('ws-results').innerHTML = '';
            document.getElementById('login-results').innerHTML = '';
            
            // 依次运行测试
            await testAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testWebSocket();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            await testLogin();
            
            // 显示总结
            statusDiv.innerHTML = '<p class="success">✅ 所有测试已完成，请查看上方各项测试结果</p>';
        }
        
        // 页面加载时显示访问信息
        window.onload = function() {
            showAccessInfo();
        };
    </script>
</body>
</html>
/**
 * ROI数据验证工具
 * 用于验证ROI数据的完整性和格式正确性
 */

export interface ROIValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  summary: {
    totalROIs: number
    validROIs: number
    yazhuROIs: number
    pailiaoROIs: number
  }
}

export interface ROIData {
  roi_id: string
  id?: string
  name?: string
  attribute: 'yazhu' | 'pailiao'
  roi_type?: string
  type?: string
  coordinates?: Array<{x: number, y: number}>
  points?: Array<{x: number, y: number}>
  color?: string
  params?: any
}

/**
 * 验证单个ROI数据
 */
export function validateSingleROI(roi: ROIData): { isValid: boolean, errors: string[], warnings: string[] } {
  const errors: string[] = []
  const warnings: string[] = []

  // 必需字段检查
  if (!roi.roi_id) {
    errors.push('缺少roi_id字段')
  }

  if (!roi.attribute) {
    errors.push('缺少attribute字段')
  } else if (!['yazhu', 'pailiao'].includes(roi.attribute)) {
    errors.push(`attribute字段值无效: ${roi.attribute}，应为 'yazhu' 或 'pailiao'`)
  }

  // 坐标数据检查
  const coordinates = roi.coordinates || roi.points
  if (!coordinates || coordinates.length === 0) {
    errors.push('缺少坐标数据')
  } else if (coordinates.length < 3) {
    warnings.push('坐标点数量少于3个，可能影响ROI区域定义')
  }

  // 参数检查
  if (!roi.params) {
    warnings.push('缺少params参数配置')
  } else {
    // 检查参数类型
    if (!roi.params.type) {
      warnings.push('params中缺少type字段')
    } else {
      const expectedType = roi.attribute === 'yazhu' ? 'direction' : 'motion'
      if (roi.params.type !== expectedType) {
        warnings.push(`参数类型不匹配: 期望${expectedType}，实际${roi.params.type}`)
      }
    }
  }

  // 可选字段检查
  if (!roi.name) {
    warnings.push('建议设置name字段')
  }

  if (!roi.color) {
    warnings.push('建议设置color字段')
  }

  // 后端兼容性检查
  if (!roi.id) {
    warnings.push('建议设置id字段以确保后端兼容性')
  }

  if (!roi.type && !roi.roi_type) {
    warnings.push('建议设置type字段以确保后端兼容性')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 验证ROI列表
 */
export function validateROIList(roiList: ROIData[]): ROIValidationResult {
  const errors: string[] = []
  const warnings: string[] = []
  let validROIs = 0
  let yazhuROIs = 0
  let pailiaoROIs = 0

  if (!roiList || roiList.length === 0) {
    errors.push('ROI列表为空')
    return {
      isValid: false,
      errors,
      warnings,
      summary: {
        totalROIs: 0,
        validROIs: 0,
        yazhuROIs: 0,
        pailiaoROIs: 0
      }
    }
  }

  // 验证每个ROI
  roiList.forEach((roi, index) => {
    const validation = validateSingleROI(roi)
    
    if (validation.isValid) {
      validROIs++
      if (roi.attribute === 'yazhu') yazhuROIs++
      if (roi.attribute === 'pailiao') pailiaoROIs++
    }

    // 添加索引信息到错误和警告
    validation.errors.forEach(error => {
      errors.push(`ROI[${index}] ${roi.roi_id || 'unknown'}: ${error}`)
    })
    
    validation.warnings.forEach(warning => {
      warnings.push(`ROI[${index}] ${roi.roi_id || 'unknown'}: ${warning}`)
    })
  })

  // ROI类型验证
  if (yazhuROIs.length === 0) {
    warnings.push('建议至少有一个压铸机ROI（yazhu类型）')
  }
  
  if (pailiaoROIs.length === 0) {
    warnings.push('建议至少有一个排料口ROI（pailiao类型）')
  }

  // 检查重复ID
  const roiIds = roiList.map(roi => roi.roi_id).filter(Boolean)
  const duplicateIds = roiIds.filter((id, index) => roiIds.indexOf(id) !== index)
  if (duplicateIds.length > 0) {
    errors.push(`发现重复的ROI ID: ${duplicateIds.join(', ')}`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    summary: {
      totalROIs: roiList.length,
      validROIs,
      yazhuROIs,
      pailiaoROIs
    }
  }
}

/**
 * 生成ROI数据报告
 */
export function generateROIReport(roiList: ROIData[]): string {
  const validation = validateROIList(roiList)
  const timestamp = new Date().toLocaleString('zh-CN')

  let report = `=== ROI数据验证报告 ===\n`
  report += `生成时间: ${timestamp}\n`
  report += `总体状态: ${validation.isValid ? '✅ 通过' : '❌ 失败'}\n\n`

  // 统计信息
  report += `【统计信息】\n`
  report += `总ROI数量: ${validation.summary.totalROIs}\n`
  report += `有效ROI数量: ${validation.summary.validROIs}\n`
  report += `压铸机ROI: ${validation.summary.yazhuROIs}\n`
  report += `排料口ROI: ${validation.summary.pailiaoROIs}\n\n`

  // 错误信息
  if (validation.errors.length > 0) {
    report += `【错误信息】\n`
    validation.errors.forEach((error, index) => {
      report += `${index + 1}. ❌ ${error}\n`
    })
    report += `\n`
  }

  // 警告信息
  if (validation.warnings.length > 0) {
    report += `【警告信息】\n`
    validation.warnings.forEach((warning, index) => {
      report += `${index + 1}. ⚠️ ${warning}\n`
    })
    report += `\n`
  }

  // 建议
  report += `【建议】\n`
  if (validation.summary.yazhuROIs === 0) {
    report += `• 建议添加至少一个压铸机ROI（yazhu类型）\n`
  }
  if (validation.summary.pailiaoROIs === 0) {
    report += `• 建议添加排料口ROI（pailiao类型）\n`
  }
  if (validation.summary.validROIs < validation.summary.totalROIs) {
    report += `• 请修复无效的ROI数据以确保检测功能正常工作\n`
  }
  if (validation.warnings.length > 0) {
    report += `• 建议处理上述警告以提高系统稳定性\n`
  }

  report += `\n=== 报告结束 ===`

  return report
}

/**
 * 修复ROI数据格式
 */
export function fixROIDataFormat(roi: ROIData): ROIData {
  const fixed: ROIData = { ...roi }

  // 确保必需字段
  if (!fixed.id && fixed.roi_id) {
    fixed.id = fixed.roi_id
  }

  if (!fixed.name) {
    fixed.name = `ROI_${fixed.roi_id || 'unknown'}`
  }

  if (!fixed.type && !fixed.roi_type) {
    fixed.type = 'polygon'
  }

  if (!fixed.color) {
    fixed.color = fixed.attribute === 'yazhu' ? '#ff0000' : '#00ff00'
  }

  // 统一坐标格式
  if (fixed.coordinates && !fixed.points) {
    fixed.points = fixed.coordinates
  } else if (fixed.points && !fixed.coordinates) {
    fixed.coordinates = fixed.points
  }

  // 确保参数配置
  if (!fixed.params) {
    if (fixed.attribute === 'yazhu') {
      fixed.params = {
        type: 'direction',
        motion_detection: {
          enabled: true,
          backgroundUpdateRate: 0.01,
          motionThreshold: 50,
          minArea: 500
        },
        direction_detection: {
          consecutiveDetectionThreshold: 3,
          minDisplacement: 2,
          maxPatience: 3
        }
      }
    } else {
      fixed.params = {
        type: 'motion',
        motion_detection: {
          algorithm: 'frame_difference',
          threshold: 30,
          frameInterval: 2,
          minArea: 300
        }
      }
    }
  }

  return fixed
}

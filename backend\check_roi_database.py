#!/usr/bin/env python3
"""
ROI数据库检查脚本
检查ROI配置相关的数据库表和数据
"""

import sqlite3
import json
import os
from pathlib import Path

def check_database():
    """检查数据库状态"""
    print("🔍 检查ROI数据库状态...")
    
    # 查找数据库文件
    possible_db_paths = [
        "die_casting_detection.db",
        "app.db",
        "app/die_casting_detection.db",
        "../die_casting_detection.db"
    ]
    
    db_path = None
    for path in possible_db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ 未找到数据库文件")
        print("💡 请确保数据库文件存在于以下位置之一:")
        for path in possible_db_paths:
            print(f"   - {path}")
        return False
    
    print(f"✅ 找到数据库文件: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='roi_configs'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ roi_configs表不存在")
            print("💡 请运行数据库迁移脚本创建表")
            return False
        
        print("✅ roi_configs表存在")
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(roi_configs)")
        columns = cursor.fetchall()
        
        print("\n📊 roi_configs表结构:")
        for column in columns:
            cid, name, type_, notnull, default_value, pk = column
            pk_str = " (主键)" if pk else ""
            notnull_str = " NOT NULL" if notnull else ""
            default_str = f" DEFAULT {default_value}" if default_value else ""
            print(f"  - {name}: {type_}{notnull_str}{default_str}{pk_str}")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM roi_configs")
        count = cursor.fetchone()[0]
        print(f"\n📈 数据统计: 共 {count} 条ROI配置记录")
        
        if count > 0:
            # 显示示例数据
            cursor.execute("SELECT roi_id, name, attribute, roi_type, is_active FROM roi_configs LIMIT 5")
            records = cursor.fetchall()
            print("\n📄 示例数据:")
            for record in records:
                roi_id, name, attribute, roi_type, is_active = record
                status = "激活" if is_active else "禁用"
                print(f"  - {roi_id}: {name} ({attribute}/{roi_type}) - {status}")
        
        # 检查字段完整性
        required_fields = ['roi_id', 'name', 'attribute', 'roi_type', 'coordinates', 'algorithm_type', 'algorithm_params']
        existing_fields = [col[1] for col in columns]
        missing_fields = [field for field in required_fields if field not in existing_fields]
        
        if missing_fields:
            print(f"\n⚠️ 缺少必要字段: {', '.join(missing_fields)}")
            print("💡 请运行数据库迁移脚本更新表结构")
        else:
            print("\n✅ 所有必要字段都存在")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ 数据库错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def test_roi_operations():
    """测试ROI操作"""
    print("\n🧪 测试ROI数据库操作...")
    
    db_path = None
    possible_db_paths = [
        "die_casting_detection.db",
        "app.db",
        "app/die_casting_detection.db",
        "../die_casting_detection.db"
    ]
    
    for path in possible_db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ 数据库文件不存在，跳过操作测试")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 测试插入
        test_roi = {
            'roi_id': 'test_check_001',
            'name': '数据库检查测试ROI',
            'attribute': 'yazhu',
            'roi_type': 'polygon',
            'color': '#ff0000',
            'coordinates': json.dumps([[100, 100], [200, 100], [200, 200], [100, 200]]),
            'algorithm_type': 'direction',
            'algorithm_params': json.dumps({
                'type': 'direction',
                'motion_detection': {
                    'enabled': True,
                    'backgroundUpdateRate': 0.01,
                    'motionThreshold': 50,
                    'minArea': 500
                },
                'direction_detection': {
                    'consecutiveDetectionThreshold': 3,
                    'minDisplacement': 2,
                    'maxPatience': 3
                }
            }),
            'video_source_id': 'test_video_source',
            'video_source_path': '/test/path',
            'is_active': True
        }
        
        # 删除可能存在的测试数据
        cursor.execute("DELETE FROM roi_configs WHERE roi_id = ?", (test_roi['roi_id'],))
        
        # 插入测试数据
        cursor.execute("""
            INSERT INTO roi_configs 
            (roi_id, name, attribute, roi_type, color, coordinates, algorithm_type, algorithm_params, video_source_id, video_source_path, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            test_roi['roi_id'], test_roi['name'], test_roi['attribute'], test_roi['roi_type'],
            test_roi['color'], test_roi['coordinates'], test_roi['algorithm_type'], 
            test_roi['algorithm_params'], test_roi['video_source_id'], test_roi['video_source_path'],
            test_roi['is_active']
        ))
        
        print("✅ 插入测试数据成功")
        
        # 查询测试数据
        cursor.execute("SELECT * FROM roi_configs WHERE roi_id = ?", (test_roi['roi_id'],))
        result = cursor.fetchone()
        
        if result:
            print("✅ 查询测试数据成功")
        else:
            print("❌ 查询测试数据失败")
        
        # 更新测试数据
        cursor.execute("UPDATE roi_configs SET name = ? WHERE roi_id = ?", 
                      ('更新后的测试ROI', test_roi['roi_id']))
        print("✅ 更新测试数据成功")
        
        # 删除测试数据
        cursor.execute("DELETE FROM roi_configs WHERE roi_id = ?", (test_roi['roi_id'],))
        print("✅ 删除测试数据成功")
        
        conn.commit()
        conn.close()
        
        print("✅ 所有数据库操作测试通过")
        return True
        
    except sqlite3.Error as e:
        print(f"❌ 数据库操作错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 操作测试错误: {e}")
        return False

def main():
    """主函数"""
    print("🚀 ROI数据库检查工具")
    print("=" * 50)
    
    # 检查数据库
    db_ok = check_database()
    
    if db_ok:
        # 测试操作
        test_roi_operations()
    
    print("\n" + "=" * 50)
    print("🏁 检查完成")
    
    if db_ok:
        print("✅ 数据库状态正常，可以进行ROI配置操作")
    else:
        print("❌ 数据库存在问题，请检查并修复")
        print("💡 建议操作:")
        print("   1. 确保数据库文件存在")
        print("   2. 运行数据库迁移脚本")
        print("   3. 检查表结构是否正确")

if __name__ == "__main__":
    main()

[{"id": 1, "name": "测试模板", "description": "测试模板", "die_casters": [{"id": 1, "name": "压铸机1", "detection_groups": [{"id": 1, "name": "11", "config": {"global_settings": {"pause_threshold": 15, "delay_time": 5, "cooldown_time": 3}, "rois": [{"id": "d22719d4-d3f5-47bc-959e-10075e363245", "name": "yazhu_d227", "roi_type": "yazhu", "shape": "Rectangle", "coordinates": [[172, 124], [484, 239]], "params": {"前置背景检测": {"enabled": true, "backgroundUpdateRate": 0.0, "motionThreshold": 1, "minArea": 10}, "后置方向检测": {"consecutiveDetectionThreshold": 1, "minDisplacement": 1, "maxPatience": 0}}}, {"id": "da299b2a-fb8f-4934-8820-6b43f781c97c", "name": "pailiao_da29", "roi_type": "pailiao", "shape": "Polygon", "coordinates": [[445, 928], [382, 1035], [461, 1068], [511, 958]], "params": {"运动检测": {"algorithm": "帧差法", "learningRate": 0.01, "detectionThreshold": 50, "shadowRemoval": 0.5, "threshold": 30, "frameInterval": 2, "minArea": 300}}}]}}, {"id": 2, "name": "22", "config": {"global_settings": {"pause_threshold": 15, "delay_time": 5, "cooldown_time": 3}, "rois": [{"id": "305f4bb4-2829-49f5-8c3d-2c01f3f98c88", "name": "yazhu_305f", "roi_type": "yazhu", "shape": "Rectangle", "coordinates": [[186, 121], [517, 218]], "params": {"前置背景检测": {"enabled": true, "backgroundUpdateRate": 0.0, "motionThreshold": 1, "minArea": 10}, "后置方向检测": {"consecutiveDetectionThreshold": 1, "minDisplacement": 1, "maxPatience": 0}}}, {"id": "68a2029d-fda1-40cd-ba32-96b529e6a9f7", "name": "pailiao_68a2", "roi_type": "pailiao", "shape": "Polygon", "coordinates": [[442, 929], [386, 1039], [458, 1073], [512, 960]], "params": {"运动检测": {"algorithm": "帧差法", "learningRate": 0.01, "detectionThreshold": 50, "shadowRemoval": 0.5, "threshold": 30, "frameInterval": 2, "minArea": 300}}}]}}]}, {"id": 2, "name": "压铸机2", "detection_groups": [{"id": 3, "name": "33", "config": {}}, {"id": 4, "name": "44", "config": {}}]}, {"id": 3, "name": "压铸机3", "detection_groups": [{"id": 5, "name": "111", "config": {"global_settings": {"pause_threshold": 15, "delay_time": 5, "cooldown_time": 3}, "rois": [{"id": "4836a125-a680-4046-bce0-b27e56058390", "name": "yazhu_4836", "roi_type": "yazhu", "shape": "Rectangle", "coordinates": [[351, 170], [845, 290]], "params": {"前置背景检测": {"enabled": true, "backgroundUpdateRate": 0.01, "motionThreshold": 50, "minArea": 500}, "后置方向检测": {"consecutiveDetectionThreshold": 3, "minDisplacement": 2, "maxPatience": 3}}}, {"id": "590e3829-ffc3-4acf-a1a9-9a7bb8a8a373", "name": "pailiao_590e", "roi_type": "pailiao", "shape": "Polygon", "coordinates": [[954, 948], [913, 1010], [1019, 1054], [1060, 982]], "params": {"运动检测": {"algorithm": "帧差法", "learningRate": 0.01, "detectionThreshold": 50, "shadowRemoval": 0.5, "threshold": 30, "frameInterval": 2, "minArea": 300}}}, {"id": "ff724dd5-7c0c-427a-a86b-918b958fb6e4", "name": "pailiao_ff72", "roi_type": "pailiao", "shape": "Polygon", "coordinates": [[920, 1057], [879, 1119], [848, 1173], [951, 1207], [974, 1136]], "params": {"运动检测": {"algorithm": "帧差法", "learningRate": 0.01, "detectionThreshold": 50, "shadowRemoval": 0.5, "threshold": 30, "frameInterval": 2, "minArea": 300}}}, {"id": "f6f9cc21-a15e-4938-9ad0-46116c5859c2", "name": "pailiao_f6f9", "roi_type": "pailiao", "shape": "Polygon", "coordinates": [[821, 1262], [736, 1375], [835, 1412], [893, 1310]], "params": {"运动检测": {"algorithm": "帧差法", "learningRate": 0.01, "detectionThreshold": 50, "shadowRemoval": 0.5, "threshold": 30, "frameInterval": 2, "minArea": 300}}}]}}, {"id": 6, "name": "222", "config": {"global_settings": {"pause_threshold": 15, "delay_time": 5, "cooldown_time": 3}, "rois": [{"id": "bc589073-d4bc-42f9-b91a-2fe751856c64", "name": "yazhu_bc58", "roi_type": "yazhu", "shape": "Rectangle", "coordinates": [[1629, 13], [2236, 187]], "params": {"前置背景检测": {"enabled": true, "backgroundUpdateRate": 0.01, "motionThreshold": 50, "minArea": 500}, "后置方向检测": {"consecutiveDetectionThreshold": 3, "minDisplacement": 2, "maxPatience": 3}}}, {"id": "f7fc934d-de8f-437d-a54a-86b119b29fdc", "name": "pailiao_f7fc", "roi_type": "pailiao", "shape": "Polygon", "coordinates": [[1799, 1088], [1636, 1201], [1690, 1296], [1878, 1136]], "params": {"运动检测": {"algorithm": "帧差法", "learningRate": 0.01, "detectionThreshold": 50, "shadowRemoval": 0.5, "threshold": 30, "frameInterval": 2, "minArea": 300}}}, {"id": "d3b29339-f0d9-491a-aa51-9ff1cf963204", "name": "pailiao_d3b2", "roi_type": "pailiao", "shape": "Polygon", "coordinates": [[1874, 1187], [1704, 1323], [1741, 1419], [1929, 1255]], "params": {"运动检测": {"algorithm": "帧差法", "learningRate": 0.01, "detectionThreshold": 50, "shadowRemoval": 0.5, "threshold": 30, "frameInterval": 2, "minArea": 300}}}, {"id": "733a0836-b79d-4cb3-82fa-d7de7aee0ac4", "name": "pailiao_733a", "roi_type": "pailiao", "shape": "Polygon", "coordinates": [[1598, 856], [1496, 917], [1527, 955], [1649, 873]], "params": {"运动检测": {"algorithm": "帧差法", "learningRate": 0.01, "detectionThreshold": 50, "shadowRemoval": 0.5, "threshold": 30, "frameInterval": 2, "minArea": 300}}}]}}]}, {"id": 4, "name": "压铸机4", "detection_groups": [{"id": 7, "name": "33333", "config": {}}, {"id": 8, "name": "4444", "config": {}}, {"id": 9, "name": "5555", "config": {}}]}]}]
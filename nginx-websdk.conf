# WebSDK Nginx配置文件
# 用于解决WebSDK在开发环境下的代理和跨域问题

server {
    listen 80;
    server_name localhost;
    
    # 根目录指向前端构建文件
    root /path/to/your/frontend/dist;
    index index.html;
    
    # 支持HTML5 History模式
    location / {
        try_files $uri $uri/ /index.html;
        
        # 添加WebSDK需要的HTTP头
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
    }
    
    # WebSDK静态资源
    location /websdk/ {
        alias /path/to/your/frontend/public/websdk/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSDK WebSocket代理 - 关键配置
    # 根据编程指南：bProxy=true时需要nginx代理转发
    location /websocket/ {
        proxy_pass http://************:80/;  # 替换为实际设备IP
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket超时设置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_connect_timeout 86400;
    }
    
    # ISAPI接口代理
    location /ISAPI/ {
        proxy_pass http://************:80/ISAPI/;  # 替换为实际设备IP
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 支持设备认证
        proxy_set_header Authorization $http_authorization;
        proxy_pass_header Authorization;
    }
}

# HTTPS配置（可选）
# 根据编程指南：性能最佳方案是使用HTTPS
server {
    listen 443 ssl;
    server_name localhost;
    
    # SSL证书配置（需要自签名证书）
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # 根据编程指南：HTTPS下需要开启跨域隔离头
    add_header 'Cross-Origin-Embedder-Policy' 'require-corp' always;
    add_header 'Cross-Origin-Opener-Policy' 'same-origin' always;
    
    # 其他配置与HTTP相同
    root /path/to/your/frontend/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /websdk/ {
        alias /path/to/your/frontend/public/websdk/;
        expires 1d;
    }
    
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # HTTPS环境下的WebSocket代理
    location /websocket/ {
        proxy_pass https://************:443/;  # HTTPS设备
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_ssl_verify off;  # 如果设备使用自签名证书
    }
}

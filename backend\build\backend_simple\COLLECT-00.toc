([('backend_service.exe',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\build\\backend_simple\\backend_service.exe',
   'EXECUTABLE'),
  ('python311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg481_64.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\opencv_videoio_ffmpeg481_64.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-65e29aac85b9409a6008e2dc84b1cc09.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-65e29aac85b9409a6008e2dc84b1cc09.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pywin32_system32\\pythoncom311.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32com\\shell\\shell.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('websockets\\speedups.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\websockets\\speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\yaml\\_yaml.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('mypy\\__init__.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\__init__.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\operators.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\operators.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\defaults.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\defaults.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\util.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\util.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\typevars.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\typevars.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\erasetype.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\erasetype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\typeops.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\typeops.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\checkmember.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\checkmember.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\checker.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\checker.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\visitor.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\visitor.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\types_utils.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\types_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\typeanal.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\typeanal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\tvar_scope.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\tvar_scope.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\semanal_shared.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\semanal_shared.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\type_visitor.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\type_visitor.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\treetransform.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\treetransform.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\traverser.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\traverser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\sharedparse.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\sharedparse.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\semanal_enum.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\semanal_enum.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\scope.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\scope.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\patterns.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\patterns.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\mro.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\mro.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\typestate.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\typestate.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\literals.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\literals.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\errors.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\errors.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\constraints.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\constraints.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\argmap.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\argmap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\checkpattern.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\checkpattern.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\binder.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\binder.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\checkexpr.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\checkexpr.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\checkstrformat.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\checkstrformat.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\parse.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\parse.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\fastparse.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\fastparse.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\reachability.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\reachability.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\applytype.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\applytype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\typetraverser.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\typetraverser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\messages.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\messages.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\message_registry.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\message_registry.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\meet.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\meet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\infer.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\infer.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\solve.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\solve.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\graph_utils.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\graph_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\subtypes.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\subtypes.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\join.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\join.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\state.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\state.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\maptype.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\maptype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\expandtype.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\expandtype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\typevartuples.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\typevartuples.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\copytype.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\copytype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\types.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\types.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\server\\trigger.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\server\\trigger.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\server\\__init__.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\server\\__init__.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\semanal.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\semanal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\semanal_typeddict.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\semanal_typeddict.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\semanal_newtype.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\semanal_newtype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\semanal_namedtuple.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\semanal_namedtuple.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\exprtotype.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\exprtotype.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\constant_fold.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\constant_fold.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\plugins\\dataclasses.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\plugins\\dataclasses.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\plugins\\common.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\plugins\\common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\fixup.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\fixup.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\lookup.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\lookup.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\plugins\\__init__.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\plugins\\__init__.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\plugin.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\plugin.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\options.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\options.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\nodes.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\nodes.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\strconv.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\strconv.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\errorcodes.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\mypy\\errorcodes.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pydantic_core\\_pydantic_core.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\cyextension\\util.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\greenlet\\_greenlet.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('psycopg2\\_psycopg.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\psycopg2\\_psycopg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('watchfiles\\_rust_notify.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\watchfiles\\_rust_notify.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('httptools\\parser\\url_parser.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\httptools\\parser\\url_parser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('httptools\\parser\\parser.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\httptools\\parser\\parser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python3.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('alembic.ini',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic.ini',
   'DATA'),
  ('alembic\\__pycache__\\env.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\__pycache__\\env.cpython-311.pyc',
   'DATA'),
  ('alembic\\env.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\env.py',
   'DATA'),
  ('alembic\\script.py.mako',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\script.py.mako',
   'DATA'),
  ('alembic\\versions\\2edca0f184a0_add_template_id_to_detection_groups.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\2edca0f184a0_add_template_id_to_detection_groups.py',
   'DATA'),
  ('alembic\\versions\\5470e460ad8a_rebuild_video_sources_table_with_.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\5470e460ad8a_rebuild_video_sources_table_with_.py',
   'DATA'),
  ('alembic\\versions\\95da47b06815_extend_video_sources_for_websdk_devices.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\95da47b06815_extend_video_sources_for_websdk_devices.py',
   'DATA'),
  ('alembic\\versions\\__pycache__\\2edca0f184a0_add_template_id_to_detection_groups.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\__pycache__\\2edca0f184a0_add_template_id_to_detection_groups.cpython-311.pyc',
   'DATA'),
  ('alembic\\versions\\__pycache__\\5470e460ad8a_rebuild_video_sources_table_with_.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\__pycache__\\5470e460ad8a_rebuild_video_sources_table_with_.cpython-311.pyc',
   'DATA'),
  ('alembic\\versions\\__pycache__\\95da47b06815_extend_video_sources_for_websdk_devices.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\__pycache__\\95da47b06815_extend_video_sources_for_websdk_devices.cpython-311.pyc',
   'DATA'),
  ('alembic\\versions\\__pycache__\\a476fe5b707a_create_material_jam_tables_only.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\__pycache__\\a476fe5b707a_create_material_jam_tables_only.cpython-311.pyc',
   'DATA'),
  ('alembic\\versions\\__pycache__\\add_material_jam_detection_table.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\__pycache__\\add_material_jam_detection_table.cpython-311.pyc',
   'DATA'),
  ('alembic\\versions\\__pycache__\\add_template_schedule_tables.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\__pycache__\\add_template_schedule_tables.cpython-311.pyc',
   'DATA'),
  ('alembic\\versions\\__pycache__\\bf57647ab1e8_add_material_jam_tables.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\__pycache__\\bf57647ab1e8_add_material_jam_tables.cpython-311.pyc',
   'DATA'),
  ('alembic\\versions\\__pycache__\\create_tables.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\__pycache__\\create_tables.cpython-311.pyc',
   'DATA'),
  ('alembic\\versions\\__pycache__\\f1e2d9154f3e_make_path_nullable_for_websdk_devices.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\__pycache__\\f1e2d9154f3e_make_path_nullable_for_websdk_devices.cpython-311.pyc',
   'DATA'),
  ('alembic\\versions\\a476fe5b707a_create_material_jam_tables_only.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\a476fe5b707a_create_material_jam_tables_only.py',
   'DATA'),
  ('alembic\\versions\\bf57647ab1e8_add_material_jam_tables.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\bf57647ab1e8_add_material_jam_tables.py',
   'DATA'),
  ('alembic\\versions\\create_tables.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\create_tables.py',
   'DATA'),
  ('alembic\\versions\\f1e2d9154f3e_make_path_nullable_for_websdk_devices.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\alembic\\versions\\f1e2d9154f3e_make_path_nullable_for_websdk_devices.py',
   'DATA'),
  ('app\\__pycache__\\main.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\__pycache__\\main.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\api.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\api.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\deps.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\deps.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\frame_processor.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\frame_processor.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\jam_detection.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\jam_detection.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\jam_detection_router.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\jam_detection_router.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\message_handlers.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\message_handlers.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\roi_config.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\roi_config.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\roi_detection.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\roi_detection.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\rtsp.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\rtsp.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\system_logs.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\system_logs.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\websocket.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\websocket.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\websocket_multi_roi.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\websocket_multi_roi.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\websocket_multi_roi_simple.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\websocket_multi_roi_simple.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\websocket_router.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\__pycache__\\websocket_router.cpython-311.pyc',
   'DATA'),
  ('app\\api\\api.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\api.py',
   'DATA'),
  ('app\\api\\deps.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\deps.py',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\alarms.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\alarms.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\analytics.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\analytics.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\auth.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\auth.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\canvas_detection.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\canvas_detection.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\card_detection.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\card_detection.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\dashboard.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\dashboard.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\database.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\database.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\detection_groups.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\detection_groups.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\detection_templates.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\detection_templates.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\device_status.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\device_status.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\die_casters.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\die_casters.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\material_jam_detection.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\material_jam_detection.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\motion_detection.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\motion_detection.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\preset_schedule.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\preset_schedule.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\preset_schedules.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\preset_schedules.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\roi_detection.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\roi_detection.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\scheduler_control.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\scheduler_control.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\system_info.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\system_info.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\system_settings.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\system_settings.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\template_schedule.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\template_schedule.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\template_schedules.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\template_schedules.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\templates.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\templates.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\users.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\users.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\video_sources.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\video_sources.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\__pycache__\\websdk_proxy.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\__pycache__\\websdk_proxy.cpython-311.pyc',
   'DATA'),
  ('app\\api\\endpoints\\alarms.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\alarms.py',
   'DATA'),
  ('app\\api\\endpoints\\analytics.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\analytics.py',
   'DATA'),
  ('app\\api\\endpoints\\auth.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\auth.py',
   'DATA'),
  ('app\\api\\endpoints\\card_detection.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\card_detection.py',
   'DATA'),
  ('app\\api\\endpoints\\database.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\database.py',
   'DATA'),
  ('app\\api\\endpoints\\detection_groups.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\detection_groups.py',
   'DATA'),
  ('app\\api\\endpoints\\detection_templates.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\detection_templates.py',
   'DATA'),
  ('app\\api\\endpoints\\device_status.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\device_status.py',
   'DATA'),
  ('app\\api\\endpoints\\die_casters.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\die_casters.py',
   'DATA'),
  ('app\\api\\endpoints\\motion_detection.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\motion_detection.py',
   'DATA'),
  ('app\\api\\endpoints\\system_info.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\system_info.py',
   'DATA'),
  ('app\\api\\endpoints\\system_settings.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\system_settings.py',
   'DATA'),
  ('app\\api\\endpoints\\users.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\users.py',
   'DATA'),
  ('app\\api\\endpoints\\video_sources.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\video_sources.py',
   'DATA'),
  ('app\\api\\endpoints\\websdk_proxy.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\endpoints\\websdk_proxy.py',
   'DATA'),
  ('app\\api\\frame_processor.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\frame_processor.py',
   'DATA'),
  ('app\\api\\message_handlers.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\message_handlers.py',
   'DATA'),
  ('app\\api\\roi_config.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\roi_config.py',
   'DATA'),
  ('app\\api\\rtsp.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\rtsp.py',
   'DATA'),
  ('app\\api\\system_logs.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\system_logs.py',
   'DATA'),
  ('app\\api\\v1\\__pycache__\\material_jam.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\v1\\__pycache__\\material_jam.cpython-311.pyc',
   'DATA'),
  ('app\\api\\websocket.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\websocket.py',
   'DATA'),
  ('app\\api\\websocket_router.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\api\\websocket_router.py',
   'DATA'),
  ('app\\core\\__pycache__\\config.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\core\\__pycache__\\config.cpython-311.pyc',
   'DATA'),
  ('app\\core\\__pycache__\\dependencies.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\core\\__pycache__\\dependencies.cpython-311.pyc',
   'DATA'),
  ('app\\core\\__pycache__\\response.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\core\\__pycache__\\response.cpython-311.pyc',
   'DATA'),
  ('app\\core\\__pycache__\\security.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\core\\__pycache__\\security.cpython-311.pyc',
   'DATA'),
  ('app\\core\\__pycache__\\template_scheduler.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\core\\__pycache__\\template_scheduler.cpython-311.pyc',
   'DATA'),
  ('app\\core\\config.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\core\\config.py',
   'DATA'),
  ('app\\core\\dependencies.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\core\\dependencies.py',
   'DATA'),
  ('app\\core\\security.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\core\\security.py',
   'DATA'),
  ('app\\db\\__pycache__\\init_db.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\db\\__pycache__\\init_db.cpython-311.pyc',
   'DATA'),
  ('app\\db\\__pycache__\\session.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\db\\__pycache__\\session.cpython-311.pyc',
   'DATA'),
  ('app\\db\\init_db.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\db\\init_db.py',
   'DATA'),
  ('app\\db\\session.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\db\\session.py',
   'DATA'),
  ('app\\main.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\main.py',
   'DATA'),
  ('app\\models\\__pycache__\\jam_detection.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\models\\__pycache__\\jam_detection.cpython-311.pyc',
   'DATA'),
  ('app\\models\\__pycache__\\material_jam.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\models\\__pycache__\\material_jam.cpython-311.pyc',
   'DATA'),
  ('app\\models\\__pycache__\\models.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\models\\__pycache__\\models.cpython-311.pyc',
   'DATA'),
  ('app\\models\\__pycache__\\roi_config.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\models\\__pycache__\\roi_config.cpython-311.pyc',
   'DATA'),
  ('app\\models\\models.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\models\\models.py',
   'DATA'),
  ('app\\schemas\\__pycache__\\auth.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\__pycache__\\auth.cpython-311.pyc',
   'DATA'),
  ('app\\schemas\\__pycache__\\card_detection.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\__pycache__\\card_detection.cpython-311.pyc',
   'DATA'),
  ('app\\schemas\\__pycache__\\dashboard.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\__pycache__\\dashboard.cpython-311.pyc',
   'DATA'),
  ('app\\schemas\\__pycache__\\detection_template.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\__pycache__\\detection_template.cpython-311.pyc',
   'DATA'),
  ('app\\schemas\\__pycache__\\die_caster.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\__pycache__\\die_caster.cpython-311.pyc',
   'DATA'),
  ('app\\schemas\\__pycache__\\material_jam.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\__pycache__\\material_jam.cpython-311.pyc',
   'DATA'),
  ('app\\schemas\\__pycache__\\preset_schedule.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\__pycache__\\preset_schedule.cpython-311.pyc',
   'DATA'),
  ('app\\schemas\\__pycache__\\system_settings.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\__pycache__\\system_settings.cpython-311.pyc',
   'DATA'),
  ('app\\schemas\\__pycache__\\template_schedule.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\__pycache__\\template_schedule.cpython-311.pyc',
   'DATA'),
  ('app\\schemas\\__pycache__\\user.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\__pycache__\\user.cpython-311.pyc',
   'DATA'),
  ('app\\schemas\\__pycache__\\video_source.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\__pycache__\\video_source.cpython-311.pyc',
   'DATA'),
  ('app\\schemas\\auth.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\auth.py',
   'DATA'),
  ('app\\schemas\\card_detection.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\card_detection.py',
   'DATA'),
  ('app\\schemas\\detection_template.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\detection_template.py',
   'DATA'),
  ('app\\schemas\\die_caster.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\die_caster.py',
   'DATA'),
  ('app\\schemas\\system_settings.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\system_settings.py',
   'DATA'),
  ('app\\schemas\\user.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\user.py',
   'DATA'),
  ('app\\schemas\\video_source.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\schemas\\video_source.py',
   'DATA'),
  ('app\\services\\__pycache__\\algorithms.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\algorithms.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\canvas_detection_service.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\canvas_detection_service.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\contour_formatter.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\contour_formatter.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\detection_config_manager.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\detection_config_manager.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\detection_frame_processor.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\detection_frame_processor.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\detection_instance_manager.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\detection_instance_manager.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\detection_logger.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\detection_logger.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\detection_manager.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\detection_manager.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\detector_manager.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\detector_manager.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\gpu_accelerator.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\gpu_accelerator.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\log_service.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\log_service.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\material_jam_service.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\material_jam_service.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\multi_roi_detector.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\multi_roi_detector.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\roi_detection.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\roi_detection.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\roi_detection_manager.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\roi_detection_manager.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\roi_detection_service.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\roi_detection_service.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\roi_direction_detector.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\roi_direction_detector.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\roi_mask_creator.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\roi_mask_creator.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\roi_motion_detector.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\roi_motion_detector.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\rtsp_service.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\rtsp_service.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\schedule_service.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\schedule_service.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\stuck_material_detector.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\stuck_material_detector.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\system_log_service.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\system_log_service.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\template_scheduler.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\template_scheduler.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\template_service.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\template_service.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\video_detection.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\video_detection.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\websocket_manager.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\websocket_manager.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\websocket_message_handler.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\__pycache__\\websocket_message_handler.cpython-311.pyc',
   'DATA'),
  ('app\\services\\algorithms.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\algorithms.py',
   'DATA'),
  ('app\\services\\detection_manager.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\detection_manager.py',
   'DATA'),
  ('app\\services\\gpu_accelerator.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\gpu_accelerator.py',
   'DATA'),
  ('app\\services\\rtsp_service.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\rtsp_service.py',
   'DATA'),
  ('app\\services\\system_log_service.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\system_log_service.py',
   'DATA'),
  ('app\\services\\video_detection.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\video_detection.py',
   'DATA'),
  ('app\\services\\websocket_manager.py',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\services\\websocket_manager.py',
   'DATA'),
  ('app\\static\\swagger-ui\\redoc.standalone.js',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\static\\swagger-ui\\redoc.standalone.js',
   'DATA'),
  ('app\\static\\swagger-ui\\swagger-ui-bundle.min.js',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\static\\swagger-ui\\swagger-ui-bundle.min.js',
   'DATA'),
  ('app\\static\\swagger-ui\\swagger-ui.min.css',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\static\\swagger-ui\\swagger-ui.min.css',
   'DATA'),
  ('app\\utils\\__pycache__\\__init__.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\utils\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\logger.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\utils\\__pycache__\\logger.cpython-311.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\param_logger.cpython-311.pyc',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\app\\utils\\__pycache__\\param_logger.cpython-311.pyc',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cv2\\config.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('cv2\\config-3.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('websockets-11.0.3.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\websockets-11.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('websockets-11.0.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\websockets-11.0.3.dist-info\\RECORD',
   'DATA'),
  ('websockets-11.0.3.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\websockets-11.0.3.dist-info\\LICENSE',
   'DATA'),
  ('websockets-11.0.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\websockets-11.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\top_level.txt',
   'DATA'),
  ('websockets-11.0.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\websockets-11.0.3.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\REQUESTED',
   'DATA'),
  ('websockets-11.0.3.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\websockets-11.0.3.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-11.0.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\websockets-11.0.3.dist-info\\METADATA',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('cv2\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'D:\\WORK\\newweb\\web_last_0710\\backend\\build\\backend_simple\\base_library.zip',
   'DATA')],)

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROI加载问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .video-container {
            position: relative;
            width: 640px;
            height: 360px;
            background: #000;
            border: 2px solid #ccc;
            margin: 20px auto;
        }
        
        #testCanvas {
            position: absolute;
            top: 0;
            left: 0;
            border: 1px solid red;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d7ff;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ROI加载问题调试</h1>
        
        <div class="info">
            <strong>调试目标:</strong> 检查ROI加载功能是否正常工作
            <br><strong>预期结果:</strong> 点击"加载测试ROI"后，画布中应该显示蓝色矩形和红色矩形
        </div>
        
        <div class="video-container">
            <canvas id="testCanvas"></canvas>
        </div>
        
        <div class="controls">
            <button id="initBtn" class="btn btn-primary">初始化绘制器</button>
            <button id="loadBtn" class="btn btn-success">加载测试ROI</button>
            <button id="clearBtn" class="btn btn-danger">清空画布</button>
            <button id="debugBtn" class="btn btn-primary">调试信息</button>
        </div>
        
        <div id="logArea" class="log-area"></div>
    </div>

    <script src="roiDrawer.js"></script>
    <script>
        let roiDrawer = null
        
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea')
            const time = new Date().toLocaleTimeString()
            const entry = document.createElement('div')
            entry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black'
            entry.textContent = `[${time}] ${message}`
            logArea.appendChild(entry)
            logArea.scrollTop = logArea.scrollHeight
            console.log(message)
        }
        
        // 测试ROI数据
        const testROIs = [
            {
                id: 'test_pailiao_1',
                name: '测试排料口',
                type: 'polygon',
                attribute: 'pailiao',
                color: '#00ffff',
                points: [
                    { x: 100, y: 100 },
                    { x: 250, y: 100 },
                    { x: 250, y: 180 },
                    { x: 100, y: 180 }
                ]
            },
            {
                id: 'test_yazhu_1',
                name: '测试压铸机',
                type: 'polygon',
                attribute: 'yazhu',
                color: '#ff0000',
                points: [
                    { x: 350, y: 150 },
                    { x: 500, y: 150 },
                    { x: 500, y: 250 },
                    { x: 350, y: 250 }
                ]
            }
        ]
        
        document.getElementById('initBtn').addEventListener('click', () => {
            const canvas = document.getElementById('testCanvas')
            
            try {
                roiDrawer = new ROIDrawer(canvas, {
                    videoWidth: 640,
                    videoHeight: 360,
                    colors: {
                        pailiao: '#00ffff',
                        yazhu: '#ff0000',
                        default: ['#00ff00', '#ffff00']
                    },
                    debugMode: true
                })
                
                log('ROI绘制器初始化成功', 'success')
                log(`画布尺寸: ${canvas.width} x ${canvas.height}`)
                log(`画布样式: ${canvas.style.width} x ${canvas.style.height}`)
                
            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error')
            }
        })
        
        document.getElementById('loadBtn').addEventListener('click', () => {
            if (!roiDrawer) {
                log('请先初始化绘制器', 'error')
                return
            }
            
            log('开始加载测试ROI...')
            log(`测试数据: ${JSON.stringify(testROIs, null, 2)}`)
            
            try {
                const success = roiDrawer.loadROIList(testROIs)
                if (success) {
                    log('ROI加载成功!', 'success')
                    log(`当前ROI数量: ${roiDrawer.rois.length}`)
                } else {
                    log('ROI加载失败', 'error')
                }
            } catch (error) {
                log(`加载过程出错: ${error.message}`, 'error')
            }
        })
        
        document.getElementById('clearBtn').addEventListener('click', () => {
            if (!roiDrawer) {
                log('请先初始化绘制器', 'error')
                return
            }
            
            roiDrawer.clearROIs()
            log('画布已清空', 'success')
        })
        
        document.getElementById('debugBtn').addEventListener('click', () => {
            if (!roiDrawer) {
                log('请先初始化绘制器', 'error')
                return
            }
            
            const canvas = roiDrawer.canvas
            const ctx = roiDrawer.ctx
            
            log('=== 调试信息 ===')
            log(`绘制器实例: ${!!roiDrawer}`)
            log(`画布元素: ${!!canvas}`)
            log(`画布上下文: ${!!ctx}`)
            log(`画布实际尺寸: ${canvas.width} x ${canvas.height}`)
            log(`画布显示尺寸: ${canvas.offsetWidth} x ${canvas.offsetHeight}`)
            log(`画布样式尺寸: ${canvas.style.width} x ${canvas.style.height}`)
            log(`ROI数量: ${roiDrawer.rois.length}`)
            log(`ROI数据: ${JSON.stringify(roiDrawer.rois, null, 2)}`)
            
            // 测试手动绘制
            if (ctx) {
                log('测试手动绘制红色矩形...')
                ctx.strokeStyle = '#ff0000'
                ctx.lineWidth = 3
                ctx.strokeRect(50, 50, 100, 80)
                log('手动绘制完成', 'success')
            }
        })
        
        // 页面加载完成后自动初始化
        window.addEventListener('load', () => {
            log('页面加载完成，准备测试ROI加载功能')
            log('请按顺序点击: 1.初始化绘制器 -> 2.加载测试ROI')
        })
    </script>
</body>
</html>

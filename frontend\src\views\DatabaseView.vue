<template>
  <div class="database-container">
    <el-card class="database-card">
      <template #header>
        <div class="card-header">
          <h2>数据库管理</h2>
          <el-button type="primary" @click="refreshData">刷新数据</el-button>
        </div>
      </template>

      <el-tabs type="border-card">
        <el-tab-pane label="数据表列表">
          <el-table :data="tables" border style="width: 100%" v-loading="tablesLoading">
            <el-table-column prop="name" label="表名" />
            <el-table-column prop="rows" label="行数" />
            <el-table-column prop="description" label="描述" />
            <el-table-column label="操作" width="300">
              <template #default="scope">
                <el-button type="primary" size="small" @click="viewTableData(scope.row.name)">
                  查看数据
                </el-button>
                <el-dropdown @command="(command) => handleTableAction(command, scope.row.name)">
                  <el-button type="info" size="small">
                    更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="export-csv">导出CSV</el-dropdown-item>
                      <el-dropdown-item command="export-json">导出JSON</el-dropdown-item>
                      <el-dropdown-item command="import">导入数据</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="SQL查询">
          <el-input
            v-model="sqlQuery"
            type="textarea"
            :rows="4"
            placeholder="输入SQL查询语句..."
          />
          <div class="query-actions">
            <el-button type="primary" @click="handleExecuteQuery" :loading="queryLoading">执行查询</el-button>
            <el-button @click="sqlQuery = ''">清空</el-button>
          </div>
          
          <div v-if="queryResult.length > 0" class="query-result">
            <h3>查询结果</h3>
            <el-table :data="queryResult" border style="width: 100%" max-height="400">
              <el-table-column
                v-for="(col, index) in queryColumns"
                :key="index"
                :prop="col"
                :label="col"
              />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="备份管理">
          <div class="backup-actions">
             <el-button type="primary" @click="handleCreateBackup" :loading="backupLoading">
               创建备份
             </el-button>
             <el-button @click="refreshBackups">刷新列表</el-button>
           </div>
          
          <el-table :data="backups" border style="width: 100%; margin-top: 20px" v-loading="backupsLoading">
            <el-table-column prop="filename" label="备份文件名" />
            <el-table-column prop="size" label="文件大小" :formatter="formatFileSize" />
            <el-table-column prop="created_at" label="创建时间" :formatter="formatDateTime" />
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button type="success" size="small" @click="restoreBackup(scope.row.filename)">
                  恢复
                </el-button>
                <el-button type="danger" size="small" @click="deleteBackup(scope.row.filename)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="数据库维护">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card class="maintenance-card">
                <template #header>
                  <h3>数据库信息</h3>
                </template>
                <div v-if="dbInfo" class="db-info">
                  <p><strong>SQLite版本:</strong> {{ dbInfo.sqlite_version }}</p>
                  <p><strong>数据库文件:</strong> {{ dbInfo.database_info?.file_path }}</p>
                  <p><strong>文件大小:</strong> {{ formatFileSize(null, null, dbInfo.database_info?.file_size) }}</p>
                  <p><strong>表数量:</strong> {{ dbInfo.total_tables }}</p>
                  <p><strong>总记录数:</strong> {{ dbInfo.total_rows }}</p>
                  <p><strong>最后修改:</strong> {{ formatDateTime(null, null, dbInfo.database_info?.modified_at) }}</p>
                </div>
                <el-button type="primary" @click="refreshDbInfo" :loading="dbInfoLoading">
                  刷新信息
                </el-button>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card class="maintenance-card">
                <template #header>
                  <h3>维护操作</h3>
                </template>
                <div class="maintenance-actions">
                  <el-button type="warning" @click="vacuumDatabase" :loading="vacuumLoading">
                    优化数据库 (VACUUM)
                  </el-button>
                  <el-button type="info" @click="analyzeDatabase" :loading="analyzeLoading">
                    更新统计信息 (ANALYZE)
                  </el-button>
                  <el-button type="success" @click="checkIntegrity" :loading="integrityLoading">
                    完整性检查
                  </el-button>
                </div>
                
                <div v-if="integrityResult" class="integrity-result">
                  <h4>完整性检查结果:</h4>
                  <el-tag :type="integrityResult.is_healthy ? 'success' : 'danger'">
                    {{ integrityResult.is_healthy ? '数据库健康' : '发现问题' }}
                  </el-tag>
                  <div v-if="!integrityResult.is_healthy" class="check-details">
                    <ul>
                      <li v-for="result in integrityResult.check_results" :key="result">{{ result }}</li>
                    </ul>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>

      <!-- 表数据对话框 -->
      <el-dialog
        v-model="tableDataVisible"
        :title="`表数据: ${currentTable}`"
        width="80%"
      >
        <div class="table-actions" v-if="selectedRows.length > 0">
          <el-button type="danger" @click="deleteSelectedRows" :loading="deleteLoading">
            删除选中的 {{ selectedRows.length }} 条记录
          </el-button>
          <el-button @click="clearSelection">取消选择</el-button>
        </div>
        
        <el-table 
          ref="tableRef"
          :data="tableData" 
          border 
          style="width: 100%" 
          height="500" 
          v-loading="tableLoading"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column
            v-for="(col, index) in tableColumns"
            :key="index"
            :prop="col"
            :label="col"
            :show-overflow-tooltip="true"
            min-width="120"
          />
        </el-table>
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalRows"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-dialog>
      
      <!-- 数据导入对话框 -->
      <el-dialog v-model="importDialogVisible" title="导入数据" width="500px">
        <el-form>
          <el-form-item label="目标表:">
            <el-input v-model="importTableName" disabled />
          </el-form-item>
          <el-form-item label="文件格式:">
            <el-radio-group v-model="importFormat">
              <el-radio label="csv">CSV</el-radio>
              <el-radio label="json">JSON</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="选择文件:">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :show-file-list="true"
              :limit="1"
              :on-change="handleFileChange"
              accept=".csv,.json"
            >
              <el-button type="primary">选择文件</el-button>
            </el-upload>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport" :loading="importLoading">导入</el-button>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import type { TableInfo, QueryResult, TableDataResponse } from '@/types'
import { 
  getTables, 
  getTableData, 
  executeQuery,
  createBackup as createBackupApi,
  getBackups,
  restoreBackup as restoreBackupApi,
  deleteBackup as deleteBackupApi,
  exportTableData,
  importTableData,
  getDatabaseInfo,
  vacuumDatabase as vacuumDatabaseApi,
  analyzeDatabase as analyzeDatabaseApi,
  checkDatabaseIntegrity
} from '@/api/database'

const tables = ref<TableInfo[]>([])
const tablesLoading = ref(false)
const currentTable = ref('')
const tableData = ref<any[]>([])
const tableColumns = ref<string[]>([])
const tableDataVisible = ref(false)
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalRows = ref(0)
const selectedRows = ref<any[]>([])
const deleteLoading = ref(false)
const tableRef = ref()

const sqlQuery = ref('')
const queryResult = ref<any[]>([])
const queryColumns = ref<string[]>([])
const queryLoading = ref(false)

// 备份管理相关
const backups = ref<any[]>([])
const backupsLoading = ref(false)
const backupLoading = ref(false)

// 数据导入导出相关
const importDialogVisible = ref(false)
const importTableName = ref('')
const importFormat = ref('csv')
const importLoading = ref(false)
const uploadRef = ref()
const selectedFile = ref<File | null>(null)

// 数据库信息和维护相关
const dbInfo = ref<any>(null)
const dbInfoLoading = ref(false)
const vacuumLoading = ref(false)
const analyzeLoading = ref(false)
const integrityLoading = ref(false)
const integrityResult = ref<any>(null)

// 获取数据库表列表
const fetchTables = async () => {
  tablesLoading.value = true
  try {
    tables.value = await getTables()
  } catch (error) {
    console.error('获取数据表列表失败', error)
    ElMessage.error('获取数据表列表失败')
  } finally {
    tablesLoading.value = false
  }
}

// 查看表数据
const viewTableData = async (tableName: string) => {
  currentTable.value = tableName
  tableDataVisible.value = true
  currentPage.value = 1
  selectedRows.value = []
  await fetchTableData()
}

// 获取表数据
const fetchTableData = async () => {
  tableLoading.value = true
  try {
    const response = await getTableData(currentTable.value, currentPage.value, pageSize.value)
    tableData.value = response.data
    tableColumns.value = response.columns
    totalRows.value = response.total
  } catch (error) {
    console.error('获取表数据失败', error)
    ElMessage.error('获取表数据失败')
  } finally {
    tableLoading.value = false
  }
}

// 执行SQL查询
const handleExecuteQuery = async () => {
  if (!sqlQuery.value.trim()) {
    ElMessage.warning('请输入SQL查询语句')
    return
  }

  queryLoading.value = true
  try {
    const response = await executeQuery(sqlQuery.value)
    queryResult.value = response.data
    queryColumns.value = response.columns
    ElMessage.success(response.message || '查询执行成功')
  } catch (error) {
    console.error('执行SQL查询失败', error)
    ElMessage.error('执行SQL查询失败')
  } finally {
    queryLoading.value = false
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchTableData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchTableData()
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 清除选择
const clearSelection = () => {
  tableRef.value?.clearSelection()
  selectedRows.value = []
}

// 删除选中的行
const deleteSelectedRows = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条记录吗？此操作不可撤销！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    deleteLoading.value = true
    
    // 构建删除的SQL语句
    const primaryKey = tableColumns.value[0] // 假设第一列是主键
    const ids = selectedRows.value.map(row => `'${row[primaryKey]}'`).join(',')
    const deleteQuery = `DELETE FROM ${currentTable.value} WHERE ${primaryKey} IN (${ids})`
    
    const response = await executeQuery(deleteQuery)
    ElMessage.success(response.message || `成功删除 ${selectedRows.value.length} 条记录`)
    
    // 刷新数据
    await fetchTableData()
    clearSelection()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除数据失败', error)
      ElMessage.error('删除数据失败')
    }
  } finally {
    deleteLoading.value = false
  }
}

// 表操作处理
const handleTableAction = async (command: string, tableName: string) => {
  switch (command) {
    case 'export-csv':
      await exportTable(tableName, 'csv')
      break
    case 'export-json':
      await exportTable(tableName, 'json')
      break
    case 'import':
      importTableName.value = tableName
      importDialogVisible.value = true
      break
  }
}

// 导出表数据
const exportTable = async (tableName: string, format: string) => {
  try {
    const response = await exportTableData(tableName, format)
    // 创建下载链接
    const blob = new Blob([response], { type: format === 'csv' ? 'text/csv' : 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${tableName}.${format}`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success(`${tableName} 导出成功`)
  } catch (error) {
    console.error('导出失败', error)
    ElMessage.error('导出失败')
  }
}

// 文件选择处理
const handleFileChange = (file: any) => {
  selectedFile.value = file.raw
}

// 确认导入
const confirmImport = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请选择文件')
    return
  }

  importLoading.value = true
  try {
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    formData.append('format', importFormat.value)
    
    await importTableData(importTableName.value, formData)
    ElMessage.success('数据导入成功')
    importDialogVisible.value = false
    await fetchTables()
  } catch (error) {
    console.error('导入失败', error)
    ElMessage.error('导入失败')
  } finally {
    importLoading.value = false
  }
}

// 备份管理
const fetchBackups = async () => {
  backupsLoading.value = true
  try {
    backups.value = await getBackups()
  } catch (error) {
    console.error('获取备份列表失败', error)
    ElMessage.error('获取备份列表失败')
  } finally {
    backupsLoading.value = false
  }
}

const handleCreateBackup = async () => {
  backupLoading.value = true
  try {
    await createBackupApi()
    ElMessage.success('备份创建成功')
    await fetchBackups()
  } catch (error) {
    console.error('创建备份失败', error)
    ElMessage.error('创建备份失败')
  } finally {
    backupLoading.value = false
  }
}

const restoreBackup = async (filename: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要恢复备份 "${filename}" 吗？这将覆盖当前数据库！`,
      '确认恢复',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await restoreBackupApi(filename)
    ElMessage.success('备份恢复成功')
    await fetchTables()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('恢复备份失败', error)
      ElMessage.error('恢复备份失败')
    }
  }
}

const deleteBackup = async (filename: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除备份 "${filename}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await deleteBackupApi(filename)
    ElMessage.success('备份删除成功')
    await fetchBackups()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除备份失败', error)
      ElMessage.error('删除备份失败')
    }
  }
}

const refreshBackups = () => {
  fetchBackups()
}

// 数据库信息和维护
const fetchDbInfo = async () => {
  dbInfoLoading.value = true
  try {
    dbInfo.value = await getDatabaseInfo()
  } catch (error) {
    console.error('获取数据库信息失败', error)
    ElMessage.error('获取数据库信息失败')
  } finally {
    dbInfoLoading.value = false
  }
}

const refreshDbInfo = () => {
  fetchDbInfo()
}

const vacuumDatabase = async () => {
  vacuumLoading.value = true
  try {
    await vacuumDatabaseApi()
    ElMessage.success('数据库优化完成')
    await fetchDbInfo()
  } catch (error) {
    console.error('数据库优化失败', error)
    ElMessage.error('数据库优化失败')
  } finally {
    vacuumLoading.value = false
  }
}

const analyzeDatabase = async () => {
  analyzeLoading.value = true
  try {
    await analyzeDatabaseApi()
    ElMessage.success('统计信息更新完成')
  } catch (error) {
    console.error('更新统计信息失败', error)
    ElMessage.error('更新统计信息失败')
  } finally {
    analyzeLoading.value = false
  }
}

const checkIntegrity = async () => {
  integrityLoading.value = true
  try {
    integrityResult.value = await checkDatabaseIntegrity()
    ElMessage.success('完整性检查完成')
  } catch (error) {
    console.error('完整性检查失败', error)
    ElMessage.error('完整性检查失败')
  } finally {
    integrityLoading.value = false
  }
}

// 格式化函数
const formatFileSize = (row: any, column: any, cellValue: number) => {
  if (!cellValue) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB']
  let size = cellValue
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`
}

const formatDateTime = (row: any, column: any, cellValue: string) => {
  if (!cellValue) return ''
  return new Date(cellValue).toLocaleString('zh-CN')
}

// 刷新所有数据
const refreshData = () => {
  fetchTables()
  fetchBackups()
  fetchDbInfo()
  queryResult.value = []
  ElMessage.success('数据已刷新')
}

onMounted(() => {
  fetchTables()
  fetchBackups()
  fetchDbInfo()
})
</script>

<style scoped>
.database-container {
  padding: 20px;
}

.database-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.query-actions {
  margin-top: 10px;
  margin-bottom: 20px;
}

.query-result {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 备份管理样式 */
.backup-actions {
  margin-bottom: 20px;
}

.backup-actions .el-button {
  margin-right: 10px;
}

/* 数据库维护样式 */
.maintenance-card {
  height: 100%;
}

.maintenance-card .el-card__header {
  padding: 15px 20px;
}

.maintenance-card h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.db-info {
  margin-bottom: 20px;
}

.db-info p {
  margin: 8px 0;
  font-size: 14px;
  color: #606266;
}

.maintenance-actions {
  margin-bottom: 20px;
}

.maintenance-actions .el-button {
  display: block;
  width: 100%;
  margin-bottom: 10px;
}

.integrity-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.integrity-result h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #303133;
}

.check-details {
  margin-top: 10px;
}

.check-details ul {
  margin: 0;
  padding-left: 20px;
}

.check-details li {
  margin: 5px 0;
  font-size: 13px;
  color: #606266;
}

/* 导入对话框样式 */
.el-upload {
  width: 100%;
}

.el-upload .el-button {
  width: 100%;
}

/* 表格操作样式 */
.table-actions {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.table-actions .el-button {
  margin-right: 10px;
}
</style>
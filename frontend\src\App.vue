<script setup lang="ts">
import { RouterView } from 'vue-router'
import { onMounted } from 'vue'

// 初始化主题
onMounted(() => {
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme === 'dark') {
    document.documentElement.classList.add('dark-theme')
    document.body.setAttribute('class', 'dark-theme')
  }
})
</script>

<template>
  <RouterView />
</template>

<style>
/* 全局样式 */
html, body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

#app {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}
</style>

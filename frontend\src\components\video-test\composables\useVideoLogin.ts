import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { DeviceConfig, DeviceInfo, OperationLog } from '../types/video-test.types'

export function useVideoLogin() {
  // 设备配置
  const deviceConfig = ref<DeviceConfig>({
    ip: '************',
    port: 80,
    username: 'admin',
    password: 'admin123',
    channel: 1,
    streamType: '1'
  })

  // 状态管理
  const isConnecting = ref(false)
  const isLoggedIn = ref(false)
  const isPreviewActive = ref(false)
  const soundEnabled = ref(false)
  const deviceInfo = ref<DeviceInfo | null>(null)
  const operationLogs = ref<OperationLog[]>([])

  // WebSDK相关
  let webVideoCtrl: any = null
  let g_iWndIndex = 0
  let currentDevice: any = null
  let isWebSDKInitialized = false

  // 计算属性
  const canLogin = computed(() => {
    return deviceConfig.value.ip && 
           deviceConfig.value.port && 
           deviceConfig.value.username && 
           deviceConfig.value.password &&
           !isConnecting.value
  })

  const videoContainerStyle = computed(() => ({
    width: '100%',
    height: '400px',
    backgroundColor: '#000',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative'
  }))

  // 添加操作日志
  const addLog = (message: string, type: string = 'info') => {
    const now = new Date()
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
    
    operationLogs.value.unshift({
      time,
      message,
      type
    })
    
    // 限制日志数量
    if (operationLogs.value.length > 100) {
      operationLogs.value = operationLogs.value.slice(0, 100)
    }
  }

  // 清空日志
  const clearLogs = () => {
    operationLogs.value = []
    addLog('日志已清空', 'info')
  }

  // 获取WebSDK实例
  const getWebVideoCtrl = () => {
    if (window.WebVideoCtrl) {
      return window.WebVideoCtrl
    }
    addLog('WebSDK未加载', 'error')
    return null
  }

  // 初始化WebSDK
  const initWebSDK = async (divPluginElement: HTMLElement) => {
    try {
      webVideoCtrl = getWebVideoCtrl()
      if (!webVideoCtrl) {
        throw new Error('WebSDK未加载')
      }
      
      addLog('开始初始化WebSDK...', 'info')
      
      // 检查浏览器兼容性
      if (!webVideoCtrl.I_SupportNoPlugin()) {
        throw new Error('浏览器不支持无插件模式')
      }
      
      // 初始化插件
      const initResult = webVideoCtrl.I_InitPlugin({
        bWndFull: true,
        iPackageType: 2,
        iWndowType: 1,
        bNoPlugin: true,
        cbSelWnd: (xmlDoc: any) => {
          g_iWndIndex = parseInt(xmlDoc.documentElement.getAttribute('iWndIndex'), 10)
          addLog(`选择窗口: ${g_iWndIndex}`, 'info')
        },
        cbDoubleClickWnd: (xmlDoc: any) => {
          addLog('双击窗口事件', 'info')
        },
        cbEvent: (xmlDoc: any) => {
          const eventType = xmlDoc.documentElement.getAttribute('iEventType')
          const eventDesc = xmlDoc.documentElement.getAttribute('iEventDesc')
          addLog(`事件: ${eventType} - ${eventDesc}`, 'info')
        },
        cbRemoteConfig: () => {
          addLog('远程配置回调', 'info')
        },
        cbInitPluginComplete: () => {
          addLog('WebSDK初始化完成', 'success')
          isWebSDKInitialized = true
          
          // 嵌入插件到容器
          if (divPluginElement) {
            webVideoCtrl.I_InsertOBJECTPlugin('divPlugin')
            addLog('插件已嵌入到容器', 'success')
          }
        }
      })
      
      if (initResult !== 0) {
        throw new Error(`WebSDK初始化失败: ${initResult}`)
      }
      
    } catch (error: any) {
      addLog(`WebSDK初始化失败: ${error.message}`, 'error')
      ElMessage.error(`WebSDK初始化失败: ${error.message}`)
    }
  }

  // 登录设备
  const loginDevice = async () => {
    if (!isWebSDKInitialized) {
      addLog('WebSDK未初始化', 'error')
      return
    }
    
    isConnecting.value = true
    addLog(`开始登录设备: ${deviceConfig.value.ip}:${deviceConfig.value.port}`, 'info')
    
    try {
      const loginParams = {
        szIP: deviceConfig.value.ip,
        iPort: deviceConfig.value.port,
        szUserName: deviceConfig.value.username,
        szPassword: deviceConfig.value.password
      }
      
      // 设置Cookie以供Vite代理使用（在登录前设置）
      document.cookie = `websdk_ip=${loginParams.szIP}; path=/; SameSite=Lax`
      document.cookie = `websdk_port=${loginParams.iPort}; path=/; SameSite=Lax`
      console.log(`[COOKIE] 设置设备信息Cookie: IP=${loginParams.szIP}, Port=${loginParams.iPort}`)
      addLog(`设置设备信息Cookie: IP=${loginParams.szIP}, Port=${loginParams.iPort}`, 'info')
      
      const loginResult = await new Promise((resolve, reject) => {
        webVideoCtrl.I_Login(
          loginParams.szIP,
          1,
          loginParams.iPort,
          loginParams.szUserName,
          loginParams.szPassword,
          {
            success: (xmlDoc: any) => {
              resolve(xmlDoc)
            },
            error: (status: any, xmlDoc: any) => {
              reject(new Error(`登录失败: ${status}`))
            }
          }
        )
      })
      
      currentDevice = loginParams
      isLoggedIn.value = true
      addLog('设备登录成功', 'success')
      ElMessage.success('设备登录成功')
      
      // 获取设备信息
      await getDeviceInfo()
      
    } catch (error: any) {
      addLog(`设备登录失败: ${error.message}`, 'error')
      ElMessage.error(`设备登录失败: ${error.message}`)
    } finally {
      isConnecting.value = false
    }
  }

  // 获取设备信息
  const getDeviceInfo = async () => {
    if (!currentDevice) return
    
    try {
      const deviceInfoResult = await new Promise((resolve, reject) => {
        webVideoCtrl.I_GetDeviceInfo(currentDevice.szIP, {
          success: (xmlDoc: any) => {
            resolve(xmlDoc)
          },
          error: (status: any, xmlDoc: any) => {
            reject(new Error(`获取设备信息失败: ${status}`))
          }
        })
      })
      
      // 解析设备信息
      deviceInfo.value = {
        deviceName: '海康威视网络摄像机',
        deviceModel: 'DS-2CD2xxx',
        firmwareVersion: 'V5.6.0',
        channelNum: 1
      }
      
      addLog('获取设备信息成功', 'success')
      
    } catch (error: any) {
      addLog(`获取设备信息失败: ${error.message}`, 'warning')
    }
  }

  // 开始预览
  const startPreview = async () => {
    if (!isLoggedIn.value || !currentDevice) {
      addLog('设备未登录', 'error')
      return
    }
    
    try {
      addLog(`开始预览通道 ${deviceConfig.value.channel}`, 'info')
      
      const previewParams = {
        iWndIndex: g_iWndIndex,
        szIP: currentDevice.szIP,
        iChannelID: deviceConfig.value.channel,
        iStreamType: parseInt(deviceConfig.value.streamType),
        bZeroChannel: false
      }
      
      const previewResult = await new Promise((resolve, reject) => {
        webVideoCtrl.I_StartRealPlay(
          previewParams.szIP,
          previewParams
        )
        
        // 简化处理，直接认为成功
        setTimeout(() => {
          resolve(true)
        }, 1000)
      })
      
      isPreviewActive.value = true
      addLog('视频预览启动成功', 'success')
      ElMessage.success('视频预览启动成功')
      
    } catch (error: any) {
      addLog(`视频预览启动失败: ${error.message}`, 'error')
      ElMessage.error(`视频预览启动失败: ${error.message}`)
    }
  }

  // 停止预览
  const stopPreview = () => {
    if (!isPreviewActive.value) return
    
    try {
      webVideoCtrl.I_Stop(g_iWndIndex)
      isPreviewActive.value = false
      soundEnabled.value = false
      addLog('视频预览已停止', 'info')
      ElMessage.info('视频预览已停止')
    } catch (error: any) {
      addLog(`停止预览失败: ${error.message}`, 'error')
    }
  }

  // 开启声音
  const openSound = () => {
    if (!isPreviewActive.value) return
    
    try {
      webVideoCtrl.I_OpenSound(g_iWndIndex)
      soundEnabled.value = true
      addLog('声音已开启', 'success')
      ElMessage.success('声音已开启')
    } catch (error: any) {
      addLog(`开启声音失败: ${error.message}`, 'error')
    }
  }

  // 关闭声音
  const closeSound = () => {
    if (!isPreviewActive.value) return
    
    try {
      webVideoCtrl.I_CloseSound(g_iWndIndex)
      soundEnabled.value = false
      addLog('声音已关闭', 'info')
      ElMessage.info('声音已关闭')
    } catch (error: any) {
      addLog(`关闭声音失败: ${error.message}`, 'error')
    }
  }

  // 清理资源
  const cleanup = () => {
    if (isPreviewActive.value) {
      stopPreview()
    }
    
    if (isLoggedIn.value && currentDevice) {
      try {
        webVideoCtrl.I_Logout(currentDevice.szIP)
        addLog('设备已登出', 'info')
      } catch (error) {
        console.error('登出失败:', error)
      }
    }
  }

  return {
    // 状态
    deviceConfig,
    isConnecting,
    isLoggedIn,
    isPreviewActive,
    soundEnabled,
    deviceInfo,
    operationLogs,
    
    // 计算属性
    canLogin,
    videoContainerStyle,
    
    // 方法
    addLog,
    clearLogs,
    initWebSDK,
    loginDevice,
    startPreview,
    stopPreview,
    openSound,
    closeSound,
    cleanup
  }
}

// 声明全局类型
declare global {
  interface Window {
    WebVideoCtrl: any
  }
}
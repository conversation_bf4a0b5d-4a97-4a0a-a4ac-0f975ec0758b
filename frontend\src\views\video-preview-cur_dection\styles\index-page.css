/* Index Page 特定样式文件 */

/* 页面滚动支持 */
.video-preview-page {
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  margin: 0;
}

/* 配置导航信息样式 */
.config-navigation {
  margin-top: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.config-navigation .el-breadcrumb {
  font-size: 14px;
}

.nav-label {
  font-weight: 600;
  color: #475569;
  margin-right: 6px;
}

.nav-value {
  font-weight: 500;
  color: #1e293b;
  background: #ffffff;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

/* 暗色模式下的配置导航样式 */
.dark-theme .config-navigation {
  background: linear-gradient(135deg, var(--bg-color-soft), var(--bg-color-mute));
  border: 1px solid var(--border-color);
}

.dark-theme .nav-label {
  color: var(--text-color);
}

.dark-theme .nav-value {
  color: var(--text-color);
  background: var(--bg-color);
  border: 1px solid var(--border-color);
}

/* 确保主要内容区域可以正常滚动 */
.preview-content {
  padding: 16px;
  min-height: calc(100vh - 120px);
}

.main-content {
  margin-top: 16px;
}

/* 调试区域样式 */
.debug-section {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.debug-btn {
  padding: 5px 10px;
  background-color: var(--bg-color-mute);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
}

.debug-btn:hover {
  background-color: var(--bg-color-hover);
}

/* 🔧 调试控制区域样式 */
.debug-control-section {
  margin-bottom: 12px;
}

.debug-control-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--bg-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.debug-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-color);
  transition: color 0.3s ease;
}

/* 暗色模式下的调试控制样式 */
.dark-theme .debug-control-compact {
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
}

.dark-theme .debug-label {
  color: var(--text-color);
}

/* 调试组件区域样式 */
.debug-components-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
  transition: border-color 0.3s ease;
}

/* 暗色模式下的调试组件区域样式 */
.dark-theme .debug-components-section {
  border-top: 1px solid var(--border-color);
}

.roi-load-section {
  margin-top: 12px;
  padding: 12px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.roi-load-section .el-text {
  color: #6b7280;
  font-size: 12px;
  transition: color 0.3s ease;
}

/* 暗色模式下的ROI加载区域样式 */
.dark-theme .roi-load-section {
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
}

.dark-theme .roi-load-section .el-text {
  color: var(--text-color-mute);
}

/* 保存配置按钮特殊样式 */
.save-config-btn {
  background: linear-gradient(135deg, #67c23a, #85ce61) !important;
  color: white !important;
  border: none !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3) !important;
  transition: all 0.3s ease !important;
}

.save-config-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5daf34, #7bc95f) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4) !important;
}

.save-config-btn:disabled {
  background: #c0c4cc !important;
  color: #909399 !important;
  box-shadow: none !important;
  transform: none !important;
}
<template>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <header class="app-header">
      <div class="header-left">
        <div class="header-logo">
          <img 
            :src="isDarkMode ? logoDark : logoLight" 
            alt="系统Logo" 
            class="logo-image"
          />
          <h1>压铸件智能检测系统</h1>
        </div>
      </div>
      
      <div class="header-right">
        <!-- 搜索框 -->
        <div class="header-search">
          <el-input
            v-model="searchQuery"
            placeholder="搜索..."
            prefix-icon="Search"
            clearable
            @keyup.enter="handleSearch"
          />
        </div>
        
        <!-- 通知中心 -->
        <div class="header-notification">
          <el-badge :value="unreadNotifications" :max="99" class="notification-badge">
            <el-button circle class="icon-button">
              <el-icon><Bell /></el-icon>
            </el-button>
          </el-badge>
        </div>
        
        <!-- 全屏按钮 -->
        <div class="header-fullscreen">
          <el-tooltip :content="fullscreenState ? '退出全屏' : '全屏'" placement="bottom">
            <el-button circle class="icon-button" @click="toggleFullScreen">
              <el-icon v-if="fullscreenState"><FullScreen style="transform: rotate(180deg);" /></el-icon>
              <el-icon v-else><FullScreen /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
        
        <!-- 后端管理按钮 -->
        <div class="header-admin">
          <el-tooltip content="后端管理" placement="bottom">
            <el-button circle class="icon-button" @click="goToAdmin">
              <el-icon><Setting /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
        
        <!-- 检测看板按钮 -->
        <div class="header-detection-dashboard">
          <el-tooltip content="检测看板" placement="bottom">
            <el-button circle class="icon-button" @click="openDetectionDashboard">
              <el-icon><DataAnalysis /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
        

        
        <!-- 主题切换 -->
        <ThemeToggle />
        
        <!-- 用户菜单 -->
        <el-dropdown @command="handleCommand">
          <div class="user-dropdown">
            <div class="user-avatar">
              <el-avatar :size="32">{{ userInitials }}</el-avatar>
            </div>
            <span class="user-name">
              {{ username }}
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>个人信息
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>
    
    <div class="app-body">
      <!-- 侧边菜单 -->
      <aside class="app-sidebar" :class="{ 'collapsed': isCollapse }">
        <el-menu
          :default-active="activeIndex"
          class="sidebar-menu"
          :router="false"
          :collapse="isCollapse"
          background-color="var(--bg-color-soft)"
          text-color="var(--text-color-soft)"
          active-text-color="var(--primary-color)"
          @select="handleMenuSelect"
        >
          <el-menu-item index="/data-dashboard">
            <el-icon><DataBoard /></el-icon>
            <span>数据看板</span>
          </el-menu-item>
<!-- 
          <el-menu-item index="/monitoring-dashboard">
            <el-icon><data-analysis /></el-icon>
            <span>监控看板</span>
          </el-menu-item> -->

          <!-- <el-menu-item index="/video-preview">
            <el-icon><video-camera /></el-icon>
            <span>视频预览(原版)</span>
          </el-menu-item>
          
          <el-menu-item index="/video-preview-cur">
            <el-icon><video-camera /></el-icon>
            <span>视频预览(新版)</span>
          </el-menu-item>  -->
          
          <el-menu-item index="/detection-config">
            <el-icon><setting /></el-icon>
            <span>检测配置</span>
          </el-menu-item>
          
          <el-menu-item index="/preset-schedule">
            <el-icon><Clock /></el-icon>
            <span>预设检测计划</span>
          </el-menu-item>
          
          <el-menu-item index="/device-management">
            <el-icon><cpu /></el-icon>
            <span>设备管理</span>
          </el-menu-item>
          

          
          <el-menu-item index="/system-settings">
            <el-icon><tools /></el-icon>
            <span>系统设置</span>
          </el-menu-item>
          
          <el-menu-item index="/database">
            <el-icon><grid /></el-icon>
            <span>数据库管理</span>
          </el-menu-item>
          
          <!-- <el-menu-item index="/video-test">
            <el-icon><video-camera /></el-icon>
            <span>视频测试</span>
          </el-menu-item>

          <el-menu-item index="/roi-config">
            <el-icon><aim /></el-icon>
            <span>ROI配置</span>
          </el-menu-item> -->

        </el-menu>
        
        <!-- 侧边栏折叠按钮 -->
        <div class="sidebar-collapse-btn" @click="toggleCollapse">
          <el-icon v-if="isCollapse"><ArrowRight /></el-icon>
          <el-icon v-else><ArrowLeft /></el-icon>
        </div>
      </aside>
      
      <!-- 主内容区 -->
      <main class="app-main">
        <el-breadcrumb class="app-breadcrumb" separator="/">
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item>{{ currentRouteName }}</el-breadcrumb-item>
        </el-breadcrumb>
        
        <div class="app-content" :class="{ 'transitioning': isTransitioning }">
          <router-view v-slot="{ Component, route }">
            <transition
              :name="transitionName"
              mode="out-in"
              @before-enter="onBeforeEnter"
              @enter="onEnter"
              @leave="onLeave"
            >
              <component :is="Component" :key="route.path" />
            </transition>
          </router-view>
        </div>
      </main>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowDown, Odometer, VideoCamera, Setting, Files, Cpu, Bell, Tools,
  User, SwitchButton, Search, ArrowRight, ArrowLeft, FullScreen, Grid, Connection, Aim, DataAnalysis, DataBoard, Clock
} from '@element-plus/icons-vue'
import { logout, getCurrentUser } from '../api/auth'
import { useUserStore } from '../stores/user'
import ThemeToggle from '../components/ThemeToggle.vue'
import logoLight from '../assets/logo_light.png'
import logoDark from '../assets/logo_dark.png'

export default defineComponent({
  name: 'MainLayout',
  components: {
    ArrowDown,
    Odometer,
    VideoCamera,
    Setting,
    Files,
    Cpu,
    Bell,
    Tools,
    User,
    SwitchButton,
    Search,
    ArrowRight,
    ArrowLeft,
    FullScreen,
    ThemeToggle,
    Grid,
    Connection,
    Aim,
    DataBoard,
    Clock
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const userStore = useUserStore()
    
    const isCollapse = ref(false)
    const username = ref('用户')
    const searchQuery = ref('')
    const unreadNotifications = ref(5)
    const fullscreenState = ref(false)
    const isDarkMode = ref(false)
    const transitionName = ref('fade-slide')
    const isTransitioning = ref(false)
    
    const activeIndex = computed(() => {
      return route.path
    })
    
    const currentRouteName = computed(() => {
      return route.meta.title || '页面'
    })
    
    const userInitials = computed(() => {
      return username.value ? username.value.charAt(0).toUpperCase() : 'U'
    })
    
    const toggleCollapse = () => {
      isCollapse.value = !isCollapse.value
    }
    
    const handleSearch = () => {
      if (searchQuery.value.trim()) {
        ElMessage.info(`搜索: ${searchQuery.value}`)
        // 实际搜索逻辑
        searchQuery.value = ''
      }
    }
    
    // 切换全屏模式
    const toggleFullScreen = () => {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().then(() => {
          fullscreenState.value = true
        }).catch(err => {
          ElMessage.error(`全屏错误: ${err.message}`)
        })
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen().then(() => {
            fullscreenState.value = false
          }).catch(err => {
            ElMessage.error(`退出全屏错误: ${err.message}`)
          })
        }
      }
    }
    
    // 监听全屏变化
    onMounted(() => {
      // 检查用户信息
      loadUserInfo()
      
      // 监听全屏变化
      document.addEventListener('fullscreenchange', () => {
        fullscreenState.value = !!document.fullscreenElement
      })
      
      // 初始化主题状态
      const savedTheme = localStorage.getItem('theme')
      isDarkMode.value = savedTheme === 'dark'
      
      // 监听主题变化
      window.addEventListener('themeChange', () => {
        isDarkMode.value = localStorage.getItem('theme') === 'dark'
      })
    })
    
    // 获取用户信息
    const loadUserInfo = async () => {
      try {
        const user = await getCurrentUser()
        username.value = user.username
      } catch (error) {
        console.error('获取用户信息失败', error)
      }
    }
    
    const handleMenuClick = (path: string) => {
      console.log('Menu clicked:', path)
      router.push(path).catch(err => {
        console.error('Navigation error:', err)
        ElMessage.error('页面跳转失败')
      })
    }
    
    const handleMenuSelect = (index: string) => {
      console.log('Menu selected:', index)
      router.push(index).catch(err => {
        console.error('Navigation error:', err)
        ElMessage.error('页面跳转失败')
      })
    }
    
    const handleCommand = (command: string) => {
      if (command === 'logout') {
        ElMessageBox.confirm('确定要退出登录吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          logout()
          router.push('/login')
          ElMessage.success('已安全退出')
        }).catch(() => {})
      } else if (command === 'profile') {
        router.push('/profile')
      }
    }
    
    // 跳转到后端管理页面
    const goToAdmin = () => {
      router.push('/admin')
    }
    
    // 打开检测看板弹窗
    const openDetectionDashboard = () => {
      const dashboardUrl = `${window.location.origin}/#/detection-dashboard`
      const windowFeatures = 'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no'
      window.open(dashboardUrl, 'detectionDashboard', windowFeatures)
    }
    
    // Transition 事件处理
    const onBeforeEnter = (el: Element) => {
      isTransitioning.value = true
      const htmlEl = el as HTMLElement
      htmlEl.style.opacity = '0'
      htmlEl.style.transform = 'translateX(20px)'
    }
    
    const onEnter = (el: Element, done: () => void) => {
      const htmlEl = el as HTMLElement
      // 强制重排
      htmlEl.offsetHeight
      htmlEl.style.transition = 'all 0.3s ease-out'
      htmlEl.style.opacity = '1'
      htmlEl.style.transform = 'translateX(0)'
      
      setTimeout(() => {
        isTransitioning.value = false
        done()
      }, 300)
    }
    
    const onLeave = (el: Element, done: () => void) => {
      const htmlEl = el as HTMLElement
      htmlEl.style.transition = 'all 0.25s ease-in'
      htmlEl.style.opacity = '0'
      htmlEl.style.transform = 'translateX(-20px)'
      
      setTimeout(done, 250)
    }
    

    
    return {
      isCollapse,
      activeIndex,
      currentRouteName,
      username,
      userInitials,
      toggleCollapse,
      handleMenuClick,
      handleMenuSelect,
      handleCommand,
      searchQuery,
      unreadNotifications,
      fullscreenState,
      isDarkMode,
      handleSearch,
      toggleFullScreen,
      logoLight,
      logoDark,
      goToAdmin,
      openDetectionDashboard,
      transitionName,
      isTransitioning,
      onBeforeEnter,
      onEnter,
      onLeave
    }
  }
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.app-header {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background-color: var(--bg-color-soft);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-logo {
  display: flex;
  align-items: center;
}

.logo-image {
  height: 40px;
  margin-right: 12px;
}

.header-logo h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
}

.header-nav {
  display: flex;
}

.header-menu {
  border: none;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-search {
  width: 200px;
}

:deep(.el-input__wrapper) {
  background-color: var(--bg-color-soft);
  box-shadow: 0 0 0 1px var(--border-color) inset !important;
  border-radius: 20px;
}

:deep(.el-input__inner) {
  color: var(--text-color);
}

:deep(.el-input__inner::placeholder) {
  color: var(--text-color-mute);
}

:deep(.el-input__prefix-inner) {
  color: var(--text-color-soft);
}

.header-notification {
  margin: 0 8px;
}

.icon-button {
  background-color: transparent;
  border: none;
  color: var(--text-color);
  font-size: 20px;
}

.icon-button:hover {
  background-color: var(--bg-color-soft);
  color: var(--primary-color);
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.user-dropdown:hover {
  background-color: var(--bg-color-soft);
  color: var(--primary-color);
}

.user-avatar {
  margin-right: 8px;
}

.user-name {
  display: flex;
  align-items: center;
  color: var(--text-color);
  font-size: 14px;
}

.app-body {
  display: flex;
  flex: 1;
  overflow: hidden;
  width: 100%;
}

.app-sidebar {
  width: 200px;
  height: 100%;
  background-color: var(--bg-color-soft);
  border-right: 1px solid var(--border-color);
  position: relative;
  transition: width 0.3s;
  flex-shrink: 0;
  z-index: 5;
}

.app-sidebar.collapsed {
  width: 64px;
}

/* 确保菜单项在白色模式下有正确的样式 */
:deep(.el-menu) {
  border-right: none;
}

:deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
}

:deep(.el-menu-item.is-active) {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  font-weight: 500;
}

:deep(.el-menu-item:hover) {
  background-color: var(--bg-color-mute);
}

.sidebar-menu {
  height: 100%;
  border-right: none;
}

.sidebar-collapse-btn {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1;
}

.app-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: var(--bg-color-mute);
  display: flex;
  flex-direction: column;
  width: 100%;
}

.app-breadcrumb {
  margin-bottom: 20px;
}

.app-content {
  flex: 1;
  background-color: var(--bg-color);
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
  height: 100%;
  overflow: auto;
}

/* 暗色主题下的特殊样式 */
.dark-theme .app-header {
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3);
}

.dark-theme :deep(.el-input__wrapper) {
  background-color: var(--bg-color-soft);
}

.dark-theme .sidebar-collapse-btn {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Tab切换动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 页面内容容器动画增强 */
.app-content {
  position: relative;
  overflow: hidden;
}

/* 过渡期间的加载状态 */
.app-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 1000;
}

.app-content.transitioning::before {
  animation: loading-bar 0.3s ease-out;
}

@keyframes loading-bar {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 菜单项点击动画增强 */
:deep(.el-menu-item) {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

:deep(.el-menu-item::before) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(var(--primary-color-rgb), 0.1), transparent);
  transition: left 0.3s ease;
}

:deep(.el-menu-item:hover::before) {
  left: 100%;
}

:deep(.el-menu-item.is-active) {
  transform: translateX(2px);
  box-shadow: 2px 0 0 var(--primary-color);
}

/* 响应式动画优化 */
@media (prefers-reduced-motion: reduce) {
  .fade-slide-enter-active,
  .fade-slide-leave-active,
  :deep(.el-menu-item),
  .app-content::before {
    transition: none;
    animation: none;
  }
}
</style>
<template>
  <div class="roi-config-dialog">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <span class="current-source">
          当前视频源:
          <el-tag v-if="isLoadingVideoSource" type="info" size="small">
            <el-icon class="is-loading"><Loading /></el-icon>
            加载中...
          </el-tag>
          <el-tag v-else-if="currentVideoSource" type="success" size="small">
            {{ currentVideoSource.name }}
          </el-tag>
          <el-tag v-else type="info" size="small">
            未选择
          </el-tag>
        </span>
      </div>
      <div class="toolbar-right">
        <el-button
          type="primary"
          size="small"
          @click="startPreview"
          :loading="isConnecting"
          :disabled="!canStartPreview"
        >
          {{ isPreviewActive ? '停止预览' : '开始预览' }}
        </el-button>
        <el-button
          type="info"
          size="small"
          @click="toggleSound"
          :disabled="!isPreviewActive"
        >
          {{ soundEnabled ? '关闭声音' : '开启声音' }}
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="16">
        <!-- 左侧：视频预览和ROI绘制 -->
        <el-col :span="16">
          <div class="video-section">
            <div class="video-container" :style="videoContainerStyle">
              <div
                id="divPlugin"
                ref="divPlugin"
                class="video-plugin"
                :style="{ width: '100%', height: '100%' }"
              >
                <div v-if="!isPreviewActive" class="video-placeholder">
                  <el-icon size="64" color="var(--text-color-mute)"><VideoCamera /></el-icon>
                  <p>{{ isLoggedIn ? '点击开始预览按钮开始视频预览' : '请先登录设备' }}</p>
                </div>

                <!-- ROI绘制层 - Canvas（放回divPlugin内部，与原版一致） -->
                <canvas
                  ref="roiCanvas"
                  class="roi-canvas"
                  :width="640"
                  :height="360"
                  :style="{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    pointerEvents: 'none',
                    zIndex: 10
                  }"
                ></canvas>
              </div>
            </div>
            
            <!-- ROI绘制控制面板 -->
            <div class="roi-controls">
              <div class="control-group">
                <span class="control-label">ROI类型：</span>
                <el-radio-group v-model="selectedROIAttribute" size="small" @change="onROIAttributeChange">
                  <el-radio-button label="yazhu">压铸机</el-radio-button>
                  <el-radio-button label="pailiao">排料口</el-radio-button>
                </el-radio-group>
              </div>
              
              <div class="control-group">
                <el-button
                  type="primary"
                  size="small"
                  @click="startDrawingROI('Rectangle')"
                  :disabled="isDrawing"
                >
                  绘制矩形
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  @click="startDrawingROI('Polygon')"
                  :disabled="isDrawing"
                >
                  绘制多边形
                </el-button>
                <el-button
                  v-if="isDrawing"
                  type="success"
                  size="small"
                  @click="completeDrawing"
                  :disabled="!canCompleteDrawing"
                >
                  完成绘制
                </el-button>
                <el-button
                  v-if="isDrawing"
                  type="warning"
                  size="small"
                  @click="cancelDrawing"
                >
                  取消绘制
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="clearAllROI"
                  :disabled="roiList.length === 0"
                >
                  清空ROI
                </el-button>
              </div>
            </div>
          </div>
        </el-col>
        
        <!-- 右侧：ROI列表和操作 -->
        <el-col :span="8">
          <div class="roi-panel">
            <!-- ROI统计 -->
            <div class="roi-stats">
              <div class="stat-item">
                <span class="stat-label">总计:</span>
                <el-tag size="small">{{ roiStats.total }}个</el-tag>
              </div>
              <div class="stat-item">
                <span class="stat-label">压铸机:</span>
                <el-tag type="danger" size="small">{{ roiStats.yazhu }}个</el-tag>
              </div>
              <div class="stat-item">
                <span class="stat-label">排料口:</span>
                <el-tag type="info" size="small">{{ roiStats.pailiao }}个</el-tag>
              </div>
            </div>

            <!-- ROI列表 -->
            <div class="roi-list">
              <div class="roi-list-header">
                <span>ROI列表</span>
                <div class="list-actions">
                  <el-button size="small" @click="exportROI">导出</el-button>
                  <el-button size="small" @click="showImportDialog = true">导入</el-button>
                </div>
              </div>
              
              <div class="roi-items">
                <div
                  v-for="roi in roiList"
                  :key="roi.roi_id"
                  class="roi-item"
                  :class="{ 'roi-item-selected': selectedROI?.roi_id === roi.roi_id }"
                  @click="selectROI(roi)"
                >
                  <div class="roi-item-header">
                    <div class="roi-name">
                      <span
                        class="roi-color-indicator"
                        :style="{ backgroundColor: getROIColor(roi.attribute) }"
                      ></span>
                      {{ roi.name }}
                    </div>
                    <div class="roi-actions">
                      <el-button
                        type="primary"
                        size="small"
                        @click.stop="editROI(roi)"
                      >
                        编辑
                      </el-button>
                      <el-button
                        type="danger"
                        size="small"
                        @click.stop="deleteROI(roi.roi_id)"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                  <div class="roi-item-info">
                    <span class="roi-type">{{ roi.attribute === 'yazhu' ? '压铸机' : '排料口' }}</span>
                    <span class="roi-shape">{{ roi.roi_type === 'rectangle' ? '矩形' : '多边形' }}</span>
                    <span class="roi-points">{{ roi.coordinates?.length || 0 }}个点</span>
                  </div>
                </div>

                <div v-if="roiList.length === 0" class="no-roi">
                  暂无ROI配置
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- ROI编辑对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑ROI" width="500px">
      <el-form v-if="editingROI" :model="editingROI" label-width="80px" size="small">
        <el-form-item label="ROI名称">
          <el-input v-model="editingROI.name" placeholder="请输入ROI名称" />
        </el-form-item>
        <el-form-item label="ROI类型">
          <el-radio-group v-model="editingROI.roi_type">
            <el-radio label="yazhu">压铸机</el-radio>
            <el-radio label="pailiao">排料口</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="形状类型">
          <el-tag :type="editingROI.shape === 'Rectangle' ? 'success' : 'info'">
            {{ editingROI.shape === 'Rectangle' ? '矩形' : '多边形' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="检测阈值">
          <el-slider
            v-model="editingROI.params.threshold"
            :min="0"
            :max="1"
            :step="0.1"
            show-input
          />
        </el-form-item>
        <el-form-item label="敏感度">
          <el-slider
            v-model="editingROI.params.sensitivity"
            :min="0"
            :max="1"
            :step="0.1"
            show-input
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="saveROIEdit">保存</el-button>
      </template>
    </el-dialog>

    <!-- ROI导入对话框 -->
    <el-dialog v-model="showImportDialog" title="导入ROI配置" width="400px">
      <el-input
        v-model="importData"
        type="textarea"
        :rows="8"
        placeholder="请粘贴ROI配置JSON数据"
      />
      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button type="primary" @click="importROI">导入</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { VideoCamera, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useWebSDK } from '@/composables/useWebSDK'
import { getVideoSource } from '@/api/video-sources'


// Props - 接收外部传入的视频源信息
interface Props {
  videoSource?: {
    id: number
    video_source_id?: number
  } | null
}

const props = withDefaults(defineProps<Props>(), {
  videoSource: null
})

// 当前视频源详细信息
const currentVideoSource = ref<any>(null)
const isLoadingVideoSource = ref(false)

// 视频配置
const videoConfig = ref({
  channel: 1,
  streamType: '1',
  protocol: 1
})

// 使用WebSDK composable
const {
  isConnecting,
  isLoggedIn,
  isPreviewActive,
  soundEnabled,
  operationLogs,
  addLog,
  clearLogs,
  initWebSDK,
  loginDevice: webSDKLoginDevice,
  startPreview: webSDKStartPreview,
  stopPreview,
  openSound,
  closeSound,
  cleanup
} = useWebSDK()

// ROI相关状态
const isDrawing = ref(false)
const roiList = ref<any[]>([])
const selectedROI = ref<any>(null)
const showEditDialog = ref(false)
const showImportDialog = ref(false)
const editingROI = ref<any>(null)
const importData = ref('')

// ROI绘制相关状态
const roiCanvas = ref<HTMLCanvasElement>()
const divPlugin = ref<HTMLElement>()
const selectedROIAttribute = ref<'yazhu' | 'pailiao' | null>(null)

// ROI统计
const roiStats = computed(() => ({
  total: roiList.value.length,
  yazhu: roiList.value.filter(roi => roi.attribute === 'yazhu').length,
  pailiao: roiList.value.filter(roi => roi.attribute === 'pailiao').length
}))

// 计算属性
const canStartPreview = computed(() => {
  return currentVideoSource.value && !isConnecting.value && !isLoadingVideoSource.value
})

const videoContainerStyle = computed(() => ({
  width: '100%',
  aspectRatio: '16/9',
  minHeight: '400px',
  maxHeight: '600px',
  backgroundColor: 'var(--bg-color-mute)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  position: 'relative' as const,
  border: '1px solid var(--border-color)',
  borderRadius: '4px'
}))

// 获取视频源详细信息
const fetchVideoSourceDetails = async (videoSourceId: number) => {
  if (!videoSourceId) return

  isLoadingVideoSource.value = true
  try {
    addLog(`正在获取视频源详细信息: ID=${videoSourceId}`, 'info')
    const videoSourceData = await getVideoSource(videoSourceId)

    // 检查视频源类型
    if (videoSourceData.source_type !== 'websdk_device') {
      ElMessage.error(`不支持的视频源类型: ${videoSourceData.source_type}，仅支持 websdk_device 类型`)
      addLog(`视频源类型不支持: ${videoSourceData.source_type}`, 'error')
      return
    }

    // 验证必要字段
    if (!videoSourceData.device_ip || !videoSourceData.device_port ||
        !videoSourceData.device_username || !videoSourceData.device_password) {
      ElMessage.error('视频源配置不完整，缺少设备连接信息')
      addLog('视频源配置不完整', 'error')
      return
    }

    // 转换为WebSDK需要的格式
    currentVideoSource.value = {
      id: videoSourceData.id,
      name: videoSourceData.name,
      ip: videoSourceData.device_ip,
      port: videoSourceData.device_port,
      username: videoSourceData.device_username,
      password: videoSourceData.device_password,
      protocol: videoSourceData.device_protocol || 1
    }

    addLog(`视频源信息获取成功: ${videoSourceData.name}`, 'success')

  } catch (error: any) {
    ElMessage.error(`获取视频源信息失败: ${error.message}`)
    addLog(`获取视频源信息失败: ${error.message}`, 'error')
  } finally {
    isLoadingVideoSource.value = false
  }
}

// 视频源变化处理
const onVideoSourceChange = () => {
  if (isPreviewActive.value) {
    stopPreview()
  }
  addLog(`切换视频源: ${currentVideoSource.value?.name || '未知'}`, 'info')
}

// 监听props中的视频源变化
watch(() => props.videoSource, async (newSource) => {
  if (newSource) {
    // 优先使用video_source_id，其次使用id
    const videoSourceId = newSource.video_source_id || newSource.id
    if (videoSourceId) {
      await fetchVideoSourceDetails(videoSourceId)
    }
  } else {
    currentVideoSource.value = null
  }
}, { immediate: true })

// 开始/停止预览
const startPreview = async () => {
  if (!currentVideoSource.value) {
    ElMessage.error('请先选择视频源')
    return
  }

  if (isPreviewActive.value) {
    stopPreview()
    return
  }

  try {
    // 先登录设备
    if (!isLoggedIn.value) {
      await webSDKLoginDevice({
        ip: currentVideoSource.value.ip,
        port: currentVideoSource.value.port,
        username: currentVideoSource.value.username,
        password: currentVideoSource.value.password,
        channel: videoConfig.value.channel,
        streamType: videoConfig.value.streamType,
        protocol: videoConfig.value.protocol
      })
    }

    // 开始预览 - 使用原来的方法
    if (isLoggedIn.value) {
      webSDKStartPreview({
        ip: currentVideoSource.value.ip,
        port: currentVideoSource.value.port,
        username: currentVideoSource.value.username,
        password: currentVideoSource.value.password,
        channel: videoConfig.value.channel,
        streamType: videoConfig.value.streamType,
        protocol: videoConfig.value.protocol
      })
    }
  } catch (error: any) {
    ElMessage.error(`预览失败: ${error.message}`)
  }
}

// 切换声音
const toggleSound = () => {
  if (soundEnabled.value) {
    closeSound()
  } else {
    openSound()
  }
}

// 简单的ROI绘制功能
const isDrawingROI = ref(false)
const drawingPoints = ref<{x: number, y: number}[]>([])
const currentDrawingType = ref<'rectangle' | 'polygon' | null>(null)

// 初始化简单的Canvas绘制
const initSimpleROIDrawing = () => {
  const canvas = roiCanvas.value
  if (!canvas) {
    addLog('[ERROR] ROI画布未找到', 'error')
    return
  }

  // 设置Canvas尺寸
  canvas.width = 640
  canvas.height = 360

  // 添加事件监听
  canvas.addEventListener('click', handleCanvasClick)
  canvas.addEventListener('contextmenu', handleCanvasRightClick)

  addLog('[ROI] 简单绘制功能初始化成功', 'success')
}

// Canvas点击事件
const handleCanvasClick = (event: MouseEvent) => {
  if (!isDrawingROI.value || !currentDrawingType.value) return

  const canvas = roiCanvas.value
  if (!canvas) return

  const rect = canvas.getBoundingClientRect()
  const x = (event.clientX - rect.left) * (canvas.width / rect.width)
  const y = (event.clientY - rect.top) * (canvas.height / rect.height)

  drawingPoints.value.push({ x, y })

  if (currentDrawingType.value === 'rectangle' && drawingPoints.value.length === 2) {
    // 矩形完成
    completeROIDrawing()
  }

  drawCanvas()
}

// Canvas右键事件
const handleCanvasRightClick = (event: MouseEvent) => {
  event.preventDefault()
  if (currentDrawingType.value === 'polygon' && drawingPoints.value.length >= 3) {
    completeROIDrawing()
  }
}

// 绘制Canvas
const drawCanvas = () => {
  const canvas = roiCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 绘制已有ROI
  roiList.value.forEach(roi => {
    ctx.strokeStyle = roi.color || '#ff0000'
    ctx.lineWidth = 2
    ctx.beginPath()

    if (roi.type === 'rectangle' && roi.points.length >= 2) {
      const p1 = roi.points[0]
      const p2 = roi.points[1]
      ctx.rect(p1.x, p1.y, p2.x - p1.x, p2.y - p1.y)
    } else if (roi.type === 'polygon' && roi.points.length >= 3) {
      ctx.moveTo(roi.points[0].x, roi.points[0].y)
      for (let i = 1; i < roi.points.length; i++) {
        ctx.lineTo(roi.points[i].x, roi.points[i].y)
      }
      ctx.closePath()
    }

    ctx.stroke()
  })

  // 绘制当前正在绘制的ROI
  if (isDrawingROI.value && drawingPoints.value.length > 0) {
    ctx.strokeStyle = selectedROIAttribute.value === 'yazhu' ? '#ff0000' : '#00ffff'
    ctx.lineWidth = 2
    ctx.setLineDash([5, 5])
    ctx.beginPath()

    if (currentDrawingType.value === 'rectangle' && drawingPoints.value.length >= 1) {
      // 绘制矩形预览
      if (drawingPoints.value.length === 2) {
        const p1 = drawingPoints.value[0]
        const p2 = drawingPoints.value[1]
        ctx.rect(p1.x, p1.y, p2.x - p1.x, p2.y - p1.y)
      }
    } else if (currentDrawingType.value === 'polygon' && drawingPoints.value.length >= 1) {
      // 绘制多边形预览
      ctx.moveTo(drawingPoints.value[0].x, drawingPoints.value[0].y)
      for (let i = 1; i < drawingPoints.value.length; i++) {
        ctx.lineTo(drawingPoints.value[i].x, drawingPoints.value[i].y)
      }
    }

    ctx.stroke()
    ctx.setLineDash([])

    // 绘制点
    drawingPoints.value.forEach(point => {
      ctx.fillStyle = selectedROIAttribute.value === 'yazhu' ? '#ff0000' : '#00ffff'
      ctx.beginPath()
      ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI)
      ctx.fill()
    })
  }
}

// 完成ROI绘制
const completeROIDrawing = () => {
  if (drawingPoints.value.length < 2) return

  const newROI = {
    roi_id: `roi_${Date.now()}`, // 🔥 使用roi_id字段
    name: `${selectedROIAttribute.value === 'yazhu' ? '压铸机' : '排料口'}${roiList.value.length + 1}`,
    attribute: selectedROIAttribute.value,
    roi_type: currentDrawingType.value, // 🔥 使用roi_type字段
    coordinates: [...drawingPoints.value], // 🔥 使用coordinates字段
    color: selectedROIAttribute.value === 'yazhu' ? '#ff0000' : '#00ffff'
  }

  roiList.value.push(newROI)
  addLog(`[ROI] 添加${newROI.name}`, 'success')

  // 重置绘制状态
  isDrawingROI.value = false
  drawingPoints.value = []
  currentDrawingType.value = null

  drawCanvas()
}

// ROI属性选择变化
const onROIAttributeChange = () => {
  addLog(`[ROI] 选择${selectedROIAttribute.value === 'yazhu' ? '压铸机' : '排料口'}属性`, 'info')
}

// ROI绘制功能
const startDrawingROI = (shape: 'Rectangle' | 'Polygon') => {
  if (!selectedROIAttribute.value) {
    ElMessage.warning('请先选择ROI类型')
    return
  }

  addLog(`开始绘制${shape === 'Rectangle' ? '矩形' : '多边形'}ROI，类型：${selectedROIAttribute.value === 'yazhu' ? '压铸机' : '排料口'}`, 'info')

  isDrawingROI.value = true
  currentDrawingType.value = shape.toLowerCase() as 'rectangle' | 'polygon'
  drawingPoints.value = []

  // 启用Canvas事件
  const canvas = roiCanvas.value
  if (canvas) {
    canvas.style.pointerEvents = 'auto'
  }
}

const completeDrawing = () => {
  if (isDrawingROI.value && drawingPoints.value.length >= 2) {
    completeROIDrawing()
  }
}

const cancelDrawing = () => {
  isDrawingROI.value = false
  drawingPoints.value = []
  currentDrawingType.value = null

  // 禁用Canvas事件
  const canvas = roiCanvas.value
  if (canvas) {
    canvas.style.pointerEvents = 'none'
  }

  drawCanvas()
  addLog('取消ROI绘制', 'info')
}

const clearAllROI = () => {
  roiList.value = []
  selectedROI.value = null
  drawCanvas()
  addLog('清空所有ROI', 'info')
}

const selectROI = (roi: any) => {
  selectedROI.value = roi
  addLog(`选择ROI: ${roi.name}`, 'info')
}

const editROI = (roi: any) => {
  editingROI.value = { ...roi }
  showEditDialog.value = true
  addLog(`编辑ROI: ${roi.name}`, 'info')
}

const deleteROI = (roiId: string) => {
  const index = roiList.value.findIndex(roi => roi.roi_id === roiId)
  if (index > -1) {
    const roi = roiList.value[index]
    roiList.value.splice(index, 1)
    if (selectedROI.value?.roi_id === roiId) {
      selectedROI.value = null
    }

    drawCanvas()
    addLog(`删除ROI: ${roi.name}`, 'info')
  }
}

const saveROIEdit = () => {
  if (!editingROI.value) return

  const index = roiList.value.findIndex(roi => roi.roi_id === editingROI.value.roi_id)
  if (index > -1) {
    roiList.value[index] = { ...editingROI.value }
    addLog(`保存ROI编辑: ${editingROI.value.name}`, 'success')
    drawCanvas()
  }

  showEditDialog.value = false
  editingROI.value = null
}

const exportROI = () => {
  const data = JSON.stringify(roiList.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `roi_config_${new Date().getTime()}.json`
  a.click()
  URL.revokeObjectURL(url)
  addLog('导出ROI配置', 'success')
}

const importROI = () => {
  if (!importData.value.trim()) {
    ElMessage.error('请输入ROI配置数据')
    return
  }

  try {
    const data = JSON.parse(importData.value)
    if (Array.isArray(data)) {
      roiList.value = data
      addLog(`导入${data.length}个ROI配置`, 'success')
      ElMessage.success(`导入${data.length}个ROI配置`)
      drawCanvas()
    } else {
      ElMessage.error('ROI配置数据格式错误')
    }
  } catch (error) {
    ElMessage.error('ROI配置数据解析失败')
  }

  showImportDialog.value = false
  importData.value = ''
}

const getROIColor = (attribute: 'yazhu' | 'pailiao') => {
  return attribute === 'yazhu' ? '#ff0000' : '#00ffff'
}





// 初始化Canvas
const initCanvas = () => {
  initSimpleROIDrawing()
}

// 添加测试ROI数据
const addTestROIData = () => {
  const testROI1 = {
    id: 'roi_001',
    name: '压铸机1',
    attribute: 'yazhu',
    type: 'rectangle',
    points: [{x: 50, y: 50}, {x: 150, y: 120}],
    color: '#ff0000'
  }

  const testROI2 = {
    id: 'roi_002',
    name: '排料口1',
    attribute: 'pailiao',
    type: 'polygon',
    points: [{x: 200, y: 80}, {x: 280, y: 60}, {x: 320, y: 120}, {x: 260, y: 150}, {x: 220, y: 130}],
    color: '#00ffff'
  }

  roiList.value = [testROI1, testROI2]

  // 绘制测试数据
  setTimeout(() => {
    drawCanvas()
  }, 500)

  addLog('添加测试ROI数据', 'info')
}

// 组件挂载时初始化
onMounted(async () => {
  await initWebSDK()
  addLog('ROI配置对话框初始化完成', 'success')

  // 等待DOM渲染完成后初始化Canvas
  await nextTick()
  setTimeout(() => {
    initCanvas()
    addTestROIData()
  }, 500)
})

// 组件卸载时清理资源
onUnmounted(() => {
  cleanup()

  // 清理Canvas事件监听
  const canvas = roiCanvas.value
  if (canvas) {
    canvas.removeEventListener('click', handleCanvasClick)
    canvas.removeEventListener('contextmenu', handleCanvasRightClick)
  }

  addLog('ROI配置对话框已卸载', 'info')
})
</script>

<style scoped>
.roi-config-dialog {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 16px;
}

.toolbar-left .current-source {
  font-size: 14px;
  color: var(--text-color);
  font-weight: 500;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.main-content {
  flex: 1;
  min-height: 0;
}

.video-section {
  height: 100%;
}

.video-container {
  margin-bottom: 16px;
  position: relative;
  z-index: 1;  /* 确保视频容器在Canvas下方 */
}

.video-plugin {
  position: relative;
  z-index: 1;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-color-mute);
}

.video-placeholder p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

/* ROI绘制画布 - 简单透明覆盖层 */
.roi-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;  /* 默认不接收事件，绘制时动态启用 */
  z-index: 10;
  background: transparent;
}

.roi-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px;
  background: var(--bg-color-soft);
  border-radius: 4px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.control-label {
  font-size: 14px;
  color: var(--text-color);
  white-space: nowrap;
}

.roi-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.roi-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: var(--bg-color-soft);
  border-radius: 4px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 14px;
  color: var(--text-color);
}

.roi-list {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.roi-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--bg-color-soft);
  border-bottom: 1px solid var(--border-color);
  font-weight: 500;
}

.list-actions {
  display: flex;
  gap: 4px;
}

.roi-items {
  max-height: 300px;
  overflow-y: auto;
}

.roi-item {
  padding: 12px;
  border-bottom: 1px solid var(--border-color-light);
  cursor: pointer;
  transition: background-color 0.2s;
}

.roi-item:hover {
  background: var(--bg-color-soft);
}

.roi-item-selected {
  background: var(--color-primary-light-9);
  border-color: var(--color-primary);
}

.roi-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.roi-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.roi-color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.roi-actions {
  display: flex;
  gap: 4px;
}

.roi-item-info {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--text-color-soft);
}

.no-roi {
  padding: 24px;
  text-align: center;
  color: var(--text-color-mute);
}
</style>

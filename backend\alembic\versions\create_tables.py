"""create_tables

Revision ID: 001
Revises: 
Create Date: 2023-06-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSON

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # 创建VideoSource表
    op.create_table(
        'video_sources',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('source_type', sa.String(), nullable=False),
        sa.Column('path', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    
    # 创建DieCaster表
    op.create_table(
        'die_casters',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('ip_address', sa.String(), nullable=True),
        sa.Column('port', sa.Integer(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    
    # 创建DetectionTemplate表
    op.create_table(
        'detection_templates',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    
    # 创建die_caster_template_association关联表
    op.create_table(
        'die_caster_template_association',
        sa.Column('die_caster_id', sa.Integer(), nullable=False),
        sa.Column('template_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['die_caster_id'], ['die_casters.id'], ),
        sa.ForeignKeyConstraint(['template_id'], ['detection_templates.id'], ),
        sa.PrimaryKeyConstraint('die_caster_id', 'template_id')
    )
    
    # 创建DetectionGroup表
    op.create_table(
        'detection_groups',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('template_id', sa.Integer(), nullable=True),
        sa.Column('die_caster_id', sa.Integer(), nullable=False),
        sa.Column('video_source_id', sa.Integer(), nullable=False),
        sa.Column('config_json', JSON(), nullable=False),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['die_caster_id'], ['die_casters.id'], ),
        sa.ForeignKeyConstraint(['template_id'], ['detection_templates.id'], ),
        sa.ForeignKeyConstraint(['video_source_id'], ['video_sources.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建Alarm表
    op.create_table(
        'alarms',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('timestamp', sa.DateTime(), nullable=True),
        sa.Column('detection_group_id', sa.Integer(), nullable=False),
        sa.Column('roi_name', sa.String(), nullable=False),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('image_path', sa.String(), nullable=True),
        sa.Column('notes', sa.String(), nullable=True),
        sa.Column('operator', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['detection_group_id'], ['detection_groups.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建User表
    op.create_table(
        'users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(), nullable=False),
        sa.Column('hashed_password', sa.String(), nullable=False),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('role', sa.String(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('email'),
        sa.UniqueConstraint('username')
    )


def downgrade():
    op.drop_table('users')
    op.drop_table('alarms')
    op.drop_table('detection_groups')
    op.drop_table('die_caster_template_association')
    op.drop_table('detection_templates')
    op.drop_table('die_casters')
    op.drop_table('video_sources') 
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>WebSDK测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .video-container { 
            width: 800px; 
            height: 450px; 
            border: 2px solid #ccc; 
            margin: 20px 0; 
            background: #000;
        }
        .controls { margin: 20px 0; }
        .controls input, .controls button { 
            margin: 5px; 
            padding: 8px 12px; 
        }
        .log { 
            height: 300px; 
            overflow-y: auto; 
            border: 1px solid #ccc; 
            padding: 10px; 
            background: #f5f5f5; 
            font-family: monospace;
            font-size: 12px;
        }
        .log-item { margin-bottom: 5px; }
        .log-time { color: #666; }
        .log-message { color: #333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSDK基础测试页面</h1>
        
        <div class="controls">
            <h3>设备登录</h3>
            <input type="text" id="ip" placeholder="设备IP" value="************">
            <input type="text" id="port" placeholder="端口" value="80">
            <input type="text" id="username" placeholder="用户名" value="admin">
            <input type="password" id="password" placeholder="密码" value="admin123">
            <button onclick="testLogin()">登录设备</button>
            <button onclick="testPreview()">开始预览</button>
            <button onclick="testStop()">停止预览</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="videoContainer" class="video-container"></div>
        
        <div class="log" id="logContainer"></div>
    </div>

    <!-- WebSDK脚本 -->
    <script src="/websdk/codebase/jsPlugin/jquery.min.js"></script>
    <script src="/websdk/codebase/encryption/AES.js"></script>
    <script src="/websdk/codebase/encryption/cryptico.min.js"></script>
    <script src="/websdk/codebase/encryption/crypto-3.1.2.min.js"></script>
    <script>
        // 确保MD5函数可以全局访问
        if (typeof CryptoJS !== 'undefined' && CryptoJS.MD5) {
            window.MD5 = function(message) {
                return CryptoJS.MD5(message).toString();
            };
        }
    </script>
    <script src="/websdk/codebase/webVideoCtrl.js"></script>
    <script src="/websdk/codebase/jsPlugin/jsPlugin-3.0.0.min.js"></script>

    <script>
        let g_iWndIndex = 0;
        let currentDevice = null;
        let isInitialized = false;

        function addLog(message) {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const logItem = document.createElement('div');
            logItem.className = 'log-item';
            logItem.innerHTML = `<span class="log-time">[${timeStr}]</span> <span class="log-message">${message}</span>`;
            logContainer.appendChild(logItem);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }

        // 初始化WebSDK
        function initWebSDK() {
            addLog('开始初始化WebSDK...');
            
            if (!window.WebVideoCtrl) {
                addLog('错误: WebSDK未加载');
                return false;
            }

            // 检查浏览器支持
            const iRet = WebVideoCtrl.I_SupportNoPlugin();
            if (!iRet) {
                addLog('错误: 当前浏览器版本过低，不支持无插件');
                return false;
            }
            addLog('浏览器支持检查通过');

            // 初始化插件
            WebVideoCtrl.I_InitPlugin("100%", "100%", {
                bWndFull: true,
                iPackageType: 2,
                iWndowType: 1,
                bNoPlugin: true,
                cbSelWnd: function(xmlDoc) {
                    g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
                    addLog(`当前选择的窗口编号：${g_iWndIndex}`);
                },
                cbInitPluginComplete: function() {
                    addLog('WebSDK插件初始化完成');
                    WebVideoCtrl.I_InsertOBJECTPlugin("videoContainer");
                    addLog('WebSDK插件嵌入完成');
                    isInitialized = true;
                },
                cbPluginErrorHandler: function(iWndIndex, iErrorCode, oError) {
                    addLog(`窗口${iWndIndex}错误: ${iErrorCode}`);
                }
            });

            return true;
        }

        function testLogin() {
            if (!isInitialized) {
                addLog('错误: WebSDK未初始化');
                return;
            }

            const ip = document.getElementById('ip').value;
            const port = document.getElementById('port').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!ip || !port) {
                addLog('错误: 请填写IP和端口');
                return;
            }

            const deviceIdentify = `${ip}_${port}`;
            addLog(`正在登录设备: ${ip}:${port}`);

            const iRet = WebVideoCtrl.I_Login(ip, 1, parseInt(port), username, password, {
                success: function(xmlDoc) {
                    addLog(`设备登录成功: ${ip}`);
                    currentDevice = { ip, port, username, password, deviceIdentify };
                },
                error: function(status, xmlDoc) {
                    addLog(`设备登录失败: ${status}`);
                }
            });

            if (iRet === -1) {
                addLog(`设备 ${deviceIdentify} 已登录过`);
                currentDevice = { ip, port, username, password, deviceIdentify };
            }
        }

        function testPreview() {
            if (!currentDevice) {
                addLog('错误: 请先登录设备');
                return;
            }

            addLog('开始预览...');
            const iRet = WebVideoCtrl.I_StartRealPlay(currentDevice.deviceIdentify, {
                iRtspPort: 554,
                iStreamType: 1,
                iChannelID: 1,
                bZeroChannel: false,
                bProxy: false,
                success: function() {
                    addLog('预览成功');
                },
                error: function(status, xmlDoc) {
                    addLog(`预览失败: ${status}`);
                }
            });

            if (iRet === -1) {
                addLog('当前窗口已在播放');
            }
        }

        function testStop() {
            addLog('停止预览...');
            const iRet = WebVideoCtrl.I_Stop();
            if (iRet === 0) {
                addLog('预览已停止');
            } else {
                addLog(`停止预览返回码: ${iRet}`);
            }
        }

        // 页面加载完成后初始化
        window.onload = function() {
            addLog('页面加载完成，开始初始化WebSDK');
            setTimeout(() => {
                if (initWebSDK()) {
                    addLog('WebSDK初始化成功');
                } else {
                    addLog('WebSDK初始化失败');
                }
            }, 100);
        };
    </script>
</body>
</html>

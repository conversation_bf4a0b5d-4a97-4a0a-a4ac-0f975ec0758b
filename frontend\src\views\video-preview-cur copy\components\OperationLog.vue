<template>
  <div class="operation-info-section">
    <div class="info-header">
      <h3>操作信息</h3>
      <div class="header-buttons">
        <button @click="copyLogs" class="copy-btn" title="复制全部日志">复制</button>
        <button @click="$emit('clear')" class="clear-btn" title="清空日志">清空</button>
      </div>
    </div>
    <div class="info-content" ref="infoContent">
      <div 
        v-for="(info, index) in logs" 
        :key="index" 
        class="info-item"
      >
        <span class="info-time">{{ info.time }}</span>
        <span class="info-message">{{ info.message }}</span>
      </div>
      <div v-if="showCopySuccess" class="copy-success-message">
        日志已复制到剪贴板
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
// defineProps 和 defineEmits 在 Vue 3 中是编译器宏，无需导入
import { ElMessage } from 'element-plus'

const props = defineProps({
  logs: {
    type: Array as () => Array<{ time: string; message: string }>,
    default: () => []
  }
})

const emit = defineEmits(['clear'])

// DOM引用
const infoContent = ref<HTMLElement | null>(null)
// 复制成功提示的显示状态
const showCopySuccess = ref(false)

// 复制日志内容到剪贴板
const copyLogs = () => {
  if (props.logs.length === 0) {
    ElMessage.warning('没有可复制的日志内容')
    return
  }

  // 组装日志文本
  const logText = props.logs.map(log => `${log.time} ${log.message}`).join('\n')
  
  try {
    // 使用Clipboard API复制文本
    navigator.clipboard.writeText(logText).then(() => {
      // 显示复制成功提示，3秒后自动消失
      showCopySuccess.value = true
      setTimeout(() => {
        showCopySuccess.value = false
      }, 3000)
      
      console.log('日志已复制到剪贴板')
    }).catch(err => {
      console.error('无法复制日志到剪贴板:', err)
      ElMessage.error('复制失败，请检查浏览器权限')
    })
  } catch (err) {
    console.error('复制到剪贴板失败:', err)
    ElMessage.error('您的浏览器不支持自动复制，请手动复制')
  }
}

// 监听日志变化，自动滚动到底部
watch(() => props.logs.length, () => {
  nextTick(() => {
    if (infoContent.value) {
      infoContent.value.scrollTop = infoContent.value.scrollHeight
    }
  })
})
</script>

<style scoped>
.operation-info-section {
  margin-top: 15px;
  border-radius: 4px;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
}

.info-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

.clear-btn,
.copy-btn {
  padding: 4px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}


.info-content {
  height: 150px;
  overflow-y: auto;
  padding: 5px 0;
  position: relative;
}

.info-item {
  padding: 3px 15px;
  font-size: 12px;
  line-height: 1.4;
  display: flex;
}


.info-time {
  margin-right: 10px;
  flex-shrink: 0;
}

.info-message {
  word-break: break-all;
}

.copy-success-message {
  position: absolute;
  bottom: 10px;
  right: 10px;
  padding: 5px 10px;
  border-radius: 3px;
  font-size: 12px;
  animation: fadeIn 0.3s ease-in, fadeOut 0.3s ease-out 2.7s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}
</style>
/* 看板组件主题样式补充 */

/* 补充缺失的CSS变量 */
:root {
  /* 成功色系 */
  --success-color-rgb: 103, 194, 58;
  --success-color-light: #85ce61;
  --success-color-dark: #529b2e;
  
  /* 警告色系 */
  --warning-color-rgb: 230, 162, 60;
  --warning-color-light: #ebb563;
  --warning-color-dark: #b88230;
  
  /* 危险色系 */
  --danger-color-rgb: 245, 108, 108;
  --danger-color-light: #f78989;
  --danger-color-dark: #c45656;
  
  /* 信息色系 */
  --info-color-rgb: 144, 147, 153;
  --info-color-light: #a6a9ad;
  --info-color-dark: #73767a;
  
  /* 看板专用颜色 */
  --dashboard-card-bg: var(--bg-color);
  --dashboard-card-border: var(--border-color);
  --dashboard-card-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  --dashboard-card-hover-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.15);
  
  /* 状态指示器颜色 */
  --status-online: var(--success-color);
  --status-offline: var(--info-color);
  --status-error: var(--danger-color);
  --status-warning: var(--warning-color);
  
  /* 渐变背景 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-dark) 100%);
  --gradient-success: linear-gradient(135deg, var(--success-color) 0%, var(--success-color-dark) 100%);
  --gradient-warning: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-color-dark) 100%);
  --gradient-danger: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-color-dark) 100%);
  --gradient-info: linear-gradient(135deg, var(--info-color) 0%, var(--info-color-dark) 100%);
}

/* 暗色主题下的看板样式 */
.dark-theme {
  --dashboard-card-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
  --dashboard-card-hover-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.25);
}

/* 看板容器样式 */
.dashboard-container {
  background: var(--bg-color);
  color: var(--text-color);
}

/* 面板通用样式 */
.panel-section {
  background: var(--dashboard-card-bg);
  border: 1px solid var(--dashboard-card-border);
  box-shadow: var(--dashboard-card-shadow);
  transition: all 0.3s ease;
}

.panel-section:hover {
  box-shadow: var(--dashboard-card-hover-shadow);
}

/* 状态指示器样式 */
.status-indicator {
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-indicator.online {
  background-color: var(--status-online);
  box-shadow: 0 0 8px rgba(var(--success-color-rgb), 0.5);
}

.status-indicator.offline {
  background-color: var(--status-offline);
}

.status-indicator.error {
  background-color: var(--status-error);
  box-shadow: 0 0 8px rgba(var(--danger-color-rgb), 0.5);
}

.status-indicator.warning {
  background-color: var(--status-warning);
  box-shadow: 0 0 8px rgba(var(--warning-color-rgb), 0.5);
}

/* 动画效果 */
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式断点 */
@media (max-width: 1400px) {
  .dashboard-container {
    padding: 16px;
  }
}

@media (max-width: 1200px) {
  .dashboard-container {
    padding: 12px;
  }
  
  .dashboard-header {
    padding: 12px 16px;
  }
  
  .dashboard-title {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 8px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .dashboard-title {
    font-size: 18px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

/* 滚动条样式优化 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: var(--bg-color-mute);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: var(--border-color-hover);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-color-mute);
}

/* 暗色主题下的滚动条 */
.dark-theme .panel-content::-webkit-scrollbar-track {
  background: var(--bg-color-soft);
}

.dark-theme .panel-content::-webkit-scrollbar-thumb {
  background: var(--border-color-hover);
}

.dark-theme .panel-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-color-soft);
}

/* 工具提示样式 */
.dashboard-tooltip {
  background: var(--bg-color-soft);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 12px;
  box-shadow: var(--dashboard-card-shadow);
}

/* 加载状态样式 */
.loading-skeleton {
  background: linear-gradient(90deg, var(--bg-color-mute) 25%, var(--bg-color-soft) 50%, var(--bg-color-mute) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 错误状态样式 */
.error-state {
  color: var(--danger-color);
  background: rgba(var(--danger-color-rgb), 0.1);
  border: 1px solid rgba(var(--danger-color-rgb), 0.3);
  border-radius: 4px;
  padding: 12px;
  text-align: center;
}

/* 成功状态样式 */
.success-state {
  color: var(--success-color);
  background: rgba(var(--success-color-rgb), 0.1);
  border: 1px solid rgba(var(--success-color-rgb), 0.3);
  border-radius: 4px;
  padding: 12px;
  text-align: center;
}

/* 警告状态样式 */
.warning-state {
  color: var(--warning-color);
  background: rgba(var(--warning-color-rgb), 0.1);
  border: 1px solid rgba(var(--warning-color-rgb), 0.3);
  border-radius: 4px;
  padding: 12px;
  text-align: center;
}

/* 确保Element Plus组件在看板中的正确显示 */
.dashboard-container .el-button {
  border-radius: 6px;
  font-weight: 500;
}

.dashboard-container .el-card {
  border-radius: 8px;
  box-shadow: var(--dashboard-card-shadow);
}

.dashboard-container .el-tag {
  border-radius: 12px;
  font-weight: 500;
}

.dashboard-container .el-progress-bar__outer {
  border-radius: 10px;
}

.dashboard-container .el-progress-bar__inner {
  border-radius: 10px;
}

/* 暗色主题下的Element Plus组件优化 */
.dark-theme .dashboard-container .el-card {
  background-color: var(--bg-color-soft);
  border-color: var(--border-color);
}

.dark-theme .dashboard-container .el-button {
  background-color: var(--bg-color-soft);
  border-color: var(--border-color);
  color: var(--text-color);
}

.dark-theme .dashboard-container .el-button:hover {
  background-color: var(--bg-color-hover);
  border-color: var(--border-color-hover);
}

.dark-theme .dashboard-container .el-select .el-input__wrapper {
  background-color: var(--bg-color-soft);
  border-color: var(--border-color);
}

.dark-theme .dashboard-container .el-tag {
  background-color: var(--bg-color-soft);
  border-color: var(--border-color);
  color: var(--text-color);
}

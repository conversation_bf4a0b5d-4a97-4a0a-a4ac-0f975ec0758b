import cv2
import numpy as np
import logging
from typing import Optional, Tuple

class GPUAccelerator:
    """
    GPU加速器类，用于检测独立显卡并将OpenCV运算迁移到GPU上
    """
    
    def __init__(self):
        self.gpu_available = False
        self.gpu_device_count = 0
        self.gpu_info = []
        self.logger = logging.getLogger(__name__)
        
        # 性能阈值 - 基于测试结果
        self.thresholds = {
            # 图像大小阈值 (像素数)
            'large_image': 1024 * 1024,      # 1M像素以上用GPU
            'medium_image': 512 * 512,       # 512K像素
            'small_image': 256 * 256,        # 256K像素
        }
        
        # 检测GPU可用性
        self._detect_gpu()
        
    def _detect_gpu(self):
        """
        检测系统中的独立显卡
        """
        try:
            # 检查OpenCV是否支持CUDA
            if cv2.cuda.getCudaEnabledDeviceCount() > 0:
                self.gpu_device_count = cv2.cuda.getCudaEnabledDeviceCount()
                self.gpu_available = True
                
                # 获取GPU信息
                for i in range(self.gpu_device_count):
                    try:
                        cv2.cuda.setDevice(i)
                        device_info = cv2.cuda.DeviceInfo(i)
                        
                        # 注意：当前使用的opencv-python wheel文件未暴露GPU名称
                        # 使用占位符名称代替
                        gpu_name = f"NVIDIA GPU (ID: {i})"

                        gpu_info = {
                            'device_id': i,
                            'name': gpu_name,
                            'major_version': device_info.majorVersion(),
                            'minor_version': device_info.minorVersion(),
                            'total_memory': device_info.totalMemory(),
                            'free_memory': device_info.freeMemory()
                        }
                        self.gpu_info.append(gpu_info)
                        self.logger.info(f"检测到GPU设备 {i}: {gpu_info['name']}")
                    except Exception as e:
                        self.logger.warning(f"获取GPU {i} 信息失败: {e}")
                        
                # 设置默认使用第一个GPU
                if self.gpu_device_count > 0:
                    cv2.cuda.setDevice(0)
                    self.logger.info(f"GPU加速已启用，使用设备: {self.gpu_info[0]['name']}")
                    
            else:
                self.logger.info("未检测到支持CUDA的GPU设备")
                
        except Exception as e:
            self.logger.warning(f"GPU检测失败: {e}")
            self.gpu_available = False
            
    def is_gpu_available(self) -> bool:
        """
        返回GPU是否可用
        """
        return self.gpu_available
        
    def get_gpu_info(self) -> list:
        """
        返回GPU信息列表
        """
        return self.gpu_info
        
    def _should_use_gpu(self, image_shape: Tuple, algorithm_type: str) -> bool:
        """
        安全智能调度：基于算法稳定性和性能优化
        修复GPU内存问题
        """
        if not self.gpu_available:
            return False
            
        # 计算图像像素数
        if len(image_shape) == 3:
            pixels = image_shape[0] * image_shape[1]
        else:
            pixels = image_shape[0] * image_shape[1]
            
        # 基于稳定性的安全调度策略
        if algorithm_type == 'bilateral':
            # 双边滤波：确认稳定，任何大小都用GPU
            return True
        elif algorithm_type in ['resize']:
            # 图像缩放：大图像用GPU，避免小图像传输开销
            return pixels >= self.thresholds['large_image']
        elif algorithm_type in ['cvtcolor', 'absdiff', 'threshold']:
            # 这些算法出现内存问题，暂时只在超大图像时使用GPU
            return pixels >= self.thresholds['large_image'] * 4
        else:
            # 其他算法（高斯模糊、形态学）：内存问题严重，暂时使用CPU
            return False
        
    def resize_gpu(self, image: np.ndarray, size: Tuple[int, int]) -> np.ndarray:
        """
        强制GPU图像缩放 - 优先使用GPU处理
        """
        use_gpu = self._should_use_gpu(image.shape, 'resize')
        
        if not use_gpu:
            self.logger.debug(f"图像缩放使用CPU处理 (GPU不可用)")
            return cv2.resize(image, size, interpolation=cv2.INTER_AREA)
            
        try:
            # 强制使用GPU
            self.logger.debug(f"图像缩放使用GPU处理 (图像大小: {image.shape})")
            gpu_image = cv2.cuda.GpuMat()
            gpu_image.upload(image)
            gpu_resized = cv2.cuda.resize(gpu_image, size, interpolation=cv2.INTER_AREA)
            result = gpu_resized.download()
            return result
            
        except Exception as e:
            self.logger.warning(f"GPU缩放失败，回退到CPU: {e}")
            return cv2.resize(image, size, interpolation=cv2.INTER_AREA)
            
    def gaussian_blur_gpu(self, image: np.ndarray, kernel_size: Tuple[int, int], sigma: float = 0) -> np.ndarray:
        """
        安全高斯模糊 - 使用CPU避免GPU内存问题
        """
        # 由于GPU内存问题，强制使用CPU确保稳定性和算法一致性
        self.logger.debug("高斯模糊使用CPU处理 (避免GPU内存问题，确保算法一致性)")
        return cv2.GaussianBlur(image, kernel_size, sigma)

    def bilateral_filter_gpu(self, image: np.ndarray, d: int = -1, sigma_color: float = 50, sigma_space: float = 50) -> np.ndarray:
        """
        双边滤波 - 强烈推荐GPU（26x加速！）
        """
        if not self.gpu_available:
            return cv2.bilateralFilter(image, d, sigma_color, sigma_space)
            
        try:
            # 双边滤波是GPU的强项，任何大小都用GPU
            self.logger.debug(f"双边滤波使用GPU处理 (预期26x加速) - 图像大小: {image.shape}")
            gpu_image = cv2.cuda.GpuMat()
            gpu_image.upload(image)
            gpu_result = cv2.cuda.bilateralFilter(gpu_image, d, sigma_color, sigma_space)
            result = gpu_result.download()
            if result is not None:
                return result
            else:
                raise Exception("GPU双边滤波返回空结果")
            
        except Exception as e:
            self.logger.warning(f"GPU双边滤波失败，回退到CPU: {e}")
            return cv2.bilateralFilter(image, d, sigma_color, sigma_space)
            
    def cvt_color_gpu(self, image: np.ndarray, code: int) -> np.ndarray:
        """
        强制GPU颜色转换 - 优先使用GPU处理
        """
        use_gpu = self._should_use_gpu(image.shape, 'cvtcolor')
        
        if not use_gpu:
            self.logger.debug(f"颜色转换使用CPU处理 (GPU不可用)")
            return cv2.cvtColor(image, code)
            
        try:
            # 强制使用GPU
            self.logger.debug(f"颜色转换使用GPU处理 (图像大小: {image.shape})")
            gpu_image = cv2.cuda.GpuMat()
            gpu_image.upload(image)
            gpu_converted = cv2.cuda.cvtColor(gpu_image, code)
            result = gpu_converted.download()
            return result
            
        except Exception as e:
            self.logger.warning(f"GPU颜色转换失败，回退到CPU: {e}")
            return cv2.cvtColor(image, code)
            
    def abs_diff_gpu(self, image1: np.ndarray, image2: np.ndarray) -> np.ndarray:
        """
        强制GPU图像差分运算 - 优先使用GPU处理
        """
        if not self.gpu_available:
            self.logger.debug("图像差分使用CPU处理 (GPU不可用)")
            return cv2.absdiff(image1, image2)
            
        try:
            # 强制使用GPU
            self.logger.debug(f"图像差分使用GPU处理 (图像大小: {image1.shape})")
            gpu_image1 = cv2.cuda.GpuMat()
            gpu_image2 = cv2.cuda.GpuMat()
            gpu_image1.upload(image1)
            gpu_image2.upload(image2)
            
            # GPU差分运算
            gpu_diff = cv2.cuda.absdiff(gpu_image1, gpu_image2)
            
            # 下载回CPU
            result = gpu_diff.download()
            return result
            
        except Exception as e:
            self.logger.warning(f"GPU差分运算失败，回退到CPU: {e}")
            return cv2.absdiff(image1, image2)
            
    def threshold_gpu(self, image: np.ndarray, thresh: float, maxval: float, type: int) -> Tuple[float, np.ndarray]:
        """
        强制GPU阈值处理 - 优先使用GPU处理
        """
        if not self.gpu_available:
            self.logger.debug("阈值处理使用CPU处理 (GPU不可用)")
            return cv2.threshold(image, thresh, maxval, type)
            
        try:
            # 强制使用GPU
            self.logger.debug(f"阈值处理使用GPU处理 (图像大小: {image.shape})")
            gpu_image = cv2.cuda.GpuMat()
            gpu_image.upload(image)
            
            # GPU阈值处理
            ret, gpu_thresh = cv2.cuda.threshold(gpu_image, thresh, maxval, type)
            
            # 下载回CPU
            result = gpu_thresh.download()
            return ret, result
            
        except Exception as e:
            self.logger.warning(f"GPU阈值处理失败，回退到CPU: {e}")
            return cv2.threshold(image, thresh, maxval, type)
            
    def morphology_gpu(self, image: np.ndarray, op: int, kernel: np.ndarray, iterations: int = 1) -> np.ndarray:
        """
        安全形态学运算 - 使用CPU避免GPU内存问题
        """
        # 由于GPU内存问题，强制使用CPU确保稳定性
        self.logger.debug("形态学运算使用CPU处理 (避免GPU内存问题)")
        if op == cv2.MORPH_ERODE:
            return cv2.erode(image, kernel, iterations=iterations)
        elif op == cv2.MORPH_DILATE:
            return cv2.dilate(image, kernel, iterations=iterations)
        else:
            return cv2.morphologyEx(image, op, kernel, iterations=iterations)

# 全局GPU加速器实例
_gpu_accelerator = None

def get_gpu_accelerator() -> GPUAccelerator:
    """
    获取全局GPU加速器实例
    """
    global _gpu_accelerator
    if _gpu_accelerator is None:
        _gpu_accelerator = GPUAccelerator()
    return _gpu_accelerator
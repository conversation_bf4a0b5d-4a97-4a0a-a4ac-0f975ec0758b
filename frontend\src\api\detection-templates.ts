import { get, post, put, del } from '@/utils/api'
import type { DieCaster, DetectionTemplate } from '@/types'

export interface DetectionTemplateWithDieCasters extends DetectionTemplate {
  die_caster_ids: number[]
}

export interface DetectionTemplateCreate {
  name: string
  description?: string
}

export interface DetectionTemplateUpdate {
  name?: string
  description?: string
  status?: 'enabled' | 'disabled'
}

export interface BatchAssociateDieCasters {
  die_caster_ids: number[]
}

// 获取所有检测模板
export const getDetectionTemplates = async (): Promise<DetectionTemplate[]> => {
  return get<DetectionTemplate[]>('/detection-templates/')
}

// 创建检测模板
export const createDetectionTemplate = async (data: DetectionTemplateCreate): Promise<DetectionTemplate> => {
  return post<DetectionTemplate>('/detection-templates/', data)
}

// 获取特定检测模板
export const getDetectionTemplate = async (id: number): Promise<DetectionTemplateWithDieCasters> => {
  return get<DetectionTemplateWithDieCasters>(`/detection-templates/${id}/`)
}

// 更新检测模板
export const updateDetectionTemplate = async (id: number, data: DetectionTemplateUpdate): Promise<DetectionTemplate> => {
  return put<DetectionTemplate>(`/detection-templates/${id}/`, data)
}

// 删除检测模板
export const deleteDetectionTemplate = async (id: number): Promise<void> => {
  return del<void>(`/detection-templates/${id}/`)
}

// 批量关联压铸机
export const associateDieCasters = async (templateId: number, data: BatchAssociateDieCasters): Promise<DetectionTemplateWithDieCasters> => {
  return post<DetectionTemplateWithDieCasters>(`/detection-templates/${templateId}/die-casters/`, data)
}

// 移除压铸机关联
export const removeDieCasterAssociation = async (templateId: number, dieCasterId: number): Promise<void> => {
  return del<void>(`/detection-templates/${templateId}/die-casters/${dieCasterId}/`)
}

// 获取可关联的压铸机列表
export const getAvailableDieCasters = async (templateId: number): Promise<DieCaster[]> => {
  return get<DieCaster[]>(`/detection-templates/${templateId}/available-die-casters/`)
}

// 复制检测模板
export const copyDetectionTemplate = async (id: number, newName: string): Promise<DetectionTemplate> => {
  const template = await getDetectionTemplate(id)
  const newTemplate: DetectionTemplateCreate = {
    name: newName,
    description: template.description ? `${template.description} (复制)` : '复制的模板'
  }
  return createDetectionTemplate(newTemplate)
}

// 获取当前运行的检测模板
export const getRunningDetectionTemplates = async (): Promise<DetectionTemplate[]> => {
  const response = await get<DetectionTemplate[]>('/detection-templates/running/current')
  return response.data
}
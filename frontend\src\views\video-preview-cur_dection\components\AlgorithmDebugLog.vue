<template>
  <div class="algorithm-debug-log">
    <el-card class="log-card">
      <template #header>
        <div class="log-header">
          <h3>算法参数日志</h3>
          <div class="header-actions">
            <el-switch
              v-model="autoScroll"
              active-text="自动滚动"
              inactive-text=""
              size="small"
            />
            <el-button type="primary" size="small" @click="clearLogs">清空</el-button>
            <el-button type="success" size="small" @click="exportLogs">导出</el-button>
          </div>
        </div>
      </template>
      
      <div class="log-content" ref="logContainer">
        <div class="filter-bar">
          <el-radio-group v-model="filterType" size="small">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="motion">运动检测</el-radio-button>
            <el-radio-button label="direction">方向检测</el-radio-button>
          </el-radio-group>
          
          <div class="roi-filter">
            <el-select v-model="selectedRoiId" size="small" placeholder="选择ROI" clearable>
              <el-option 
                v-for="roi in roiList" 
                :key="roi.roi_id" 
                :label="`${roi.name || roi.roi_id} (${roi.attribute || '未知'})`"
                :value="roi.roi_id" 
              />
            </el-select>
          </div>
        </div>
        
        <div v-if="filteredLogs.length === 0" class="no-logs">
          <el-empty description="暂无算法日志数据" :image-size="80" />
        </div>
        
        <div v-else class="logs-container">
          <div 
            v-for="(log, index) in filteredLogs" 
            :key="index"
            class="log-item"
            :class="log.type"
          >
            <div class="log-timestamp">{{ formatTime(log.timestamp) }}</div>
            <div class="log-roi-info">
              <span class="roi-id">{{ log.roi_name || log.roi_id }}</span>
              <span class="roi-attribute">{{ log.attribute || '未知' }}</span>
            </div>
            <div class="log-algorithm">
              <strong>{{ getAlgorithmName(log) }}</strong>
              <el-tag v-if="log.type === 'direction' && log.enabled === false" 
                      type="danger" size="small" effect="dark">已禁用</el-tag>
              <el-tag v-else-if="log.type === 'direction' && log.enabled === true" 
                      type="success" size="small" effect="dark">已启用</el-tag>
            </div>
            <div class="log-params">
              <div class="section-title">输入参数:</div>
              <pre>{{ formatParams(log.input_params) }}</pre>
            </div>
            <div class="log-results">
              <div class="section-title">返回结果:</div>
              <pre>{{ formatParams(log.results) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useAlgorithmDebugLog, type ROI, type AlgorithmLog } from '../composables/useAlgorithmDebugLog';
import '../styles/algorithm-debug-log.css';
import { ElEmpty, ElTag } from 'element-plus';

const props = defineProps({
  roiList: {
    type: Array as () => ROI[],
    default: () => []
  }
});

const {
  logs,
  filteredLogs,
  filterType,
  selectedRoiId,
  autoScroll,
  logContainer,
  addLog,
  clearLogs,
  exportLogs,
  formatTime,
  formatParams,
  getAlgorithmName
} = useAlgorithmDebugLog();

// 暴露方法供父组件调用
defineExpose({
  addLog
});
</script> 
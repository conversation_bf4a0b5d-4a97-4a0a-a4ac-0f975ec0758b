<template>
  <div class="roi-list-table">
    <el-table
      ref="tableRef"
      :data="roiList"
      :loading="loading"
      @selection-change="handleSelectionChange"
      row-key="roi_id"
      height="400"
      empty-text="暂无ROI数据"
    >
      <!-- 选择列 -->
      <el-table-column type="selection" width="55" />
      
      <!-- ROI ID -->
      <el-table-column prop="roi_id" label="ROI ID" width="180">
        <template #default="{ row }">
          <el-text class="roi-id" type="primary">{{ row.roi_id }}</el-text>
        </template>
      </el-table-column>
      
      <!-- ROI名称 -->
      <el-table-column prop="name" label="名称" width="120">
        <template #default="{ row }">
          <el-text>{{ row.name || '未命名' }}</el-text>
        </template>
      </el-table-column>
      
      <!-- ROI属性 -->
      <el-table-column prop="attribute" label="属性" width="100">
        <template #default="{ row }">
          <el-tag
            :type="row.attribute === 'yazhu' ? 'danger' : 'primary'"
            size="small"
          >
            {{ getAttributeLabel(row.attribute) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- ROI类型 -->
      <el-table-column prop="roi_type" label="形状" width="80">
        <template #default="{ row }">
          <el-text size="small">
            {{ getShapeLabel(row.roi_type) }}
          </el-text>
        </template>
      </el-table-column>
      
      <!-- 算法类型 -->
      <el-table-column label="算法" width="120">
        <template #default="{ row }">
          <el-tag
            :type="getAlgorithmTagType(row.algorithm_type)"
            size="small"
          >
            {{ getAlgorithmLabel(row.algorithm_type) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <!-- 坐标信息 -->
      <el-table-column label="坐标" width="100">
        <template #default="{ row }">
          <el-text type="info" size="small">
            {{ row.coordinates?.length || 0 }} 个点
          </el-text>
        </template>
      </el-table-column>
      
      <!-- 创建时间 -->
      <el-table-column prop="created_at" label="创建时间" width="160">
        <template #default="{ row }">
          <el-text size="small">
            {{ formatDate(row.created_at) }}
          </el-text>
        </template>
      </el-table-column>
      
      <!-- 状态 -->
      <el-table-column prop="is_active" label="状态" width="80">
        <template #default="{ row }">
          <el-tag 
            :type="row.is_active ? 'success' : 'info'"
            size="small"
          >
            {{ row.is_active ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <!-- 操作 -->
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            text
            @click="handlePreview(row)"
          >
            <el-icon><View /></el-icon>
            预览
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 批量操作工具栏 -->
    <div v-if="selectedRois.length > 0" class="batch-toolbar">
      <el-alert
        :title="`已选择 ${selectedRois.length} 个ROI`"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="batch-actions">
            <el-button size="small" @click="selectAll">全选</el-button>
            <el-button size="small" @click="clearSelection">清空选择</el-button>
            <el-button size="small" @click="selectByType('yazhu')">选择压铸机</el-button>
            <el-button size="small" @click="selectByType('pailiao')">选择排料口</el-button>
          </div>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { View } from '@element-plus/icons-vue'
import type { ElTable } from 'element-plus'

// Props
interface Props {
  roiList: any[]
  loading?: boolean
  selectedRois: any[]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits
interface Emits {
  (e: 'selection-change', selection: any[]): void
  (e: 'preview', roi: any): void
}

const emit = defineEmits<Emits>()

// 引用
const tableRef = ref<InstanceType<typeof ElTable>>()

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  emit('selection-change', selection)
}

// 处理预览
const handlePreview = (roi: any) => {
  emit('preview', roi)
}

// 全选
const selectAll = () => {
  nextTick(() => {
    props.roiList.forEach(row => {
      tableRef.value?.toggleRowSelection(row, true)
    })
  })
}

// 清空选择
const clearSelection = () => {
  tableRef.value?.clearSelection()
}

// 按类型选择
const selectByType = (type: string) => {
  nextTick(() => {
    // 先清空选择
    tableRef.value?.clearSelection()
    // 选择指定类型的ROI
    props.roiList.forEach(row => {
      if (row.attribute === type) {
        tableRef.value?.toggleRowSelection(row, true)
      }
    })
  })
}

// 获取属性标签
const getAttributeLabel = (attribute: string) => {
  switch (attribute) {
    case 'yazhu':
      return '压铸机'
    case 'pailiao':
      return '排料口'
    default:
      return attribute || '未知'
  }
}

// 获取形状标签
const getShapeLabel = (shape: string) => {
  switch (shape) {
    case 'polygon':
      return '多边形'
    case 'rectangle':
      return '矩形'
    case 'circle':
      return '圆形'
    default:
      return shape || '多边形'
  }
}

// 获取算法标签
const getAlgorithmLabel = (algorithm: string) => {
  switch (algorithm) {
    case 'direction':
      return '方向检测'
    case 'motion':
      return '运动检测'
    case 'background_subtraction':
      return '背景减除'
    case 'frame_difference':
      return '帧差法'
    default:
      return algorithm || '未知'
  }
}

// 获取算法标签类型
const getAlgorithmTagType = (algorithm: string) => {
  switch (algorithm) {
    case 'direction':
      return 'warning'
    case 'motion':
    case 'background_subtraction':
    case 'frame_difference':
      return 'success'
    default:
      return 'info'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateString
  }
}

// 暴露方法给父组件
defineExpose({
  selectAll,
  clearSelection,
  selectByType
})
</script>

<style scoped>
.roi-list-table {
  width: 100%;
}

.roi-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.batch-toolbar {
  margin-top: 12px;
}

.batch-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-table .el-table__row) {
  cursor: pointer;
}

:deep(.el-table .el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 修复暗色模式下多选框的颜色问题 */
:deep(.el-checkbox) {
  --el-checkbox-checked-bg-color: #409eff;
  --el-checkbox-checked-border-color: #409eff;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: var(--el-checkbox-checked-bg-color) !important;
  border-color: var(--el-checkbox-checked-border-color) !important;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner::after) {
  border-color: #fff !important;
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  :deep(.el-table .el-table__row:hover) {
    background-color: #262727;
  }

  :deep(.el-checkbox) {
    --el-checkbox-checked-bg-color: #409eff;
    --el-checkbox-checked-border-color: #409eff;
  }
}
</style>

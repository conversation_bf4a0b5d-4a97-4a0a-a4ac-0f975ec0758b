import { request } from './request'

// 基本设置接口
export interface BasicSettings {
  system_name: string
  system_description: string
  admin_email: string
  log_retention_days: number
}

// 存储设置接口
export interface StorageSettings {
  video_storage_path: string
  detection_image_path: string
  auto_cleanup: boolean
  file_retention_days: number
  disk_space_threshold: number
}

// 系统设置项接口
export interface SystemSetting {
  id: number
  setting_key: string
  setting_value: string
  setting_type: string
  description?: string
  category: string
  created_at?: string
  updated_at?: string
}

// 获取基本设置
export const getBasicSettings = () => {
  return request<BasicSettings>({
    url: '/system-settings/basic',
    method: 'GET'
  })
}

// 更新基本设置
export const updateBasicSettings = (data: Partial<BasicSettings>) => {
  return request<BasicSettings>({
    url: '/system-settings/basic',
    method: 'PUT',
    data
  })
}

// 获取存储设置
export const getStorageSettings = () => {
  return request<StorageSettings>({
    url: '/system-settings/storage',
    method: 'GET'
  })
}

// 更新存储设置
export const updateStorageSettings = (data: Partial<StorageSettings>) => {
  return request<StorageSettings>({
    url: '/system-settings/storage',
    method: 'PUT',
    data
  })
}

// 获取所有设置
export const getAllSettings = (params?: { skip?: number; limit?: number }) => {
  return request<SystemSetting[]>({
    url: '/system-settings/',
    method: 'GET',
    params
  })
}

// 创建或更新单个设置
export const createOrUpdateSetting = (data: {
  setting_key: string
  setting_value: string
  setting_type?: string
  description?: string
  category?: string
}) => {
  return request<SystemSetting>({
    url: '/system-settings/',
    method: 'POST',
    data
  })
}
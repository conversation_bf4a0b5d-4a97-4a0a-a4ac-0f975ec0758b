import cv2
import numpy as np
import time
import threading
import socket
import logging
from typing import Dict, Any, Optional, Callable, Tuple, List
from enum import Enum

from .detection_manager import DetectionManager, DetectionResult

class ConnectionStatus(Enum):
    """连接状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"

class VideoWorker:
    """
    视频工作器 - 处理视频源并与检测管理器集成
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.source_url = config.get('source_url', '')
        self.source_type = config.get('source_type', 'rtsp')
        self.reconnect_interval = config.get('reconnect_interval', 5)
        self.max_reconnect_attempts = config.get('max_reconnect_attempts', 10)
        self.connection_timeout = config.get('connection_timeout', 10)
        
        # 视频捕获
        self.cap = None
        self.is_running = False
        self.is_paused = False
        
        # 连接状态
        self.connection_status = ConnectionStatus.DISCONNECTED
        self.reconnect_attempts = 0
        self.last_frame_time = 0
        
        # 帧缓冲
        self.current_frame = None
        self.frame_lock = threading.Lock()
        
        # 检测管理器
        self.detection_manager: Optional[DetectionManager] = None
        
        # 线程
        self.capture_thread = None
        self.processing_thread = None
        
        # 回调函数
        self.frame_callback: Optional[Callable[[np.ndarray], None]] = None
        self.result_callback: Optional[Callable[[List[DetectionResult]], None]] = None
        self.status_callback: Optional[Callable[[ConnectionStatus, str], None]] = None
        
        # 性能设置
        self.processing_interval = 1.0 / config.get('target_fps', 30)
        
        # 日志记录器
        self.logger = logging.getLogger("VideoWorker")
    
    def set_detection_manager(self, detection_manager: DetectionManager):
        """
        设置检测管理器
        
        Args:
            detection_manager: 检测管理器实例
        """
        self.detection_manager = detection_manager
    
    def start(self):
        """
        启动视频工作器
        """
        if self.is_running:
            return
        
        self.is_running = True
        self.is_paused = False
        self.reconnect_attempts = 0
        
        # 启动捕获线程
        self.capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
        self.capture_thread.start()
        
        # 启动处理线程
        self.processing_thread = threading.Thread(target=self._processing_loop, daemon=True)
        self.processing_thread.start()
        
        self.logger.info(f"Started video worker with source: {self.source_url}")
    
    def stop(self):
        """
        停止视频工作器
        """
        self.is_running = False
        
        # 等待线程结束
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2.0)
        
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=2.0)
        
        # 释放资源
        self._release_capture()
        
        self.logger.info("Stopped video worker")
    
    def pause(self):
        """
        暂停视频工作器
        """
        self.is_paused = True
        if self.detection_manager:
            self.detection_manager.pause()
    
    def resume(self):
        """
        恢复视频工作器
        """
        self.is_paused = False
        if self.detection_manager:
            self.detection_manager.resume()
    
    def _capture_loop(self):
        """
        视频捕获循环
        """
        while self.is_running:
            if self.is_paused:
                time.sleep(0.1)
                continue
            
            # 检查连接状态
            if self.connection_status != ConnectionStatus.CONNECTED:
                self._connect_to_source()
            
            # 读取帧
            if self.cap and self.cap.isOpened():
                try:
                    ret, frame = self.cap.read()
                    
                    if not ret:
                        self._handle_connection_error("Failed to read frame")
                        continue
                    
                    # 更新帧
                    with self.frame_lock:
                        self.current_frame = frame
                        self.last_frame_time = time.time()
                    
                    # 检查帧间隔
                    frame_interval = 1.0 / self.config.get('source_fps', 30)
                    sleep_time = max(0, frame_interval - 0.01)
                    if sleep_time > 0:
                        time.sleep(sleep_time)
                    
                except Exception as e:
                    self._handle_connection_error(f"Error reading frame: {str(e)}")
            else:
                time.sleep(0.1)
    
    def _processing_loop(self):
        """
        视频处理循环
        """
        last_process_time = 0
        
        while self.is_running:
            if self.is_paused:
                time.sleep(0.1)
                continue
            
            current_time = time.time()
            
            # 控制处理频率
            if current_time - last_process_time < self.processing_interval:
                time.sleep(0.001)
                continue
            
            # 获取当前帧
            frame = None
            with self.frame_lock:
                if self.current_frame is not None:
                    frame = self.current_frame.copy()
            
            if frame is not None:
                # 调用帧回调
                if self.frame_callback:
                    try:
                        self.frame_callback(frame)
                    except Exception as e:
                        self.logger.error(f"Error in frame callback: {str(e)}")
                
                # 处理帧
                if self.detection_manager:
                    try:
                        results = self.detection_manager.process_frame(frame)
                        
                        # 调用结果回调
                        if self.result_callback and results:
                            self.result_callback(results)
                    except Exception as e:
                        self.logger.error(f"Error in detection manager: {str(e)}")
                
                last_process_time = time.time()
            else:
                time.sleep(0.01)
    
    def _connect_to_source(self):
        """
        连接到视频源
        """
        # 如果已经达到最大重连次数，停止尝试
        if self.reconnect_attempts >= self.max_reconnect_attempts and self.max_reconnect_attempts > 0:
            self._update_connection_status(ConnectionStatus.ERROR, "Max reconnect attempts reached")
            time.sleep(self.reconnect_interval)
            return
        
        # 释放之前的捕获
        self._release_capture()
        
        # 更新状态
        self._update_connection_status(ConnectionStatus.CONNECTING, f"Connecting to {self.source_url}")
        
        try:
            # 检查网络连接
            if self.source_type.lower() in ['rtsp', 'http', 'https']:
                if not self._test_network_connection():
                    self._update_connection_status(ConnectionStatus.ERROR, "Network connection test failed")
                    self.reconnect_attempts += 1
                    time.sleep(self.reconnect_interval)
                    return
            
            # 设置OpenCV捕获参数
            self.cap = cv2.VideoCapture(self.source_url)
            
            # 设置缓冲区大小
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
            
            # 检查连接是否成功
            if not self.cap.isOpened():
                self._update_connection_status(ConnectionStatus.ERROR, "Failed to open video source")
                self.reconnect_attempts += 1
                time.sleep(self.reconnect_interval)
                return
            
            # 读取一帧测试连接
            ret, frame = self.cap.read()
            if not ret or frame is None:
                self._update_connection_status(ConnectionStatus.ERROR, "Failed to read initial frame")
                self.reconnect_attempts += 1
                time.sleep(self.reconnect_interval)
                return
            
            # 连接成功
            self._update_connection_status(ConnectionStatus.CONNECTED, "Connected to video source")
            self.reconnect_attempts = 0
            
            # 更新帧
            with self.frame_lock:
                self.current_frame = frame
                self.last_frame_time = time.time()
            
        except Exception as e:
            self._update_connection_status(ConnectionStatus.ERROR, f"Connection error: {str(e)}")
            self.reconnect_attempts += 1
            time.sleep(self.reconnect_interval)
    
    def _release_capture(self):
        """
        释放视频捕获资源
        """
        if self.cap:
            try:
                self.cap.release()
            except:
                pass
            self.cap = None
    
    def _handle_connection_error(self, error_message: str):
        """
        处理连接错误
        
        Args:
            error_message: 错误消息
        """
        self._update_connection_status(ConnectionStatus.ERROR, error_message)
        self._release_capture()
        time.sleep(self.reconnect_interval)
    
    def _update_connection_status(self, status: ConnectionStatus, message: str):
        """
        更新连接状态
        
        Args:
            status: 连接状态
            message: 状态消息
        """
        if self.connection_status != status:
            self.connection_status = status
            self.logger.info(f"Connection status: {status.value} - {message}")
            
            # 调用状态回调
            if self.status_callback:
                try:
                    self.status_callback(status, message)
                except Exception as e:
                    self.logger.error(f"Error in status callback: {str(e)}")
    
    def _test_network_connection(self) -> bool:
        """
        测试网络连接
        
        Returns:
            连接是否成功
        """
        try:
            # 解析URL获取主机名和端口
            host, port = self._parse_url_host_port(self.source_url)
            if not host:
                return False
            
            # 创建套接字连接测试
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.connection_timeout)
            
            result = sock.connect_ex((host, port))
            sock.close()
            
            return result == 0
        except Exception as e:
            self.logger.error(f"Network connection test error: {str(e)}")
            return False
    
    def _parse_url_host_port(self, url: str) -> Tuple[str, int]:
        """
        从URL解析主机名和端口
        
        Args:
            url: URL字符串
            
        Returns:
            (主机名, 端口)元组
        """
        try:
            # 处理不同协议
            if url.startswith('rtsp://'):
                url = url[7:]  # 移除 'rtsp://'
                default_port = 554
            elif url.startswith('http://'):
                url = url[7:]  # 移除 'http://'
                default_port = 80
            elif url.startswith('https://'):
                url = url[8:]  # 移除 'https://'
                default_port = 443
            else:
                # 本地文件或其他格式
                return ('localhost', 0)
            
            # 移除URL中的路径部分
            if '/' in url:
                url = url.split('/', 1)[0]
            
            # 解析主机名和端口
            if ':' in url:
                host, port_str = url.split(':', 1)
                # 处理可能的认证信息
                if '@' in host:
                    host = host.split('@', 1)[1]
                port = int(port_str)
            else:
                # 处理可能的认证信息
                if '@' in url:
                    host = url.split('@', 1)[1]
                else:
                    host = url
                port = default_port
            
            return (host, port)
        except Exception as e:
            self.logger.error(f"Error parsing URL: {str(e)}")
            return ('', 0)
    
    def get_current_frame(self) -> Optional[np.ndarray]:
        """
        获取当前帧
        
        Returns:
            当前帧或None
        """
        with self.frame_lock:
            if self.current_frame is not None:
                return self.current_frame.copy()
        return None
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取状态信息
        
        Returns:
            状态信息字典
        """
        frame_age = 0
        if self.last_frame_time > 0:
            frame_age = time.time() - self.last_frame_time
        
        return {
            'connection_status': self.connection_status.value,
            'source_url': self.source_url,
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'reconnect_attempts': self.reconnect_attempts,
            'last_frame_time': self.last_frame_time,
            'frame_age': frame_age,
            'has_current_frame': self.current_frame is not None
        }
    
    def set_source(self, source_url: str, source_type: str = None):
        """
        设置视频源
        
        Args:
            source_url: 视频源URL
            source_type: 视频源类型
        """
        if source_url == self.source_url and (source_type is None or source_type == self.source_type):
            return
        
        self.source_url = source_url
        if source_type:
            self.source_type = source_type
        
        # 重置连接
        self._release_capture()
        self.reconnect_attempts = 0
        self.connection_status = ConnectionStatus.DISCONNECTED
    
    def set_callbacks(self, frame_callback: Callable = None, result_callback: Callable = None, 
                     status_callback: Callable = None):
        """
        设置回调函数
        
        Args:
            frame_callback: 帧回调函数
            result_callback: 结果回调函数
            status_callback: 状态回调函数
        """
        if frame_callback:
            self.frame_callback = frame_callback
        if result_callback:
            self.result_callback = result_callback
        if status_callback:
            self.status_callback = status_callback
    
    def update_config(self, config: Dict[str, Any]):
        """
        更新配置
        
        Args:
            config: 配置字典
        """
        # 更新重连设置
        if 'reconnect_interval' in config:
            self.reconnect_interval = config['reconnect_interval']
        if 'max_reconnect_attempts' in config:
            self.max_reconnect_attempts = config['max_reconnect_attempts']
        if 'connection_timeout' in config:
            self.connection_timeout = config['connection_timeout']
        
        # 更新处理设置
        if 'target_fps' in config:
            self.processing_interval = 1.0 / config['target_fps']
        
        # 更新源设置
        source_changed = False
        if 'source_url' in config and config['source_url'] != self.source_url:
            self.source_url = config['source_url']
            source_changed = True
        if 'source_type' in config and config['source_type'] != self.source_type:
            self.source_type = config['source_type']
            source_changed = True
        
        # 如果源发生变化，重置连接
        if source_changed:
            self._release_capture()
            self.reconnect_attempts = 0
            self.connection_status = ConnectionStatus.DISCONNECTED
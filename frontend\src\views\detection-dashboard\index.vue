<template>
  <div class="detection-dashboard">
    <!-- 顶部工具栏 -->
    <div class="dashboard-header">
      <div class="header-left">
        <h2 class="dashboard-title">检测看板监测</h2>
        <div class="layout-controls">
          <span class="control-label">布局:</span>
          <el-radio-group v-model="currentLayout" @change="handleLayoutChange">
            <el-radio-button label="1x1">1×1</el-radio-button>
            <el-radio-button label="2x2">2×2</el-radio-button>
            <el-radio-button label="3x3">3×3</el-radio-button>
            <el-radio-button label="4x4">4×4</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="header-right">
        <el-button type="success" @click="showTemplateDialog = true">
          模板切换
        </el-button>
        <el-button type="primary" @click="showAddDetectionDialog = true">
          添加检测组
        </el-button>
        <el-button @click="refreshAllDetections">
          刷新全部
        </el-button>
        <el-button 
          :type="isFullscreen ? 'warning' : 'info'" 
          @click="toggleFullscreen"
          :icon="isFullscreen ? 'FullScreen' : 'FullScreen'"
        >
          {{ isFullscreen ? '退出全屏' : '全屏显示' }}
        </el-button>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="dashboard-content">
      <!-- 检测区域 -->
      <div class="detection-section">
        <!-- 操作面板区域 -->
        <div class="operation-panel">
          <div class="panel-header">
            <h3 class="panel-title">
              <el-icon><Monitor /></el-icon>
              检测看板操作中心
            </h3>
            <div class="panel-actions">
              <el-button size="small" type="primary" @click="refreshAllDetections">
                <el-icon><Refresh /></el-icon>
                刷新全部
              </el-button>
              <el-button size="small" type="success" @click="showTemplateDialog = true">
                <el-icon><Setting /></el-icon>
                模板管理
              </el-button>
            </div>
          </div>
          <div class="panel-content">
            <div class="quick-stats">
              <div class="stat-item">
                <span class="stat-label">活跃检测组</span>
                <span class="stat-value">{{ activeDetectionCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">当前布局</span>
                <span class="stat-value">{{ currentLayout }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">运行状态</span>
                <span class="stat-value status-running">正常运行</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">当前模板</span>
                <span class="stat-value">{{ currentTemplateName || '未选择' }}</span>
              </div>
            </div>
          </div>
        </div>
         
        <!-- 检测内容区域 -->
        <div class="detection-content">
          <!-- 检测网格容器 -->
          <div 
            class="detection-grid" 
            :class="`layout-${currentLayout.replace('x', '-')}`"
          >
            <div 
              v-for="(detection, index) in detectionList" 
              :key="detection.id"
              class="detection-item"
              :class="{ 'empty': !detection.detectionGroupId }"
            >
              <!-- iframe容器 -->
              <div class="iframe-container">
                <iframe 
                  v-if="detection.detectionGroupId"
                  :src="getDetectionUrl(detection)"
                  :key="detection.refreshKey"
                  frameborder="0"
                  class="detection-iframe"
                  @load="onIframeLoad(index)"
                ></iframe>
                
                <!-- 空白占位 -->
                <div v-else class="empty-placeholder" @click="addDetectionToSlot(index)">
                  <el-icon class="add-icon"><Plus /></el-icon>
                  <span>点击添加检测组</span>
                </div>
              </div>

              <!-- 加载状态 -->
              <div v-if="detection.loading" class="loading-overlay">
                <el-loading-spinner />
                <span>加载中...</span>
              </div>
            </div>
          </div>

          <!-- 卡料检测看板 -->
          <div class="card-detection-section">
            <CardDetectionDashboard />
          </div>
        </div>
      </div>


    </div>

    <!-- 添加检测组对话框 -->
    <el-dialog 
      v-model="showAddDetectionDialog" 
      title="添加检测组" 
      width="500px"
    >
      <el-form :model="addDetectionForm" label-width="100px">
        <el-form-item label="检测组ID">
          <el-input 
            v-model="addDetectionForm.detectionGroupId" 
            placeholder="请输入检测组ID"
          />
        </el-form-item>
        <el-form-item label="显示名称">
          <el-input 
            v-model="addDetectionForm.name" 
            placeholder="请输入显示名称（可选）"
          />
        </el-form-item>
        <el-form-item label="模板ID">
          <el-input 
            v-model="addDetectionForm.templateId" 
            placeholder="请输入检测模板ID（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDetectionDialog = false">取消</el-button>
        <el-button type="primary" @click="addDetection">确定</el-button>
      </template>
    </el-dialog>

    <!-- 模板切换对话框 -->
    <el-dialog 
      v-model="showTemplateDialog" 
      title="检测模板切换" 
      width="800px"
      :close-on-click-modal="false"
    >
      <TemplateSwitch 
        @template-selected="handleTemplateSelected"
        @close="showTemplateDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, FullScreen, Monitor, Refresh, Setting } from '@element-plus/icons-vue'
import { useDetectionStateStore } from '@/stores/detection-state'
import type { DetectionItem, LayoutType } from './types'
import TemplateSwitch from './components/TemplateSwitch.vue'
import CardDetectionDashboard from './components/CardDetectionDashboard.vue'


// 全局检测状态管理
const detectionStateStore = useDetectionStateStore()

// 响应式数据
const currentLayout = ref<LayoutType>('2x2')
const showAddDetectionDialog = ref(false)
const showTemplateDialog = ref(false)
const selectedSlotIndex = ref<number | null>(null)
const isFullscreen = ref(false)
const currentTemplateName = ref<string>('')

// 添加检测组表单
const addDetectionForm = reactive({
  detectionGroupId: '',
  name: '',
  templateId: ''
})

// 检测组列表
const detectionList = ref<DetectionItem[]>([])

// 计算网格大小
const gridSize = computed(() => {
  const [rows, cols] = currentLayout.value.split('x').map(Number)
  return rows * cols
})

// 计算活跃检测组数量
const activeDetectionCount = computed(() => {
  return detectionList.value.filter(item => item.detectionGroupId).length
})

// 初始化检测列表
const initDetectionList = () => {
  const size = gridSize.value
  detectionList.value = Array.from({ length: size }, (_, index) => ({
    id: `slot-${index}`,
    detectionGroupId: '',
    name: '',
    templateId: '',
    loading: false,
    refreshKey: 0
  }))
}

// 处理布局变化
const handleLayoutChange = (layout: LayoutType) => {
  const oldSize = detectionList.value.length
  const newSize = gridSize.value
  
  if (newSize > oldSize) {
    // 增加槽位
    for (let i = oldSize; i < newSize; i++) {
      detectionList.value.push({
        id: `slot-${i}`,
        detectionGroupId: '',
        name: '',
        templateId: '',
        loading: false,
        refreshKey: 0
      })
    }
  } else if (newSize < oldSize) {
    // 减少槽位，保留有内容的槽位
    const activeDetections = detectionList.value
      .filter(item => item.detectionGroupId)
      .slice(0, newSize)
    
    detectionList.value = Array.from({ length: newSize }, (_, index) => 
      activeDetections[index] || {
        id: `slot-${index}`,
        detectionGroupId: '',
        name: '',
        templateId: '',
        loading: false,
        refreshKey: 0
      }
    )
  }
}

// 生成检测URL
const getDetectionUrl = (detection: DetectionItem): string => {
  const baseUrl = window.location.origin
  const params = new URLSearchParams({
    detectionMode: 'true',
    detectionGroupId: detection.detectionGroupId
  })
  
  if (detection.templateId) {
    params.append('templateId', detection.templateId)
  }
  
  return `${baseUrl}/#/video-preview-cur_dection_JIANCE?${params.toString()}`
}

// 添加检测组到指定槽位
const addDetectionToSlot = (index: number) => {
  selectedSlotIndex.value = index
  showAddDetectionDialog.value = true
}

// 添加检测组
const addDetection = () => {
  if (!addDetectionForm.detectionGroupId.trim()) {
    ElMessage.warning('请输入检测组ID')
    return
  }
  
  const targetIndex = selectedSlotIndex.value ?? detectionList.value.findIndex(item => !item.detectionGroupId)
  
  if (targetIndex === -1) {
    ElMessage.warning('没有可用的槽位')
    return
  }
  
  detectionList.value[targetIndex] = {
    id: `detection-${Date.now()}`,
    detectionGroupId: addDetectionForm.detectionGroupId.trim(),
    name: addDetectionForm.name.trim() || `检测组 ${addDetectionForm.detectionGroupId}`,
    templateId: addDetectionForm.templateId.trim(),
    loading: true,
    refreshKey: Date.now()
  }
  
  // 重置表单
  addDetectionForm.detectionGroupId = ''
  addDetectionForm.name = ''
  addDetectionForm.templateId = ''
  selectedSlotIndex.value = null
  showAddDetectionDialog.value = false
  
  ElMessage.success('检测组添加成功')
}

// 移除检测组
const removeDetection = (index: number) => {
  detectionList.value[index] = {
    id: `slot-${index}`,
    detectionGroupId: '',
    name: '',
    templateId: '',
    loading: false,
    refreshKey: 0
  }
  ElMessage.success('检测组已移除')
}

// 刷新单个检测
const refreshDetection = (index: number) => {
  const detection = detectionList.value[index]
  if (detection.detectionGroupId) {
    detection.loading = true
    detection.refreshKey = Date.now()
  }
}

// 刷新全部检测
const refreshAllDetections = async () => {
  const activeDetections = detectionList.value.filter(detection => detection.detectionGroupId)
  
  if (activeDetections.length === 0) {
    ElMessage.warning('没有可刷新的检测组')
    return
  }
  
  ElMessage.success(`正在刷新 ${activeDetections.length} 个检测组，每个间隔2秒`)
  
  // 按顺序刷新检测组，每个间隔2秒
  for (let i = 0; i < detectionList.value.length; i++) {
    const detection = detectionList.value[i]
    if (detection.detectionGroupId) {
      detection.loading = true
      detection.refreshKey = Date.now()
      
      // 如果不是最后一个检测组，等待2秒
      if (i < detectionList.value.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }
  }
}

// iframe加载完成
const onIframeLoad = (index: number) => {
  setTimeout(() => {
    detectionList.value[index].loading = false
  }, 1000)
}

// 全屏切换
const toggleFullscreen = async () => {
  try {
    if (!isFullscreen.value) {
      // 进入全屏
      const element = document.documentElement
      if (element.requestFullscreen) {
        await element.requestFullscreen()
      } else if ((element as any).webkitRequestFullscreen) {
        await (element as any).webkitRequestFullscreen()
      } else if ((element as any).msRequestFullscreen) {
        await (element as any).msRequestFullscreen()
      }
    } else {
      // 退出全屏
      if (document.exitFullscreen) {
        await document.exitFullscreen()
      } else if ((document as any).webkitExitFullscreen) {
        await (document as any).webkitExitFullscreen()
      } else if ((document as any).msExitFullscreen) {
        await (document as any).msExitFullscreen()
      }
    }
  } catch (error) {
    console.error('全屏切换失败:', error)
    ElMessage.error('全屏切换失败')
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!(document.fullscreenElement || 
    (document as any).webkitFullscreenElement || 
    (document as any).msFullscreenElement)
}

// 根据检测组数量自动调整布局
const adjustLayoutByDetectionCount = (detectionCount: number) => {
  let newLayout: LayoutType
  
  if (detectionCount < 4) {
    newLayout = '2x2'
  } else if (detectionCount <= 9) {
    newLayout = '3x3'
  } else {
    newLayout = '4x4'
  }
  
  if (currentLayout.value !== newLayout) {
    currentLayout.value = newLayout
    handleLayoutChange(newLayout)
    ElMessage.info(`根据检测组数量(${detectionCount}个)自动调整布局为 ${newLayout}`)
  }
}

// 处理模板选择
const handleTemplateSelected = async (templateData: { templateId: number, templateName?: string, detectionGroups: any[] }) => {
  try {
    // 更新当前模板名称
    currentTemplateName.value = templateData.templateName || `模板 ${templateData.templateId}`
    
    // 根据检测组数量自动调整布局
    adjustLayoutByDetectionCount(templateData.detectionGroups.length)
    
    // 清空当前所有检测组
    detectionList.value.forEach(item => {
      item.detectionGroupId = ''
      item.name = ''
      item.templateId = ''
      item.loading = false
      item.refreshKey = 0
    })
    
    // 按顺序加载检测组，每个间隔2秒
    for (let i = 0; i < templateData.detectionGroups.length && i < detectionList.value.length; i++) {
      const detectionGroup = templateData.detectionGroups[i]
      
      // 延迟加载，防止瞬间加载过多，每个检测组间隔2秒
      await new Promise(resolve => setTimeout(resolve, i * 2000))
      
      detectionList.value[i] = {
        id: `template-detection-${detectionGroup.id}`,
        detectionGroupId: detectionGroup.id.toString(),
        name: detectionGroup.name || `检测组 ${detectionGroup.id}`,
        templateId: templateData.templateId.toString(),
        loading: true,
        refreshKey: Date.now()
      }
    }
    
    showTemplateDialog.value = false
    ElMessage.success(`模板切换成功，已加载 ${Math.min(templateData.detectionGroups.length, detectionList.value.length)} 个检测组`)
  } catch (error) {
    console.error('模板切换失败:', error)
    ElMessage.error('模板切换失败')
  }
}

// 检查自动启动模板
const checkAutoStartTemplate = async () => {
  try {
    // 首先检查全局状态中是否有运行中的检测
    if (detectionStateStore.isDetectionRunning) {
      const runningDetection = detectionStateStore.currentRunningDetection
      if (runningDetection) {
        console.log('[检测看板] 检测到运行中的模板:', runningDetection.templateName)
        currentTemplateName.value = runningDetection.templateName
        ElMessage.info(`继续执行检测模板 "${runningDetection.templateName}"`)
        return
      }
    }

    // 检查sessionStorage中的自动启动数据
    const autoStartData = sessionStorage.getItem('autoStartTemplate')
    if (autoStartData) {
      const templateData = JSON.parse(autoStartData)
      console.log('[检测看板] 检测到自动启动模板数据:', templateData)

      // 清除sessionStorage中的数据，避免重复执行
      sessionStorage.removeItem('autoStartTemplate')

      if (templateData.autoStart && (templateData.id || templateData.templateId)) {
        const templateId = templateData.id || templateData.templateId
        const templateName = templateData.name || templateData.templateName

        ElMessage.info(`正在自动启动检测模板: ${templateName}`)

        // 延迟3秒后自动启动模板，确保页面完全加载
        setTimeout(async () => {
          await handleTemplateSelected({
            templateId: templateId,
            templateName: templateName,
            detectionGroups: templateData.detectionGroups || []
          })

          ElMessage.success(`预设计划自动启动完成: ${templateName}`)
        }, 3000)
      }
    }
  } catch (error) {
    console.error('[检测看板] 自动启动模板失败:', error)
    // 清除可能损坏的数据
    sessionStorage.removeItem('autoStartTemplate')
  }
}

// 组件挂载
onMounted(() => {
  initDetectionList()
  
  // 检查自动启动模板
  checkAutoStartTemplate()
  
  // 添加全屏状态变化监听器
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.addEventListener('msfullscreenchange', handleFullscreenChange)
})

// 组件卸载
onUnmounted(() => {
  // 移除全屏状态变化监听器
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.removeEventListener('msfullscreenchange', handleFullscreenChange)
})
</script>

<style scoped>
.detection-dashboard {
  height: 100vh;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color);
  box-sizing: border-box;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.dashboard-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.layout-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.header-right {
  display: flex;
  gap: 12px;
}

.dashboard-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
  height: calc(100vh - 100px);
}

.detection-section {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 操作面板样式 */
.operation-panel {
  width: 100%;
  height: 15%;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px var(--el-box-shadow-light);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

.panel-content {
  flex: 1;
  display: flex;
  align-items: center;
}

.quick-stats {
  display: flex;
  gap: 24px;
  width: 100%;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  background: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.stat-value.status-running {
  color: var(--el-color-success);
}

/* 检测内容区域 */
.detection-content {
  width: 100%;
  height: 85%;
  display: flex;
  flex-direction: row;
  gap: 16px;
}

.detection-grid {
  width: 80%;
  height: 100%;
  display: grid;
  gap: 4px;
  padding: 0;
  margin: 0;
  overflow: hidden;
  box-sizing: border-box;
}

.detection-grid .detection-item {
  aspect-ratio: 16/9;
  width: 100%;
  height: auto;
}

.card-detection-section {
  width: 20%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card-detection-section .card-detection-dashboard {
  flex: 1;
  min-height: 0;
}



.layout-1-1 {
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
}

.layout-2-2 {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.layout-3-3 {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
}

.layout-4-4 {
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
}

.detection-item {
  position: relative;
  background-color: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.detection-item:hover {
  box-shadow: 0 4px 12px var(--el-box-shadow-light);
}



.iframe-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  aspect-ratio: 16/9;
}

.detection-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.empty-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: var(--el-text-color-placeholder);
  transition: all 0.3s ease;
  aspect-ratio: 16/9;
}

.empty-placeholder:hover {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.add-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--el-overlay-color-lighter);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.detection-item.empty {
  border-style: dashed;
  border-color: var(--el-border-color-lighter);
}

/* 暗色模式支持 */
.dark-theme .detection-dashboard {
  background-color: var(--bg-color);
}

.dark-theme .dashboard-header {
  background-color: var(--bg-color-soft);
  border-bottom-color: var(--border-color);
}

.dark-theme .detection-item {
  background-color: var(--bg-color-soft);
  border-color: var(--border-color);
}

.dark-theme .operation-panel {
  background-color: var(--bg-color-soft);
  border-color: var(--border-color);
}

.dark-theme .stat-item {
  background-color: var(--bg-color);
  border-color: var(--border-color);
}

.dark-theme .detection-item:hover {
  box-shadow: 0 4px 12px var(--shadow-color-hover);
}

.dark-theme .empty-placeholder {
  color: var(--text-color-mute);
}

.dark-theme .empty-placeholder:hover {
  color: var(--primary-color);
  background-color: var(--bg-color-mute);
}

.dark-theme .loading-overlay {
  background-color: rgba(var(--bg-color-rgb), 0.8);
  color: var(--text-color);
}

/* 全屏模式样式优化 */
:fullscreen .detection-dashboard,
:-webkit-full-screen .detection-dashboard,
:-moz-full-screen .detection-dashboard,
:-ms-fullscreen .detection-dashboard {
  height: 100vh;
  width: 100vw;
}

:fullscreen .dashboard-header,
:-webkit-full-screen .dashboard-header,
:-moz-full-screen .dashboard-header,
:-ms-fullscreen .dashboard-header {
  padding: 12px 16px;
}

:fullscreen .detection-grid,
:-webkit-full-screen .detection-grid,
:-moz-full-screen .detection-grid,
:-ms-fullscreen .detection-grid {
  padding: 12px;
  gap: 6px;
}
</style>
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
压铸检测系统后端独立打包脚本
自动化打包过程，生成可独立运行的exe文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import time

def check_pyinstaller():
    """
    检查PyInstaller是否安装
    """
    try:
        import PyInstaller
        print(f"✅ PyInstaller 已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        return False

def clean_build_dirs():
    """
    清理构建目录
    """
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_path)

def prepare_database():
    """
    准备数据库文件（复制到外部目录）
    """
    db_file = Path("die_casting_detection.db")
    
    if db_file.exists():
        # 创建database目录
        db_dir = Path("database")
        db_dir.mkdir(exist_ok=True)
        
        # 复制数据库文件
        target_db = db_dir / "die_casting_detection.db"
        shutil.copy2(db_file, target_db)
        print(f"📋 数据库文件已复制到: {target_db}")
        
        return True
    else:
        print("⚠️  警告: 未找到数据库文件，将在运行时创建")
        return False

def build_executable():
    """
    构建可执行文件
    """
    spec_file = "backend_standalone.spec"
    
    if not Path(spec_file).exists():
        print(f"❌ 找不到配置文件: {spec_file}")
        return False
    
    print(f"🔨 开始构建可执行文件...")
    print(f"📄 使用配置文件: {spec_file}")
    
    try:
        # 运行PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", spec_file]
        print(f"🚀 执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            return True
        else:
            print("❌ 构建失败！")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现异常: {e}")
        return False

def create_deployment_package():
    """
    创建部署包
    """
    print("📦 创建部署包...")
    
    # 创建部署目录
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    deploy_dir = Path(f"压铸检测系统_部署包_{timestamp}")
    deploy_dir.mkdir(exist_ok=True)
    
    # 复制可执行文件
    exe_file = Path("dist") / "压铸检测系统_后端服务.exe"
    if exe_file.exists():
        shutil.copy2(exe_file, deploy_dir / "压铸检测系统_后端服务.exe")
        print(f"✅ 可执行文件已复制")
    else:
        print(f"❌ 找不到可执行文件: {exe_file}")
        return False
    
    # 复制数据库目录
    db_dir = Path("database")
    if db_dir.exists():
        shutil.copytree(db_dir, deploy_dir / "database")
        print(f"✅ 数据库目录已复制")
    
    # 复制初始化脚本
    init_files = [
        "init_database.py",
        "alembic.ini"
    ]
    
    for file_name in init_files:
        file_path = Path(file_name)
        if file_path.exists():
            shutil.copy2(file_path, deploy_dir / file_name)
            print(f"✅ 已复制: {file_name}")
    
    # 复制alembic目录
    alembic_dir = Path("alembic")
    if alembic_dir.exists():
        shutil.copytree(alembic_dir, deploy_dir / "alembic")
        print(f"✅ Alembic目录已复制")
    
    # 创建启动脚本
    create_batch_scripts(deploy_dir)
    
    # 创建说明文档
    create_readme(deploy_dir)
    
    print(f"\n🎉 部署包创建完成: {deploy_dir}")
    return deploy_dir

def create_batch_scripts(deploy_dir):
    """
    创建批处理启动脚本
    """
    # 启动脚本
    start_script = deploy_dir / "启动系统.bat"
    start_content = '''@echo off
chcp 65001 > nul
echo 正在启动压铸检测系统后端服务...
echo.

REM 检查数据库是否存在
if not exist "die_casting_detection.db" (
    echo 数据库文件不存在，正在从database目录复制...
    if exist "database\\die_casting_detection.db" (
        copy "database\\die_casting_detection.db" "die_casting_detection.db"
        echo 数据库文件复制完成
    ) else (
        echo 警告: 未找到数据库文件，系统将在首次运行时创建
    )
    echo.
)

REM 启动服务
"压铸检测系统_后端服务.exe"

echo.
echo 系统已停止运行
pause
'''
    
    with open(start_script, 'w', encoding='utf-8') as f:
        f.write(start_content)
    
    # 停止脚本
    stop_script = deploy_dir / "停止系统.bat"
    stop_content = '''@echo off
chcp 65001 > nul
echo 正在停止压铸检测系统后端服务...

REM 查找并终止进程
taskkill /f /im "压铸检测系统_后端服务.exe" 2>nul
if %errorlevel%==0 (
    echo 系统已成功停止
) else (
    echo 未找到运行中的系统进程
)

echo.
pause
'''
    
    with open(stop_script, 'w', encoding='utf-8') as f:
        f.write(stop_content)
    
    print("✅ 批处理脚本已创建")

def create_readme(deploy_dir):
    """
    创建说明文档
    """
    readme_content = '''# 压铸检测系统后端服务 - 部署说明

## 📁 文件说明

- `压铸检测系统_后端服务.exe` - 主程序可执行文件
- `启动系统.bat` - 启动脚本（推荐使用）
- `停止系统.bat` - 停止脚本
- `database/` - 数据库文件目录
- `init_database.py` - 数据库初始化脚本
- `alembic/` - 数据库迁移文件
- `alembic.ini` - 数据库迁移配置

## 🚀 快速启动

### 方法1: 使用启动脚本（推荐）
1. 双击运行 `启动系统.bat`
2. 系统会自动检查数据库并启动服务
3. 看到 "Application startup complete" 表示启动成功
4. 访问 http://127.0.0.1:8000 查看API文档

### 方法2: 直接运行
1. 双击 `压铸检测系统_后端服务.exe`
2. 按照提示操作

## 🗄️ 数据库管理

### 首次部署
- 如果 `database/` 目录中有数据库文件，系统会自动复制
- 如果没有数据库文件，系统会在首次运行时创建

### 数据库初始化
如果需要重新初始化数据库：
1. 运行 `python init_database.py`
2. 或者删除 `die_casting_detection.db` 文件后重启系统

### 数据库备份
- 数据库文件: `die_casting_detection.db`
- 定期备份此文件以防数据丢失

## 🌐 访问地址

- **API服务**: http://127.0.0.1:8000
- **API文档**: http://127.0.0.1:8000/docs
- **ReDoc文档**: http://127.0.0.1:8000/redoc

## 📋 系统要求

- Windows 7/8/10/11 (64位)
- 至少 2GB 可用内存
- 至少 1GB 可用磁盘空间

## 🔧 故障排除

### 端口占用
如果提示端口8000被占用：
1. 检查是否有其他程序使用8000端口
2. 使用 `netstat -ano | findstr :8000` 查看占用进程
3. 结束占用进程或修改配置文件中的端口

### 数据库错误
1. 确保 `die_casting_detection.db` 文件存在且可读写
2. 尝试运行数据库初始化脚本
3. 检查磁盘空间是否充足

### 权限问题
1. 以管理员身份运行
2. 确保程序目录有读写权限

## 📞 技术支持

如遇到问题，请提供以下信息：
- 错误信息截图
- 系统版本
- 操作步骤

---

**注意**: 请勿随意删除或修改程序文件，以免影响系统正常运行。
'''
    
    readme_file = deploy_dir / "README.md"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 说明文档已创建")

def main():
    """
    主函数
    """
    print("=" * 60)
    print("🏭 压铸检测系统后端独立打包工具")
    print("=" * 60)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        input("按回车键退出...")
        return
    
    print("\n🔍 开始打包流程...")
    
    try:
        # 1. 清理构建目录
        print("\n1️⃣ 清理构建目录")
        clean_build_dirs()
        
        # 2. 准备数据库
        print("\n2️⃣ 准备数据库文件")
        prepare_database()
        
        # 3. 构建可执行文件
        print("\n3️⃣ 构建可执行文件")
        if not build_executable():
            print("❌ 构建失败，退出")
            input("按回车键退出...")
            return
        
        # 4. 创建部署包
        print("\n4️⃣ 创建部署包")
        deploy_dir = create_deployment_package()
        
        if deploy_dir:
            print("\n" + "=" * 60)
            print("🎉 打包完成！")
            print("=" * 60)
            print(f"📦 部署包位置: {deploy_dir.absolute()}")
            print("\n📋 部署包内容:")
            for item in deploy_dir.iterdir():
                if item.is_file():
                    size = item.stat().st_size / (1024*1024)
                    print(f"  📄 {item.name} ({size:.1f} MB)")
                else:
                    print(f"  📁 {item.name}/")
            
            print("\n🚀 使用说明:")
            print(f"  1. 将 {deploy_dir.name} 文件夹复制到目标机器")
            print(f"  2. 双击运行 '启动系统.bat' 启动服务")
            print(f"  3. 访问 http://127.0.0.1:8000 查看API")
            
        else:
            print("❌ 创建部署包失败")
            
    except Exception as e:
        print(f"\n❌ 打包过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
创建卡料检测结果表的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from app.core.config import settings
from app.models.models import Base, CardDetectionResult
from app.db.session import engine

def create_card_detection_table():
    """创建卡料检测结果表"""
    try:
        print("正在创建卡料检测结果表...")
        
        # 只创建CardDetectionResult表
        CardDetectionResult.__table__.create(engine, checkfirst=True)
        
        print("✅ 卡料检测结果表创建成功！")
        print(f"表名: {CardDetectionResult.__tablename__}")
        print("表结构:")
        for column in CardDetectionResult.__table__.columns:
            print(f"  - {column.name}: {column.type}")
            
    except Exception as e:
        print(f"❌ 创建表时出错: {e}")
        return False
    
    return True

def verify_table():
    """验证表是否创建成功"""
    try:
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        if CardDetectionResult.__tablename__ in tables:
            print(f"✅ 表 '{CardDetectionResult.__tablename__}' 已存在")
            
            # 获取表的列信息
            columns = inspector.get_columns(CardDetectionResult.__tablename__)
            print("表列信息:")
            for column in columns:
                print(f"  - {column['name']}: {column['type']}")
            
            # 获取索引信息
            indexes = inspector.get_indexes(CardDetectionResult.__tablename__)
            if indexes:
                print("索引信息:")
                for index in indexes:
                    print(f"  - {index['name']}: {index['column_names']}")
            
            return True
        else:
            print(f"❌ 表 '{CardDetectionResult.__tablename__}' 不存在")
            return False
            
    except Exception as e:
        print(f"❌ 验证表时出错: {e}")
        return False

if __name__ == "__main__":
    print("=== 卡料检测结果表创建脚本 ===")
    print(f"数据库连接: {engine.url}")
    
    # 首先验证表是否已存在
    if verify_table():
        print("表已存在，无需重复创建")
    else:
        # 创建表
        if create_card_detection_table():
            print("\n验证创建结果:")
            verify_table()
        else:
            print("创建失败")
            sys.exit(1)
    
    print("\n=== 脚本执行完成 ===")
<template>
  <div class="template-switch">
    <!-- Tab 导航 -->
    <el-tabs v-model="activeTab" class="template-tabs">
      <!-- 手动切换检测模板 -->
      <el-tab-pane label="手动切换" name="manual">
        <div class="manual-switch">
          <div class="search-section">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索检测模板..."
              clearable
              class="search-input"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button 
              type="primary"
              @click="loadTemplates" 
              :loading="loading"
            >
              刷新
            </el-button>
          </div>

          <div class="template-list">
            <el-empty v-if="filteredTemplates.length === 0 && !loading" description="暂无检测模板" />
            
            <div v-else class="template-grid">
              <div 
                v-for="template in filteredTemplates" 
                :key="template.id"
                class="template-card"
                :class="{ 'selected': selectedTemplate?.id === template.id }"
                @click="selectTemplate(template)"
              >
                <div class="template-header">
                  <h4 class="template-name">{{ template.name }}</h4>
                  <el-tag :type="getStatusType(template.status)" size="small">
                    {{ getStatusText(template.status) }}
                  </el-tag>
                </div>
                
                <div class="template-info">
                  <p class="template-desc">{{ template.description || '暂无描述' }}</p>
                  <div class="template-meta">
                    <span class="meta-item">
                      <el-icon><Clock /></el-icon>
                      {{ formatDate(template.updated_at) }}
                    </span>
                  </div>
                </div>

                <div class="template-actions">
                  <el-button 
                    type="primary" 
                    size="small"
                    :loading="startingTemplateId === template.id"
                    @click="handleStartTemplate(template)"
                  >
                    启动模板
                  </el-button>
                  <el-button
                  :type="getToggleButtonType(template.status)"
                  size="small"
                  :disabled="template.status === 'switching'"
                  :loading="template.status === 'switching'"
                  @click="handleToggleTemplate(template)"
                >
                  {{ getToggleButtonText(template.status) }}
                </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 预设排版切换 -->
      <el-tab-pane label="预设排版" name="preset">
        <div class="preset-switch">
          <PresetScheduleView />
        </div>
      </el-tab-pane>

      <!-- 自动切换控制 -->
      <el-tab-pane label="自动切换" name="auto">
        <div class="auto-switch">
          <el-empty description="自动切换功能开发中..." />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Document, Clock, Refresh } from '@element-plus/icons-vue'
import { useTemplateSwitch } from '../composables/useTemplateSwitch'
import { updateDetectionTemplate } from '@/api/detection-templates'
import PresetScheduleView from './PresetScheduleView.vue'
import type { DetectionTemplate } from '@/types'

// 定义事件
const emit = defineEmits<{
  templateSelected: [data: { templateId: number, templateName: string, detectionGroups: any[] }]
  close: []
}>()

// 使用组合式函数
const {
  templates,
  loading,
  searchKeyword,
  selectedTemplate,
  startingTemplateId,
  loadTemplates,
  selectTemplate,
  startTemplate: originalStartTemplate
} = useTemplateSwitch()

// 状态切换相关
const togglingTemplateId = ref<number | null>(null)

// 当前激活的Tab
const activeTab = ref('manual')

// 过滤后的模板列表
const filteredTemplates = computed(() => {
  if (!searchKeyword.value) {
    return templates.value
  }
  return templates.value.filter(template => 
    template.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    template.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 启动模板
const startTemplate = async (template: DetectionTemplate) => {
  try {
    const result = await originalStartTemplate(template)
    if (result) {
      emit('templateSelected', result)
    }
  } catch (error) {
    console.error('启动模板失败:', error)
    ElMessage.error('启动模板失败')
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'enabled':
      return 'success'
    case 'disabled':
      return 'info'
    case 'active':
      return 'success'
    case 'inactive':
      return 'info'
    case 'switching':
      return 'warning' // 切换中状态显示为警告色
    default:
      return 'warning' // 未知状态显示为警告色
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'enabled':
      return '启用'
    case 'disabled':
      return '禁用'
    case 'active':
      return '运行中'
    case 'inactive':
      return '未运行'
    case 'switching':
      return '切换中...'
    default:
      return '未知' // 显示原始状态值或"未知"
  }
}

// 获取切换按钮类型
const getToggleButtonType = (status: string) => {
  switch (status) {
    case 'enabled':
      return 'warning' // 启用状态显示警告色（表示可以禁用）
    case 'switching':
      return 'info' // 切换中状态
    default:
      return 'success' // 其他状态显示成功色（表示可以启用）
  }
}

// 获取切换按钮文本
const getToggleButtonText = (status: string) => {
  switch (status) {
    case 'enabled':
      return '禁用' // 当前启用，点击后禁用
    case 'switching':
      return '切换中...' // 切换中状态
    default:
      return '启用' // 当前非启用状态，点击后启用
  }
}

// 处理启动模板
const handleStartTemplate = async (template: DetectionTemplate) => {
  await startTemplate(template)
}

// 处理状态切换
const handleToggleTemplate = async (template: DetectionTemplate) => {
  try {
    togglingTemplateId.value = template.id
    
    // 修复状态切换逻辑：根据当前状态决定下一个状态
    let newStatus: string
    if (template.status === 'enabled') {
      newStatus = 'disabled'
    } else if (template.status === 'disabled') {
      newStatus = 'enabled'
    } else {
      // 对于unknown、active、inactive等其他状态，默认切换到enabled
      newStatus = 'enabled'
    }
    
    console.log(`切换模板 ${template.name} 从 ${template.status} 到 ${newStatus}`)
    
    // 先设置临时状态显示正在切换
    const tempIndex = templates.value.findIndex(t => t.id === template.id)
    if (tempIndex !== -1) {
      const tempTemplate = {
        ...templates.value[tempIndex],
        status: 'switching' as any // 临时状态
      }
      templates.value.splice(tempIndex, 1, tempTemplate)
    }
    
    await updateDetectionTemplate(template.id, { status: newStatus })
    
    // 立即更新本地状态以提供即时反馈
    const updateIndex = templates.value.findIndex(t => t.id === template.id)
    if (updateIndex !== -1) {
      // 创建新的模板对象以触发响应式更新
      const updatedTemplate = {
        ...templates.value[updateIndex],
        status: newStatus
      }
      // 使用splice确保响应式更新
      templates.value.splice(updateIndex, 1, updatedTemplate)
    }
    
    // 延迟重新加载模板列表以确保服务器状态同步
    setTimeout(async () => {
      await loadTemplates()
    }, 100)
    
    ElMessage.success(`模板已${newStatus === 'enabled' ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('切换模板状态失败:', error)
    ElMessage.error('切换模板状态失败')
  } finally {
    togglingTemplateId.value = null
  }
}

// 组件挂载时加载模板
onMounted(() => {
  loadTemplates()
})
</script>

<style scoped>
.template-switch {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.template-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.template-tabs :deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
}

.template-tabs :deep(.el-tab-pane) {
  height: 100%;
  overflow: hidden;
}

.manual-switch {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-section {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.search-input {
  flex: 1;
}

.template-list {
  flex: 1;
  overflow-y: auto;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  padding: 4px;
}

.template-card {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--el-bg-color);
}

.template-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.template-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.template-info {
  margin-bottom: 16px;
}

.template-desc {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

.template-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.template-actions {
  display: flex;
  justify-content: flex-end;
}

.preset-switch,
.auto-switch {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .template-card {
    background: var(--el-bg-color-page);
    border-color: var(--el-border-color-darker);
  }
  
  .template-card:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
  }
}
</style>
from fastapi import APIRouter

from app.api.endpoints import auth, users, database, detection_groups, die_casters, video_sources, system_settings, detection_templates, websdk_proxy, motion_detection, system_info, card_detection, device_status, alarms, analytics, preset_schedules
from app.api import roi_config, system_logs

api_router = APIRouter()

# 挂载各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户"])
api_router.include_router(database.router, prefix="/database", tags=["数据库"])
api_router.include_router(system_settings.router, prefix="/system-settings", tags=["系统设置"])

api_router.include_router(detection_templates.router, prefix="/detection-templates", tags=["检测模板"])
api_router.include_router(detection_groups.router, prefix="/detection-groups", tags=["检测组"])
api_router.include_router(die_casters.router, prefix="/die-casters", tags=["压铸机"])
api_router.include_router(video_sources.router, prefix="/video-sources", tags=["视频源"])
api_router.include_router(websdk_proxy.router, tags=["WebSDK代理"])
api_router.include_router(motion_detection.router, prefix="/motion-detection", tags=["运动检测"])
api_router.include_router(card_detection.router, prefix="/card-detection", tags=["卡料检测"])
api_router.include_router(roi_config.router, tags=["ROI配置"])
api_router.include_router(system_info.router, prefix="/admin", tags=["系统管理"])
api_router.include_router(system_logs.router, prefix="/admin", tags=["系统日志"])
api_router.include_router(device_status.router, prefix="/device", tags=["设备状态"])
api_router.include_router(alarms.router, prefix="/alarms", tags=["报警管理"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["数据分析"])
api_router.include_router(preset_schedules.router, prefix="/preset-schedules", tags=["预设检测计划"])
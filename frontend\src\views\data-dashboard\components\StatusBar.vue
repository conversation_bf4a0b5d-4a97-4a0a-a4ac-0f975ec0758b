<template>
  <div class="status-bar">
    <div class="status-item">
      <el-icon><Monitor /></el-icon>
      <span>系统状态:</span>
      <span class="status-text status-text--success">运行中</span>
    </div>
    <div class="status-item">
      <el-icon><Grid /></el-icon>
      <span>当前模板:</span>
      <span class="status-text">{{ templateName }}</span>
    </div>
    <div class="status-item">
      <el-icon><Cpu /></el-icon>
      <span>活跃检测组:</span>
      <span class="status-text">{{ activeGroupsText }}</span>
    </div>
    <div class="status-item">
      <el-icon><Timer /></el-icon>
      <span>最后更新:</span>
      <span class="status-text">刚刚</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Monitor, Grid, Cpu, Timer } from '@element-plus/icons-vue'
import type { PropType } from 'vue'
import type { DeviceStatus, DetectionTemplate } from '../types/dashboard.types'

const props = defineProps({
  status: {
    type: Object as PropType<DeviceStatus | null>,
    required: true
  },
  template: {
    type: Array as PropType<DetectionTemplate[] | null>,
    required: true
  }
})

const templateName = computed(() => {
  if (props.template && props.template.length > 0) {
    return props.template[0].name
  }
  return '无'
})

const activeGroupsText = computed(() => {
  const count = props.status?.active_detection_groups ?? 0
  return `${count}个`
})
</script>

<style scoped>
.status-bar {
  grid-column: 1 / -1;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12px 20px;
  background-color: var(--bg-color-soft);
  color: var(--text-color-soft);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px var(--shadow-color);
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.status-item .el-icon {
  font-size: 18px;
  color: var(--text-color);
}

.status-text {
  font-weight: 600;
  color: var(--text-color);
}

.status-text--success {
  color: var(--success-color);
}
</style> 
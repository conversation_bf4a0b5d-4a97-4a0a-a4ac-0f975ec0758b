!function(){if(!window.WebVideoCtrl){var e=function(){var e="100%",n="100%",r="";oSecurityCap={},szAESKey="";var s={szversion:"websdk3.220200429",szContainerID:"",szColorProperty:"",szOcxClassId:"clsid:FDF0038A-CF64-4634-81AB-80F0A7946D6C",szMimeTypes:"application/webvideo-plugin-kit",szBasePath:"",iWndowType:1,iPlayMode:2,bWndFull:!0,iPackageType:2,bDebugMode:!1,bNoPlugin:!0,cbSelWnd:null,cbDoubleClickWnd:null,cbEvent:null,cbRemoteConfig:null,cbInitPluginComplete:null,proxyAddress:null},o=null,i=0,a=!1,c=[],u=[],l=null,d=null,p=null,f=this,I=null;this.w_options=s,this.w_deviceSet=c,this.w_wndSet=u,this.w_xmlLocalCfg=I;var h="IPCamera",P="IPDome",m="IPZoom";window.GetSelectWndInfo=function(e){if(q()){i=e;var t=[];t.push("<RealPlayInfo>"),t.push("<SelectWnd>"+e+"</SelectWnd>"),t.push("</RealPlayInfo>"),s.cbSelWnd&&s.cbSelWnd(p.loadXML(t.join("")))}},window.WindowDblClick=function(e){a=e,s.cbDoubleClickWnd&&s.cbDoubleClickWnd(i,a)},window.ZoomInfoCallback=function(e){var t=f.findWndIndexByIndex(i);if(-1!=t){var n=u[t];if(-1!=(t=f.findDeviceIndexByIP(n.szDeviceIdentify))){var r=c[t];r.oProtocolInc.set3DZoom(r,n,e,{success:function(e){},error:function(){}})}}},window.PluginEventHandler=function(e,t,n){q()&&s.cbEvent&&s.cbEvent(e,t,n)},window.GetHttpInfo=function(e,t,n){X.prototype.processCallback(e,t)},window.RemoteConfigInfo=function(e){s.cbRemoteConfig&&s.cbRemoteConfig(e)},window.KeyBoardEventInfo=function(e){100===parseInt(e,10)&&(a=!1,s.cbDoubleClickWnd&&s.cbDoubleClickWnd(i,a))};var g=function(){if(s.bDebugMode){var e=v(arguments);l._alert(e)}},v=function(){for(var e=arguments[0],t=1;t<arguments.length;t++)e=e.replace("%s",arguments[t]);return e},S=function(e){var t=e.indexOf(":");return t>-1?e.substring(0,t):e},y=function(e){return void 0===e},C=function(e){return"[object Object]"===Object.prototype.toString.call(e)},x=function(e,t){var n="",r={type:"GET",async:!1,success:function(e){e&&e.Token&&(n=e.Token.value)}};return f.I_SendHTTPRequest(t,"ISAPI/Security/token?format=json",r),e.oAuthType[e.szIP]&&parseInt(e.oAuthType[e.szIP],10)>=2?n:""},b=function(e,n,r){f.I_GetSecurityVersion(e,{success:function(e){oSecurityCap.iKeyIterateNum=parseInt(t.$XML(e).find("keyIterateNum").eq(0).text(),10),oSecurityCap.oIrreversibleEncrypt={bSupport:"true"===t.$XML(e).find("isIrreversible").eq(0).text(),salt:t.$XML(e).find("salt").eq(0).text()},szAESKey=p.strToAESKey(n,r)}})},D=function(e){f.I_GetDeviceInfo(e.szIP,{success:function(n){e.szDeviceType=t.$XML(n).find("deviceType").eq(0).text()}}),f.I_GetAnalogChannelInfo(e.szIP,{success:function(e){},error:function(){}}),f.I_GetAudioInfo(e.szIP,{success:function(n){var r=t.$XML(n).find("audioCompressionType",!0);if(r.length>0){var s=t.$XML(r).eq(0).text(),o=0;"G.711ulaw"==s?o=1:"G.711alaw"==s?o=2:"G.726"==s?o=3:"MP2L2"==s||"MPEL2"==s?o=4:"G.722.1"==s?o=0:"AAC"==s?o=5:"PCM"==s&&(o=6),e.iAudioType=o}""!==t.$XML(n).find("audioBitRate").eq(0).text()?e.m_iAudioBitRate=1e3*parseInt(t.$XML(n).find("audioBitRate").eq(0).text(),10):e.m_iAudioBitRate=0,""!==t.$XML(n).find("audioSamplingRate").eq(0).text()?e.m_iAudioSamplingRate=1e3*parseInt(t.$XML(n).find("audioSamplingRate").eq(0).text(),10):e.m_iAudioSamplingRate=0}})},T=function(e){e.bSupportWebsocket=!1,e.bSupportSubStreamPlayback=!1,e.oProtocolInc.getSystemCapa(e,{success:function(n){var r=t.$XML(n).find("NetworkCap").eq(0).find("isSupportWebsocket",!0);r.length>0&&(e.bSupportWebsocket="true"===t.$XML(n).find("NetworkCap").eq(0).find("isSupportWebsocket").eq(0).text()),e.bSupportWebsocket=!0,(r=t.$XML(n).find("RacmCap").eq(0).find("isSupportMainAndSubRecord",!0)).length>0&&(e.bSupportSubStreamPlayback="true"===t.$XML(n).find("RacmCap").eq(0).find("isSupportMainAndSubRecord").eq(0).text()),e.bSupportSubStreamPlayback=!0}})},z=function(e){var n=-1,r=-1,s=-1,o=-1,i=-1;return e.oProtocolInc.getPortInfo(e,{async:!1,success:function(e){var a=t.$XML(e).find("AdminAccessProtocol",!0);n=554;for(var c=0,u=a.length;c<u;c++)"rtsp"===t.$XML(a).eq(c).find("protocol").eq(0).text().toLowerCase()&&(n=parseInt(t.$XML(a).eq(c).find("portNo").eq(0).text(),10)),"http"===t.$XML(a).eq(c).find("protocol").eq(0).text().toLowerCase()&&(r=parseInt(t.$XML(a).eq(c).find("portNo").eq(0).text(),10)),"dev_manage"===t.$XML(a).eq(c).find("protocol").eq(0).text().toLowerCase()&&(i=parseInt(t.$XML(a).eq(c).find("portNo").eq(0).text(),10)),"websocket"===t.$XML(a).eq(c).find("protocol").eq(0).text().toLowerCase()&&(s=parseInt(t.$XML(a).eq(c).find("portNo").eq(0).text(),10)),"websockets"===t.$XML(a).eq(c).find("protocol").eq(0).text().toLowerCase()&&(o=parseInt(t.$XML(a).eq(c).find("portNo").eq(0).text(),10))},error:function(){n=-1,r=-1,i=-1,s=-1,o=-1}}),{iRtspPort:n,iHttpPort:r,iDevicePort:i,iWebSocketsPort:o,iWebSocketPort:s}},w=function(e){var n=-1,r=-1,s=-1;return iWebSocketPort=-1,iWebSocketsPort=-1,e.oProtocolInc.getUPnPPortStatus(e,{async:!1,success:function(e){for(var o=t.$XML(e).find("portStatus",!0),i=0,a=o.length;i<a;i++)"rtsp"==t.$XML(o).eq(i).find("internalPort").eq(0).text().toLowerCase()&&(n=parseInt(t.$XML(o).eq(i).find("externalPort").eq(0).text(),10)),"http"==t.$XML(o).eq(i).find("internalPort").eq(0).text().toLowerCase()&&(r=parseInt(t.$XML(o).eq(i).find("externalPort").eq(0).text(),10)),"admin"==t.$XML(o).eq(i).find("internalPort").eq(0).text().toLowerCase()&&(s=parseInt(t.$XML(o).eq(i).find("externalPort").eq(0).text(),10)),"WebSocket"==t.$XML(o).eq(i).find("internalPort").eq(0).text().toLowerCase()&&(iWebSocketPort=parseInt(t.$XML(o).eq(i).find("externalPort").eq(0).text(),10)),"WebSocketS"==t.$XML(o).eq(i).find("internalPort").eq(0).text().toLowerCase()&&(iWebSocketsPort=parseInt(t.$XML(o).eq(i).find("externalPort").eq(0).text(),10))},error:function(){n=-1,r=-1,s=-1,iWebSocketsPort=-1,iWebSocketPort=-1}}),{iRtspPort:n,iHttpPort:r,iDevicePort:s,iWebSocketsPort:iWebSocketsPort,iWebSocketPort:iWebSocketPort}},R=function(e){var n=[];return e.oProtocolInc.getNetworkBond(e,{async:!1,success:function(r){"true"==t.$XML(r).find("enabled").eq(0).text()?n.push({ipv4:t.$XML(r).find("ipAddress").eq(0).text(),ipv6:t.$XML(r).find("ipv6Address").eq(0).text()}):e.oProtocolInc.getNetworkInterface(e,{async:!1,success:function(e){for(var r=0,s=t.$XML(e).find("NetworkInterface",!0).length;r<s;r++){n.push({ipv4:t.$XML(e).find("ipAddress").eq(0).text(),ipv6:t.$XML(e).find("ipv6Address").eq(0).text()});break}},error:function(){}})},error:function(){e.oProtocolInc.getNetworkInterface(e,{async:!1,success:function(e){for(var r=0,s=t.$XML(e).find("NetworkInterface",!0).length;r<s;r++){n.push({ipv4:t.$XML(e).find("ipAddress").eq(0).text(),ipv6:t.$XML(e).find("ipv6Address").eq(0).text()});break}},error:function(){}})}}),n},A=function(e){var n=!1;return e.oProtocolInc.getPPPoEStatus(e,{async:!1,success:function(e){n=t.$XML(e).find("ipAddress",!0).length>0||t.$XML(e).find("ipv6Address",!0).length>0},error:function(){n=!1}}),n},L=function(e){e.oProtocolInc instanceof _&&e.oProtocolInc.getSDKCapa(e,{async:!1,success:function(n){e.oStreamCapa.bObtained=!0,e.oStreamCapa.bSupportShttpPlay="true"===t.$XML(n).find("isSupportHttpPlay").eq(0).text(),e.oStreamCapa.bSupportShttpPlayback="true"===t.$XML(n).find("isSupportHttpPlayback").eq(0).text(),e.oStreamCapa.bSupportShttpsPlay="true"===t.$XML(n).find("isSupportHttpsPlay").eq(0).text(),e.oStreamCapa.bSupportShttpsPlayback="true"===t.$XML(n).find("isSupportHttpsPlayback").eq(0).text(),e.oStreamCapa.bSupportShttpPlaybackTransCode="true"===t.$XML(n).find("isSupportHttpTransCodePlayback").eq(0).text(),e.oStreamCapa.bSupportShttpsPlaybackTransCode="true"===t.$XML(n).find("isSupportHttpsTransCodePlayback").eq(0).text(),t.$XML(n).find("ipChanBase",!0).length>0?e.oStreamCapa.iIpChanBase=parseInt(t.$XML(n).find("ipChanBase").eq(0).text(),10):e.oStreamCapa.iIpChanBase=1},error:function(){e.oStreamCapa.bObtained=!0}})},q=function(){if(s.bNoPlugin){var e=p.browser();return!!(e.chrome&&parseInt(e.version,10)>90||e.mozilla&&parseInt(e.version,10)>90)}return!1},M=function(e){var t=location.hostname,n=location.port;return"http:"==location.protocol?n=n||"80":"https:"==location.protocol&&(n=n||"443"),s.proxyAddress&&(t=s.proxyAddress.ip,n=s.proxyAddress.port),/^(http|https):\/\/([^\/]+)(.+)$/.test(e)&&(e=e.replace(RegExp.$2,t+":"+n)),p.cookie("webVideoCtrlProxy",RegExp.$2,{raw:!0}),e},k=function(e){p.cookie("webVideoCtrlProxyWss",null),p.cookie("webVideoCtrlProxyWs",null);var t=location.hostname,n=location.port;return/^(ws):\/\/([^\/:]+):(\d+)\/(.+)$/.test(e)?(n=location.port||"80",e=e.replace(RegExp.$2+":"+RegExp.$3,t+":"+n)):/^(wss):\/\/([^\/:]+):(\d+)\/(.+)$/.test(e)&&(n=location.port||"443",e=e.replace(RegExp.$2+":"+RegExp.$3,t+":"+n)),e.indexOf("wss")>-1?p.cookie("webVideoCtrlProxyWss",RegExp.$2+":"+RegExp.$3,{raw:!0}):p.cookie("webVideoCtrlProxyWs",RegExp.$2+":"+RegExp.$3,{raw:!0}),p.cookie("webVideoCtrlProxyWsChannel",RegExp.$4,{raw:!0}),e+"/webSocketVideoCtrlProxy"},G=function(){return p.loadXML("<ResponseStatus><requestURL></requestURL><statusCode>4</statusCode><statusString>Invalid Operation</statusString><subStatusCode>notSupport</subStatusCode></ResponseStatus>")};function W(){if(void 0!==W.unique)return W.unique;this.szIP="",this.szHostName="",this.szAuth="",this.szHttpProtocol="http://",this.iCGIPort=80,this.szDeviceIdentify="",this.iDevicePort=-1,this.iHttpPort=-1,this.iHttpsPort=-1,this.iRtspPort=-1,this.iWSPort=-1,this.iAudioType=1,this.m_iAudioBitRate=-1,this.m_iAudioSamplingRate=-1,this.iDeviceProtocol=1,this.oProtocolInc=null,this.iAnalogChannelNum=0,this.szDeviceType="",this.bVoiceTalk=!1,this.oAuthType={},this.oStreamCapa={bObtained:!1,bSupportShttpPlay:!1,bSupportShttpPlayback:!1,bSupportShttpsPlay:!1,bSupportShttpsPlayback:!1,bSupportShttpPlaybackTransCode:!1,bSupportShttpsPlaybackTransCode:!1,iIpChanBase:1},W.unique=this}this.I_SupportNoPlugin=function(){return q()},this.I_DestroyWorker=function(){null!==o&&q()&&o.JS_DestroyWorker()},this.I_Resize=function(t,r){null!==o&&q()&&(e=t,n=r,o.JS_Resize(t,r))},this.I_InitPlugin=function(t,r,i){if(e=t,n=r,p.extend(s,i),q()){var c=p.getDirName();c&&("object"==typeof exports&&"undefined"!=typeof module||("function"==typeof define&&define.amd?require([c+"/jsPlugin/jsPlugin-3.0.0.min"],(function(e){window.JSPlugin=e.JSPlugin,i.cbInitPluginComplete&&i.cbInitPluginComplete()})):p.loadScript(c+"/jsPlugin/jsPlugin-3.0.0.min.js",(function(){i.cbInitPluginComplete&&i.cbInitPluginComplete()})))),window.addEventListener("resize",(function(){if(null!==o&&q()){var e=$("#"+s.szContainerID);o.JS_Resize(e.width(),e.height())}})),y(document.fullScreen)?y(document.webkitIsFullScreen)?y(document.mozFullScreen)||document.addEventListener("mozfullscreenchange",(function(e){var t=document.fullscreen||document.webkitIsFullScreen||document.mozFullScreen||!1;a&&!t&&window.KeyBoardEventInfo(100)})):document.addEventListener("webkitfullscreenchange",(function(e){var t=document.fullscreen||document.webkitIsFullScreen||document.mozFullScreen||!1;a&&!t&&window.KeyBoardEventInfo(100)})):document.addEventListener("fullscreenchange",(function(e){var t=document.fullscreen||document.webkitIsFullScreen||document.mozFullScreen||!1;a&&!t&&window.KeyBoardEventInfo(100)})),window.addEventListener("unload",(function(){null!==o&&o.JS_DestroyWorker()}))}},this.I_InsertOBJECTPlugin=function(t){if(y(t)||(s.szContainerID=t),null==document.getElementById(s.szContainerID))return-1;if(null!=document.getElementById(r)||0!=document.getElementsByName(r).length)return-1;var i=document.getElementById(t);if(e=i.offsetWidth,n=i.offsetHeight,q()){var a={szId:t,iType:1,iWidth:e,iHeight:n,iMaxSplit:4,iCurrentSplit:s.iWndowType,szBasePath:p.getDirName()+"/jsPlugin"},c=s.szColorProperty;if(""!=c){for(var u={},l=c.split(";"),d="",f=0,h=l.length;f<h;f++)(d=l[f]).indexOf("sub-background")>-1?u.background="#"+d.split(":")[1]:d.indexOf("sub-border-select")>-1?u.borderSelect="#"+d.split(":")[1]:d.indexOf("sub-border")>-1&&(u.border="#"+d.split(":")[1]);a.oStyle=u}(o=new JSPlugin(a)).JS_SetWindowControlCallback({onGetSelectWndInfo:window.GetSelectWndInfo,pluginErrorHandler:function(e,t,n){console.log(e,t,n),s.cbPluginErrorHandler(e,t,n)},performanceLack:function(){s.cbPerformanceLack()},secretKeyError:function(e){s.cbSecretKeyError(e)}})}return null==o&&null==o.object?-1:("object"==typeof window.attachEvent&&p.browser().msie&&(o.attachEvent("GetSelectWndInfo",GetSelectWndInfo),o.attachEvent("ZoomInfoCallback",ZoomInfoCallback),o.attachEvent("GetHttpInfo",GetHttpInfo),o.attachEvent("PluginEventHandler",PluginEventHandler),o.attachEvent("RemoteConfigInfo",RemoteConfigInfo),o.attachEvent("KeyBoardEventInfo",KeyBoardEventInfo)),function(){if(!q()&&null!==o){var e=o.HWP_GetLocalConfig();I=p.loadXML(e)}}(),0)},this.I2_OpenFileDlg=function(e){return new Promise((function(t){let n=window.document.createElement("input");n.type="file",n.addEventListener("change",(function(){let r="";1===e&&(r=n.value),t({szFileName:r,file:n.files[0]})}));let r=document.createEvent("MouseEvents");r.initEvent("click",!0,!0),n.dispatchEvent(r)}))},this.getAuthType=function(e,n,r,s,o,i){e.oAuthType[n]=1;var a={async:!0,success:function(r){e.oAuthType[n]=t.$XML(r).find("sessionIDVersion").eq(0).text(),i()},error:function(t){t>500&&(e.oAuthType[n]=t),i(t)}};d.getSessionCap(n,r,s,o,a)},this.setDeviceInfo=function(e,t,n,r,s){return(t=new W).szIP=n,2==r?(t.szHttpProtocol="https://",t.iHttpsPort=s):(t.szHttpProtocol="http://",t.iHttpPort=s),t.iCGIPort=s,t.szDeviceIdentify=n+"_"+s,t.iDeviceProtocol=1,t.oProtocolInc=e,t},this.successV1cb=function(e,n,r,s,o,i,a,u){(n=this.setDeviceInfo(e,n,r,s,o)).szAuth=t.$XML(i).find("sessionID").eq(0).text(),D(n),g("使用%s协议登录成功",1),n.sessionFailed=0,c.push(n),T(n),b(n.szDeviceIdentify,a,u),n.sesstionTimer=setInterval((function(){e.sessionHeartbeat(n,(function(){n.sessionFailed=0}),(function(){n.sessionFailed++,n.sessionFailed>=5&&(window.PluginEventHandler(null,-1,n.szDeviceIdentify),clearInterval(n.sesstionTimer))}))}),3e4)},this.successV2cb=function(e,n,r,s,o,i,a,u){(n=this.setDeviceInfo(e,n,r,s,o)).szAuth=x(n,n.szDeviceIdentify);let l=t.$XML(i).find("sessionTag").eq(0).text();l&&(n.sessionTag=l),D(n),g("使用%s协议登录成功",1),n.sessionFailed=0,c.push(n),T(n),b(n.szDeviceIdentify,a,u),n.sesstionTimer=setInterval((function(){e.sessionHeartbeat(n,(function(){n.sessionFailed=0}),(function(){n.sessionFailed++,n.sessionFailed>=5&&(window.PluginEventHandler(null,-1,n.szDeviceIdentify),clearInterval(n.sesstionTimer))}))}),3e4)},this.I_LoginV1=function(e,t,n,r,s,o,i,a){var c=this,u={success:null,error:null};return p.extend(u,{success:function(u){var l={success:null,error:null};p.extend(l,{success:function(u){a.success&&(a.success(u),c.successV1cb(e,t,n,r,s,u,i,o))},error:function(e,t){a.error&&a.error(e,t)}}),e.sessionLogin(n,r,s,o,i,u,l)},error:function(e,t){a.error&&a.error(e,t)}}),u},this.I_LoginV2=function(e,t,n,r,s,o,i,a,c){var u={success:null,error:null},l=this;return p.extend(u,{success:function(u){var d={success:null,error:null};p.extend(d,{success:function(e){l.successV2cb(t,n,r,s,o,e,a,i),c.success&&c.success(e)},error:function(e,t){c.error&&c.error(e,t)}}),t.sessionV2Login(e,r,s,o,i,a,u,d)},error:function(e,t){c.error&&c.error(e,t)}}),u},this.I_Login=function(e,t,n,r,s,o){var i=e+"_"+n;if(-1!=this.findDeviceIndexByIP(i))return g("设备已经登录过"),-1;var a=d,c=1;if(y(o.cgi)||1==o.cgi&&(a=d,c=1),q())if(1==c){var u=new W;this.getAuthType(u,e,t,n,r,(function(){var i=u.oAuthType[e];if(i>2){var c=MD5((new Date).getTime().toString()).substring(0,8);c=parseInt(c.replace("#",""),16).toString().substring(0,8);var l=this.I_LoginV2(c,a,u,e,t,n,r,s,o);a.getSessionV2Cap(c,e,t,n,r,l)}else if(i<2){l=this.I_LoginV1(a,u,e,t,n,r,s,o);a.getSessionCap(e,t,n,r,l)}else{c=MD5((new Date).getTime().toString()).substring(0,8);c=parseInt(c.replace("#",""),16).toString().substring(0,8);l=this.I_LoginV2(c,a,u,e,t,n,r,s,o);a.getSessionV2Cap(c,e,t,n,r,l)}}))}else o.error&&o.error(403,G())},this.I_Logout=function(e){var t=this.findDeviceIndexByIP(e);if(-1!=t){if(q()){var n=c[t];clearInterval(n.sesstionTimer),n.oProtocolInc.sessionLogout(n,{})}return c.splice(t,1),0}return-1},this.I_GetAudioInfo=function(e,t){var n=this.findDeviceIndexByIP(e);if(-1!=n){var r=c[n],s={success:null,error:null};p.extend(s,t),r.oProtocolInc.getAudioInfo(r,s)}},this.I_GetDeviceInfo=function(e,t){var n=this.findDeviceIndexByIP(e);if(-1!=n){var r=c[n],s={success:null,error:null};p.extend(s,t),r.oProtocolInc.getDeviceInfo(r,s)}},this.I_GetAnalogChannelInfo=function(e,t){var n=this.findDeviceIndexByIP(e);if(-1!=n){var r=c[n],s={success:null,error:null};p.extend(s,t),r.oProtocolInc.getAnalogChannelInfo(r,s)}},this.I_GetSecurityVersion=function(e,t){var n=this.findDeviceIndexByIP(e);if(-1!=n){var r=c[n],s={success:null,error:null};p.extend(s,t),r.oProtocolInc.getSecurityVersion(r,s)}},this.I_GetDigitalChannelInfo=function(e,t){var n=this.findDeviceIndexByIP(e);if(-1!=n){var r=c[n],s={success:null,error:null};p.extend(s,t),r.oProtocolInc.getDigitalChannelInfo(r,s)}},this.I_GetZeroChannelInfo=function(e,t){var n=this.findDeviceIndexByIP(e);if(-1!=n){var r=c[n],s={success:null,error:null};p.extend(s,t),r.oProtocolInc.getZeroChannelInfo(r,s)}},this.getStream=function(e,t,n){var r=$.Deferred();return q()&&o.JS_Play(e,t,n).then((function(){addToWndSet(),r.resolve()}),(function(){r.reject()})),r},this.I_StartRealPlay=function(e,t){var n=this.findDeviceIndexByIP(e),r="",s="",o=-1,a=0,l=0,d=!1,f={iWndIndex:i,iStreamType:1,iChannelID:1,bZeroChannel:!1};if(p.extend(f,t),-1!=n){L(c[n]);var I=c[n];if(q()){if(!I.bSupportWebsocket)return void(t.error&&t.error(403,G()));r=I.oProtocolInc.CGI.startWsRealPlay;var h=location.protocol;/^(https)(.*)$/.test(h)?(s="wss://",o=I.iWebSocketsPort):(s="ws://",o=I.iWebSocketPort),y(f.iWSPort)||(o=f.iWSPort),l=f.iStreamType,a=f.iChannelID<=I.iAnalogChannelNum?f.iChannelID:I.oStreamCapa.iIpChanBase+parseInt(f.iChannelID,10)-I.iAnalogChannelNum-1,d=!0}if(-1==o)return g("获取端口号失败"),void(t.error&&t.error());p.extend(f,{urlProtocol:s,cgi:r,iPort:o,iStreamType:l,iChannelID:a}),n=this.findWndIndexByIndex(f.iWndIndex);var P=this;-1==n&&I.oProtocolInc.startRealPlay(I,f).then((function(){n=P.findWndIndexByIndex(f.iWndIndex),u[n].bShttpIPChannel=d,t.success&&t.success()}),(function(){q()||(I.iRtspPort=-1),t.error&&t.error()}))}else t.error&&t.error()},this.I_GetWndSet=function(e){return JSON.parse(JSON.stringify(u))},this.I_Stop=function(e){var t;t=0===e.iIndex?{iWndIndex:0}:{iWndIndex:e.iIndex||i},C(e)?p.extend(t,e):y(e)||(t.iWndIndex=e);var n=this.findWndIndexByIndex(t.iWndIndex);if(-1!=n){var r=u[n];r.bRecord&&q(),r.bSound&&q(),r.bEZoom&&q(),q()&&o.JS_Stop(t.iWndIndex).then((function(){u.splice(n,1),t.success&&t.success()}),(function(){t.error&&t.error()}))}else t.error&&t.error()},this.I_StopAll=function(){return u=[],o.JS_StopRealPlayAll()},this.I_OpenSound=function(e){return new Promise(((t,n)=>{e=y(e)?i:e,-1!=this.findWndIndexByIndex(e)?o.JS_OpenSound(e).then((()=>{t()})).catch((e=>{n(e)})):n()}))},this.I_CloseSound=function(e){return new Promise(((t,n)=>{e=y(e)?i:e;var r=this.findWndIndexByIndex(e);if(-1!=r){var s=u[r];o.JS_CloseSound(e).then((()=>{s.bSound=!1,t()})).catch((e=>{n(e)}))}else n()}))},this.I_SetVolume=function(e,t){var n=Promise.reject();return e=parseInt(e,10),isNaN(e)||e<0||e>100?n:(t=y(t)?i:t,-1!=this.findWndIndexByIndex(t)?n=o.JS_SetVolume(t,e):n)},this.I2_CapturePic=function(e,t){var n={iWndIndex:i,bDateDir:!0};C(t)?p.extend(n,t):y(t)||(n.iWndIndex=t);var r=this.findWndIndexByIndex(n.iWndIndex),s=$.Deferred();if(-1!=r){if(q()){var a="JPEG";".jpg"===e.slice(-4).toLowerCase()?e=e.slice(0,-4):".jpeg"===e.slice(-5).toLowerCase()?e=e.slice(0,-5):".bmp"===e.slice(-4).toLowerCase()&&(e=e.slice(0,-4),a="BMP"),o.JS_CapturePicture(n.iWndIndex,e,a,n.cbCallback).then((function(){s.resolve()}),(function(){s.reject()}))}}else s.reject();return s},this.I_StartRecord=function(e,t){var n={iWndIndex:i,bDateDir:!0};C(t)?p.extend(n,t):y(t)||(n.iWndIndex=t);var r=this.findWndIndexByIndex(n.iWndIndex);if(-1!=r){var s=u[r];if(s.bRecord)n.error&&n.error();else if(q())p.browser().chrome?o.JS_StartSave(n.iWndIndex,e).then((function(){s.bRecord=!0,n.success&&n.success()}),(function(){n.error&&n.error()})):n.error&&n.error()}else n.error&&n.error()},this.I_StopRecord=function(e){var t={iWndIndex:i};C(e)?p.extend(t,e):y(e)||(t.iWndIndex=e);var n=this.findWndIndexByIndex(t.iWndIndex);if(-1!=n){var r=u[n];if(r.bRecord){if(q())p.browser().chrome?o.JS_StopSave(t.iWndIndex).then((function(){r.bRecord=!1,t.success&&t.success()}),(function(){t.error&&t.error()})):t.error&&t.error()}else t.error&&t.error()}else t.error&&t.error()},this.I_PTZControl=function(e,t,n){var r={iWndIndex:i,iPTZIndex:e,iPTZSpeed:4};p.extend(r,n),p.extend(r,{async:!1});var s=this.findWndIndexByIndex(r.iWndIndex);if(-1!=s){var o=u[s];if(-1!=(s=this.findDeviceIndexByIP(o.szIP))){var a=c[s];9==e?a.oProtocolInc.ptzAutoControl(a,t,o,r):a.oProtocolInc.ptzControl(a,t,o,r)}}},this.I_EnableEZoom=function(e){return new Promise(((t,n)=>{e=y(e)?i:e;var r=this.findWndIndexByIndex(e);if(-1!=r){var s=u[r];s.bEZoom?n():q()&&o.JS_EnableZoom(e).then((e=>{s.bEZoom=!0,t()})).catch((()=>{n()}))}else n()}))},this.I_DisableEZoom=function(e){return new Promise(((t,n)=>{e=y(e)?i:e;var r=this.findWndIndexByIndex(e);if(-1!=r){var s=u[r];s.bEZoom?q()&&o.JS_DisableZoom(e).then((e=>{s.bEZoom=!1,t()})).catch((()=>{n()})):n()}else n()}))},this.I_Enable3DZoom=function(e,t){e=y(e)?i:e;var n=this.findWndIndexByIndex(e);if(-1!=n){var r=u[n];if(!r.b3DZoom)return o.JS_Enable3DZoom(e,(function(e){window.ZoomInfoCallback(e),t&&t(e)})).then((()=>{r.b3DZoom=!0}))}return Promise.reject()},this.I_Disable3DZoom=function(e){e=y(e)?i:e;var t=this.findWndIndexByIndex(e),n=-1;if(-1!=t){var r=u[t];r.b3DZoom&&(q()&&(o.JS_Disable3DZoom(e),n=0),0==n&&(r.b3DZoom=!1))}return n},this.I_FullScreen=function(e){return o.JS_FullScreenDisplay(!0)},this.I_SetPreset=function(e,t){var n={iWndIndex:i,iPresetID:e};p.extend(n,t);var r=this.findWndIndexByIndex(n.iWndIndex);if(-1!=r){var s=u[r];if(-1!=(r=this.findDeviceIndexByIP(s.szIP))){var o=c[r];o.oProtocolInc.setPreset(o,s,n)}}},this.I_GoPreset=function(e,t){var n={iWndIndex:i,iPresetID:e};p.extend(n,t);var r=this.findWndIndexByIndex(n.iWndIndex);if(-1!=r){var s=u[r];if(-1!=(r=this.findDeviceIndexByIP(s.szIP))){var o=c[r];o.oProtocolInc.goPreset(o,s,n)}}},this.I_RecordSearch=function(e,t,n,r,s){var o=this.findDeviceIndexByIP(e);if(-1!=o){var i=c[o],a={iChannelID:t,szStartTime:n,szEndTime:r,iSearchPos:0,iStreamType:1,success:null,error:null};p.extend(a,s),i.oProtocolInc.recordSearch(i,a)}},this.I_StartPlayback=function(e,t){var n=this.findDeviceIndexByIP(e),r="",s="",o=-1,a=1,u=0,l=p.dateFormat(new Date,"yyyy-MM-dd"),d={iWndIndex:i,iStreamType:1,iChannelID:1,szStartTime:l+" 00:00:00",szEndTime:l+" 23:59:59"};if(p.extend(d,t),-1!=n){L(c[n]);var f=c[n];if(q()){if(!f.bSupportWebsocket)return void(t.error&&t.error(403,G()));if(!y(d.oTransCodeParam))return void(t.error&&t.error());r=f.oProtocolInc.CGI.startWsPlayback;var I=location.protocol;/^(https)(.*)$/.test(I)?(s="wss://",o=f.iWebSocketsPort):(s="ws://",o=f.iWebSocketPort),y(d.iWSPort)||(o=d.iWSPort),u=d.iStreamType,a=100*(a=d.iChannelID<=f.iAnalogChannelNum?d.iChannelID:f.oStreamCapa.iIpChanBase+parseInt(d.iChannelID,10)-f.iAnalogChannelNum-1)+u}if(-1==o)return g("获取端口号失败"),void(t.error&&t.error());p.extend(d,{urlProtocol:s,cgi:r,iPort:o,iChannelID:a}),-1==(n=this.findWndIndexByIndex(d.iWndIndex))&&q()&&(d.szStartTime=d.szStartTime.replace(" ","T")+"Z",d.szEndTime=d.szEndTime.replace(" ","T")+"Z",f.oProtocolInc.startPlayback(f,d).then((function(){t.success&&t.success()}),(function(){t.error&&(d.szStartTime=d.szStartTime.replace(" ","T").replace(/-|:/g,"")+"Z",d.szEndTime=d.szEndTime.replace(" ","T").replace(/-|:/g,"")+"Z",f.oProtocolInc.startPlayback(f,d).then((function(){f.timeTypeChange=!0,t.success&&t.success()}),(function(){t.error&&t.error()})))})))}else t.error&&t.error()},this.I_Pause=function(e){var t={iWndIndex:i};C(e)?p.extend(t,e):y(e)||(t.iWndIndex=e);var n=this.findWndIndexByIndex(t.iWndIndex);if(-1!=n){var r=u[n],s=r.iPlayStatus,a=-1;if(2==s)a=3;else{if(5!=s)return void(t.error&&t.error());a=6}q()&&o.JS_Pause(t.iWndIndex).then((function(){r.iPlayStatus=a,t.success&&t.success()}),(function(){t.error&&t.error()}))}else t.error&&t.error()},this.I_Resume=function(e){var t={iWndIndex:i};C(e)?p.extend(t,e):y(e)||(t.iWndIndex=e);var n=this.findWndIndexByIndex(t.iWndIndex);if(-1!=n){var r=u[n],s=r.iPlayStatus,a=-1;if(3==s||4==s)a=2;else{if(6!=s)return void(t.error&&t.error());a=5}q()&&o.JS_Resume(t.iWndIndex).then((function(){r.iPlayStatus=a,t.success&&t.success()}),(function(){t.error&&t.error()}))}else t.error&&t.error()},this.I_PlaySlow=function(e){var t={iWndIndex:i};C(e)?p.extend(t,e):y(e)||(t.iWndIndex=e);var n=this.findWndIndexByIndex(t.iWndIndex);-1!=n?2==u[n].iPlayStatus?o.JS_Slow(t.iWndIndex).then((function(){t.success&&t.success()}),(function(){t.error&&t.error()})):t.error&&t.error():t.error&&t.error()},this.I_PlayFast=function(e){var t={iWndIndex:i};C(e)?p.extend(t,e):y(e)||(t.iWndIndex=e);var n=this.findWndIndexByIndex(t.iWndIndex);-1!=n?2==u[n].iPlayStatus?o.JS_Fast(t.iWndIndex).then((function(){t.success&&t.success()}),(function(){t.error&&t.error()})):t.error&&t.error():t.error&&t.error()},this.I_GetOSDTime=function(e){var t={iWndIndex:i};C(e)?p.extend(t,e):y(e)||(t.iWndIndex=e),-1!=this.findWndIndexByIndex(t.iWndIndex)?q()&&o.JS_GetOSDTime(t.iWndIndex).then((function(e){if(t.success){var n=p.dateFormat(new Date(1e3*e),"yyyy-MM-dd hh:mm:ss");t.success(n)}}),(function(){t.error&&t.error()})):t.error&&t.error()},this.I_StartDownloadRecord=function(e,t,n,r){return new Promise(((s,o)=>{var i=this.findDeviceIndexByIP(e);if(-1!=i){var a=c[i],u={szPlaybackURI:t,szFileName:n,bDateDir:!0};y(r)||p.extend(u,r),a.oProtocolInc.startDownloadRecord(a,u).then((e=>{s(e)})).catch((e=>{o(e)}))}}))},this.I_StartDownloadRecordByTime=function(e,t,n,r,s,o){return new Promise(((i,a)=>{var u=this.findDeviceIndexByIP(e);if(-1!=u){var l=c[u],d={szPlaybackURI:t=t.split("?")[0]+"?starttime="+r.replace(" ","T")+"Z&endtime="+s.replace(" ","T")+"Z",szFileName:n,bDateDir:!0};y(o)||p.extend(d,o),l.oProtocolInc.startDownloadRecord(l,d).then((e=>{i(e)})).catch((e=>{a(e)}))}}))},this.I_ExportDeviceConfig=function(e,t){var n=this.findDeviceIndexByIP(e);if(-1!=n){var r=c[n];return r.oProtocolInc.exportDeviceConfig(r,t)}return Promise.reject()},this.I_ImportDeviceConfig=function(e,t,n,r){return new Promise(((s,o)=>{var i=this.findDeviceIndexByIP(e);if(-1!=i){var a=c[i],u={szFileName:t,file:r};a.oProtocolInc.importDeviceConfig(a,u,n).then((()=>{s()})).catch((()=>{o()}))}else o()}))},this.I_RestoreDefault=function(e,t,n){var r={success:null,error:null};p.extend(r,n);var s=this.findDeviceIndexByIP(e);if(-1!=s){var o=c[s];o.oProtocolInc.restore(o,t,r)}},this.I_Restart=function(e,t){var n=this.findDeviceIndexByIP(e),r={success:null,error:null};if(p.extend(r,t),-1!=n){var s=c[n];s.oProtocolInc.restart(s,r)}},this.I_Reconnect=function(e,t){var n=this.findDeviceIndexByIP(e),r={success:null,error:null};if(p.extend(r,t),-1!=n){var s=c[n];s.oProtocolInc.login(s.szIP,s.iCGIPort,s.szAuth,r,s)}},this.I2_StartUpgrade=function(e,t,n){var r=this.findDeviceIndexByIP(e);if(-1!=r){var s=c[r],o={szFileName:t,UpgradeFile:n};return s.oProtocolInc.asyncstartUpgrade(s,o)}return Promise.reject()},this.I_UpgradeProgress=function(e){var t=this.findDeviceIndexByIP(e),n="";if(-1!=t){var r=c[t];n=v("%s%s:%s/ISAPI/System/upgradeStatus",r.szHttpProtocol,r.szIP,r.iCGIPort),n=M(n)}return new Promise(((e,t)=>{var s=new XMLHttpRequest;p.cookie("updatePlugin",!1,{raw:!0}),s.onreadystatechange=function(){if(4===s.readyState)if(200===s.status){let t="true"===$(p.parseXmlFromStr(s.responseText)).find("upgrading").text(),n=parseInt($(p.parseXmlFromStr(s.responseText)).find("percent").text(),10);e({upgrading:t,percent:n})}else t()},s.open("get",n,!1),r.sessionTag&&s.setRequestHeader("SessionTag",r.sessionTag),s.send(null)}))},this.I_CheckPluginVersion=function(){return q()?0:o.HWP_CheckPluginUpdate("<?xml version='1.0' encoding='utf-8'?><FileVersion><Platform name='win32'><npWebVideoKitPlugin.dll>3,0,6,2</npWebVideoKitPlugin.dll><WebVideoKitActiveX.ocx>3,0,6,2</WebVideoKitActiveX.ocx><PlayCtrl.dll>7,3,3,61</PlayCtrl.dll><StreamTransClient.dll>1,1,3,6</StreamTransClient.dll><SystemTransform.dll>2,5,2,8</SystemTransform.dll><NetStream.dll>1,0,5,59</NetStream.dll></Platform></FileVersion>")?-1:0},this.I_SendHTTPRequest=function(e,t,n){var r=this.findDeviceIndexByIP(e);if(!(r<0)){var s=c[r],o=new X,i={type:"GET",url:s.szHttpProtocol+s.szIP+":"+s.iCGIPort+"/"+t,auth:s.szAuth,success:null,error:null};p.extend(i,n),p.extend(i,{success:function(e){n.success&&n.success(e)},error:function(e,t){n.error&&n.error(e,t)}}),o.setRequestParam(i),o.submitRequest()}},this.I_ChangeWndNum=function(e){return isNaN(parseInt(e,10))?Promise.reject():o.JS_ArrangeWindow(e)},this.I_GetWindowStatus=function(e){if(y(e)){var t=[];return p.extend(t,u),t}var n=this.findWndIndexByIndex(e);if(-1!=n){t={};return p.extend(t,u[n]),t}return null},this.I_DeviceCapturePic=function(e,t,n,r){var s=this.findDeviceIndexByIP(e),o=-1;if(-1!=s){var i=c[s],a={bDateDir:!0};if(p.extend(a,r),!y(a.iResolutionWidth)&&!p.isInt(a.iResolutionWidth))return o;if(!y(a.iResolutionHeight)&&!p.isInt(a.iResolutionHeight))return o;o=i.oProtocolInc.deviceCapturePic(i,t,n,a)}return o},this.I_GetDevicePort=function(e){var t=this.findDeviceIndexByIP(e),n=null;if(-1!=t){var r=c[t];n=function(e){var t=null;if(A(e))(t=z(e)).iRtspPort,t.iDevicePort;else{for(var n=R(e),r=!1,s=0;s<n.length;s++)if(n[s].ipv4==e.szIP||n[s].ipv6==e.szIP){r=!0;break}if(r)t=z(e);else if(-1===(t=w(e)).iRtspPort&&-1===t.iDevicePort)t=z(e);else if(-1===t.iWebSocketPort&&-1===t.iWebSocketsPort){const{iWebSocketsPort:n,iWebSocketPort:r}=z(e);t={...t,iWebSocketsPort:n,iWebSocketPort:r}}t.iRtspPort,t.iHttpPort,t.iDevicePort}return console.log("oPort",t),t}(r),c[t]={...r,...n}}return n},this.I_GetTextOverlay=function(e,t,n){var r=this.findDeviceIndexByIP(t);if(-1!=r){var s=c[r],o=(u[r],{async:!1,type:"GET",success:n.success,error:n.error});this.I_SendHTTPRequest(s.szIP+"_"+s.iCGIPort,e,o)}return-1},this.findDeviceIndexByIP=function(e){if(e.indexOf("_")>-1){for(var t=0,n=c.length;t<n;t++)if(c[t].szDeviceIdentify==e)return t}else for(t=0,n=c.length;t<n;t++)if(c[t].szIP==e)return t;return-1},this.findWndIndexByIndex=function(e){for(var t=0,n=u.length;t<n;t++)if(u[t].iIndex==e)return t;return-1},this.I_SetSecretKey=function(e,t){return t||(t=i),o.JS_SetSecretKey(t,e)};var E=function(){this.iIndex=0,this.szIP="",this.iCGIPort=80,this.szDeviceIdentify="",this.iChannelID="",this.iPlayStatus=0,this.bSound=!1,this.bRecord=!1,this.bPTZAuto=!1,this.bEZoom=!1,this.b3DZoom=!1},X=function(){this.options={type:"GET",url:"",auth:"",timeout:1e4,data:"",async:!0,success:null,error:null},this.m_szHttpHead="",this.m_szHttpContent="",this.m_szHttpData=""};X.prototype.m_httpRequestSet=[],X.prototype.setRequestParam=function(e){p.extend(this.options,e)},X.prototype.submitRequest=function(){var e,t=null,n=this;if(q()){this.options.auth?p.cookie("WebSession",this.options.auth):p.cookie("WebSession",null);var r={};try{var o=function(e){if(e.indexOf("_")>-1){for(var t=0,n=c.length;t<n;t++)if(c[t].szDeviceIdentify==e)return t}else for(t=0,n=c.length;t<n;t++)if(c[t].szIP==e)return t;return-1}((e=>e.match(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/)?.[0]||null)(this.options.url));-1!==o&&(r=c[o])}catch(e){}var i=M(this.options.url),a=new window.XMLHttpRequest;a.open(this.options.type,i,this.options.async);if(s.proxyAddress){a.withCredentials=!0;var u=(e=this.options.url,/^(http|https):\/\/([^\/]+)(.+)$/.test(e)?RegExp.$2:"");a.setRequestHeader("deviceIdentify",u)}a.setRequestHeader("X-Requested-With","XMLHttpRequest"),a.setRequestHeader("If-Modified-Since","0"),r.sessionTag&&a.setRequestHeader("SessionTag",r.sessionTag),a.send(this.options.data||null);var l=function(){if(4===a.readyState){t={funSuccessCallback:n.options.success,funErrorCallback:n.options.error};var e=a.status+a.responseText;0===a.status&&(e=""),n.httpDataAnalyse(t,e)}};this.options.async?(a.timeout=this.options.timeout,a.onreadystatechange=function(){l()}):l()}},X.prototype.getHttpMethod=function(e){var t={GET:1,POST:2,PUT:5,DELETE:6}[e];return t||-1},X.prototype.processCallback=function(e,t){for(var n=null,r=0;r<this.m_httpRequestSet.length;r++)if(e==this.m_httpRequestSet[r].iRequestID){n=this.m_httpRequestSet[r],this.m_httpRequestSet.splice(r,1);break}null!=n&&(this.httpDataAnalyse(n,t),delete n)},X.prototype.httpDataAnalyse=function(e,t){var n="",r=0;""==t||y(t)?e.funErrorCallback():(r=parseInt(t.substring(0,3)),n=t.substring(3,t.length),isNaN(r)?e.funErrorCallback():200==r?this.options&&this.options.url.indexOf("?format=json")>-1||this.options&&this.options.url.indexOf("?format=json")>-1?e.funSuccessCallback(JSON.parse(n)):e.funSuccessCallback(p.loadXML(n)):e.funErrorCallback&&e.funErrorCallback(r,p.loadXML(n)))};var _=function(){};_.prototype.CGI={login:"%s%s:%s/ISAPI/Security/userCheck",getAudioInfo:"%s%s:%s/ISAPI/System/TwoWayAudio/channels",getDeviceInfo:"%s%s:%s/ISAPI/System/deviceInfo",getAnalogChannelInfo:"%s%s:%s/ISAPI/System/Video/inputs/channels",getDigitalChannel:"%s%s:%s/ISAPI/ContentMgmt/InputProxy/channels",getDigitalChannelInfo:"%s%s:%s/ISAPI/ContentMgmt/InputProxy/channels/status",getZeroChannelInfo:"%s%s:%s/ISAPI/ContentMgmt/ZeroVideo/channels",getStreamChannels:{analog:"%s%s:%s/ISAPI/Streaming/channels",digital:"%s%s:%s/ISAPI/ContentMgmt/StreamingProxy/channels"},getStreamDynChannels:"%s%s:%s/PSIA/Custom/SelfExt/ContentMgmt/DynStreaming/channels",startRealPlay:{channels:"%s%s:%s/PSIA/streaming/channels/%s",zeroChannels:"%s%s:%s/PSIA/Custom/SelfExt/ContentMgmt/ZeroStreaming/channels/%s"},startShttpRealPlay:{channels:"%s%s:%s/SDK/play/%s/004",zeroChannels:"%s%s:%s/SDK/play/100/004/ZeroStreaming"},startWsRealPlay:{channels:"%s%s:%s/%s",zeroChannels:"%s%s:%s/%s"},startVoiceTalk:{open:"%s%s:%s/ISAPI/System/TwoWayAudio/channels/%s/open",close:"%s%s:%s/ISAPI/System/TwoWayAudio/channels/%s/close",audioData:"%s%s:%s/ISAPI/System/TwoWayAudio/channels/%s/audioData"},ptzControl:{analog:"%s%s:%s/ISAPI/PTZCtrl/channels/%s/continuous",digital:"%s%s:%s/ISAPI/ContentMgmt/PTZCtrlProxy/channels/%s/continuous"},ptzAutoControl:{ipdome:"%s%s:%s/ISAPI/PTZCtrl/channels/%s/presets/%s/goto",analog:"%s%s:%s/ISAPI/PTZCtrl/channels/%s/autoPan",digital:"%s%s:%s/ISAPI/ContentMgmt/PTZCtrlProxy/channels/%s/autoPan"},setPreset:{analog:"%s%s:%s/ISAPI/PTZCtrl/channels/%s/presets/%s",digital:"%s%s:%s/ISAPI/ContentMgmt/PTZCtrlProxy/channels/%s/presets/%s"},goPreset:{analog:"%s%s:%s/ISAPI/PTZCtrl/channels/%s/presets/%s/goto",digital:"%s%s:%s/ISAPI/ContentMgmt/PTZCtrlProxy/channels/%s/presets/%s/goto"},ptzFocus:{analog:"%s%s:%s/ISAPI/Image/channels/%s/focus",digital:"%s%s:%s/ISAPI/ContentMgmt/ImageProxy/channels/%s/focus",ipc:"%s%s:%s/ISAPI/System/Video/inputs/channels/%s/focus"},ptzIris:{analog:"%s%s:%s/ISAPI/Image/channels/%s/iris",digital:"%s%s:%s/ISAPI/ContentMgmt/ImageProxy/channels/%s/iris",ipc:"%s%s:%s/ISAPI/System/Video/inputs/channels/%s/iris"},getNetworkBond:"%s%s:%s/ISAPI/System/Network/Bond",getNetworkInterface:"%s%s:%s/ISAPI/System/Network/interfaces",getUPnPPortStatus:"%s%s:%s/ISAPI/System/Network/UPnP/ports/status",getPPPoEStatus:"%s%s:%s/ISAPI/System/Network/PPPoE/1/status",getPortInfo:"%s%s:%s/ISAPI/Security/adminAccesses",recordSearch:"%s%s:%s/ISAPI/ContentMgmt/search",startPlayback:"%s%s:%s/PSIA/streaming/tracks/%s?starttime=%s&endtime=%s",startWsPlayback:"%s%s:%s/%s",startShttpPlayback:"%s%s:%s/SDK/playback/%s",startShttpReversePlayback:"%s%s:%s/SDK/playback/%s/reversePlay",startTransCodePlayback:"%s%s:%s/SDK/playback/%s/transcoding",startDownloadRecord:"%s%s:%s/ISAPI/ContentMgmt/download",downloaddeviceConfig:"%s%s:%s/ISAPI/System/configurationData",uploaddeviceConfig:"%s%s:%s/ISAPI/System/configurationData",restart:"%s%s:%s/ISAPI/System/reboot",restore:"%s%s:%s/ISAPI/System/factoryReset?mode=%s",startUpgrade:{upgrade:"%s%s:%s/ISAPI/System/updateFirmware",status:"%s%s:%s/ISAPI/System/upgradeStatus"},set3DZoom:{analog:"%s%s:%s/ISAPI/PTZCtrl/channels/%s/position3D",digital:"%s%s:%s/ISAPI/ContentMgmt/PTZCtrlProxy/channels/%s/position3D"},getSecurityVersion:"%s%s:%s/ISAPI/Security/capabilities?username=admin",SDKCapabilities:"%s%s:%s/SDK/capabilities",deviceCapture:{channels:"%s%s:%s/ISAPI/Streaming/channels/%s/picture"},overlayInfo:{analog:"%s%s:%s/ISAPI/System/Video/inputs/channels/%s/overlays/",digital:"%s%s:%s/ISAPI/ContentMgmt/InputProxy/channels/%s/video/overlays"},sessionCap:"%s%s:%s/ISAPI/Security/sessionLogin/capabilities?username=%s",sessionLogin:"%s%s:%s/ISAPI/Security/sessionLogin",sessionHeartbeat:"%s%s:%s/ISAPI/Security/sessionHeartbeat",sessionLogout:"%s%s:%s/ISAPI/Security/sessionLogout",systemCapabilities:"%s%s:%s/ISAPI/System/capabilities"},_.prototype.login=function(e,t,n,r,s){r.protocol;var o=v(this.CGI.login,s.szHttpProtocol,e,t),i=new X,a={type:"GET",url:o,auth:n,success:null,error:null};p.extend(a,r),p.extend(a,{success:function(e){r.success&&r.success(e)},error:function(e,t){r.error&&r.error(e,t)}}),i.setRequestParam(a),i.submitRequest()},_.prototype.getAudioInfo=function(e,t){var n=v(this.CGI.getAudioInfo,e.szHttpProtocol,e.szIP,e.iCGIPort),r=new X,s={type:"GET",url:n,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()},_.prototype.getSecurityVersion=function(e,t){var n=v(this.CGI.getSecurityVersion,e.szHttpProtocol,e.szIP,e.iCGIPort),r=new X,s={type:"GET",url:n,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()},_.prototype.getDeviceInfo=function(e,n){var r=v(this.CGI.getDeviceInfo,e.szHttpProtocol,e.szIP,e.iCGIPort),s=new X,o={type:"GET",url:r,auth:e.szAuth,success:null,error:null};p.extend(o,n),p.extend(o,{success:function(e){var r=[];r.push("<DeviceInfo>"),r.push("<deviceName>"+p.escape(t.$XML(e).find("deviceName").eq(0).text())+"</deviceName>"),r.push("<deviceID>"+t.$XML(e).find("deviceID").eq(0).text()+"</deviceID>"),r.push("<deviceType>"+t.$XML(e).find("deviceType").eq(0).text()+"</deviceType>"),r.push("<model>"+t.$XML(e).find("model").eq(0).text()+"</model>"),r.push("<serialNumber>"+t.$XML(e).find("serialNumber").eq(0).text()+"</serialNumber>"),r.push("<macAddress>"+t.$XML(e).find("macAddress").eq(0).text()+"</macAddress>"),r.push("<firmwareVersion>"+t.$XML(e).find("firmwareVersion").eq(0).text()+"</firmwareVersion>"),r.push("<firmwareReleasedDate>"+t.$XML(e).find("firmwareReleasedDate").eq(0).text()+"</firmwareReleasedDate>"),r.push("<encoderVersion>"+t.$XML(e).find("encoderVersion").eq(0).text()+"</encoderVersion>"),r.push("<encoderReleasedDate>"+t.$XML(e).find("encoderReleasedDate").eq(0).text()+"</encoderReleasedDate>"),r.push("</DeviceInfo>"),e=p.loadXML(r.join("")),n.success&&n.success(e)},error:function(e,t){n.error&&n.error(e,t)}}),s.setRequestParam(o),s.submitRequest()},_.prototype.getAnalogChannelInfo=function(e,n){var r=v(this.CGI.getAnalogChannelInfo,e.szHttpProtocol,e.szIP,e.iCGIPort),s=new X,o={type:"GET",url:r,auth:e.szAuth,success:null,error:null};p.extend(o,n),p.extend(o,{success:function(r){var s=[];s.push("<VideoInputChannelList>");var o=t.$XML(r).find("VideoInputChannel",!0);e.iAnalogChannelNum=o.length;for(var i=0,a=o.length;i<a;i++){var c=o[i];s.push("<VideoInputChannel>"),s.push("<id>"+t.$XML(c).find("id").eq(0).text()+"</id>"),s.push("<inputPort>"+t.$XML(c).find("inputPort").eq(0).text()+"</inputPort>"),s.push("<name>"+p.escape(t.$XML(c).find("name").eq(0).text())+"</name>"),s.push("<videoFormat>"+t.$XML(c).find("videoFormat").eq(0).text()+"</videoFormat>"),s.push("</VideoInputChannel>")}s.push("</VideoInputChannelList>"),r=p.loadXML(s.join("")),n.success&&n.success(r)},error:function(t,r){e.iAnalogChannelNum=0,n.error&&n.error(t,r)}}),s.setRequestParam(o),s.submitRequest()},_.prototype.getDigitalChannel=function(e,n){var r=v(this.CGI.getDigitalChannel,e.szHttpProtocol,e.szIP,e.iCGIPort),s=new X,o={type:"GET",url:r,auth:e.szAuth,success:null,error:null};p.extend(o,n),p.extend(o,{success:function(e){var r=[];r.push("<InputProxyChannelList>");for(var s=t.$XML(e).find("InputProxyChannel",!0),o=0,i=s.length;o<i;o++){var a=s[o];r.push("<InputProxyChannel>"),r.push("<id>"+t.$XML(a).find("id").eq(0).text()+"</id>"),r.push("<name>"+p.escape(t.$XML(a).find("name").eq(0).text())+"</name>"),r.push("</InputProxyChannel>")}r.push("</InputProxyChannelList>"),e=p.loadXML(r.join("")),n.success&&n.success(e)},error:function(e,t){n.error&&n.error(e,t)}}),s.setRequestParam(o),s.submitRequest()},_.prototype.getDigitalChannelInfo=function(e,n){var r=null,s={};if(this.getDigitalChannel(e,{async:!1,success:function(e){r=e;for(var n=t.$XML(r).find("InputProxyChannel",!0),o=0,i=n.length;o<i;o++){var a=n[o],c=t.$XML(a).find("id").eq(0).text(),u=t.$XML(a).find("name").eq(0).text();s[c]=u}},error:function(e,t){n.error&&n.error(e,t)}}),null!==r){var o=v(this.CGI.getDigitalChannelInfo,e.szHttpProtocol,e.szIP,e.iCGIPort),i=new X,a={type:"GET",url:o,auth:e.szAuth,success:null,error:null};p.extend(a,n),p.extend(a,{success:function(e){var r=[];r.push("<InputProxyChannelStatusList>");for(var o=t.$XML(e).find("InputProxyChannelStatus",!0),i=0,a=o.length;i<a;i++){var c=o[i],u=t.$XML(c).find("id").eq(0).text();r.push("<InputProxyChannelStatus>"),r.push("<id>"+u+"</id>"),r.push("<sourceInputPortDescriptor>"),r.push("<proxyProtocol>"+t.$XML(c).find("proxyProtocol").eq(0).text()+"</proxyProtocol>"),r.push("<addressingFormatType>"+t.$XML(c).find("addressingFormatType").eq(0).text()+"</addressingFormatType>"),r.push("<ipAddress>"+t.$XML(c).find("ipAddress").eq(0).text()+"</ipAddress>"),r.push("<managePortNo>"+t.$XML(c).find("managePortNo").eq(0).text()+"</managePortNo>"),r.push("<srcInputPort>"+t.$XML(c).find("srcInputPort").eq(0).text()+"</srcInputPort>"),r.push("<userName>"+p.escape(t.$XML(c).find("userName").eq(0).text())+"</userName>"),r.push("<streamType>"+t.$XML(c).find("streamType").eq(0).text()+"</streamType>"),r.push("<online>"+t.$XML(c).find("online").eq(0).text()+"</online>"),r.push("<name>"+p.escape(s[u])+"</name>"),r.push("</sourceInputPortDescriptor>"),r.push("</InputProxyChannelStatus>")}r.push("</InputProxyChannelStatusList>"),e=p.loadXML(r.join("")),n.success&&n.success(e)},error:function(e,t){n.error&&n.error(e,t)}}),i.setRequestParam(a),i.submitRequest()}},_.prototype.getZeroChannelInfo=function(e,t){var n=v(this.CGI.getZeroChannelInfo,e.szHttpProtocol,e.szIP,e.iCGIPort),r=new X,s={type:"GET",url:n,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()},_.prototype.getStreamChannels=function(e,t){if(0!=e.iAnalogChannelNum)var n=v(this.CGI.getStreamChannels.analog,e.szHttpProtocol,e.szIP,e.iCGIPort);else n=v(this.CGI.getStreamChannels.digital,e.szHttpProtocol,e.szIP,e.iCGIPort);var r=new X,s={type:"GET",url:n,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()},_.prototype.getPPPoEStatus=function(e,t){var n=v(this.CGI.getPPPoEStatus,e.szHttpProtocol,e.szIP,e.iCGIPort),r=new X,s={type:"GET",url:n,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()},_.prototype.getUPnPPortStatus=function(e,t){var n=v(this.CGI.getUPnPPortStatus,e.szHttpProtocol,e.szIP,e.iCGIPort),r=new X,s={type:"GET",url:n,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()},_.prototype.getNetworkBond=function(e,t){var n=v(this.CGI.getNetworkBond,e.szHttpProtocol,e.szIP,e.iCGIPort),r=new X,s={type:"GET",url:n,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()},_.prototype.getNetworkInterface=function(e,t){var n=v(this.CGI.getNetworkInterface,e.szHttpProtocol,e.szIP,e.iCGIPort),r=new X,s={type:"GET",url:n,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()},_.prototype.getPortInfo=function(e,t){var n=v(this.CGI.getPortInfo,e.szHttpProtocol,e.szIP,e.iCGIPort),r=new X,s={type:"GET",url:n,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()},_.prototype.startRealPlay=function(e,t){var n=100*t.iChannelID+t.iStreamType,r="",i=t.bProxy,a=e.szIP;if("rtsp://"===t.urlProtocol&&(a=S(a)),t.bZeroChannel?(q()&&(n=0),r=v(t.cgi.zeroChannels,t.urlProtocol,a,t.iPort,n)):r=v(t.cgi.channels,t.urlProtocol,a,t.iPort,n),s.proxyAddress&&q()){p.cookie("webVideoCtrlProxy",a+":"+t.iPort,{raw:!0}),r=v(t.cgi.zeroChannels,t.urlProtocol,s.proxyAddress.ip,s.proxyAddress.port,n);var c=a+":"+t.iPort;r.indexOf("?")>-1?r+="&deviceIdentify="+c:r+="?deviceIdentify="+c}var l=$.Deferred();if(q()){var d={sessionID:e.szAuth,token:x(e,e.szDeviceIdentify)};i&&(r=k(r)),o.JS_Play(r,d,t.iWndIndex).then((function(){var n;(n=new E).iIndex=t.iWndIndex,n.szIP=e.szIP,n.iCGIPort=e.iCGIPort,n.szDeviceIdentify=e.szDeviceIdentify,n.iChannelID=t.iChannelID,n.iPlayStatus=1,u.push(n),l.resolve()}),(function(){l.reject()}))}return l},_.prototype.startVoiceTalk=function(e,t){var n=v(this.CGI.startVoiceTalk.open,e.szHttpProtocol,e.szIP,e.iCGIPort,t),r=v(this.CGI.startVoiceTalk.close,e.szHttpProtocol,e.szIP,e.iCGIPort,t),s=v(this.CGI.startVoiceTalk.audioData,e.szHttpProtocol,e.szIP,e.iCGIPort,t);return o.HWP_StartVoiceTalkEx(n,r,s,e.szAuth,e.iAudioType,e.m_iAudioBitRate,e.m_iAudioSamplingRate)},_.prototype.ptzAutoControl=function(e,t,n,r){var s=n.iChannelID,o="",i="";if(r.iPTZSpeed=r.iPTZSpeed<7?15*r.iPTZSpeed:100,t&&(r.iPTZSpeed=0),e.szDeviceType!=P)o=s<=e.iAnalogChannelNum?v(this.CGI.ptzAutoControl.analog,e.szHttpProtocol,e.szIP,e.iCGIPort,n.iChannelID):n.bShttpIPChannel?v(this.CGI.ptzAutoControl.digital,e.szHttpProtocol,e.szIP,e.iCGIPort,n.iChannelID-e.oStreamCapa.iIpChanBase+1+e.iAnalogChannelNum):v(this.CGI.ptzAutoControl.digital,e.szHttpProtocol,e.szIP,e.iCGIPort,n.iChannelID),i="<?xml version='1.0' encoding='UTF-8'?><autoPanData><autoPan>"+r.iPTZSpeed+"</autoPan></autoPanData>";else{0===r.iPTZSpeed&&(t=!0);var a=99;t&&(a=96),o=v(this.CGI.ptzAutoControl.ipdome,e.szHttpProtocol,e.szIP,e.iCGIPort,n.iChannelID,a)}var c=new X,u={type:"PUT",url:o,async:!1,auth:e.szAuth,data:i,success:null,error:null},l=this;p.extend(u,r),p.extend(u,{success:function(e){n.bPTZAuto=!n.bPTZAuto,r.success&&r.success(e)},error:function(t,s){if(h==e.szDeviceType||m==e.szDeviceType){o=n.bShttpIPChannel?v(l.CGI.ptzControl.analog,e.szHttpProtocol,e.szIP,e.iCGIPort,n.iChannelID-e.oStreamCapa.iIpChanBase+1+e.iAnalogChannelNum):v(l.CGI.ptzControl.analog,e.szHttpProtocol,e.szIP,e.iCGIPort,n.iChannelID),i="<?xml version='1.0' encoding='UTF-8'?><PTZData><pan>"+r.iPTZSpeed+"</pan><tilt>0</tilt></PTZData>";var a=new X,c={type:"PUT",url:o,async:!1,auth:e.szAuth,data:i,success:null,error:null};p.extend(c,r),a.setRequestParam(c),a.submitRequest()}else r.error&&r.error(t,s)}}),c.setRequestParam(u),c.submitRequest()},_.prototype.ptzControl=function(e,t,n,r){var s=n.iChannelID,o="";n.bPTZAuto&&this.ptzAutoControl(e,!0,n,{iPTZSpeed:0}),r.iPTZSpeed=t?0:r.iPTZSpeed<7?15*r.iPTZSpeed:100;var i=[{},{pan:0,tilt:r.iPTZSpeed},{pan:0,tilt:-r.iPTZSpeed},{pan:-r.iPTZSpeed,tilt:0},{pan:r.iPTZSpeed,tilt:0},{pan:-r.iPTZSpeed,tilt:r.iPTZSpeed},{pan:-r.iPTZSpeed,tilt:-r.iPTZSpeed},{pan:r.iPTZSpeed,tilt:r.iPTZSpeed},{pan:r.iPTZSpeed,tilt:-r.iPTZSpeed},{},{speed:r.iPTZSpeed},{speed:-r.iPTZSpeed},{speed:r.iPTZSpeed},{speed:-r.iPTZSpeed},{speed:r.iPTZSpeed},{speed:-r.iPTZSpeed}],a="",c={};switch(r.iPTZIndex){case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:c=this.CGI.ptzControl,a="<?xml version='1.0' encoding='UTF-8'?><PTZData><pan>"+i[r.iPTZIndex].pan+"</pan><tilt>"+i[r.iPTZIndex].tilt+"</tilt></PTZData>";break;case 10:case 11:c=this.CGI.ptzControl,a="<?xml version='1.0' encoding='UTF-8'?><PTZData><zoom>"+i[r.iPTZIndex].speed+"</zoom></PTZData>";break;case 12:case 13:c=this.CGI.ptzFocus,a="<?xml version='1.0' encoding='UTF-8'?><FocusData><focus>"+i[r.iPTZIndex].speed+"</focus></FocusData>";break;case 14:case 15:c=this.CGI.ptzIris,a="<?xml version='1.0' encoding='UTF-8'?><IrisData><iris>"+i[r.iPTZIndex].speed+"</iris></IrisData>";break;default:return void(r.error&&r.error())}o=c!=this.CGI.ptzFocus&&c!=this.CGI.ptzIris||e.szDeviceType!=h&&e.szDeviceType!=P&&e.szDeviceType!=m?s<=e.iAnalogChannelNum?v(c.analog,e.szHttpProtocol,e.szIP,e.iCGIPort,n.iChannelID):n.bShttpIPChannel?v(c.digital,e.szHttpProtocol,e.szIP,e.iCGIPort,n.iChannelID-e.oStreamCapa.iIpChanBase+1+e.iAnalogChannelNum):v(c.digital,e.szHttpProtocol,e.szIP,e.iCGIPort,n.iChannelID):v(c.ipc,e.szHttpProtocol,e.szIP,e.iCGIPort,n.iChannelID);var u=new X,l={type:"PUT",url:o,async:!1,auth:e.szAuth,data:a,success:null,error:null};p.extend(l,r),p.extend(l,{success:function(e){r.success&&r.success(e)},error:function(e,t){r.error&&r.error(e,t)}}),u.setRequestParam(l),u.submitRequest()},_.prototype.setPreset=function(e,t,n){var r="",s="";r=t.iChannelID<=e.iAnalogChannelNum?v(this.CGI.setPreset.analog,e.szHttpProtocol,e.szIP,e.iCGIPort,t.iChannelID,n.iPresetID):t.bShttpIPChannel?v(this.CGI.setPreset.digital,e.szHttpProtocol,e.szIP,e.iCGIPort,t.iChannelID-e.oStreamCapa.iIpChanBase+1+e.iAnalogChannelNum,n.iPresetID):v(this.CGI.setPreset.digital,e.szHttpProtocol,e.szIP,e.iCGIPort,t.iChannelID,n.iPresetID),s="<?xml version='1.0' encoding='UTF-8'?>",s+="<PTZPreset>",s+="<id>"+n.iPresetID+"</id>",e.szDeviceType!=P&&(s+="<presetName>Preset"+n.iPresetID+"</presetName>"),s+="</PTZPreset>";var o=new X,i={type:"PUT",url:r,auth:e.szAuth,data:s,success:null,error:null};p.extend(i,n),p.extend(i,{success:function(e){n.success&&n.success(e)},error:function(e,t){n.error&&n.error(e,t)}}),o.setRequestParam(i),o.submitRequest()},_.prototype.goPreset=function(e,t,n){var r="";r=t.iChannelID<=e.iAnalogChannelNum?v(this.CGI.goPreset.analog,e.szHttpProtocol,e.szIP,e.iCGIPort,t.iChannelID,n.iPresetID):t.bShttpIPChannel?v(this.CGI.goPreset.digital,e.szHttpProtocol,e.szIP,e.iCGIPort,t.iChannelID-e.oStreamCapa.iIpChanBase+1+e.iAnalogChannelNum,n.iPresetID):v(this.CGI.goPreset.digital,e.szHttpProtocol,e.szIP,e.iCGIPort,t.iChannelID,n.iPresetID);var s=new X,o={type:"PUT",url:r,auth:e.szAuth,success:null,error:null};p.extend(o,n),p.extend(o,{success:function(e){n.success&&n.success(e)},error:function(e,t){n.error&&n.error(e,t)}}),s.setRequestParam(o),s.submitRequest()},_.prototype.overlayInfo=function(){return szUrl=this.CGI.overlayInfo.analog,szUrl},_.prototype.recordSearch=function(e,n){var r,s,o=n.iChannelID,i=n.iStreamType,a=n.szStartTime.replace(" ","T")+"Z",c=n.szEndTime.replace(" ","T")+"Z";r=v(this.CGI.recordSearch,e.szHttpProtocol,e.szIP,e.iCGIPort),s="<?xml version='1.0' encoding='UTF-8'?><CMSearchDescription><searchID>"+new F+"</searchID><trackList><trackID>"+(100*o+i)+"</trackID></trackList><timeSpanList><timeSpan><startTime>"+a+"</startTime><endTime>"+c+"</endTime></timeSpan></timeSpanList><maxResults>40</maxResults><searchResultPostion>"+n.iSearchPos+"</searchResultPostion><metadataList><metadataDescriptor>//metadata.ISAPI.org/VideoMotion</metadataDescriptor></metadataList></CMSearchDescription>";var u=new X,l={type:"POST",url:r,auth:e.szAuth,data:s,success:null,error:null};p.extend(l,n),p.extend(l,{success:function(e){var r=[];r.push("<CMSearchResult>"),r.push("<responseStatus>"+t.$XML(e).find("responseStatus").eq(0).text()+"</responseStatus>"),r.push("<responseStatusStrg>"+t.$XML(e).find("responseStatusStrg").eq(0).text()+"</responseStatusStrg>"),r.push("<numOfMatches>"+t.$XML(e).find("numOfMatches").eq(0).text()+"</numOfMatches>"),r.push("<matchList>");for(var s=t.$XML(e).find("searchMatchItem",!0),o=0,i=s.length;o<i;o++){var a=s[o];r.push("<searchMatchItem>"),r.push("<trackID>"+t.$XML(a).find("trackID").eq(0).text()+"</trackID>"),r.push("<startTime>"+t.$XML(a).find("startTime").eq(0).text()+"</startTime>"),r.push("<endTime>"+t.$XML(a).find("endTime").eq(0).text()+"</endTime>"),r.push("<playbackURI>"+p.escape(t.$XML(a).find("playbackURI").eq(0).text())+"</playbackURI>"),r.push("<metadataDescriptor>"+t.$XML(a).find("metadataDescriptor").eq(0).text().split("/")[1]+"</metadataDescriptor>"),r.push("</searchMatchItem>")}r.push("</matchList>"),r.push("</CMSearchResult>"),e=p.loadXML(r.join("")),n.success&&n.success(e)},error:function(e,t){n.error&&n.error(e,t)}}),u.setRequestParam(l),u.submitRequest()},_.prototype.startPlayback=function(e,t){var n=t.iWndIndex,r="",i=t.szStartTime,a=t.szEndTime,c=t.bProxy,l=e.szIP;if("rtsp://"===t.urlProtocol&&(l=S(l)),q())if(s.proxyAddress){p.cookie("webVideoCtrlProxy",l+":"+t.iPort,{raw:!0}),r=v(t.cgi,t.urlProtocol,s.proxyAddress.ip,s.proxyAddress.port,t.iChannelID);var d=l+":"+t.iPort;r.indexOf("?")>-1?r+="&deviceIdentify="+d:r+="?deviceIdentify="+d}else r=v(t.cgi,t.urlProtocol,l,t.iPort,t.iChannelID,i,a);if(!y(t.oTransCodeParam)){var f=function(e){var t={TransFrameRate:"",TransResolution:"",TransBitrate:""};if(p.extend(t,e),""==t.TransFrameRate||""==t.TransResolution||""==t.TransBitrate)return"";var n=[];return n.push("<?xml version='1.0' encoding='UTF-8'?>"),n.push("<CompressionInfo>"),n.push("<TransFrameRate>"+t.TransFrameRate+"</TransFrameRate>"),n.push("<TransResolution>"+t.TransResolution+"</TransResolution>"),n.push("<TransBitrate>"+t.TransBitrate+"</TransBitrate>"),n.push("</CompressionInfo>"),n.join("")}(t.oTransCodeParam);if(""==f)return-1;o.HWP_SetTrsPlayBackParam(n,f)}var I=$.Deferred();if(q()){var h={sessionID:e.szAuth,token:x(e,e.szDeviceIdentify)};c&&(r=k(r)),o.JS_Play(r,h,n,i,a).then((function(){var r;(r=new E).iIndex=n,r.szIP=e.szIP,r.iCGIPort=e.iCGIPort,r.szDeviceIdentify=e.szDeviceIdentify,r.iChannelID=t.iChannelID,r.iPlayStatus=2,u.push(r),I.resolve()}),(function(){I.reject()}))}return I},_.prototype.reversePlayback=function(e,t){var n=t.iWndIndex,r=t.szStartTime,s=t.szEndTime,i=e.szIP;"rtsp://"===t.urlProtocol&&(i=S(i));var a=v(t.cgi,t.urlProtocol,i,t.iPort,t.iChannelID,r,s),c=o.HWP_ReversePlay(a,e.szAuth,n,r,s);if(0==c){var l=new E;l.iIndex=n,l.szIP=e.szIP,l.iCGIPort=e.iCGIPort,l.szDeviceIdentify=e.szDeviceIdentify,l.iChannelID=t.iChannelID,l.iPlayStatus=5,u.push(l)}return c},_.prototype.startDownloadRecord=function(e,n){var r=v(this.CGI.startDownloadRecord,e.szHttpProtocol,e.szIP,e.iCGIPort),s="<?xml version='1.0' encoding='UTF-8'?><downloadRequest><playbackURI>"+p.escape(n.szPlaybackURI)+"</playbackURI></downloadRequest>";return function(n,r,s,o){var i,a,c=t.$XML(p.parseXmlFromStr(o)).find("playbackURI").eq(0).text(),u=n+"?token="+x(e,e.szDeviceIdentify)+"&playbackURI="+c,l=".mp4";if(n.indexOf("picture/Streaming/tracks")>0&&(u=n,l=".jpg"),!s){var d=u.indexOf("&name=")+6,f=u.indexOf("&size=");s=u.substring(d,f),-1===f&&(s="file")}return i=u,a=s+l,new Promise(((t,n)=>{fetch(i,{headers:{Accept:"application/octet-stream",SessionTag:e.sessionTag}}).then((e=>{if(console.log("response",e),200===e.status)return e.blob();n()})).then((e=>{if(console.log("blob",e),!e)return void n();const r=document.createElement("a");r.href=URL.createObjectURL(e),r.download=a||"download",r.click(),URL.revokeObjectURL(r.href),t()}))}))}(r=M(r),e.szAuth,n.szFileName,s)},_.prototype.exportDeviceConfig=function(e,t){return new Promise(((n,r)=>{var s=v(this.CGI.downloaddeviceConfig,e.szHttpProtocol,e.szIP,e.iCGIPort);s=M(s),t&&(s=p.exportPasswordDeviceConfig(s,t)),p.nativeAjax({url:s,responseType:"blob",sessionTag:e.sessionTag,success:(e,t)=>{t("file"),n()},error:()=>{r()}})}))},_.prototype.importDeviceConfig=function(e,t,n){var r,s=v(this.CGI.uploaddeviceConfig,e.szHttpProtocol,e.szIP,e.iCGIPort);return s=M(s),n&&(s=p.exportPasswordDeviceConfig(s,n)),r=s,new Promise(((n,s)=>{var o=new XMLHttpRequest;o.onreadystatechange=function(){4===o.readyState&&(200===o.status?n():s())},o.open("put",r,!1),e.sessionTag&&o.setRequestHeader("SessionTag",e.sessionTag),o.send(t.file)}))},_.prototype.restart=function(e,t){var n=v(this.CGI.restart,e.szHttpProtocol,e.szIP,e.iCGIPort),r=new X,s={type:"PUT",url:n,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()},_.prototype.restore=function(e,t,n){var r=v(this.CGI.restore,e.szHttpProtocol,e.szIP,e.iCGIPort,t),s=new X,o={type:"PUT",url:r,auth:e.szAuth,success:null,error:null};p.extend(o,n),p.extend(o,{success:function(e){n.success&&n.success(e)},error:function(e,t){n.error&&n.error(e,t)}}),s.setRequestParam(o),s.submitRequest()},_.prototype.startUpgrade=function(e,t){$.Deferred();var n=v(this.CGI.startUpgrade.upgrade,e.szHttpProtocol,e.szIP,e.iCGIPort),r=v(this.CGI.startUpgrade.status,e.szHttpProtocol,e.szIP,e.iCGIPort);return szRet=o.HWP_StartUpgrade(n,r,e.szAuth,t.szFileName)},_.prototype.asyncstartUpgrade=function(e,t){var n=$.Deferred(),r=v(this.CGI.startUpgrade.upgrade,e.szHttpProtocol,e.szIP,e.iCGIPort),s=v(this.CGI.startUpgrade.status,e.szHttpProtocol,e.szIP,e.iCGIPort);if(q()){(function(n,r){return new Promise((function(s,o){n||o(),r||o();var i=new XMLHttpRequest;i.onreadystatechange=function(){if(4===i.readyState)if(200===i.status)s();else{var e=p.parseXmlFromStr(i.responseText);"lowPrivilege"===$(e).find("subStatusCode").text()?o(403):o()}},i.open("put",n,!0),e.sessionTag&&i.setRequestHeader("SessionTag",e.sessionTag),i.send(t.UpgradeFile)}))})(r=M(r)+"?token="+x(e,e.szDeviceIdentify),s=M(s)).then((function(e){n.resolve(e)}),(function(e){n.reject(e)}))}return n},_.prototype.set3DZoom=function(e,t,n,r){var s="";if(s=t.iChannelID<=e.iAnalogChannelNum?v(this.CGI.set3DZoom.analog,e.szHttpProtocol,e.szIP,e.iCGIPort,t.iChannelID):t.bShttpIPChannel?v(this.CGI.set3DZoom.digital,e.szHttpProtocol,e.szIP,e.iCGIPort,t.iChannelID-e.oStreamCapa.iIpChanBase+1+e.iAnalogChannelNum):v(this.CGI.set3DZoom.digital,e.szHttpProtocol,e.szIP,e.iCGIPort,t.iChannelID),q()){const e=o.JS_GetWndContainer(t.iIndex).getBoundingClientRect(),r=e.width,s=e.height;console.log("窗口宽高：",r,s);p.loadXML(n);var i=parseInt(n.startPos[0]/r*255,10),a=parseInt(n.startPos[1]/s*255,10),c=parseInt(n.endPos[0]/r*255,10),u=parseInt(n.endPos[1]/s*255,10)}var l="<?xml version='1.0' encoding='UTF-8'?><Position3D><StartPoint><positionX>"+i+"</positionX><positionY>"+(255-a)+"</positionY></StartPoint><EndPoint><positionX>"+c+"</positionX><positionY>"+(255-u)+"</positionY></EndPoint></Position3D>",d=new X,f={type:"PUT",url:s,data:l,auth:e.szAuth,success:null,error:null};p.extend(f,r),p.extend(f,{success:function(e){r.success&&r.success(e)},error:function(e,t){r.error&&r.error(e,t)}}),d.setRequestParam(f),d.submitRequest()},_.prototype.getSDKCapa=function(e,t){var n=v(this.CGI.SDKCapabilities,e.szHttpProtocol,e.szIP,e.iCGIPort),r=new X,s={type:"GET",url:n,async:!1,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()},_.prototype.deviceCapturePic=function(e,t,n,r){t=100*t+1;var s=-1,o=v(this.CGI.deviceCapture.channels,e.szHttpProtocol,e.szIP,e.iCGIPort,t),i=[];if(p.isInt(r.iResolutionWidth)&&i.push("videoResolutionWidth="+r.iResolutionWidth),p.isInt(r.iResolutionHeight)&&i.push("videoResolutionHeight="+r.iResolutionHeight),i.length>0&&(o+="?"+i.join("&")),q()){s=function(e,t){return $("body").append('<a id="jsplugin_download_a" href="'+e+'" download='+t+'.jpg><li id="jsplugin_download_li"></li></a>'),$("#jsplugin_download_li").trigger("click"),$("#jsplugin_download_a").remove(),0}(o=M(o),n)}return s},_.prototype.getSessionV2Cap=function(e,t,n,r,s,o){var i="";i=2==n?"https://":"http://";var a=v(this.CGI.sessionCap,i,t,r,encodeURIComponent(s));a=a+"&random:"+e;var c=new X,u={type:"GET",url:a,auth:"",success:null,error:null};p.extend(u,o),p.extend(u,{success:function(e){o.success&&o.success(e)},error:function(e,t){o.error&&o.error(e,t)}}),c.setRequestParam(u),c.submitRequest()},_.prototype.getSessionCap=function(e,t,n,r,s){var o="";o=2==t?"https://":"http://";var i=v(this.CGI.sessionCap,o,e,n,encodeURIComponent(r)),a=new X,c={type:"GET",url:i,auth:"",success:null,error:null};p.extend(c,s),p.extend(c,{success:function(e){s.success&&s.success(e)},error:function(e,t){s.error&&s.error(e,t)}}),a.setRequestParam(c),a.submitRequest()},_.prototype.sessionV2Login=function(e,n,r,s,o,i,a,c){var u="";u=2==r?"https://":"http://";var l=parseInt(t.$XML(a).find("sessionIDVersion").eq(0).text(),10),d="true"===t.$XML(a).find("isSupportSessionTag").eq(0).text(),f="true"===t.$XML(a).find("isSessionIDValidLongTerm").eq(0).text(),I=v(this.CGI.sessionLogin,u,n,s)+"?timeStamp="+Date.now(),h=t.$XML(a).find("sessionID").eq(0).text(),P=t.$XML(a).find("challenge").eq(0).text(),m=parseInt(t.$XML(a).find("iterations").eq(0).text(),10),g=!1,S="";t.$XML(a).find("isIrreversible",!0).length>0&&(g="true"===t.$XML(a).find("isIrreversible").eq(0).text(),S=t.$XML(a).find("salt").eq(0).text()),this.m_oInfoForLocalPlgin={szRandom:e,sessionID:h,iterations:m,challenge:P,user:o};var y=p.encodePwd(i,{challenge:P,userName:o,salt:S,iIterate:m},g),C="<SessionLogin>";C+="<userName>"+p.escape(o)+"</userName>",C+="<password>"+y+"</password>",C+="<sessionID>"+h+"</sessionID>",C+="<isSessionIDValidLongTerm>"+f+"</isSessionIDValidLongTerm>",C+="<sessionIDVersion>"+l+"</sessionIDVersion>",C+="<isNeedSessionTag>"+d+"</isNeedSessionTag>",C+="</SessionLogin>";var x=new X,b={type:"POST",url:I,data:C,auth:"",success:null,error:null};p.extend(b,c),p.extend(b,{success:function(e){c.success&&c.success(e)},error:function(e,t){c.error&&c.error(e,t)}}),x.setRequestParam(b),x.submitRequest()},_.prototype.sessionLogin=function(e,n,r,s,o,i,a){var c="";c=2==n?"https://":"http://";var u=v(this.CGI.sessionLogin,c,e,r),l=t.$XML(i).find("sessionID").eq(0).text(),d=t.$XML(i).find("challenge").eq(0).text(),f=parseInt(t.$XML(i).find("iterations").eq(0).text(),10),I=!1,h="";t.$XML(i).find("isIrreversible",!0).length>0&&(I="true"===t.$XML(i).find("isIrreversible").eq(0).text(),h=t.$XML(i).find("salt").eq(0).text());var P="";if(I){P=p.sha256(s+h+o),P=p.sha256(P+d);for(m=2;m<f;m++)P=p.sha256(P)}else{P=p.sha256(o)+d;for(var m=1;m<f;m++)P=p.sha256(P)}var g="<SessionLogin>";g+="<userName>"+p.escape(s)+"</userName>",g+="<password>"+P+"</password>",g+="<sessionID>"+l+"</sessionID>",g+="</SessionLogin>";var S=new X,y={type:"POST",url:u,data:g,auth:"",success:null,error:null};p.extend(y,a),p.extend(y,{success:function(e){a.success&&a.success(e)},error:function(e,t){a.error&&a.error(e,t)}}),S.setRequestParam(y),S.submitRequest()},_.prototype.sessionHeartbeat=function(e,t,n){var r=v(this.CGI.getDeviceInfo,e.szHttpProtocol,e.szIP,e.iCGIPort),s=new X,o={type:"GET",url:r,auth:e.szAuth,success:null,error:null};p.extend(o,{success:function(e){t&&t(e)},error:function(e,t){n&&n(e,t)}}),s.setRequestParam(o),s.submitRequest()},_.prototype.sessionLogout=function(e,t){var n=v(this.CGI.sessionLogout,e.szHttpProtocol,e.szIP,e.iCGIPort),r=new X,s={type:"PUT",url:n,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()},_.prototype.getSystemCapa=function(e,t){var n=v(this.CGI.systemCapabilities,e.szHttpProtocol,e.szIP,e.iCGIPort),r=new X,s={type:"GET",url:n,async:!1,auth:e.szAuth,success:null,error:null};p.extend(s,t),p.extend(s,{success:function(e){t.success&&t.success(e)},error:function(e,n){t.error&&t.error(e,n)}}),r.setRequestParam(s),r.submitRequest()};var B,H,Z=function(){};Z.prototype._alert=function(e){s.bDebugMode&&console.log(e)},B=this,(H=function(e){this.elems=[],this.length=0,this.length=this.elems.push(e)}).prototype.find=function(e,t){var n=this.elems[this.length-1]?this.elems[this.length-1].getElementsByTagName(e):[];return this.length=this.elems.push(n),t?n:this},H.prototype.eq=function(e,t){var n=this.elems[this.length-1].length,r=null;return n>0&&e<n&&(r=this.elems[this.length-1][e]),this.length=this.elems.push(r),t?r:this},H.prototype.text=function(e){return this.elems[this.length-1]?e?void(window.DOMParser?this.elems[this.length-1].textContent=e:this.elems[this.length-1].text=e):window.DOMParser?this.elems[this.length-1].textContent:this.elems[this.length-1].text:""},H.prototype.attr=function(e){if(this.elems[this.length-1]){var t=this.elems[this.length-1].attributes.getNamedItem(e);return t?t.value:""}},B.$XML=function(e){return new H(e)};var N=function(){};function F(){this.id=this.createUUID()}N.prototype.parseXmlFromStr=function(e){if(null===e||""===e)return null;var t=function(){for(var e,t=["MSXML2.DOMDocument","MSXML2.DOMDocument.5.0","MSXML2.DOMDocument.4.0","MSXML2.DOMDocument.3.0","Microsoft.XmlDom"],n=0,r=t.length;n<r;n++)try{e=new ActiveXObject(t[n]);break}catch(t){e=document.implementation.createDocument("","",null);break}return e.async="false",e}();"Netscape"===navigator.appName||"Opera"===navigator.appName?t=(new DOMParser).parseFromString(e,"text/xml"):t.loadXML(e);return t},N.prototype.extend=function(){for(var e,t=arguments[0]||{},n=1,r=arguments.length;n<r;n++)if(null!=(e=arguments[n]))for(var s in e){t[s];var o=e[s];t!==o&&("object"==typeof o?t[s]=this.extend({},o):void 0!==o&&(t[s]=o))}return t},N.prototype.browser=function(){var e=navigator.userAgent.toLowerCase(),t=/(chrome)[ \/]([\w.]+)/.exec(e)||/(safari)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version)?[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||/(trident.*rv:)([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+))?/.exec(e)||["unknow","0"];t.length>0&&t[1].indexOf("trident")>-1&&(t[1]="msie");var n={};return n[t[1]]=!0,n.version=t[2],n},N.prototype.loadXML=function(e){if(null==e||""==e)return null;var t=null;window.DOMParser?t=(new DOMParser).parseFromString(e,"text/xml"):((t=new ActiveXObject("Microsoft.XMLDOM")).async=!1,t.loadXML(e));return t},N.prototype.toXMLStr=function(e){var t="";try{t=(new XMLSerializer).serializeToString(e)}catch(n){try{t=e.xml}catch(e){return""}}return-1==t.indexOf("<?xml")&&(t="<?xml version='1.0' encoding='utf-8'?>"+t),t},N.prototype.escape=function(e){return e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"):e},N.prototype.encodePwd=function(e,t,n){var r="";if(n){r=p.sha256(t.userName+t.salt+e),r=p.sha256(r+t.challenge);for(s=2;s<t.iIterate;s++)r=p.sha256(r)}else{r=p.sha256(e)+t.challenge;for(var s=1;s<t.iIterate;s++)r=p.sha256(r)}return r},N.prototype.dateFormat=function(e,t){var n={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()};for(var r in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length))),n)new RegExp("("+r+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?n[r]:("00"+n[r]).substr((""+n[r]).length)));return t},N.prototype.Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){var t,n,r,s,o,i,a,c="",u=0;for(e=N.prototype.Base64._utf8_encode(e);u<e.length;)s=(t=e.charCodeAt(u++))>>2,o=(3&t)<<4|(n=e.charCodeAt(u++))>>4,i=(15&n)<<2|(r=e.charCodeAt(u++))>>6,a=63&r,isNaN(n)?i=a=64:isNaN(r)&&(a=64),c=c+this._keyStr.charAt(s)+this._keyStr.charAt(o)+this._keyStr.charAt(i)+this._keyStr.charAt(a);return c},decode:function(e){var t,n,r,s,o,i,a="",c=0;for(e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");c<e.length;)t=this._keyStr.indexOf(e.charAt(c++))<<2|(s=this._keyStr.indexOf(e.charAt(c++)))>>4,n=(15&s)<<4|(o=this._keyStr.indexOf(e.charAt(c++)))>>2,r=(3&o)<<6|(i=this._keyStr.indexOf(e.charAt(c++))),a+=String.fromCharCode(t),64!=o&&(a+=String.fromCharCode(n)),64!=i&&(a+=String.fromCharCode(r));return a=N.prototype.Base64._utf8_decode(a)},_utf8_encode:function(e){e=e.replace(/\r\n/g,"\n");for(var t="",n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t+=String.fromCharCode(r):r>127&&r<2048?(t+=String.fromCharCode(r>>6|192),t+=String.fromCharCode(63&r|128)):(t+=String.fromCharCode(r>>12|224),t+=String.fromCharCode(r>>6&63|128),t+=String.fromCharCode(63&r|128))}return t},_utf8_decode:function(e){for(var t="",n=0,r=c1=c2=0;n<e.length;)(r=e.charCodeAt(n))<128?(t+=String.fromCharCode(r),n++):r>191&&r<224?(c2=e.charCodeAt(n+1),t+=String.fromCharCode((31&r)<<6|63&c2),n+=2):(c2=e.charCodeAt(n+1),c3=e.charCodeAt(n+2),t+=String.fromCharCode((15&r)<<12|(63&c2)<<6|63&c3),n+=3);return t}},N.prototype.createEventScript=function(e,t,n){var r=document.createElement("script");r.htmlFor=e,r.event=t,r.innerHTML=n,document.body.parentNode.appendChild(r)},N.prototype.isInt=function(e){return/^\d+$/.test(e)},N.prototype.getDirName=function(){var e="";if(""!==s.szBasePath)e=s.szBasePath;else{var t=/[^?#]*\//,n=document.getElementById("videonode");if(n)e=n.src.match(t)[0];else{for(var r=document.scripts,o=0,i=r.length;o<i;o++)if(r[o].src.indexOf("webVideoCtrl.js")>-1){n=r[o];break}n&&(e=n.src.match(t)[0])}}return e},N.prototype.loadScript=function(e,t){var n=document.createElement("script");n.type="text/javascript",n.onload=function(){t()},n.src=e,document.getElementsByTagName("head")[0].appendChild(n)},N.prototype.encodeString=function(e){return e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"):""},N.prototype.getIrreversibleKey=function(e,t){var n=e;if(oSecurityCap.oIrreversibleEncrypt.bSupport){var r=oSecurityCap.oIrreversibleEncrypt.salt;return p.sha256(t+r+e)}return n},N.prototype.strToAESKey=function(e,t){var n="";if(oSecurityCap.iKeyIterateNum>0){n=p.sha256(p.getIrreversibleKey(e,t)+"AaBbCcDd1234!@#$");for(var r=1;r<oSecurityCap.iKeyIterateNum;r++)n=p.sha256(n)}return n=n&&n.substring(0,32)},N.prototype.exportPasswordDeviceConfig=function(e,t){var n=MD5((new Date).getTime().toString());return e+"?secretkey="+p.encodeAES(p.Base64.encode(p.encodeString(t)),szAESKey,n)+"&security=1&iv="+n},N.prototype.encodeAES=function(e,t,n,r){var s="";if("ecb"===r)for(var o=e.length,i=0;o>0;)s+=o>16?aes_encrypt(e.substring(i,i+16),t,!0):aes_encrypt(e.substring(i),t,!0),o-=16,i+=16;else{void 0===n&&(n="6cd9616beb39d4034fdebe107df9a399");var a=CryptoJS.enc.Hex.parse(t),c=CryptoJS.enc.Hex.parse(n);s=CryptoJS.AES.encrypt(e,a,{mode:CryptoJS.mode.CBC,iv:c,padding:CryptoJS.pad.Pkcs7}).ciphertext.toString()}return s},N.prototype.sha256=function(e){function t(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function n(e,t){return e>>>t|e<<32-t}return e=function(e){e=e.replace(/\r\n/g,"\n");for(var t="",n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t+=String.fromCharCode(r):(r>127&&r<2048?t+=String.fromCharCode(r>>6|192):(t+=String.fromCharCode(r>>12|224),t+=String.fromCharCode(r>>6&63|128)),t+=String.fromCharCode(63&r|128))}return t}(e),function(e){for(var t="",n=0;n<4*e.length;n++)t+="0123456789abcdef".charAt(e[n>>2]>>8*(3-n%4)+4&15)+"0123456789abcdef".charAt(e[n>>2]>>8*(3-n%4)&15);return t}(function(e,r){var s,o,i,a,c,u,l,d,p,f,I,h,P=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],m=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],g=Array(64);for(e[r>>5]|=128<<24-r%32,e[15+(r+64>>9<<4)]=r,p=0;p<e.length;p+=16){for(s=m[0],o=m[1],i=m[2],a=m[3],c=m[4],u=m[5],l=m[6],d=m[7],f=0;f<64;f++)g[f]=f<16?e[f+p]:t(t(t(n(g[f-2],17)^n(g[f-2],19)^g[f-2]>>>10,g[f-7]),n(g[f-15],7)^n(g[f-15],18)^g[f-15]>>>3),g[f-16]),I=t(t(t(t(d,n(c,6)^n(c,11)^n(c,25)),c&u^~c&l),P[f]),g[f]),h=t(n(s,2)^n(s,13)^n(s,22),s&o^s&i^o&i),d=l,l=u,u=c,c=t(a,I),a=i,i=o,o=s,s=t(I,h);m[0]=t(s,m[0]),m[1]=t(o,m[1]),m[2]=t(i,m[2]),m[3]=t(a,m[3]),m[4]=t(c,m[4]),m[5]=t(u,m[5]),m[6]=t(l,m[6]),m[7]=t(d,m[7])}return m}(function(e){for(var t=[],n=0;n<8*e.length;n+=8)t[n>>5]|=(255&e.charCodeAt(n/8))<<24-n%32;return t}(e),8*e.length))},N.prototype.cookie=function(e,t,n){if(arguments.length>1&&(null===t||"object"!=typeof t)){if(n=this.extend({},n),null===t&&(n.expires=-1),"number"==typeof n.expires){var r=n.expires,s=n.expires=new Date;s.setDate(s.getDate()+r)}return document.cookie=[encodeURIComponent(e),"=",n.raw?String(t):encodeURIComponent(String(t)),n.expires?"; expires="+n.expires.toUTCString():"",n.path?"; path="+n.path:"; path=/",n.domain?"; domain="+n.domain:"",n.secure?"; secure":""].join("")}var o,i=(n=t||{}).raw?function(e){return e}:decodeURIComponent;return(o=new RegExp("(?:^|; )"+encodeURIComponent(e)+"=([^;]*)").exec(document.cookie))?i(o[1]):null},N.prototype.nativeAjax=function(e){if(null===e||"object"!=typeof e)throw new Error("希望接收一个对象作为参数");if(!e.url)throw new Error("不合理的url");var t={type:"GET",async:!0,responseType:"json",timeout:6e4};for(var n in t)-1===Object.keys(e).indexOf(n)&&(e[n]=t[n]);var r=new XMLHttpRequest;r.open(e.type,e.url,e.async),r.timeout=e.timeout,e.ContentType&&r.setRequestHeader("Content-Type",e.ContentType),e.ContentLength&&r.setRequestHeader("Content-Length",e.ContentLength),e.sessionTag&&r.setRequestHeader("SessionTag",e.sessionTag),r.responseType=e.responseType,r.onload=function(){4===r.readyState&&(200===r.status?e.success(r.response,o,r):e.error(r.response,r))},r.error=function(){e.error(r.response,r)};var s=e.data||null;function o(e,t){if(t=t||r.response,window.navigator&&window.navigator.msSaveOrOpenBlob)window.navigator.msSaveOrOpenBlob(t,e);else{var n=document.createElement("a");document.body.appendChild(n),n.style="display: none";let r=[];r.push(t);var s=window.URL.createObjectURL(new Blob(r,{type:"application/octet-stream"}));n.href=s,n.download=e,n.click(),n.remove?n.remove():document.body.removeChild(n),window.URL.revokeObjectURL(s)}}r.send(s)},F.prototype.valueOf=function(){return this.id},F.prototype.toString=function(){return this.id},F.prototype.createUUID=function(){var e=new Date(1582,10,15,0,0,0,0),t=(new Date).getTime()-e.getTime(),n="-";return F.getIntegerBits(t,0,31)+n+F.getIntegerBits(t,32,47)+n+(F.getIntegerBits(t,48,59)+"1")+n+F.getIntegerBits(F.rand(4095),0,7)+F.getIntegerBits(F.rand(4095),0,7)+n+(F.getIntegerBits(F.rand(8191),0,7)+F.getIntegerBits(F.rand(8191),8,15)+F.getIntegerBits(F.rand(8191),0,7)+F.getIntegerBits(F.rand(8191),8,15)+F.getIntegerBits(F.rand(8191),0,15))},F.getIntegerBits=function(e,t,n){var r=F.returnBase(e,16),s=new Array,o="",i=0;for(i=0;i<r.length;i++)s.push(r.substring(i,i+1));for(i=Math.floor(t/4);i<=Math.floor(n/4);i++)s[i]&&""!=s[i]?o+=s[i]:o+="0";return o},F.returnBase=function(e,t){var n=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];if(e<t)var r=n[e];else{var s=""+Math.floor(e/t),o=e-s*t;if(s>=t)r=this.returnBase(s,t)+n[o];else r=n[s]+n[o]}return r},F.rand=function(e){return Math.floor(Math.random()*e)},d=new _,l=new Z;var V=(p=new N).dateFormat(new Date,"yyyyMMddhhmmss");return r="webVideoCtrl"+V,"object"!=typeof window.attachEvent&&p.browser().msie&&(p.createEventScript(r,"GetSelectWndInfo(SelectWndInfo)","GetSelectWndInfo(SelectWndInfo);"),p.createEventScript(r,"ZoomInfoCallback(szZoomInfo)","ZoomInfoCallback(szZoomInfo);"),p.createEventScript(r,"GetHttpInfo(lID, lpInfo, lReverse)","GetHttpInfo(lID, lpInfo, lReverse);"),p.createEventScript(r,"PluginEventHandler(iEventType, iParam1, iParam2)","PluginEventHandler(iEventType, iParam1, iParam2);"),p.createEventScript(r,"RemoteConfigInfo(lID)","RemoteConfigInfo(lID);"),p.createEventScript(r,"KeyBoardEventInfo(iKeyCode)","KeyBoardEventInfo(iKeyCode);")),this}(),t=window.WebVideoCtrl=e;t.version="1.1.0"}}(),"object"==typeof exports&&"undefined"!=typeof module||("function"==typeof define&&define.amd?define((function(){return WebVideoCtrl})):"function"==typeof define&&define.cmd&&define((function(e,t,n){n.exports=WebVideoCtrl})));
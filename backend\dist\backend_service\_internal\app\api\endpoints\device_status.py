from typing import Any, List, Dict
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from datetime import datetime

from app.api import deps
from app.models.models import DetectionGroup, DieCaster, VideoSource

router = APIRouter()


class DeviceStatusResponse(BaseModel):
    """设备状态响应模型"""
    device_id: int
    device_name: str
    device_type: str  # "die_caster" 或 "video_source"
    status: str
    last_update: str
    detection_groups: List[Dict[str, Any]]


class DeviceOverviewResponse(BaseModel):
    """设备概览响应模型"""
    total_devices: int
    online_devices: int
    offline_devices: int
    active_detection_groups: int
    devices: List[DeviceStatusResponse]


@router.get("/device-status", response_model=DeviceOverviewResponse)
def get_device_status(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取设备状态概览
    """
    devices = []
    
    # 获取所有压铸机状态
    die_casters = db.query(DieCaster).all()
    for dc in die_casters:
        # 获取关联的检测组
        detection_groups = db.query(DetectionGroup).filter(
            DetectionGroup.die_caster_id == dc.id
        ).all()
        
        device_info = DeviceStatusResponse(
            device_id=dc.id,
            device_name=dc.name,
            device_type="die_caster",
            status=dc.status,
            last_update=dc.updated_at.isoformat() if dc.updated_at else datetime.now().isoformat(),
            detection_groups=[
                {
                    "id": dg.id,
                    "name": dg.name,
                    "status": dg.status
                } for dg in detection_groups
            ]
        )
        devices.append(device_info)
    
    # 获取所有视频源状态
    video_sources = db.query(VideoSource).all()
    for vs in video_sources:
        # 获取关联的检测组
        detection_groups = db.query(DetectionGroup).filter(
            DetectionGroup.video_source_id == vs.id
        ).all()
        
        device_info = DeviceStatusResponse(
            device_id=vs.id,
            device_name=vs.name,
            device_type="video_source",
            status=vs.device_status or "offline",
            last_update=vs.updated_at.isoformat() if vs.updated_at else datetime.now().isoformat(),
            detection_groups=[
                {
                    "id": dg.id,
                    "name": dg.name,
                    "status": dg.status
                } for dg in detection_groups
            ]
        )
        devices.append(device_info)
    
    # 统计信息
    total_devices = len(devices)
    online_devices = len([d for d in devices if d.status in ["online", "active"]])
    offline_devices = total_devices - online_devices
    
    # 统计活跃检测组数量
    active_detection_groups = db.query(DetectionGroup).filter(
        DetectionGroup.status == "active"
    ).count()
    
    return DeviceOverviewResponse(
        total_devices=total_devices,
        online_devices=online_devices,
        offline_devices=offline_devices,
        active_detection_groups=active_detection_groups,
        devices=devices
    )


@router.get("/device-status/{device_type}/{device_id}", response_model=DeviceStatusResponse)
def get_specific_device_status(
    *,
    db: Session = Depends(deps.get_db),
    device_type: str,
    device_id: int,
) -> Any:
    """
    获取特定设备状态
    """
    if device_type == "die_caster":
        device = db.query(DieCaster).filter(DieCaster.id == device_id).first()
        if not device:
            raise HTTPException(status_code=404, detail="压铸机不存在")
    elif device_type == "video_source":
        device = db.query(VideoSource).filter(VideoSource.id == device_id).first()
        if not device:
            raise HTTPException(status_code=404, detail="视频源不存在")
    else:
        raise HTTPException(status_code=400, detail="无效的设备类型")
    
    # 获取关联的检测组
    if device_type == "die_caster":
        detection_groups = db.query(DetectionGroup).filter(
            DetectionGroup.die_caster_id == device_id
        ).all()
    else:
        detection_groups = db.query(DetectionGroup).filter(
            DetectionGroup.video_source_id == device_id
        ).all()
    
    return DeviceStatusResponse(
        device_id=device.id,
        device_name=device.name,
        device_type=device_type,
        status=device.device_status if device_type == "video_source" else device.status,
        last_update=device.updated_at.isoformat() if device.updated_at else datetime.now().isoformat(),
        detection_groups=[
            {
                "id": dg.id,
                "name": dg.name,
                "status": dg.status
            } for dg in detection_groups
        ]
    )
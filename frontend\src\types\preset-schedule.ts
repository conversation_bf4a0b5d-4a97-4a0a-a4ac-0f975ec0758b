// 预设检测计划相关类型定义

export interface PresetSchedule {
  id: string
  name: string
  description?: string
  templateId: string
  templateName: string
  date: string
  startTime: string
  endTime: string
  isEnabled: boolean
  autoStart: boolean
  requireConfirmation: boolean
  retryCount: number
  createdAt: string
  updatedAt: string
  lastExecutedAt?: string
  executionStatus: 'pending' | 'running' | 'completed' | 'failed' | 'stopped'
  executionResult?: {
    success: boolean
    message: string
    detectionCount?: number
    errorCount?: number
    duration?: number
  }
}

export interface PresetScheduleCreate {
  name: string
  description?: string
  templateId: string
  date: string
  startTime: string
  endTime: string
  isEnabled?: boolean
  autoStart?: boolean
  requireConfirmation?: boolean
  retryCount?: number
}

export interface PresetScheduleUpdate {
  name?: string
  description?: string
  templateId?: string
  date?: string
  startTime?: string
  endTime?: string
  isEnabled?: boolean
  autoStart?: boolean
  requireConfirmation?: boolean
  retryCount?: number
}

export interface ScheduleExecutionStatus {
  id: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'stopped'
  startedAt?: string
  completedAt?: string
  progress?: number
  message?: string
  schedule?: PresetSchedule
  template?: {
    id: number
    name: string
    detectionGroups: {
      id: number
      name: string
      status: string
    }[]
  }
  dashboardUrl?: string
  autoStart?: boolean
  result?: {
    success: boolean
    detectionCount?: number
    errorCount?: number
    duration?: number
    errors?: string[]
  }
}

export interface ScheduleConflict {
  hasConflict: boolean
  conflictingSchedules?: {
    id: string
    name: string
    startTime: string
    endTime: string
  }[]
  message?: string
}

// 预设计划统计信息
export interface ScheduleStatistics {
  total: number
  enabled: number
  disabled: number
  todayScheduled: number
  running: number
  completed: number
  failed: number
}

// 预设计划执行历史
export interface ScheduleExecutionHistory {
  id: string
  scheduleId: string
  scheduleName: string
  executedAt: string
  status: 'completed' | 'failed' | 'stopped'
  duration: number
  detectionCount?: number
  errorCount?: number
  message?: string
}
<template>
  <div class="detection-carousel" :class="{ 'compact-mode': isCompactMode }">
    <!-- 优化后的标题栏 -->
    <div class="carousel-header">
      <div class="header-left">
        <div class="header-icon">
          <el-icon><camera /></el-icon>
        </div>
        <div class="header-info">
          <h3 class="header-title">检测信息轮播</h3>
          <span class="header-subtitle">实时监控 · 智能检测</span>
        </div>
      </div>
      <div class="header-right">
        <div class="control-buttons">
          <el-button-group size="small">
            <el-button @click="toggleAutoPlay" :type="isPlaying ? 'primary' : 'default'">
              <el-icon><video-play v-if="!isPlaying" /><video-pause v-else /></el-icon>
            </el-button>
            <el-button @click="refreshDetections">
              <el-icon><refresh /></el-icon>
            </el-button>
            <el-button @click="toggleCompactMode">
              <el-icon><expand v-if="isCompactMode" /><fold v-else /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <div class="panel-content">
      <!-- 当日统计概览 -->
      <div class="daily-stats">
        <div class="stat-card">
          <div class="stat-icon total">
            <el-icon><data-analysis /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ dailyStats.totalDetections }}</div>
            <div class="stat-label">总检测次数</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon jam">
            <el-icon><warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ dailyStats.jamCount }}</div>
            <div class="stat-label">卡料次数</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon rate">
            <el-icon><pie-chart /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ dailyStats.jamRate }}%</div>
            <div class="stat-label">卡料率</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon accuracy">
            <el-icon><aim /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ dailyStats.accuracy }}%</div>
            <div class="stat-label">检测准确率</div>
          </div>
        </div>
      </div>

      <!-- 检测轮播 -->
      <div class="carousel-container">
        <!-- 主要检测展示 -->
        <div class="main-detection-display">
          <el-carousel
            ref="carouselRef"
            :interval="carouselInterval"
            :autoplay="isPlaying"
            height="280px"
            indicator-position="none"
            arrow="hover"
            class="detection-carousel-main"
          >
            <el-carousel-item v-for="detection in detectionList" :key="detection.id">
              <div class="detection-item">
                <!-- 检测图片 -->
                <div class="detection-image">
                  <img :src="detection.imageUrl" :alt="detection.title" />
                  <div class="image-overlay">
                    <div class="detection-type" :class="detection.type">
                      {{ getDetectionTypeText(detection.type) }}
                    </div>
                    <div class="detection-time">{{ formatTime(detection.timestamp) }}</div>
                  </div>
                </div>

                <!-- 检测信息 -->
                <div class="detection-info">
                  <div class="detection-header">
                    <h4 class="detection-title">{{ detection.title }}</h4>
                    <el-tag
                      :type="getDetectionTagType(detection.type)"
                      size="small"
                      effect="dark"
                    >
                      {{ getDetectionTypeText(detection.type) }}
                    </el-tag>
                  </div>

                  <div class="detection-details">
                    <div class="detail-row">
                      <span class="detail-label">设备:</span>
                      <span class="detail-value">{{ detection.deviceName }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">检测组:</span>
                      <span class="detail-value">{{ detection.groupName }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">置信度:</span>
                      <span class="detail-value">{{ detection.confidence }}%</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">处理时间:</span>
                      <span class="detail-value">{{ detection.processingTime }}ms</span>
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div class="detection-actions">
                    <el-button size="small" @click="viewDetails(detection)">
                      详情
                    </el-button>
                    <el-button
                      v-if="detection.type === 'jam'"
                      size="small"
                      type="primary"
                      @click="handleJam(detection)"
                    >
                      <el-icon><tools /></el-icon>
                      处理
                    </el-button>
                  </div>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>

        <!-- 最近检测消息列表 -->
        <div class="recent-detections-list">
          <div class="recent-header">
            <span class="recent-title">最近检测</span>
            <span class="recent-count">{{ recentDetections.length }} 条</span>
          </div>
          <div class="recent-items">
            <div
              v-for="(item, index) in recentDetections.slice(0, 6)"
              :key="item.id"
              class="recent-item"
              :class="[item.type, { 'active': index === currentCarouselIndex }]"
              @click="jumpToDetection(item, index)"
            >
              <div class="recent-icon">
                <el-icon>
                  <warning v-if="item.type === 'jam'" />
                  <check v-else-if="item.type === 'normal'" />
                  <question-filled v-else />
                </el-icon>
              </div>
              <div class="recent-content">
                <div class="recent-device">{{ item.deviceName }}</div>
                <div class="recent-time">{{ formatTime(item.timestamp) }}</div>
              </div>
              <div class="recent-status" :class="item.type"></div>
            </div>
          </div>
        </div>
      </div>


    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Camera, VideoPlay, VideoPause, Refresh, DataAnalysis, Warning,
  PieChart, Aim, Tools, Check, QuestionFilled,
  Expand, Fold
} from '@element-plus/icons-vue'

// 响应式数据
const isPlaying = ref(true)
const carouselInterval = ref(5000) // 5秒轮播
const carouselRef = ref()
const isCompactMode = ref(false)
const currentCarouselIndex = ref(0)

// 当日统计数据
const dailyStats = ref({
  totalDetections: 1247,
  jamCount: 8,
  jamRate: 0.64,
  accuracy: 98.5
})

// 检测数据列表
const detectionList = ref([
  {
    id: 1,
    title: '压铸机1-检测组1发现卡料',
    type: 'jam',
    deviceName: '压铸机1',
    groupName: '检测组1',
    roiName: 'ROI-压铸区域',
    timestamp: new Date(),
    processingTime: 120,
    confidence: 96.8,
    imageUrl: '/api/placeholder/400/300?text=卡料检测图片1'
  },
  {
    id: 2,
    title: '压铸机2-检测组3正常检测',
    type: 'normal',
    deviceName: '压铸机2',
    groupName: '检测组3',
    roiName: 'ROI-排料区域',
    timestamp: new Date(Date.now() - 300000),
    processingTime: 85,
    confidence: 99.2,
    imageUrl: '/api/placeholder/400/300?text=正常检测图片1'
  },
  {
    id: 3,
    title: '压铸机1-检测组2异常检测',
    type: 'abnormal',
    deviceName: '压铸机1',
    groupName: '检测组2',
    roiName: 'ROI-压铸区域',
    timestamp: new Date(Date.now() - 600000),
    processingTime: 156,
    confidence: 87.3,
    imageUrl: '/api/placeholder/400/300?text=异常检测图片1'
  }
])

// 最近检测历史
const recentDetections = ref([
  { id: 1, type: 'jam', deviceName: '压铸机1', timestamp: new Date() },
  { id: 2, type: 'normal', deviceName: '压铸机2', timestamp: new Date(Date.now() - 120000) },
  { id: 3, type: 'normal', deviceName: '压铸机1', timestamp: new Date(Date.now() - 240000) },
  { id: 4, type: 'abnormal', deviceName: '压铸机3', timestamp: new Date(Date.now() - 360000) },
  { id: 5, type: 'normal', deviceName: '压铸机2', timestamp: new Date(Date.now() - 480000) }
])

// 方法
const getDetectionTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    jam: '卡料',
    normal: '正常',
    abnormal: '异常'
  }
  return typeMap[type] || '未知'
}

const getDetectionTagType = (type: string) => {
  const tagMap: Record<string, string> = {
    jam: 'danger',
    normal: 'success',
    abnormal: 'warning'
  }
  return tagMap[type] || 'info'
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`
  
  return date.toLocaleDateString()
}



const toggleCompactMode = () => {
  isCompactMode.value = !isCompactMode.value
  ElMessage.info(isCompactMode.value ? '已切换到紧凑模式' : '已切换到完整模式')
}

const toggleAutoPlay = () => {
  isPlaying.value = !isPlaying.value
  if (carouselRef.value) {
    if (isPlaying.value) {
      carouselRef.value.play()
    } else {
      carouselRef.value.pause()
    }
  }
}

const refreshDetections = () => {
  ElMessage.success('检测数据已刷新')
  // 这里添加实际的数据刷新逻辑
}

const viewDetails = (detection: any) => {
  ElMessage.info(`查看检测详情: ${detection.title}`)
  // 这里添加查看详情的逻辑
}

const handleJam = (detection: any) => {
  ElMessage.warning(`处理卡料: ${detection.title}`)
  // 这里添加处理卡料的逻辑
}

const jumpToDetection = (item: any, index?: number) => {
  // 跳转到对应的检测项
  const carouselIndex = index !== undefined ? index : detectionList.value.findIndex(d => d.id === item.id)
  if (carouselIndex !== -1 && carouselRef.value) {
    currentCarouselIndex.value = carouselIndex
    carouselRef.value.setActiveItem(carouselIndex)
  }
}

// 定时更新数据
let updateTimer: number | null = null

onMounted(() => {
  // 每分钟更新一次统计数据
  updateTimer = setInterval(() => {
    // 模拟数据更新
    dailyStats.value.totalDetections += Math.floor(Math.random() * 3)
    if (Math.random() < 0.1) { // 10%概率增加卡料
      dailyStats.value.jamCount += 1
    }
    dailyStats.value.jamRate = Number((dailyStats.value.jamCount / dailyStats.value.totalDetections * 100).toFixed(2))
  }, 60000)

  // 监听轮播变化
  if (carouselRef.value) {
    carouselRef.value.$on('change', (index: number) => {
      currentCarouselIndex.value = index
    })
  }
})

onUnmounted(() => {
  if (updateTimer) {
    clearInterval(updateTimer)
  }
})
</script>

<style scoped>
.detection-carousel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

/* 优化后的标题栏样式 */
.carousel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-color-soft) 100%);
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.carousel-header::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color) 0%, transparent 100%);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color) 0%, rgba(var(--primary-color-rgb), 0.8) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3);
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  line-height: 1.2;
}

.header-subtitle {
  font-size: 12px;
  color: var(--text-color-soft);
  font-weight: 400;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-buttons .el-button-group {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.control-buttons .el-button {
  border-color: var(--border-color);
  transition: all 0.3s ease;
}

.control-buttons .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.2);
}

/* 紧凑模式样式 */
.detection-carousel.compact-mode .daily-stats {
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.detection-carousel.compact-mode .stat-card {
  padding: 8px;
}

.detection-carousel.compact-mode .stat-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 6px;
  font-size: 14px;
}

.detection-carousel.compact-mode .stat-value {
  font-size: 16px;
}

.detection-carousel.compact-mode .stat-label {
  font-size: 11px;
}

.detection-carousel.compact-mode .carousel-container {
  height: 200px;
}

.detection-carousel.compact-mode .main-detection-display .el-carousel {
  height: 200px !important;
}

.detection-carousel.compact-mode .recent-detections-list {
  height: 200px;
}

.detection-carousel.compact-mode .detection-image {
  height: 100px;
}

.detection-carousel.compact-mode .detection-info {
  padding: 8px;
}

.detection-carousel.compact-mode .detection-title {
  font-size: 13px;
}

.detection-carousel.compact-mode .detail-row {
  font-size: 11px;
  margin-bottom: 3px;
}

.detection-carousel.compact-mode .detection-actions .el-button {
  font-size: 11px;
  padding: 3px 6px;
}

.panel-content {
  flex: 1;
  padding: 16px 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 当日统计样式 */
.daily-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.jam {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.rate {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #d4691a;
}

.stat-icon.accuracy {
  background: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%);
  color: #3498db;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-color-soft);
}

/* 轮播样式 */
.carousel-container {
  display: flex;
  gap: 16px;
  height: 280px;
  margin-bottom: 16px;
  flex: 1;
  min-height: 0;
}

.main-detection-display {
  flex: 2;
  min-width: 0;
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.recent-detections-list {
  flex: 1;
  min-width: 280px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
}

.recent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.recent-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
}

.recent-count {
  font-size: 12px;
  color: var(--text-color-mute);
  background-color: var(--bg-color-mute);
  padding: 2px 6px;
  border-radius: 10px;
}

.recent-items {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.recent-item:hover {
  background-color: var(--bg-color-hover);
  transform: translateX(2px);
}

.recent-item.active {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  border-color: var(--primary-color);
}

.recent-item.jam {
  border-left: 3px solid var(--danger-color);
}

.recent-item.normal {
  border-left: 3px solid var(--success-color);
}

.recent-item.abnormal {
  border-left: 3px solid var(--warning-color);
}

.recent-icon {
  font-size: 14px;
  color: var(--text-color-soft);
  flex-shrink: 0;
}

.recent-content {
  flex: 1;
  min-width: 0;
}

.recent-device {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recent-time {
  font-size: 10px;
  color: var(--text-color-mute);
  margin-top: 2px;
}

.recent-status {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.recent-status.jam {
  background-color: var(--danger-color);
  animation: pulse 2s infinite;
}

.recent-status.normal {
  background-color: var(--success-color);
}

.recent-status.abnormal {
  background-color: var(--warning-color);
}

/* 动画效果 */
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.detection-item {
  display: flex;
  height: 100%;
  background-color: var(--bg-color);
  overflow: hidden;
}

.detection-image {
  width: 50%;
  position: relative;
  overflow: hidden;
}

.detection-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, transparent 100%);
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.detection-type {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.detection-type.jam {
  background-color: var(--danger-color);
}

.detection-type.normal {
  background-color: var(--success-color);
}

.detection-type.abnormal {
  background-color: var(--warning-color);
}

.detection-time {
  font-size: 12px;
  color: white;
  opacity: 0.9;
}

.detection-info {
  width: 50%;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.detection-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.detection-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  flex: 1;
  margin-right: 12px;
}

.detection-details {
  flex: 1;
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 12px;
}

.detail-label {
  color: var(--text-color-soft);
  font-weight: 500;
}

.detail-value {
  color: var(--text-color);
  font-weight: 600;
}

.detection-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}



/* 响应式设计 */
@media (max-width: 1400px) {
  .carousel-container {
    flex-direction: column;
    height: auto;
    gap: 12px;
  }

  .main-detection-display {
    height: 250px;
  }

  .main-detection-display .el-carousel {
    height: 250px !important;
  }

  .recent-detections-list {
    height: 120px;
    min-width: auto;
  }

  .recent-items {
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
    gap: 6px;
  }

  .recent-item {
    min-width: 120px;
    flex-shrink: 0;
  }

  .detection-item {
    flex-direction: column;
  }

  .detection-image,
  .detection-info {
    width: 100%;
  }

  .detection-image {
    height: 120px;
  }

  .detection-info {
    padding: 12px;
  }
}

@media (max-width: 1200px) {
  .daily-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 16px;
  }

  .stat-card {
    padding: 8px;
  }

  .stat-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
    font-size: 14px;
  }

  .stat-value {
    font-size: 16px;
  }

  .stat-label {
    font-size: 11px;
  }

  .carousel-container .el-carousel {
    height: 300px !important;
  }

  .detection-image {
    height: 150px;
  }

  .detection-info {
    padding: 12px;
  }

  .detection-title {
    font-size: 14px;
  }

  .detail-row {
    font-size: 12px;
    margin-bottom: 6px;
  }

  .detection-actions {
    flex-wrap: wrap;
    gap: 6px;
  }

  .detection-actions .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }
}

@media (max-width: 768px) {
  .panel-content {
    padding: 12px 16px;
  }

  .daily-stats {
    grid-template-columns: 1fr;
    gap: 6px;
    margin-bottom: 12px;
  }

  .stat-card {
    padding: 6px 8px;
  }

  .stat-icon {
    width: 28px;
    height: 28px;
    margin-right: 6px;
    font-size: 12px;
  }

  .stat-value {
    font-size: 14px;
  }

  .stat-label {
    font-size: 10px;
  }

  .carousel-container {
    margin-bottom: 12px;
  }

  .carousel-container .el-carousel {
    height: 250px !important;
  }

  .detection-image {
    height: 120px;
  }

  .detection-info {
    padding: 8px;
  }

  .detection-title {
    font-size: 13px;
    margin-bottom: 8px;
  }

  .detail-row {
    font-size: 11px;
    margin-bottom: 4px;
  }

  .detection-actions {
    justify-content: center;
    gap: 4px;
  }

  .detection-actions .el-button {
    font-size: 11px;
    padding: 3px 6px;
  }

  .detection-history {
    padding: 12px;
  }

  .history-list {
    gap: 6px;
  }

  .history-item {
    padding: 6px 8px;
    min-width: 100px;
  }

  .history-device {
    font-size: 11px;
  }

  .history-time {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .panel-header {
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .panel-title {
    font-size: 14px;
  }

  .panel-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .panel-actions .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }

  .daily-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 4px;
  }

  .carousel-container .el-carousel {
    height: 200px !important;
  }

  .detection-image {
    height: 100px;
  }

  .detection-info {
    padding: 6px;
  }

  .detection-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    margin-bottom: 8px;
  }

  .detection-title {
    font-size: 12px;
    margin-right: 0;
  }

  .detail-row {
    font-size: 10px;
    margin-bottom: 3px;
  }

  .detection-actions {
    flex-direction: column;
    width: 100%;
  }

  .detection-actions .el-button {
    width: 100%;
    justify-content: center;
  }
}
</style>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试认证API问题
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def test_auth_formats():
    """测试不同的认证格式"""
    print("测试不同的认证API格式...\n")
    
    # 测试格式1: form data
    print("1. 测试 form data 格式:")
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = requests.post(f"{API_BASE}/auth/login", data=login_data)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ form data 格式成功")
            print(f"响应: {response.json()}")
            return response.json().get("access_token")
        else:
            print(f"❌ form data 格式失败: {response.text}")
    except Exception as e:
        print(f"❌ form data 格式异常: {e}")
    
    print("\n" + "-"*50)
    
    # 测试格式2: JSON
    print("2. 测试 JSON 格式:")
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = requests.post(f"{API_BASE}/auth/login", json=login_data)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ JSON 格式成功")
            print(f"响应: {response.json()}")
            return response.json().get("access_token")
        else:
            print(f"❌ JSON 格式失败: {response.text}")
    except Exception as e:
        print(f"❌ JSON 格式异常: {e}")
    
    print("\n" + "-"*50)
    
    # 测试格式3: OAuth2 form
    print("3. 测试 OAuth2 form 格式:")
    try:
        login_data = {
            "username": "admin",
            "password": "admin123",
            "grant_type": "password"
        }
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = requests.post(f"{API_BASE}/auth/login", data=login_data, headers=headers)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ OAuth2 form 格式成功")
            print(f"响应: {response.json()}")
            return response.json().get("access_token")
        else:
            print(f"❌ OAuth2 form 格式失败: {response.text}")
    except Exception as e:
        print(f"❌ OAuth2 form 格式异常: {e}")
    
    print("\n" + "-"*50)
    
    # 测试格式4: 不同的端点
    print("4. 测试不同的认证端点:")
    endpoints = [
        "/auth/login/",
        "/auth/token",
        "/auth/token/",
        "/token",
        "/token/",
        "/login",
        "/login/"
    ]
    
    for endpoint in endpoints:
        try:
            print(f"\n测试端点: {API_BASE}{endpoint}")
            login_data = {
                "username": "admin",
                "password": "admin123"
            }
            response = requests.post(f"{API_BASE}{endpoint}", data=login_data)
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                print(f"✅ 端点 {endpoint} 成功")
                print(f"响应: {response.json()}")
                return response.json().get("access_token")
            elif response.status_code != 404:
                print(f"❌ 端点 {endpoint} 失败: {response.text}")
        except Exception as e:
            print(f"❌ 端点 {endpoint} 异常: {e}")
    
    return None

def test_preset_schedules_with_token(token):
    """使用token测试预设计划API"""
    if not token:
        print("\n❌ 没有有效的token，跳过API测试")
        return
    
    print(f"\n使用token测试预设计划API: {token[:20]}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试获取所有预设计划
    try:
        response = requests.get(f"{API_BASE}/preset-schedules/", headers=headers)
        print(f"\nGET /preset-schedules/ 状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取预设计划成功，共 {len(data)} 条记录")
        else:
            print(f"❌ 获取预设计划失败: {response.text}")
    except Exception as e:
        print(f"❌ 获取预设计划异常: {e}")

def main():
    print("调试认证API问题...\n")
    
    # 测试不同的认证格式
    token = test_auth_formats()
    
    # 如果获取到token，测试预设计划API
    test_preset_schedules_with_token(token)
    
    print("\n" + "="*60)
    print("调试总结:")
    print("="*60)
    if token:
        print("✅ 找到有效的认证方式")
        print("✅ 可以继续测试预设计划API")
    else:
        print("❌ 所有认证方式都失败")
        print("❌ 需要检查后端认证配置")

if __name__ == "__main__":
    main()
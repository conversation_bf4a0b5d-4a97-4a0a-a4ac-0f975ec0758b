import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import * as videoSourcesApi from '@/api/video-sources'
import type { VideoSource } from '@/types'

export const useVideoSourceStore = defineStore('videoSource', () => {
  const videoSources = ref<VideoSource[]>([])
  const loading = ref(false)
  const currentVideoSource = ref<VideoSource | null>(null)
  
  // 获取所有视频源
  const fetchVideoSources = async () => {
    loading.value = true
    try {
      const response = await videoSourcesApi.getVideoSources()
      videoSources.value = response.items || response || []
      return videoSources.value
    } catch (error: any) {
      console.error('获取视频源失败', error)
      ElMessage.error('获取视频源列表失败')
      return []
    } finally {
      loading.value = false
    }
  }
  
  // 获取单个视频源
  const fetchVideoSource = async (id: number) => {
    loading.value = true
    try {
      const response = await videoSourcesApi.getVideoSource(id)
      currentVideoSource.value = response
      return response
    } catch (error: any) {
      console.error(`获取视频源(ID: ${id})失败`, error)
      ElMessage.error('获取视频源详情失败')
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 创建视频源
  const createVideoSource = async (data: Partial<VideoSource>) => {
    loading.value = true
    try {
      const response = await videoSourcesApi.createVideoSource(data)
      videoSources.value.push(response)
      ElMessage.success('视频源创建成功')
      return response
    } catch (error: any) {
      console.error('创建视频源失败', error)
      ElMessage.error('创建视频源失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 更新视频源
  const updateVideoSource = async (id: number, data: Partial<VideoSource>) => {
    loading.value = true
    try {
      const response = await videoSourcesApi.updateVideoSource(id, data)
      const index = videoSources.value.findIndex(item => item.id === id)
      if (index !== -1) {
        videoSources.value[index] = { ...videoSources.value[index], ...response }
      }
      ElMessage.success('视频源更新成功')
      return response
    } catch (error: any) {
      console.error(`更新视频源(ID: ${id})失败`, error)
      ElMessage.error('更新视频源失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 删除视频源
  const deleteVideoSource = async (id: number) => {
    loading.value = true
    try {
      await videoSourcesApi.deleteVideoSource(id)
      videoSources.value = videoSources.value.filter(item => item.id !== id)
      ElMessage.success('视频源删除成功')
      return true
    } catch (error: any) {
      console.error(`删除视频源(ID: ${id})失败`, error)
      ElMessage.error('删除视频源失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 测试视频源连接
  const testVideoSource = async (id: number) => {
    loading.value = true
    try {
      const response = await videoSourcesApi.testVideoSource(id)
      ElMessage.success('视频源连接测试成功')
      return response
    } catch (error: any) {
      console.error(`测试视频源(ID: ${id})连接失败`, error)
      ElMessage.error('视频源连接测试失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  return {
    videoSources,
    loading,
    currentVideoSource,
    fetchVideoSources,
    fetchVideoSource,
    createVideoSource,
    updateVideoSource,
    deleteVideoSource,
    testVideoSource
  }
}) 
import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def get_auth_token(username="admin", password="123456"):
    """获取认证token"""
    login_url = f"{API_BASE}/auth/login/json"
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data["access_token"]
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_api_with_auth(method, url, data=None):
    """使用认证测试API接口"""
    token = get_auth_token()
    if not token:
        print(f"无法获取token，跳过 {method} {url}")
        return None
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers)
        
        print(f"{method} {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code < 400:
            print("✅ 成功")
            success = True
        else:
            print("❌ 失败")
            success = False
            
        try:
            response_data = response.json()
            if success:
                print(f"响应: 成功获取数据")
            else:
                print(f"错误响应: {response_data}")
        except:
            print(f"响应: {response.text}")
        print("-" * 50)
        
        return response
    except Exception as e:
        print(f"请求失败: {e}")
        print("-" * 50)
        return None

def main():
    print("测试修复后的前端API调用格式...\n")
    
    # 测试数据
    test_schedule_data = {
        "name": "前端API修复测试",
        "description": "验证修复后的API调用",
        "templateId": 1,
        "date": "2025-01-26",
        "startTime": "14:00",
        "endTime": "15:00",
        "isEnabled": True
    }
    
    test_results = []
    
    # 1. 获取所有预设计划 (修复后: 无尾部斜杠)
    print("1. 测试获取所有预设计划:")
    response = test_api_with_auth("GET", f"{API_BASE}/preset-schedules")
    test_results.append(("获取所有计划", response and response.status_code == 200))
    
    # 2. 创建预设计划 (修复后: 无尾部斜杠)
    print("\n2. 测试创建预设计划:")
    response = test_api_with_auth("POST", f"{API_BASE}/preset-schedules", test_schedule_data)
    test_results.append(("创建计划", response and response.status_code == 200))
    
    if not response or response.status_code != 200:
        print("创建失败，无法继续测试")
        return
    
    new_schedule = response.json()
    test_id = new_schedule.get('id')
    print(f"创建成功，测试ID: {test_id}")
    
    # 3. 获取特定预设计划 (修复后: 无尾部斜杠)
    print(f"\n3. 测试获取特定预设计划 (ID: {test_id}):")
    response = test_api_with_auth("GET", f"{API_BASE}/preset-schedules/{test_id}")
    test_results.append(("获取特定计划", response and response.status_code == 200))
    
    # 4. 更新预设计划 (修复后: 无尾部斜杠)
    print(f"\n4. 测试更新预设计划 (ID: {test_id}):")
    update_data = {
        "name": "前端API修复测试 - 已更新",
        "description": "验证修复后的API调用 - 更新版本",
        "templateId": 1,
        "date": "2025-01-26",
        "startTime": "14:30",
        "endTime": "15:30",
        "isEnabled": True
    }
    response = test_api_with_auth("PUT", f"{API_BASE}/preset-schedules/{test_id}", update_data)
    test_results.append(("更新计划", response and response.status_code == 200))
    
    # 5. 启用/禁用预设计划 (修复后: 无尾部斜杠)
    print(f"\n5. 测试切换预设计划状态 (ID: {test_id}):")
    response = test_api_with_auth("PUT", f"{API_BASE}/preset-schedules/{test_id}/toggle", {"isEnabled": False})
    test_results.append(("切换状态", response and response.status_code == 200))
    
    # 6. 检查时间冲突
    print("\n6. 测试检查时间冲突:")
    conflict_data = {
        "date": "2025-01-26",
        "startTime": "14:00",
        "endTime": "15:00",
        "excludeId": str(test_id)
    }
    response = test_api_with_auth("POST", f"{API_BASE}/preset-schedules/check-conflict", conflict_data)
    test_results.append(("检查冲突", response and response.status_code == 200))
    
    # 7. 获取执行状态
    print(f"\n7. 测试获取执行状态 (ID: {test_id}):")
    response = test_api_with_auth("GET", f"{API_BASE}/preset-schedules/{test_id}/status")
    test_results.append(("获取状态", response and response.status_code == 200))
    
    # 8. 获取今日计划
    print("\n8. 测试获取今日计划:")
    today = "2025-01-26"  # 使用测试日期
    response = test_api_with_auth("GET", f"{API_BASE}/preset-schedules/by-date/{today}")
    test_results.append(("获取今日计划", response and response.status_code == 200))
    
    # 9. 获取日期范围计划 (修复后: 无尾部斜杠)
    print("\n9. 测试获取日期范围计划:")
    response = test_api_with_auth("GET", f"{API_BASE}/preset-schedules/by-date-range?start_date=2025-01-25&end_date=2025-01-27")
    test_results.append(("获取日期范围计划", response and response.status_code == 200))
    
    # 10. 删除预设计划 (修复后: 无尾部斜杠)
    print(f"\n10. 测试删除预设计划 (ID: {test_id}):")
    response = test_api_with_auth("DELETE", f"{API_BASE}/preset-schedules/{test_id}")
    test_results.append(("删除计划", response and response.status_code == 200))
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    success_count = 0
    total_count = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总计: {success_count}/{total_count} 个测试通过")
    
    if success_count == total_count:
        print("\n🎉 所有API测试通过！前端API调用格式修复成功！")
        print("\n修复内容:")
        print("- 移除了所有API URL的尾部斜杠")
        print("- 修复了FastAPI路由匹配问题")
        print("- 解决了前端404错误")
    else:
        print(f"\n⚠️  还有 {total_count - success_count} 个API需要进一步修复")
    
    print("\n修复的API端点:")
    print("- GET /preset-schedules (获取所有计划)")
    print("- GET /preset-schedules/{id} (获取特定计划)")
    print("- POST /preset-schedules (创建计划)")
    print("- PUT /preset-schedules/{id} (更新计划)")
    print("- DELETE /preset-schedules/{id} (删除计划)")
    print("- PUT /preset-schedules/{id}/toggle (切换状态)")
    print("- GET /preset-schedules/by-date-range (日期范围查询)")

if __name__ == "__main__":
    main()
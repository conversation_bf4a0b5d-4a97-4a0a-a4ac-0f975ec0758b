import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def get_auth_token(username="admin", password="123456"):
    """获取认证token"""
    login_url = f"{API_BASE}/auth/login/json"
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data["access_token"]
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_api_with_fresh_token(method, url, data=None):
    """使用新token测试API接口"""
    # 为每个请求获取新的token
    token = get_auth_token()
    if not token:
        print(f"无法获取token，跳过 {method} {url}")
        return None
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers)
        
        print(f"{method} {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code < 400:
            print("✅ 成功")
        else:
            print("❌ 失败")
            
        try:
            response_data = response.json()
            if len(str(response_data)) > 200:
                print(f"响应: {str(response_data)[:200]}...")
            else:
                print(f"响应: {response_data}")
        except:
            print(f"响应: {response.text[:200]}...")
        print("-" * 50)
        
        return response
    except Exception as e:
        print(f"请求失败: {e}")
        print("-" * 50)
        return None

def main():
    print("开始全面测试预设检测计划API...\n")
    
    results = {
        "成功": [],
        "失败": []
    }
    
    # 测试用例列表
    test_cases = [
        {
            "name": "获取所有预设检测计划",
            "method": "GET",
            "url": f"{API_BASE}/preset-schedules/"
        },
        {
            "name": "获取特定预设检测计划",
            "method": "GET",
            "url": f"{API_BASE}/preset-schedules/1"
        },
        {
            "name": "检查时间冲突",
            "method": "POST",
            "url": f"{API_BASE}/preset-schedules/check-conflict",
            "data": {
                "date": "2025-01-15",
                "startTime": "09:00",
                "endTime": "10:00",
                "excludeId": None
            }
        },
        {
            "name": "创建新的预设检测计划",
            "method": "POST",
            "url": f"{API_BASE}/preset-schedules/",
            "data": {
                "name": "API测试计划",
                "description": "通过API创建的测试计划",
                "templateId": 1,
                "date": "2025-01-20",
                "startTime": "10:00",
                "endTime": "11:00",
                "isEnabled": True
            }
        },
        {
            "name": "更新预设检测计划",
            "method": "PUT",
            "url": f"{API_BASE}/preset-schedules/1",
            "data": {
                "name": "更新后的测试计划",
                "description": "这是一个更新后的测试计划",
                "templateId": 1,
                "date": "2025-01-15",
                "startTime": "14:00",
                "endTime": "15:00",
                "isEnabled": True
            }
        },
        {
            "name": "切换启用状态",
            "method": "PUT",
            "url": f"{API_BASE}/preset-schedules/1/toggle",
            "data": {}
        },
        {
            "name": "获取执行状态",
            "method": "GET",
            "url": f"{API_BASE}/preset-schedules/1/status"
        },
        {
            "name": "按日期获取预设检测计划",
            "method": "GET",
            "url": f"{API_BASE}/preset-schedules/by-date/2025-01-15"
        },
        {
            "name": "执行预设检测计划",
            "method": "POST",
            "url": f"{API_BASE}/preset-schedules/1/execute",
            "data": {}
        },
        {
            "name": "停止预设检测计划",
            "method": "POST",
            "url": f"{API_BASE}/preset-schedules/1/stop",
            "data": {}
        }
    ]
    
    # 执行测试
    for i, test_case in enumerate(test_cases, 1):
        print(f"[{i}/{len(test_cases)}] 测试: {test_case['name']}")
        
        response = test_api_with_fresh_token(
            test_case['method'],
            test_case['url'],
            test_case.get('data')
        )
        
        if response and response.status_code < 400:
            results["成功"].append(test_case['name'])
        else:
            results["失败"].append(test_case['name'])
        
        # 在请求之间稍作延迟
        time.sleep(0.5)
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"总测试数: {len(test_cases)}")
    print(f"成功: {len(results['成功'])}")
    print(f"失败: {len(results['失败'])}")
    
    if results["成功"]:
        print("\n✅ 成功的API:")
        for api in results["成功"]:
            print(f"  - {api}")
    
    if results["失败"]:
        print("\n❌ 失败的API:")
        for api in results["失败"]:
            print(f"  - {api}")
    
    print("\n测试完成！")
    
    # 如果所有API都成功，说明问题已解决
    if len(results["失败"]) == 0:
        print("🎉 所有预设检测计划API都正常工作！")
    elif len(results["成功"]) > len(results["失败"]):
        print("✅ 大部分API正常工作，只有少数问题需要解决。")
    else:
        print("⚠️ 仍有较多API存在问题，需要进一步调试。")

if __name__ == "__main__":
    main()
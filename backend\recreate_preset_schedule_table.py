#!/usr/bin/env python3
"""
重新创建PresetSchedule表的数据库迁移脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.core.config import settings
from app.models.models import Base, PresetSchedule

def recreate_preset_schedule_table():
    """删除并重新创建PresetSchedule表"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.DATABASE_URL)
        
        # 删除现有表
        with engine.connect() as conn:
            conn.execute(text("DROP TABLE IF EXISTS preset_schedules"))
            conn.commit()
            print("已删除现有的preset_schedules表")
        
        # 重新创建PresetSchedule表
        PresetSchedule.__table__.create(engine)
        
        print("PresetSchedule表重新创建成功！")
        
    except Exception as e:
        print(f"重新创建PresetSchedule表失败: {str(e)}")
        raise

if __name__ == "__main__":
    recreate_preset_schedule_table()
<template>
  <el-dialog
    v-model="visible"
    title="系统日志管理"
    width="90%"
    :before-close="handleClose"
    class="log-manager-dialog"
  >
    <!-- 工具栏 -->
    <div class="log-toolbar">
      <div class="toolbar-left">
        <el-select v-model="filters.level" placeholder="日志级别" size="small" style="width: 120px" @change="loadLogs">
          <el-option label="全部级别" value="" />
          <el-option label="错误" value="error" />
          <el-option label="警告" value="warning" />
          <el-option label="信息" value="info" />
          <el-option label="调试" value="debug" />
        </el-select>
        
        <el-select v-model="filters.module" placeholder="模块" size="small" style="width: 150px; margin-left: 10px" @change="loadLogs">
          <el-option label="全部模块" value="" />
          <el-option v-for="module in modules" :key="module" :label="module" :value="module" />
        </el-select>
        
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          size="small"
          style="margin-left: 10px"
          @change="loadLogs"
        />
        
        <el-select v-model="logSource" placeholder="数据源" size="small" style="width: 120px; margin-left: 10px" @change="loadLogs">
          <el-option label="日志文件" value="file" />
          <el-option label="内存缓存" value="cache" />
          <el-option label="数据库" value="database" />
        </el-select>
      </div>
      
      <div class="toolbar-right">
        <el-button size="small" @click="loadLogs" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        
        <!-- 日志启停控制按钮 -->
        <el-button 
          size="small" 
          :type="logStatus.enabled ? 'danger' : 'success'"
          @click="toggleLogging"
          :loading="toggleLoading"
        >
          <el-icon v-if="logStatus.enabled"><VideoPause /></el-icon>
          <el-icon v-else><VideoPlay /></el-icon>
          {{ logStatus.enabled ? '暂停日志' : '启用日志' }}
        </el-button>
        
        <el-button size="small" @click="copyAllLogs" :loading="copyLoading">
          <el-icon><DocumentCopy /></el-icon>
          复制全部
        </el-button>
        <el-dropdown @command="handleExportCommand" size="small">
          <el-button size="small">
            <el-icon><Download /></el-icon>
            导出
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="txt">导出为TXT</el-dropdown-item>
              <el-dropdown-item command="json">导出为JSON</el-dropdown-item>
              <el-dropdown-item command="csv">导出为CSV</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button size="small" @click="syncToDatabase" :loading="syncLoading">
          <el-icon><Upload /></el-icon>
          同步到数据库
        </el-button>
        <el-button size="small" type="danger" @click="clearLogs">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
      </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="log-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <span class="stat-label">总日志数:</span>
            <span class="stat-value">{{ stats.total_logs || 0 }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <span class="stat-label">24小时内:</span>
            <span class="stat-value">{{ stats.recent_24h || 0 }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item error">
            <span class="stat-label">错误:</span>
            <span class="stat-value">{{ stats.level_distribution?.error || 0 }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item warning">
            <span class="stat-label">警告:</span>
            <span class="stat-value">{{ stats.level_distribution?.warning || 0 }}</span>
          </div>
        </el-col>
      </el-row>

      <!-- 日志文件信息 -->
      <el-row :gutter="20" style="margin-top: 15px;">
        <el-col :span="12">
          <div class="stat-item info">
            <span class="stat-label">日志文件:</span>
            <span class="stat-value">{{ logStatus.file_path || '未知' }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item info">
            <span class="stat-label">文件大小:</span>
            <span class="stat-value">{{ logStatus.file_size || '0 KB' }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item" :class="logStatus.enabled ? 'success' : 'danger'">
            <span class="stat-label">日志状态:</span>
            <span class="stat-value">{{ logStatus.enabled ? '已启用' : '已禁用' }}</span>
          </div>
        </el-col>
      </el-row>

      <!-- 最后修改时间 -->
      <el-row v-if="logStatus.last_modified" :gutter="20" style="margin-top: 15px;">
        <el-col :span="24">
          <div class="stat-item">
            <span class="stat-label">最后更新:</span>
            <span class="stat-value">{{ logStatus.last_modified }}</span>
          </div>
        </el-col>
      </el-row>

      <!-- 快捷操作提示 -->
      <div class="quick-tips">
        <el-text size="small" type="info">
          💡 提示：日志默认保存在后端服务器的logs目录中，可以通过上方控制按钮启用/禁用日志记录
        </el-text>
      </div>
    </div>
    
    <!-- 日志列表 -->
    <div class="log-content">
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      
      <div v-else-if="logs.length === 0" class="empty-container">
        <el-icon><Document /></el-icon>
        <span>暂无日志记录</span>
      </div>
      
      <div v-else class="log-list">
        <div
          v-for="log in logs"
          :key="log.id"
          class="log-item"
          :class="`level-${log.level}`"
        >
          <div class="log-header">
            <div class="log-level">
              <el-tag :type="getLevelTagType(log.level)" size="small">
                {{ log.level.toUpperCase() }}
              </el-tag>
            </div>
            <div class="log-time">{{ formatTime(log.timestamp) }}</div>
            <div class="log-module">{{ log.module }}</div>
            <div class="log-actions">
              <el-button
                size="small"
                text
                @click="copySingleLog(log)"
                title="复制此条日志"
              >
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </div>

          <div class="log-message">{{ log.message }}</div>

          <div v-if="log.filename" class="log-details">
            <span class="detail-item">{{ log.filename }}:{{ log.line_number }}</span>
            <span v-if="log.function_name" class="detail-item">{{ log.function_name }}()</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页 -->
    <div class="log-pagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[50, 100, 200, 500]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Download, Delete, Loading, Document, DocumentCopy, ArrowDown, Upload, VideoPlay, VideoPause } from '@element-plus/icons-vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const copyLoading = ref(false)
const syncLoading = ref(false)
const logs = ref<any[]>([])
const modules = ref<string[]>([])
const stats = ref<any>({})
const dateRange = ref<[Date, Date] | null>(null)
const logSource = ref('file') // 日志数据源
const logStatus = ref<any>({
  enabled: true,
  file_path: '',
  file_size: '0 KB',
  last_modified: ''
}) // 日志状态
const toggleLoading = ref(false) // 日志启停加载状态
const fileContent = ref('') // 日志文件内容

const filters = reactive({
  level: '',
  module: ''
})

const pagination = reactive({
  page: 1,
  size: 100,
  total: 0
})

// 方法
const loadLogs = async () => {
  loading.value = true
  try {
    if (logSource.value === 'file') {
      // 从日志文件加载
      const response = await fetch(`/api/admin/logs/file?lines=${pagination.size}`)
      const result = await response.json()
      
      if (result.content) {
        fileContent.value = result.content
        // 将文本日志解析为结构化数据
        logs.value = parseLogFile(result.content)
        pagination.total = result.lines
      } else {
        logs.value = []
        pagination.total = 0
      }
    } else {
      // 从数据库加载
      const params = new URLSearchParams({
        limit: pagination.size.toString(),
        offset: ((pagination.page - 1) * pagination.size).toString()
      })
      
      if (filters.level) params.append('level', filters.level)
      if (filters.module) params.append('module', filters.module)
      if (dateRange.value) {
        params.append('start_time', dateRange.value[0].toISOString())
        params.append('end_time', dateRange.value[1].toISOString())
      }
      
      const response = await fetch(`/api/admin/logs?${params}`)
      const result = await response.json()
      
      if (result.success) {
        logs.value = result.data.logs
        pagination.total = result.data.total
      } else {
        ElMessage.error('加载日志失败')
      }
    }
  } catch (error) {
    console.error('加载日志失败:', error)
    ElMessage.error('加载日志失败')
  } finally {
    loading.value = false
  }
}

// 解析日志文件内容为结构化数据
const parseLogFile = (content: string) => {
  if (!content) return []
  
  const lines = content.split('\n').filter(line => line.trim())
  const parsedLogs = []
  
  for (const line of lines) {
    try {
      // 尝试匹配日志格式: 2023-07-06 12:34:56,789 - module_name - LEVEL - message
      const match = line.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - ([^-]+) - ([^-]+) - (.+)$/)
      
      if (match) {
        const [, timestamp, module, level, message] = match
        parsedLogs.push({
          id: `file-${parsedLogs.length}`,
          timestamp,
          module: module.trim(),
          level: level.trim().toLowerCase(),
          message: message.trim(),
          filename: '',
          line_number: '',
          function_name: ''
        })
      } else {
        // 如果不匹配标准格式，作为上一条日志的延续
        if (parsedLogs.length > 0) {
          parsedLogs[parsedLogs.length - 1].message += '\n' + line
        } else {
          // 如果是第一行且不匹配，创建一个新日志
          parsedLogs.push({
            id: `file-0`,
            timestamp: new Date().toISOString(),
            module: 'unknown',
            level: 'info',
            message: line,
            filename: '',
            line_number: '',
            function_name: ''
          })
        }
      }
    } catch (error) {
      console.error('解析日志行失败:', error, line)
    }
  }
  
  return parsedLogs
}

const loadModules = async () => {
  try {
    const response = await fetch('/api/admin/logs/modules')
    const result = await response.json()
    
    if (result.success) {
      modules.value = result.data
    }
  } catch (error) {
    console.error('加载模块列表失败:', error)
  }
}

const loadStats = async () => {
  try {
    const response = await fetch('/api/admin/logs/stats')
    const result = await response.json()
    
    if (result.success) {
      stats.value = result.data
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const loadLogStatus = async () => {
  try {
    const response = await fetch('/api/admin/logs/status')
    const result = await response.json()
    
    logStatus.value = {
      enabled: result.enabled,
      file_path: result.file_path,
      file_size: result.file_size,
      last_modified: result.last_modified
    }
  } catch (error) {
    console.error('加载日志状态失败:', error)
  }
}

const toggleLogging = async () => {
  toggleLoading.value = true
  try {
    const response = await fetch(`/api/admin/logs/toggle?enable=${!logStatus.value.enabled}`, {
      method: 'POST'
    })
    const result = await response.json()
    if (result.success) {
      logStatus.value.enabled = !logStatus.value.enabled
      ElMessage.success(`日志已${logStatus.value.enabled ? '启用' : '禁用'}`)
    } else {
      ElMessage.error('切换日志状态失败')
    }
  } catch (error) {
    console.error('切换日志状态失败:', error)
    ElMessage.error('切换日志状态失败')
  } finally {
    toggleLoading.value = false
  }
}

// 复制单条日志
const copySingleLog = async (log: any) => {
  try {
    const logText = formatLogForCopy(log)
    await navigator.clipboard.writeText(logText)
    ElMessage.success('日志已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动选择文本复制')
  }
}

// 复制所有当前显示的日志
const copyAllLogs = async () => {
  if (logs.value.length === 0) {
    ElMessage.warning('没有日志可复制')
    return
  }

  copyLoading.value = true
  try {
    const allLogsText = logs.value.map(log => formatLogForCopy(log)).join('\n\n')
    await navigator.clipboard.writeText(allLogsText)
    ElMessage.success(`已复制 ${logs.value.length} 条日志到剪贴板`)
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动选择文本复制')
  } finally {
    copyLoading.value = false
  }
}

// 格式化日志用于复制
const formatLogForCopy = (log: any) => {
  let text = `[${log.timestamp}] [${log.level.toUpperCase()}] [${log.module}] ${log.message}`
  if (log.filename) {
    text += `\n位置: ${log.filename}:${log.line_number}`
    if (log.function_name) {
      text += ` ${log.function_name}()`
    }
  }
  return text
}

// 处理导出命令
const handleExportCommand = (command: string) => {
  exportLogs(command)
}

// 导出日志（支持多种格式）
const exportLogs = async (format: string = 'txt') => {
  try {
    const params = new URLSearchParams()
    if (filters.level) params.append('level', filters.level)
    if (filters.module) params.append('module', filters.module)
    if (dateRange.value) {
      params.append('start_time', dateRange.value[0].toISOString())
      params.append('end_time', dateRange.value[1].toISOString())
    }
    params.append('format', format)
    if (logSource.value) params.append('source', logSource.value) // 根据数据源导出

    const response = await fetch(`/api/admin/logs/export?${params}`)
    const result = await response.json()

    if (result.success) {
      if (format === 'csv') {
        // CSV格式需要特殊处理
        const csvContent = convertLogsToCSV(logs.value)
        downloadFile(csvContent, `system_logs_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`, 'text/csv')
      } else {
        // TXT和JSON格式
        downloadFile(result.data.content, result.data.filename, result.data.format)
      }

      ElMessage.success(`日志已导出为${format.toUpperCase()}格式`)
    } else {
      ElMessage.error('导出日志失败')
    }
  } catch (error) {
    console.error('导出日志失败:', error)
    ElMessage.error('导出日志失败')
  }
}

// 下载文件
const downloadFile = (content: string, filename: string, mimeType: string) => {
  const blob = new Blob([content], { type: mimeType })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  window.URL.revokeObjectURL(url)
}

// 转换日志为CSV格式
const convertLogsToCSV = (logs: any[]) => {
  const headers = ['时间戳', '级别', '模块', '消息', '文件名', '行号', '函数名']
  const csvRows = [headers.join(',')]

  logs.forEach(log => {
    const row = [
      `"${log.timestamp}"`,
      `"${log.level}"`,
      `"${log.module}"`,
      `"${log.message.replace(/"/g, '""')}"`,
      `"${log.filename || ''}"`,
      `"${log.line_number || ''}"`,
      `"${log.function_name || ''}"`
    ]
    csvRows.push(row.join(','))
  })

  return csvRows.join('\n')
}

// 同步日志到数据库
const syncToDatabase = async () => {
  syncLoading.value = true
  try {
    const response = await fetch('/api/admin/logs/sync-to-database', {
      method: 'POST'
    })
    const result = await response.json()

    if (result.success) {
      ElMessage.success(`已同步 ${result.synced_count} 条日志到数据库`)
      // 同步后重新加载统计信息
      await loadStats()
    } else {
      ElMessage.error('同步日志到数据库失败')
    }
  } catch (error) {
    console.error('同步日志失败:', error)
    ElMessage.error('同步日志到数据库失败')
  } finally {
    syncLoading.value = false
  }
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有日志吗？此操作不可撤销。', '确认清空', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await fetch('/api/admin/logs/clear', { method: 'POST' })
    const result = await response.json()
    
    if (result.success) {
      ElMessage.success('日志已清空')
      await loadLogs()
      await loadStats()
    } else {
      ElMessage.error('清空日志失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空日志失败:', error)
      ElMessage.error('清空日志失败')
    }
  }
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const getLevelTagType = (level: string) => {
  const typeMap: Record<string, string> = {
    error: 'danger',
    warning: 'warning',
    info: 'info',
    debug: 'info'
  }
  return typeMap[level] || 'info'
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadLogs()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadLogs()
}

const handleClose = () => {
  visible.value = false
}

// 监听对话框打开
watch(visible, (newVal) => {
  if (newVal) {
    loadLogs()
    loadModules()
    loadStats()
    loadLogStatus() // 监听对话框打开时加载日志状态
    
    // 设置自动刷新
    const interval = setInterval(() => {
      if (visible.value) {
        loadLogs()
        loadStats()
        loadLogStatus() // 每10秒刷新一次日志状态
      } else {
        clearInterval(interval)
      }
    }, 10000) // 每10秒刷新一次
  }
})
</script>

<style scoped>
.log-manager-dialog {
  --el-dialog-content-font-size: 14px;
}

.log-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}

.log-stats {
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  border-left: 4px solid var(--el-color-primary);
}

.stat-item.error {
  border-left-color: var(--el-color-danger);
}

.stat-item.warning {
  border-left-color: var(--el-color-warning);
}

.stat-item.success {
  border-left-color: var(--el-color-success);
}

.stat-item.info {
  border-left-color: var(--el-color-info);
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.quick-tips {
  margin-top: 15px;
  padding: 8px 12px;
  background-color: var(--el-color-info-light-9);
  border: 1px solid var(--el-color-info-light-7);
  border-radius: 4px;
  text-align: center;
}

.log-content {
  height: 500px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  background-color: var(--el-bg-color);
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--el-text-color-placeholder);
}

.loading-container .el-icon {
  font-size: 24px;
  margin-bottom: 10px;
  color: var(--el-color-primary);
}

.empty-container .el-icon {
  font-size: 48px;
  margin-bottom: 10px;
  color: var(--el-text-color-disabled);
}

.log-list {
  padding: 10px;
}

.log-item {
  margin-bottom: 10px;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid var(--el-border-color);
  background-color: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.2s;
}

.log-item:hover {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color);
}

/* 暗色模式和亮色模式兼容的日志级别样式 */
.log-item.level-error {
  border-left-color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
  border-color: var(--el-color-danger-light-8);
}

.log-item.level-warning {
  border-left-color: var(--el-color-warning);
  background-color: var(--el-color-warning-light-9);
  border-color: var(--el-color-warning-light-8);
}

.log-item.level-info {
  border-left-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-8);
}

.log-item.level-debug {
  border-left-color: var(--el-color-info);
  background-color: var(--el-color-info-light-9);
  border-color: var(--el-color-info-light-8);
}

.log-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 8px;
}

.log-level {
  flex-shrink: 0;
}

.log-actions {
  flex-shrink: 0;
  margin-left: auto;
  opacity: 0;
  transition: opacity 0.2s;
}

.log-item:hover .log-actions {
  opacity: 1;
}

.log-actions .el-button {
  padding: 4px;
  min-height: auto;
}

.log-time {
  flex-shrink: 0;
  font-size: 12px;
  color: var(--el-text-color-regular);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  background-color: var(--el-fill-color-lighter);
  padding: 2px 6px;
  border-radius: 3px;
}

.log-module {
  flex-shrink: 0;
  font-size: 12px;
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
}

.log-message {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: var(--el-text-color-primary);
  word-break: break-all;
  white-space: pre-wrap;
  background-color: var(--el-fill-color-extra-light);
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
}

.log-details {
  margin-top: 8px;
  font-size: 11px;
  color: var(--el-text-color-regular);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.detail-item {
  margin-right: 15px;
  background-color: var(--el-fill-color-light);
  padding: 1px 4px;
  border-radius: 2px;
  color: var(--el-text-color-secondary);
}

.log-pagination {
  margin-top: 20px;
  text-align: center;
  padding: 15px;
  background-color: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
}

/* 针对特定日志级别的文字颜色优化 */
.log-item.level-error .log-message {
  color: var(--el-color-danger-dark-2);
}

.log-item.level-warning .log-message {
  color: var(--el-color-warning-dark-2);
}

.log-item.level-info .log-message {
  color: var(--el-text-color-primary);
}

.log-item.level-debug .log-message {
  color: var(--el-text-color-regular);
}

/* 暗色模式下的特殊处理 */
@media (prefers-color-scheme: dark) {
  .log-item.level-error .log-message {
    color: var(--el-color-danger-light-3);
  }

  .log-item.level-warning .log-message {
    color: var(--el-color-warning-light-3);
  }

  .log-item.level-info .log-message {
    color: var(--el-text-color-primary);
  }

  .log-item.level-debug .log-message {
    color: var(--el-text-color-regular);
  }

  .log-time {
    background-color: var(--el-fill-color-dark);
  }

  .log-module {
    background-color: var(--el-color-primary-dark-2);
    color: var(--el-color-primary-light-3);
  }

  .log-message {
    background-color: var(--el-fill-color-darker);
    border-color: var(--el-border-color-dark);
  }
}
</style>

<template>
  <fieldset class="websdk-fieldset">
    <legend>视频播放器</legend>
    <div class="player-container">
      <!-- 视频播放区域 - 对应demo中的divPlugin -->
      <div 
        id="websdkPlayer" 
        ref="playerDiv"
        class="video-player"
        :style="{ width: playerWidth, height: playerHeight }"
      >
        <!-- WebSDK插件将在这里嵌入 -->
      </div>
      
      <!-- 播放器控制栏 -->
      <div class="player-controls">
        <div class="control-group">
          <label>窗口分割:</label>
          <select v-model="windowSplit" @change="changeWindowSplit" class="control-select">
            <option value="1">1x1</option>
            <option value="12">1x2</option>
            <option value="21">2x1</option>
            <option value="2">2x2</option>
            <option value="3">3x3</option>
            <option value="4">4x4</option>
          </select>
        </div>
        
        <div class="control-group">
          <button @click="toggleFullscreen" class="control-btn">
            {{ isFullscreen ? '退出全屏' : '全屏' }}
          </button>
        </div>
        
        <div class="control-group">
          <label>音量:</label>
          <input 
            v-model="volume" 
            type="range" 
            min="0" 
            max="100" 
            @change="setVolume"
            class="volume-slider"
          />
          <span>{{ volume }}</span>
        </div>
      </div>
    </div>
  </fieldset>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

// 声明jQuery，WebSDK依赖jQuery
declare const $: any

// 播放器状态
const playerDiv = ref<HTMLElement>()
const playerWidth = ref('100%')
const playerHeight = ref('400px')
const windowSplit = ref('1')
const volume = ref(50)
const isFullscreen = ref(false)

// WebSDK实例和全局变量
let webVideoCtrl: any = null
let isInitialized = ref(false)
let g_iWndIndex = ref(0) // 当前选中窗口索引，对应demo中的全局变量

// 错误码定义，对应demo.js中的ErrorCodes
const ErrorCodes: Record<number, string> = {
  1001: "码流传输过程异常",
  1002: "回放结束",
  1003: "取流失败，连接被动断开",
  1004: "对讲连接被动断开",
  1005: "广播连接被动断开",
  1006: "视频编码格式不支持 目前只支持h264 和 h265",
  1007: "网络异常导致websocket断开",
  1008: "首帧回调超时",
  1009: "对讲码流传输过程异常",
  1010: "广播码流传输过程异常",
  1011: "数据接收异常，请检查是否修改了视频格式",
  1012: "播放资源不足",
  1013: "当前环境不支持该鱼眼展开模式",
  1014: "外部强制关闭了",
  1015: "获取播放url失败",
  1016: "文件下载完成",
  1017: "密码错误",
  1018: "链接到萤石平台失败",
  1019: "未找到录像片段",
  1020: "水印模式等场景，当前通道需要重新播放",
  1021: "缓存溢出",
  1022: "采集音频失败，可能是在非https/localhost域下使用对讲导致,或者没有插耳机等"
}

// 事件定义
const emit = defineEmits<{
  'websdk-initialized': []
  'websdk-error': [error: string]
  'window-selected': [index: number]
  'plugin-error': [windowIndex: number, errorCode: number, error: any]
  'websdk-callback': [event: string, type: string, data?: any]
}>()

// 初始化WebSDK - 完全基于demo.js的初始化逻辑
const initWebSDK = async () => {
  try {
    // 检查WebSDK是否已加载
    if (!(window as any).WebVideoCtrl) {
      throw new Error('WebSDK未加载，请检查脚本引用')
    }

    webVideoCtrl = (window as any).WebVideoCtrl

    // 检查浏览器支持 - 对应demo.js第33行
    const iRet = webVideoCtrl.I_SupportNoPlugin()
    if (!iRet) {
      throw new Error('当前浏览器版本过低，不支持无插件，请升级后再试！')
    }

    console.log('浏览器支持检查通过')

    // 初始化插件参数及插入无插件 - 对应demo.js第40行
    webVideoCtrl.I_InitPlugin("100%", "100%", {
      bWndFull: true,     // 是否支持单窗口双击全屏，默认支持 true:支持 false:不支持
      iPackageType: 2,    // 2:PS 11:MP4
      iWndowType: 1,
      bNoPlugin: true,

      // 窗口选择回调 - 对应demo.js第46行
      cbSelWnd: (xmlDoc: any) => {
        console.log('cbSelWnd回调被触发，xmlDoc:', xmlDoc)
        try {
          // 使用jQuery方式解析，对应demo.js第47行
          const windowText = $(xmlDoc).find("SelectWnd").eq(0).text()
          console.log('SelectWnd文本内容:', windowText)
          g_iWndIndex.value = parseInt(windowText, 10)
          console.log('当前选择的窗口编号：', g_iWndIndex.value)
          emit('window-selected', g_iWndIndex.value)
          emit('websdk-callback', `当前选择的窗口编号：${g_iWndIndex.value}`, 'window', { windowIndex: g_iWndIndex.value })
        } catch (error) {
          console.error('解析窗口选择XML失败:', error)
          emit('websdk-callback', '解析窗口选择XML失败', 'error', { error })
        }
      },

      // 双击窗口回调 - 对应demo.js第51行
      cbDoubleClickWnd: (iWndIndex: number, bFullScreen: boolean) => {
        const szInfo = bFullScreen
          ? `当前放大的窗口编号：${iWndIndex}`
          : `当前还原的窗口编号：${iWndIndex}`
        console.log(szInfo)
        emit('websdk-callback', szInfo, 'window', { windowIndex: iWndIndex, fullScreen: bFullScreen })
      },

      // 事件回调 - 对应demo.js第58行
      cbEvent: (iEventType: number, iParam1: number, iParam2: number) => {
        let eventMsg = ''
        if (2 == iEventType) { // 回放正常结束
          eventMsg = `窗口${iParam1}回放结束！`
          console.log(eventMsg)
          emit('websdk-callback', eventMsg, 'playback', { eventType: iEventType, param1: iParam1, param2: iParam2 })
        } else if (-1 == iEventType) {
          eventMsg = `设备${iParam1}网络错误！`
          console.log(eventMsg)
          emit('websdk-callback', eventMsg, 'error', { eventType: iEventType, param1: iParam1, param2: iParam2 })
        } else if (3001 == iEventType) {
          eventMsg = `录像结束事件：窗口${iParam1}`
          console.log(eventMsg)
          emit('websdk-callback', eventMsg, 'preview', { eventType: iEventType, param1: iParam1, param2: iParam2 })
        } else {
          eventMsg = `事件回调：类型${iEventType}，参数1：${iParam1}，参数2：${iParam2}`
          console.log(eventMsg)
          emit('websdk-callback', eventMsg, 'other', { eventType: iEventType, param1: iParam1, param2: iParam2 })
        }
      },

      // 远程配置回调 - 对应demo.js第67行
      cbRemoteConfig: () => {
        console.log('关闭远程配置库！')
      },

      // 插件初始化完成回调 - 对应demo.js第70行
      cbInitPluginComplete: () => {
        console.log('WebSDK插件初始化完成')
        isInitialized.value = true
        emit('websdk-callback', 'WebSDK插件初始化完成', 'plugin')

        // 嵌入插件到指定容器
        nextTick(() => {
          if (playerDiv.value) {
            webVideoCtrl.I_InsertOBJECTPlugin('websdkPlayer')
            console.log('WebSDK插件嵌入完成')
            emit('websdk-callback', 'WebSDK插件嵌入完成', 'plugin')
            emit('websdk-initialized')
          }
        })
      },

      // 插件错误处理回调 - 对应demo.js第73行
      cbPluginErrorHandler: (iWndIndex: number, iErrorCode: number, oError: any) => {
        const errorMsg = ErrorCodes[iErrorCode] || `未知错误(${iErrorCode})`
        console.error(`窗口${iWndIndex}：${errorMsg}`, oError)
        emit('plugin-error', iWndIndex, iErrorCode, oError)

        // 获取窗口状态并停止播放 - 对应demo.js第75行
        const oWndInfo = webVideoCtrl.I_GetWindowStatus(iWndIndex)
        if (oWndInfo != null) {
          webVideoCtrl.I_Stop({
            success: () => {
              console.log(`窗口${iWndIndex}停止预览成功`)
            },
            error: () => {
              console.log(`窗口${iWndIndex}停止预览失败`)
            }
          })
        }
      },

      // 性能不足回调 - 对应demo.js第91行
      cbPerformanceLack: () => {
        console.log('性能不足！')
      },

      // 码流加密秘钥错误回调 - 对应demo.js第94行
      cbSecretKeyError: (iWndIndex: number) => {
        console.error(`窗口${iWndIndex}：码流加密秘钥错误！`)

        const oWndInfo = webVideoCtrl.I_GetWindowStatus(iWndIndex)
        if (oWndInfo != null) {
          webVideoCtrl.I_Stop({
            success: () => {
              console.log(`窗口${iWndIndex}停止预览成功`)
            },
            error: () => {
              console.log(`窗口${iWndIndex}停止预览失败`)
            }
          })
        }
      }
    })

  } catch (error) {
    console.error('WebSDK初始化失败:', error)
    emit('websdk-error', error instanceof Error ? error.message : String(error))
    throw error
  }
}

// 改变窗口分割数 - 对应demo.js第265行
const changeWindowSplit = () => {
  if (!webVideoCtrl || !isInitialized.value) return

  try {
    const iType = parseInt(windowSplit.value, 10)
    webVideoCtrl.I_ChangeWndNum(iType)
    console.log('窗口分割数已改变:', windowSplit.value)
  } catch (error) {
    console.error('改变窗口分割数失败:', error)
  }
}

// 设置音量 - 基于WebSDK API
const setVolume = () => {
  if (!webVideoCtrl || !isInitialized.value) return

  try {
    // 设置当前窗口的音量
    webVideoCtrl.I_SetVolume(g_iWndIndex.value, parseInt(volume.value))
    console.log('音量已设置:', volume.value)
  } catch (error) {
    console.error('设置音量失败:', error)
  }
}

// 全屏切换
const toggleFullscreen = () => {
  if (!webVideoCtrl || !isInitialized.value) return
  
  try {
    if (isFullscreen.value) {
      // 退出全屏
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
    } else {
      // 进入全屏
      if (playerDiv.value?.requestFullscreen) {
        playerDiv.value.requestFullscreen()
      }
    }
  } catch (error) {
    console.error('全屏切换失败:', error)
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 暴露给父组件的方法 - 开始预览，对应demo.js第528行
const startPreview = (device: any, channel: any, options: any = {}) => {
  if (!webVideoCtrl || !isInitialized.value) {
    console.error('WebSDK未初始化')
    return Promise.reject('WebSDK未初始化')
  }

  if (!device || !channel) {
    console.error('设备或通道信息不完整')
    return Promise.reject('设备或通道信息不完整')
  }

  return new Promise((resolve, reject) => {
    console.log('开始预览:', { device, channel, options })

    const szDeviceIdentify = device.id
    const iChannelID = parseInt(channel.channelNo, 10)
    const iStreamType = parseInt(options.streamType || '1', 10) // 默认主码流
    const bZeroChannel = channel.type === 'zero' // 是否为零通道
    const bProxy = options.useProxy || false // 是否使用代理
    const iRtspPort = parseInt(device.rtspPort || '554', 10)

    // 检查当前窗口状态
    const oWndInfo = webVideoCtrl.I_GetWindowStatus(g_iWndIndex.value)

    const startRealPlay = () => {
      webVideoCtrl.I_StartRealPlay(szDeviceIdentify, {
        iRtspPort: iRtspPort,
        iStreamType: iStreamType,
        iChannelID: iChannelID,
        bZeroChannel: bZeroChannel,
        bProxy: bProxy,
        success: () => {
          console.log('开始预览成功！')
          resolve('预览成功')
        },
        error: (status: number, xmlDoc: any) => {
          let errorMsg = '开始预览失败！'
          if (403 === status) {
            errorMsg = '设备不支持Websocket取流！'
          }
          console.error(errorMsg, { status, xmlDoc })
          reject(errorMsg)
        }
      })
    }

    // 如果当前窗口已经在播放，先停止
    if (oWndInfo != null) {
      webVideoCtrl.I_Stop({
        success: () => {
          startRealPlay()
        },
        error: () => {
          reject('停止当前预览失败')
        }
      })
    } else {
      startRealPlay()
    }
  })
}

// 停止预览 - 对应demo.js第608行
const stopPreview = () => {
  if (!webVideoCtrl || !isInitialized.value) return Promise.reject('WebSDK未初始化')

  return new Promise((resolve, reject) => {
    const oWndInfo = webVideoCtrl.I_GetWindowStatus(g_iWndIndex.value)

    if (oWndInfo != null) {
      webVideoCtrl.I_Stop({
        success: () => {
          console.log('停止预览成功！')
          resolve('停止预览成功')
        },
        error: () => {
          console.error('停止预览失败！')
          reject('停止预览失败')
        }
      })
    } else {
      resolve('当前窗口未在播放')
    }
  })
}

const startPlayback = (device: any, channel: any, options: any = {}) => {
  if (!webVideoCtrl || !isInitialized.value) return
  
  console.log('开始回放:', { device, channel, options })
  // 这里将实现回放逻辑
}

const stopPlayback = () => {
  if (!webVideoCtrl || !isInitialized.value) return
  
  console.log('停止回放')
  // 这里将实现停止回放逻辑
}

const capture = (format: string = '.jpg') => {
  if (!webVideoCtrl || !isInitialized.value) return
  
  console.log('抓图:', format)
  // 这里将实现抓图逻辑
}

const startRecord = () => {
  if (!webVideoCtrl || !isInitialized.value) return
  
  console.log('开始录像')
  // 这里将实现录像逻辑
}

const stopRecord = () => {
  if (!webVideoCtrl || !isInitialized.value) return
  
  console.log('停止录像')
  // 这里将实现停止录像逻辑
}

const pause = () => {
  if (!webVideoCtrl || !isInitialized.value) return
  
  console.log('暂停')
  // 这里将实现暂停逻辑
}

const resume = () => {
  if (!webVideoCtrl || !isInitialized.value) return
  
  console.log('恢复')
  // 这里将实现恢复逻辑
}

const playSlow = () => {
  if (!webVideoCtrl || !isInitialized.value) return
  
  console.log('慢放')
  // 这里将实现慢放逻辑
}

const playFast = () => {
  if (!webVideoCtrl || !isInitialized.value) return

  console.log('快放')
  // 这里将实现快放逻辑
}

// 窗口分割功能 - 对应demo.js第265行
const changeWndNum = (iType: number) => {
  if (!webVideoCtrl) {
    console.error('WebSDK未初始化')
    return
  }

  try {
    webVideoCtrl.I_ChangeWndNum(iType)
    console.log(`窗口分割已切换为: ${iType}`)
    emit('websdk-callback', `窗口分割已切换为: ${iType}`, 'window', { wndNum: iType })
  } catch (error) {
    console.error('切换窗口分割失败:', error)
    emit('websdk-callback', '切换窗口分割失败', 'error', { error })
  }
}

// 获取当前选中的窗口索引
const getCurrentWindowIndex = () => {
  return g_iWndIndex.value
}

// 暴露方法给父组件
defineExpose({
  startPreview,
  stopPreview,
  startPlayback,
  stopPlayback,
  capture,
  startRecord,
  stopRecord,
  pause,
  resume,
  playSlow,
  playFast,
  changeWndNum,
  getCurrentWindowIndex
})

onMounted(async () => {
  // 直接初始化WebSDK，不拦截ISAPI请求
  // WebSDK在无插件模式下会直接与设备通信，不需要代理
  try {
    await initWebSDK()
  } catch (error) {
    console.error('WebSDK初始化失败:', error)
  }

  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  // 清理WebSDK
  if (webVideoCtrl && isInitialized.value) {
    try {
      webVideoCtrl.I_DestroyPlugin()
    } catch (error) {
      console.error('销毁WebSDK失败:', error)
    }
  }
  
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style scoped>
.websdk-fieldset {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px;
  margin: 0;
  background: white;
}

.websdk-fieldset legend {
  padding: 0 8px;
  font-weight: bold;
  color: #333;
}

.player-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.video-player {
  border: 1px solid #ccc;
  background: #000;
  min-height: 400px;
  position: relative;
}

.player-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.control-btn {
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.control-btn:hover {
  background: #0056b3;
}

.volume-slider {
  width: 100px;
}
</style>

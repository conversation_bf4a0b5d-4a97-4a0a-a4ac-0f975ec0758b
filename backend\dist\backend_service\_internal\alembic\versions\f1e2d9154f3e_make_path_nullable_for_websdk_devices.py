"""make_path_nullable_for_websdk_devices

Revision ID: f1e2d9154f3e
Revises: 95da47b06815
Create Date: 2025-07-06 07:02:11.369265

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f1e2d9154f3e'
down_revision = '95da47b06815'
branch_labels = None
depends_on = None


def upgrade():
    # 简单方法：由于API已经修复，我们只需要确保现有的WebSDK设备有有效的path值
    # 对于WebSDK设备类型，将NULL或空的path设置为空字符串
    op.execute("""
        UPDATE video_sources
        SET path = ''
        WHERE source_type = 'websdk_device' AND (path IS NULL OR path = '')
    """)


def downgrade():
    # 回滚：将WebSDK设备的path设置为'N/A'
    op.execute("""
        UPDATE video_sources
        SET path = 'N/A'
        WHERE source_type = 'websdk_device' AND path = ''
    """)
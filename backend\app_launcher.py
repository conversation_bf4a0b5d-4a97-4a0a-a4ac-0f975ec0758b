#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
压铸检测系统后端启动器
"""

import os
import sys
import uvicorn
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置工作目录
os.chdir(current_dir)

if __name__ == "__main__":
    # 启动FastAPI应用
    uvicorn.run(
        "app.main:app",
        host="127.0.0.1",
        port=8000,
        reload=False,
        log_level="info"
    )

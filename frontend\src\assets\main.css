@import './base.css';

#app {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-weight: normal;
  color: var(--text-color);
  background-color: var(--bg-color);
}

/* 主题切换过渡动画 */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 常用布局类 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.flex {
  display: flex;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

a {
  text-decoration: none;
  color: #409eff;
  transition: 0.4s;
}

@media (hover: hover) {
  a:hover {
    color: #66b1ff;
  }
}

/* 移除了限制布局的媒体查询 */

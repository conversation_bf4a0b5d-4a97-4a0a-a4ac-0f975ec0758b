#!/usr/bin/env python3
"""
数据库迁移脚本：为ROI配置表添加颜色字段
"""

import sqlite3
import os
import sys

def add_color_field_to_roi_configs():
    """为roi_configs表添加color字段"""
    
    # 数据库文件路径
    db_path = "app.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查color字段是否已存在
        cursor.execute("PRAGMA table_info(roi_configs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'color' in columns:
            print("✅ color字段已存在，无需添加")
            conn.close()
            return True
        
        print("🔧 开始添加color字段...")
        
        # 添加color字段
        cursor.execute("""
            ALTER TABLE roi_configs 
            ADD COLUMN color VARCHAR(20) DEFAULT '#ff0000'
        """)
        
        # 为现有记录设置默认颜色
        cursor.execute("""
            UPDATE roi_configs 
            SET color = CASE 
                WHEN attribute = 'yazhu' THEN '#ff0000'  -- 压铸机：红色
                WHEN attribute = 'pailiao' THEN '#00ff00'  -- 排料口：绿色
                ELSE '#ff0000'  -- 默认：红色
            END
            WHERE color IS NULL OR color = ''
        """)
        
        # 提交更改
        conn.commit()
        
        # 验证字段添加成功
        cursor.execute("PRAGMA table_info(roi_configs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'color' in columns:
            print("✅ color字段添加成功")
            
            # 显示更新的记录数
            cursor.execute("SELECT COUNT(*) FROM roi_configs WHERE color IS NOT NULL")
            count = cursor.fetchone()[0]
            print(f"✅ 已为 {count} 条记录设置默认颜色")
            
            # 显示示例记录
            cursor.execute("SELECT roi_id, name, attribute, color FROM roi_configs LIMIT 5")
            records = cursor.fetchall()
            if records:
                print("\n📋 示例记录:")
                for record in records:
                    roi_id, name, attribute, color = record
                    print(f"  - {roi_id}: {name} ({attribute}) -> {color}")
            
            conn.close()
            return True
        else:
            print("❌ color字段添加失败")
            conn.close()
            return False
            
    except sqlite3.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        if conn:
            conn.close()
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        if conn:
            conn.close()
        return False

def verify_migration():
    """验证迁移结果"""
    db_path = "app.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(roi_configs)")
        columns = cursor.fetchall()
        
        print("\n📊 当前roi_configs表结构:")
        for column in columns:
            cid, name, type_, notnull, default_value, pk = column
            print(f"  {name}: {type_} (默认值: {default_value})")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM roi_configs")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM roi_configs WHERE color IS NOT NULL")
        color_count = cursor.fetchone()[0]
        
        print(f"\n📈 数据统计:")
        print(f"  总记录数: {total_count}")
        print(f"  有颜色的记录数: {color_count}")
        
        if total_count > 0:
            cursor.execute("""
                SELECT attribute, color, COUNT(*) as count 
                FROM roi_configs 
                GROUP BY attribute, color
            """)
            color_stats = cursor.fetchall()
            
            print(f"\n🎨 颜色分布:")
            for attribute, color, count in color_stats:
                print(f"  {attribute}: {color} ({count}条)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始ROI配置表颜色字段迁移")
    print("=" * 50)
    
    # 执行迁移
    success = add_color_field_to_roi_configs()
    
    if success:
        print("\n🔍 验证迁移结果...")
        verify_migration()
        print("\n🎉 迁移完成！")
        print("\n📝 后续步骤:")
        print("1. 重启后端服务器")
        print("2. 测试ROI颜色编辑功能")
        print("3. 验证颜色数据持久化")
    else:
        print("\n❌ 迁移失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()

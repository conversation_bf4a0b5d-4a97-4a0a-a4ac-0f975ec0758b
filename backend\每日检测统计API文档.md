# 每日检测统计API文档

## API概述

新增的每日检测统计API提供了从卡料检测结果中获取每日检测统计信息的功能，支持按检测模板、压铸机和检测组进行层级统计。

## API端点

### 获取每日检测统计信息

**接口**: `GET /api/card-detection/daily-statistics`

**功能**: 获取指定日期的检测统计信息，包括：
- 每个检测模板的检测情况（检测次数，卡料次数）
- 每个检测模板下面，每台压铸机的检测情况（检测次数、卡料次数）
- 每个压铸机下面的检测组的检测情况（检测次数，卡料次数）

**参数**:
- `date` (可选): 日期，格式为 YYYY-MM-DD，默认为今天

**请求示例**:
```bash
# 获取今天的统计数据
GET /api/card-detection/daily-statistics

# 获取指定日期的统计数据
GET /api/card-detection/daily-statistics?date=2024-01-15
```

**响应数据结构**:
```json
{
  "date": "2024-01-15",
  "templates": [
    {
      "template_id": 1,
      "template_name": "模板A",
      "total_detections": 150,
      "jam_detections": 5,
      "success_rate": 0.967,
      "die_casters": [
        {
          "die_caster_id": 1,
          "die_caster_name": "压铸机001",
          "total_detections": 80,
          "jam_detections": 3,
          "success_rate": 0.9625,
          "detection_groups": [
            {
              "group_id": 1,
              "group_name": "检测组001",
              "total_detections": 80,
              "jam_detections": 3,
              "success_rate": 0.9625
            }
          ]
        },
        {
          "die_caster_id": 2,
          "die_caster_name": "压铸机002",
          "total_detections": 70,
          "jam_detections": 2,
          "success_rate": 0.971,
          "detection_groups": [
            {
              "group_id": 2,
              "group_name": "检测组002",
              "total_detections": 70,
              "jam_detections": 2,
              "success_rate": 0.971
            }
          ]
        }
      ]
    }
  ]
}
```

## 数据模型说明

### DailyDetectionStatistics
每日检测统计信息的根模型
- `date`: 日期 (YYYY-MM-DD)
- `templates`: 检测模板统计列表

### TemplateStatistics
检测模板统计信息模型
- `template_id`: 模板ID
- `template_name`: 模板名称
- `total_detections`: 总检测次数
- `jam_detections`: 卡料次数
- `success_rate`: 成功率 (0-1)
- `die_casters`: 压铸机统计列表

### DieCasterStatistics
压铸机统计信息模型
- `die_caster_id`: 压铸机ID
- `die_caster_name`: 压铸机名称
- `total_detections`: 总检测次数
- `jam_detections`: 卡料次数
- `success_rate`: 成功率 (0-1)
- `detection_groups`: 检测组统计列表

### 检测组统计信息
每个检测组的统计信息包含：
- `group_id`: 检测组ID
- `group_name`: 检测组名称
- `total_detections`: 总检测次数
- `jam_detections`: 卡料次数
- `success_rate`: 成功率 (0-1)

## 使用场景

1. **日报生成**: 获取每日的检测统计报告
2. **性能分析**: 分析不同模板、压铸机的检测性能
3. **故障排查**: 识别卡料频率较高的设备或检测组
4. **数据看板**: 为管理看板提供统计数据

## 错误处理

- **400 Bad Request**: 日期格式错误
- **500 Internal Server Error**: 服务器内部错误

## 注意事项

1. 只统计有模板关联的检测组数据
2. 成功率计算公式：(总检测次数 - 卡料次数) / 总检测次数
3. 如果某个检测组当天没有检测数据，不会出现在统计结果中
4. 时间范围为指定日期的00:00:00到23:59:59

## 测试建议

1. 确保数据库中有检测结果数据
2. 确保检测组已关联到检测模板
3. 确保压铸机信息完整
4. 测试不同日期的数据获取
5. 测试无数据日期的响应
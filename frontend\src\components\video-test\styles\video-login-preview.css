/* 登录预览测试页面样式 */
.login-preview-test {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0;
  margin: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
}

/* 配置区域 */
.config-section {
  flex-shrink: 0;
}

/* 预览区域 */
.preview-section {
  flex: 1;
  min-height: 0;
}

/* 卡片样式 */
.config-card,
.video-card,
.info-card,
.log-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(var(--text-color-rgb, 0, 0, 0), 0.1);
  transition: box-shadow 0.3s ease;
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
}

.config-card:hover,
.video-card:hover,
.info-card:hover,
.log-card:hover {
  box-shadow: 0 4px 16px rgba(var(--text-color-rgb, 0, 0, 0), 0.15);
  border-color: var(--primary-color-light);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: var(--text-color);
  padding: 0;
  margin: 0;
}

/* 视频控制按钮组 */
.video-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.video-controls .el-button {
  min-width: 80px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.video-controls .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.3);
}

/* 视频容器 */
.video-container {
  border: 2px solid var(--border-color);
  border-radius: 6px;
  overflow: hidden;
  background: linear-gradient(135deg, var(--bg-color-mute) 0%, var(--bg-color-soft) 100%);
  position: relative;
  transition: border-color 0.3s ease;
  /* 确保容器能够正确应用aspect-ratio */
  contain: layout;
}

.video-container:hover {
  border-color: var(--primary-color);
}

/* 视频插件 */
.video-plugin {
  background-color: var(--bg-color-mute);
  width: 100%;
  height: 100%;
  position: relative;
  /* 确保插件内容适配容器比例 */
  object-fit: contain;
}

/* WebSDK插件容器适配 */
.video-plugin object,
.video-plugin embed,
.video-plugin canvas,
.video-plugin video {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain;
}

/* 视频占位符 */
.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-color-mute);
  text-align: center;
  background: radial-gradient(circle at center, var(--bg-color-soft) 0%, var(--bg-color-mute) 70%);
}

.video-placeholder .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--text-color-mute);
  opacity: 0.6;
  animation: pulse 2s infinite;
}

.video-placeholder p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.5;
  opacity: 0.8;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
}

/* 状态信息区域 */
.status-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background-color: var(--bg-color-soft);
  border-radius: 6px;
  margin-bottom: 16px;
  border: 1px solid var(--border-color);
}

.status-info h4 {
  margin: 0 0 12px 0;
  color: var(--text-color);
  font-size: 14px;
  font-weight: 600;
}

.status-info p {
  margin: 6px 0;
  font-size: 13px;
  color: var(--text-color-soft);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-item,
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: background-color 0.2s ease;
}

.status-item:hover,
.info-item:hover {
  background-color: var(--el-fill-color-lighter);
  margin: 0 -12px;
  padding: 10px 12px;
  border-radius: 4px;
}

.status-item:last-child,
.info-item:last-child {
  border-bottom: none;
}

.status-item .label,
.info-item .label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  font-size: 13px;
}

/* 设备信息区域 */
.device-info {
  margin-top: 20px;
  padding: 16px;
  background-color: var(--bg-color-soft);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.device-info p {
  margin: 6px 0;
  font-size: 13px;
  color: var(--text-color-soft);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-info h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-info h4::before {
  content: '';
  width: 4px;
  height: 16px;
  background: var(--el-color-primary);
  border-radius: 2px;
}

/* 日志容器 */
.log-container {
  height: 200px;
  overflow-y: auto;
  background-color: var(--bg-color-mute);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-container::-webkit-scrollbar {
  width: 6px;
}

.log-container::-webkit-scrollbar-track {
  background: var(--el-fill-color-light);
  border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb {
  background: var(--el-border-color-dark);
  border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-darker);
}

/* 日志项 */
.log-item {
  margin-bottom: 4px;
  padding: 2px 0;
  word-break: break-all;
  color: var(--text-color-soft);
}

.log-item:last-child {
  margin-bottom: 0;
}

.log-item.error {
  color: var(--error-color);
}

.log-item.success {
  color: var(--success-color);
}

.log-item.warning {
  color: var(--warning-color);
}

/* 无日志提示 */
.no-logs {
  text-align: center;
  color: var(--el-text-color-placeholder);
  padding: 40px 20px;
  font-size: 14px;
  font-style: italic;
}

/* 表单样式增强 */
.el-form-item {
  margin-bottom: 18px;
}

.el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.el-input__wrapper {
  transition: all 0.3s ease;
}

.el-input__wrapper:hover {
  box-shadow: 0 0 0 1px var(--el-color-primary-light-7);
}

/* 按钮样式增强 */
.el-button {
  transition: all 0.3s ease;
  font-weight: 500;
}

.el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.el-button:active {
  transform: translateY(0);
}

/* 标签样式增强 */
.el-tag {
  font-weight: 500;
  border-radius: 12px;
  padding: 0 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .preview-section .el-col {
    margin-bottom: 20px;
  }
  
  .video-controls {
    justify-content: center;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  /* 中等屏幕下的视频容器调整 */
  .video-container {
    min-height: 250px;
    max-height: 500px;
  }
}

@media (max-width: 768px) {
  .login-preview-test {
    gap: 16px;
  }
  
  .preview-section .el-row {
    flex-direction: column;
  }
  
  .preview-section .el-col {
    margin-bottom: 16px;
  }
  
  .video-controls {
    justify-content: center;
    gap: 6px;
  }
  
  .video-controls .el-button {
    min-width: 70px;
    font-size: 11px;
  }
  
  .status-info,
  .device-info {
    padding: 12px;
  }
  
  .log-container {
    height: 150px;
  }
  
  .status-item,
  .info-item {
    padding: 8px 0;
  }
  
  /* 平板设备下的视频容器调整 */
  .video-container {
    min-height: 200px;
    max-height: 400px;
  }
}

@media (max-width: 480px) {
  .login-preview-test {
    gap: 12px;
  }
  
  .config-section .el-form-item {
    margin-bottom: 16px;
  }
  
  .video-controls {
    gap: 6px;
  }
  
  .video-controls .el-button {
    min-width: 60px;
    font-size: 10px;
    padding: 6px 8px;
  }
  
  .status-info,
  .device-info {
    padding: 10px;
  }
  
  .log-container {
    height: 120px;
    font-size: 11px;
  }
  
  /* 手机设备下的视频容器调整 */
  .video-container {
    min-height: 180px;
    max-height: 320px;
    /* 在小屏幕上可能需要稍微调整比例 */
    aspect-ratio: 16/9;
  }
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 平滑过渡效果 */
.login-preview-test * {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 加载动画 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--el-color-primary-light-8);
  border-top: 3px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
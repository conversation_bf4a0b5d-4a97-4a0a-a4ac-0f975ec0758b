import { get, post, put, del } from '@/utils/api'
import type { VideoSource } from '@/types'

/**
 * 获取所有视频源
 */
export const getVideoSources = (): Promise<VideoSource[]> => {
  return get<VideoSource[]>('/video-sources/')
}

/**
 * 获取单个视频源
 */
export const getVideoSource = (id: number): Promise<VideoSource> => {
  return get<VideoSource>(`/video-sources/${id}/`)
}

/**
 * 创建视频源
 */
export const createVideoSource = (data: Partial<VideoSource>): Promise<VideoSource> => {
  return post<VideoSource>('/video-sources/', data)
}

/**
 * 更新视频源
 */
export const updateVideoSource = (id: number, data: Partial<VideoSource>): Promise<VideoSource> => {
  return put<VideoSource>(`/video-sources/${id}/`, data)
}

/**
 * 删除视频源
 */
export const deleteVideoSource = (id: number): Promise<VideoSource> => {
  return del<VideoSource>(`/video-sources/${id}/`)
}

/**
 * 测试视频源连接
 */
export const testVideoSource = (id: number): Promise<{success: boolean, message?: string}> => {
  return post<{success: boolean, message?: string}>(`/video-sources/${id}/test/`)
} 
<template>
  <div class="metrics-panel">
    <div v-for="metric in metrics" :key="metric.label" class="metric-card dashboard-card">
      <div class="metric-header">
        <el-icon :style="{ color: metric.iconColor }"><component :is="metric.icon" /></el-icon>
        <span class="metric-label">{{ metric.label }}</span>
      </div>
      <div class="metric-value">{{ metric.value }}</div>
      <div class="metric-description">{{ metric.description }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PropType } from 'vue'
import {
  DataAnalysis,
  CircleCheck,
  CircleClose,
  Timer
} from '@element-plus/icons-vue'
import type { DashboardOverview } from '../types/dashboard.types'

const props = defineProps({
  overview: {
    type: Object as PropType<DashboardOverview | null>,
    required: true
  }
})

const metrics = computed(() => {
  if (!props.overview) return []
  return [
    {
      label: '今日检测',
      value: props.overview.total_detections_today.toLocaleString(),
      icon: DataAnalysis,
      iconColor: 'var(--primary-color-light)',
      description: '今日总检测次数'
    },
    {
      label: '正常率',
      value: `${(props.overview.success_rate_today * 100).toFixed(2)}%`,
      icon: CircleCheck,
      iconColor: 'var(--success-color)',
      description: '今日检测成功率'
    },
    {
      label: '卡料次数',
      value: props.overview.jam_detections_today.toLocaleString(),
      icon: CircleClose,
      iconColor: 'var(--danger-color)',
      description: '今日卡料报警次数'
    },
    {
      label: '平均检测用时',
      value: `${Math.round(props.overview.avg_detection_time)}ms`,
      icon: Timer,
      iconColor: 'var(--warning-color)',
      description: '本周平均检测耗时'
    }
  ]
})
</script>

<style scoped>
.metrics-panel {
  grid-column: 1 / -1;
  grid-row: 2;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
}

.metric-card {
  padding: 20px;
  text-align: center;
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.metric-header .el-icon {
  font-size: 24px;
}

.metric-label {
  font-size: 16px;
  color: var(--text-color-soft);
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 8px;
}

.metric-description {
  font-size: 13px;
  color: var(--text-color-mute);
}
</style>
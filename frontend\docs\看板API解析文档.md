
# 看板API解析文档

## 1. 卡料检测相关API

### 1.1 获取卡料检测结果列表
- **接口**: `GET /api/card-detection/`
- **参数**: 
  - `page`: 页码 (默认: 1)
  - `page_size`: 每页数量 (默认: 20)
- **返回数据**:
```typescript
interface CardDetectionResultList {
  total: number;
  page: number;
  page_size: number;
  items: Array<{
    id: number;
    detection_group_id: number;
    timestamp: string;
    created_at: string;
    is_normal: boolean;
    detection_time: number;
    undetected_rois: string[];
    detected_rois: string[];
    trigger_roi_id: string | null;
    result_details: {
      algorithm_version?: string;
      confidence?: number;
      detection_method?: string;
      frame_count?: number;
      total_pailiao_rois?: number;
      detected_pailiao_count?: number;
      notes?: string;
    };
  }>;
}
```

### 1.2 获取看板概览数据
- **接口**: `GET /api/card-detection/dashboard/overview`
- **参数**: 无
- **返回数据**:
```typescript
interface DashboardOverview {
  total_detections_today: number;
  normal_detections_today: number;
  jam_detections_today: number;
  success_rate_today: number;
  total_detections_week: number;
  normal_detections_week: number;
  jam_detections_week: number;
  success_rate_week: number;
  avg_detection_time: number;
  recent_results: Array<CardDetectionResult>; // 与1.1中的items元素相同结构
}
```

### 1.3 获取趋势数据
- **接口**: `GET /api/card-detection/trends/daily`
- **参数**:
  - `days`: 天数，默认7
- **返回数据**:
```typescript
interface TrendData {
  date: string;
  total: number;
  normal: number;
  card_detection: number;
  success_rate: number;
}[]
```

### 1.4 获取检测组统计信息
- **接口**: `GET /api/card-detection/statistics/{group_id}`
- **参数**:
  - `group_id`: 检测组ID
- **返回数据**:
```typescript
interface DetectionGroupStatistics {
  detection_group_id: number;
  total_detections: number;
  normal_count: number;
  abnormal_count: number;
  success_rate: number;
  avg_detection_time: number;
  recent_results: Array<CardDetectionResult>; // 与1.1中的items元素相同结构
}
```

## 2. 检测组相关API

### 2.1 获取所有检测组
- **接口**: `GET /api/detection-groups/`
- **参数**: 无
- **返回数据**:
```typescript
interface DetectionGroup {
  id: number;
  name: string;
  template_id: number | null;
  die_caster_id: number;
  video_source_id: number;
  status: string; // "active" | "inactive"
  config_json: {
    rois?: Array<any>;
    roiIds?: string[];
    globalSettings?: {
      delayTime?: number;
      pauseThreshold?: number;
      cooldownTime?: number;
      cardDelayTime?: number;
    };
    global_parameters?: {
      frame_skip?: number;
      cool_down_time?: number;
      detection_interval?: number;
    };
    roi_config?: {
      rois: Array<any>;
      global_settings: {
        detection_mode: string;
        save_results: boolean;
        image_preprocessing: {
          brightness: number;
          contrast: number;
          gamma: number;
        };
      };
    };
    last_updated?: string;
  } | null;
  created_at: string;
  updated_at: string;
}[]
```

### 2.2 根据模板ID获取检测组
- **接口**: `GET /api/detection-groups/by-template/{template_id}`
- **参数**:
  - `template_id`: 模板ID
- **返回数据**:
```typescript
// 与2.1结构相同，但只包含符合条件的检测组
DetectionGroup[]
```

## 3. 检测模板相关API

### 3.1 获取所有检测模板
- **接口**: `GET /api/detection-templates/`
- **参数**: 无
- **返回数据**:
```typescript
interface DetectionTemplate {
  id: number;
  name: string;
  description: string;
  status: string; // "enabled" | "disabled"
  created_at: string;
  updated_at: string;
}[]
```

### 3.2 获取特定模板详情
- **接口**: `GET /api/detection-templates/{template_id}/`
- **参数**:
  - `template_id`: 模板ID
- **返回数据**:
```typescript
interface DetectionTemplateDetail extends DetectionTemplate {
  die_caster_ids: number[];
}
```

### 3.3 获取当前运行的监测模板名称
- **接口**: `GET /api/detection-templates/running/current`
- **参数**: 无
- **返回数据**: Array<DetectionTemplate>

## 4. 系统监控相关API

### 4.1 获取系统信息
- **接口**: `GET /api/admin/system-info`
- **参数**: 无
- **返回数据**:
```typescript
interface SystemInfo {
  version: string;
  python_version: string;
  platform: string;
  architecture: string;
  uptime: string;
  cpu_usage: number;
  cpu_count: number;
  memory_usage: number;
  memory_total: string;
  memory_available: string;
  disk_usage: number;
  disk_total: string;
  disk_free: string;
  network_info: {
    bytes_sent: string;
    bytes_recv: string;
    packets_sent: number;
    packets_recv: number;
  };
  process_count: number;
  boot_time: string;
}
```

### 4.2 获取系统日志
- **接口**: `GET /api/admin/logs`
- **参数**:
  - `limit`: 日志数量限制 (默认: 10)
  - `offset`: 偏移量 (默认: 0)
- **返回数据**:
```typescript
interface SystemLogs {
  success: boolean;
  data: {
    logs: Array<{
      id: number;
      timestamp: string;
      level: string; // "info" | "warning" | "error"
      module: string;
      message: string;
      filename: string;
      line_number: number;
      function_name: string;
    }>;
    total: number;
    limit: number;
    offset: number;
    source: string;
  };
}
```

### 4.3 获取日志统计
- **接口**: `GET /api/admin/logs/stats`
- **参数**: 无
- **返回数据**:
```typescript
interface LogStats {
  success: boolean;
  data: {
    total_logs: number;
    recent_24h: number;
    level_distribution: Record<string, number>; // 如 {"info": 10, "warning": 5}
    hourly_distribution: Record<string, number>; // 如 {"00": 5, "01": 10}
    cache_size: number;
    max_cache_size: number;
  };
}
```

### 4.4 获取日志级别统计
- **接口**: `GET /api/admin/logs/levels`
- **参数**: 无
- **返回数据**:
```typescript
interface LogLevelStats {
  success: boolean;
  data: Record<string, number>; // 如 {"info": 10, "warning": 5}
}
```

### 4.5 获取日志模块列表
- **接口**: `GET /api/admin/logs/modules`
- **参数**: 无
- **返回数据**:
```typescript
interface LogModules {
  success: boolean;
  data: string[]; // 如 ["app.api.system_logs", "app.api.card_detection"]
}
```

### 4.6 获取系统性能数据
- **接口**: `GET /api/admin/system-performance`
- **参数**: 无
- **返回数据**:
```typescript
interface SystemPerformance {
  timestamp: string;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_io: {
    bytes_sent_mb: number;
    bytes_recv_mb: number;
  };
  active_processes: number;
  load_average: number[];
}
```

## 5. 设备与报警相关API

### 5.1 获取设备状态
- **接口**: `GET /api/device/device-status`
- **参数**: 无
- **返回数据**:
```typescript
interface DeviceStatus {
  total_devices: number;
  online_devices: number;
  offline_devices: number;
  active_detection_groups: number;
  devices: Array<{
    device_id: number;
    device_name: string;
    device_type: string; // "die_caster" | "video_source"
    status: string; // "online" | "offline" | "active"
    last_update: string;
    detection_groups: Array<{
      id: number;
      name: string;
      status: string; // "active" | "inactive"
    }>;
  }>;
}
```

### 5.2 获取报警列表
- **接口**: `GET /api/alarms/`
- **参数**:
  - `page`: 页码 (默认: 1)
  - `limit`: 每页数量限制 (默认: 50)
- **返回数据**:
```typescript
interface AlarmList {
  items: Array<{
    id: string;
    type: string;
    level: string; // "low" | "medium" | "high" | "critical"
    title: string;
    message: string;
    source: string;
    timestamp: string;
    is_resolved: boolean;
    resolved_at: string | null;
    detection_group_id: number;
    metadata: {
      undetected_rois: string[];
      detected_rois: string[];
      trigger_roi_id: string | null;
    };
  }>;
  statistics: {
    total_alarms: number;
    unresolved_alarms: number;
    critical_alarms: number;
    high_alarms: number;
    medium_alarms: number;
    low_alarms: number;
    recent_24h: number;
    recent_7d: number;
  };
  total: number;
  page: number;
  limit: number;
}
```

### 5.3 获取检测效率分析
- **接口**: `GET /api/analytics/detection-efficiency`
- **参数**: 无
- **返回数据**:
```typescript
interface DetectionEfficiency {
  total_detections: number;
  overall_efficiency_rate: number;
  active_detection_groups: number;
  avg_detections_per_hour: number;
  top_performing_groups: Array<{
    id: number;
    name: string;
    efficiency_rate: number;
    total_detections: number;
  }>;
  efficiency_trends: Array<{
    date: string;
    rate: number;
  }>;
}
```

## API与UI组件的映射关系

根据看板实现文件中的UI设计，我们可以建立API与UI组件的映射关系：

1. **实时状态栏**:
   - `GET /api/detection-templates/running/current` - 显示当前模板
   - `GET /api/device/device-status` - 显示活跃检测组数量
   - 最后更新时间可以使用前端计算

2. **核心指标面板**:
   - `GET /api/card-detection/dashboard/overview` - 获取今日检测数、成功率、卡料次数等核心指标

3. **趋势分析区**:
   - `GET /api/card-detection/trends/daily` - 获取24小时检测趋势数据

4. **检测详情**:
   - `GET /api/card-detection/` - 获取最近检测记录

5. **检测组状态监控**:
   - `GET /api/detection-groups/` - 获取所有检测组信息
   - `GET /api/card-detection/statistics/{group_id}` - 获取各检测组的统计信息

6. **智能报警中心**:
   - `GET /api/alarms/` - 获取报警列表和统计信息

7. **系统资源**:
   - `GET /api/admin/system-performance` - 获取CPU、内存、磁盘等系统资源使用情况
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as loginApi, getCurrentUser } from '@/api/auth'
import { ElMessage } from 'element-plus'
import type { User } from '@/types'
import router from '@/router'

export const useUserStore = defineStore('user', () => {
  const token = ref<string | null>(localStorage.getItem('token'))
  const currentUser = ref<User | null>(null)
  const loading = ref(false)
  
  const isLoggedIn = computed(() => !!token.value && !!currentUser.value)
  const isAdmin = computed(() => currentUser.value?.is_superuser === true)
  
  // 登录
  const login = async (username: string, password: string) => {
    loading.value = true
    try {
      const response = await loginApi({ username, password })
      
      // 保存token
      token.value = response.access_token
      localStorage.setItem('token', response.access_token)
      
      // 获取用户信息
      await fetchCurrentUser()
      
      if (currentUser.value) {
        // 保存用户信息到localStorage
        localStorage.setItem('user', JSON.stringify(currentUser.value))
        
        ElMessage.success('登录成功')
        
        // 跳转到数据看板或重定向页面
        const redirect = router.currentRoute.value.query.redirect as string
        router.push(redirect || '/data-dashboard')
        
        return true
      } else {
        ElMessage.error('获取用户信息失败')
        token.value = null
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        return false
      }
    } catch (error: any) {
      console.error('登录失败', error)
      ElMessage.error('登录失败：' + (error.response?.data?.detail || '用户名或密码错误'))
      return false
    } finally {
      loading.value = false
    }
  }
  
  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    if (!token.value) return null
    
    loading.value = true
    try {
      currentUser.value = await getCurrentUser()
      return currentUser.value
    } catch (error: any) {
      console.error('获取用户信息失败', error)
      if (error.response?.status === 401) {
        // 如果是401错误，清除token
        token.value = null
        currentUser.value = null
        localStorage.removeItem('token')
      }
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 登出
  const logout = () => {
    token.value = null
    currentUser.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    router.push('/login')
    ElMessage.success('已退出登录')
  }
  
  // 初始化用户信息
  const init = async () => {
    // 首先尝试从localStorage恢复用户信息
    const savedUser = localStorage.getItem('user')
    if (savedUser && token.value) {
      try {
        currentUser.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析保存的用户信息失败', error)
        localStorage.removeItem('user')
      }
    }
    
    if (token.value) {
      try {
        // 验证token有效性并获取最新用户信息
        await fetchCurrentUser()
        // 如果获取用户信息失败，清除token
        if (!currentUser.value) {
          token.value = null
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          // 检查当前路由是否是video-preview-detection，如果是则不重定向
          if (router.currentRoute.value.meta.requiresAuth && 
              router.currentRoute.value.name !== 'video-preview-detection') {
            router.push('/login')
          }
        } else {
          // 保存最新的用户信息到localStorage
          localStorage.setItem('user', JSON.stringify(currentUser.value))
        }
      } catch (error) {
        console.error('初始化用户信息失败', error)
        token.value = null
        currentUser.value = null
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        // 检查当前路由是否是video-preview-detection，如果是则不重定向
        if (router.currentRoute.value.meta.requiresAuth && 
            router.currentRoute.value.name !== 'video-preview-detection') {
          router.push('/login')
        }
      }
    }
  }
  
  return {
    token,
    currentUser,
    loading,
    isLoggedIn,
    isAdmin,
    login,
    logout,
    fetchCurrentUser,
    init
  }
})
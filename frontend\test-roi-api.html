<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROI API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 ROI API 测试工具</h1>
        <p>测试后端ROI配置API的连通性和功能</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>🔗 1. 连接测试</h3>
            <p>测试后端服务是否启动并可访问</p>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="testHealthCheck()">健康检查</button>
        </div>

        <div class="test-section">
            <h3>📋 2. ROI配置列表</h3>
            <p>获取所有ROI配置</p>
            <button onclick="testGetROIList()">获取ROI列表</button>
            <button onclick="testGetROIListByVideoSource()">按视频源获取</button>
        </div>

        <div class="test-section">
            <h3>💾 3. ROI配置保存</h3>
            <p>测试保存ROI配置到数据库</p>
            <div>
                <label>ROI ID:</label>
                <input type="text" id="testRoiId" value="test_roi_001" placeholder="ROI唯一标识">
            </div>
            <div>
                <label>ROI名称:</label>
                <input type="text" id="testRoiName" value="测试ROI" placeholder="ROI名称">
            </div>
            <div>
                <label>ROI属性:</label>
                <select id="testRoiAttribute">
                    <option value="yazhu">yazhu (压铸机)</option>
                    <option value="pailiao">pailiao (排料口)</option>
                </select>
            </div>
            <div>
                <label>坐标数据 (JSON):</label>
                <textarea id="testRoiCoordinates">[[100,100],[200,100],[200,200],[100,200]]</textarea>
            </div>
            <button onclick="testSaveROI()">保存测试ROI</button>
            <button onclick="testGetROI()">获取测试ROI</button>
            <button onclick="testDeleteROI()">删除测试ROI</button>
        </div>

        <div class="test-section">
            <h3>📊 4. 数据库测试</h3>
            <p>测试数据库连接和表结构</p>
            <button onclick="testDatabaseTables()">查看数据库表</button>
            <button onclick="testROIConfigTable()">查看ROI配置表</button>
        </div>
    </div>

    <div class="container">
        <h3>📝 测试日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function testConnection() {
            log('🔗 测试后端连接...', 'info');
            try {
                const response = await fetch(`${API_BASE.replace('/api', '')}/`);
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 连接成功: ${data.message}`, 'success');
                } else {
                    log(`❌ 连接失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 连接错误: ${error.message}`, 'error');
                log(`💡 请确保后端服务已启动 (python -m uvicorn app.main:app --reload)`, 'warning');
            }
        }

        async function testHealthCheck() {
            log('🏥 执行健康检查...', 'info');
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 健康检查通过: ${JSON.stringify(data)}`, 'success');
                } else {
                    log(`⚠️ 健康检查失败: HTTP ${response.status}`, 'warning');
                }
            } catch (error) {
                log(`❌ 健康检查错误: ${error.message}`, 'error');
            }
        }

        async function testGetROIList() {
            log('📋 获取ROI配置列表...', 'info');
            try {
                const response = await fetch(`${API_BASE}/roi-config/list`);
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 获取成功: 共 ${data.total} 个ROI配置`, 'success');
                    if (data.data && data.data.length > 0) {
                        log(`📄 示例数据: ${JSON.stringify(data.data[0], null, 2)}`, 'info');
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ 获取失败: HTTP ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                log(`❌ 获取错误: ${error.message}`, 'error');
            }
        }

        async function testSaveROI() {
            log('💾 保存测试ROI配置...', 'info');
            
            const roiData = {
                roi_id: document.getElementById('testRoiId').value,
                name: document.getElementById('testRoiName').value,
                attribute: document.getElementById('testRoiAttribute').value,
                roi_type: 'polygon',
                color: '#ff0000',
                coordinates: JSON.parse(document.getElementById('testRoiCoordinates').value),
                algorithm_type: 'motion',
                algorithm_params: {
                    type: 'motion',
                    motion_detection: {
                        algorithm: 'frame_difference',
                        threshold: 30,
                        minArea: 300
                    }
                },
                video_source_id: 'test_video_source',
                video_source_path: '/test/path',
                is_active: true
            };

            try {
                const response = await fetch(`${API_BASE}/roi-config/save`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(roiData)
                });

                if (response.ok) {
                    const result = await response.json();
                    log(`✅ 保存成功: ${result.message}`, 'success');
                } else {
                    const errorText = await response.text();
                    log(`❌ 保存失败: HTTP ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                log(`❌ 保存错误: ${error.message}`, 'error');
            }
        }

        async function testGetROI() {
            const roiId = document.getElementById('testRoiId').value;
            log(`🔍 获取ROI配置: ${roiId}`, 'info');
            
            try {
                const response = await fetch(`${API_BASE}/roi-config/${roiId}`);
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 获取成功: ${JSON.stringify(data.data, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    log(`❌ 获取失败: HTTP ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                log(`❌ 获取错误: ${error.message}`, 'error');
            }
        }

        async function testDeleteROI() {
            const roiId = document.getElementById('testRoiId').value;
            log(`🗑️ 删除ROI配置: ${roiId}`, 'info');

            try {
                const response = await fetch(`${API_BASE}/roi-config/${roiId}`, {
                    method: 'DELETE'
                });
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 删除成功: ${data.message}`, 'success');
                } else {
                    const errorText = await response.text();
                    log(`❌ 删除失败: HTTP ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                log(`❌ 删除错误: ${error.message}`, 'error');
            }
        }

        async function testGetROIListByVideoSource() {
            log('📋 按视频源获取ROI配置...', 'info');
            try {
                const response = await fetch(`${API_BASE}/roi-config/load-by-video-source`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        video_source_id: 'test_video_source'
                    })
                });
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 获取成功: 共 ${data.total} 个ROI配置`, 'success');
                    if (data.data && data.data.length > 0) {
                        log(`📄 示例数据: ${JSON.stringify(data.data[0], null, 2)}`, 'info');
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ 获取失败: HTTP ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                log(`❌ 获取错误: ${error.message}`, 'error');
            }
        }

        async function testDatabaseTables() {
            log('📊 查看数据库表结构...', 'info');
            try {
                const response = await fetch(`${API_BASE}/database/tables`);
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 获取数据库表成功: 共 ${data.length} 个表`, 'success');

                    // 查找ROI相关的表
                    const roiTables = data.filter(table =>
                        table.name && table.name.toLowerCase().includes('roi')
                    );

                    if (roiTables.length > 0) {
                        log(`🎯 找到ROI相关表: ${roiTables.map(t => t.name).join(', ')}`, 'success');
                        roiTables.forEach(table => {
                            log(`📋 表: ${table.name} - ${table.row_count || 0} 条记录`, 'info');
                        });
                    } else {
                        log(`⚠️ 未找到ROI相关表`, 'warning');
                    }

                    // 显示所有表
                    log(`📄 所有表: ${data.map(t => t.name).join(', ')}`, 'info');
                } else {
                    const errorText = await response.text();
                    log(`❌ 获取失败: HTTP ${response.status} - ${errorText}`, 'error');
                    log(`💡 可能需要管理员权限或数据库API未启用`, 'warning');
                }
            } catch (error) {
                log(`❌ 获取错误: ${error.message}`, 'error');
            }
        }

        async function testROIConfigTable() {
            log('🔍 查看ROI配置表详情...', 'info');
            try {
                // 先尝试获取表结构信息
                const structureResponse = await fetch(`${API_BASE}/database/tables`);
                if (structureResponse.ok) {
                    const tables = await structureResponse.json();
                    const roiTable = tables.find(t => t.name === 'roi_configs');

                    if (roiTable) {
                        log(`✅ roi_configs表存在`, 'success');
                        log(`📊 表信息: ${JSON.stringify(roiTable, null, 2)}`, 'info');
                    } else {
                        log(`❌ roi_configs表不存在`, 'error');
                        log(`💡 可用的表: ${tables.map(t => t.name).join(', ')}`, 'info');
                    }
                }

                // 尝试获取ROI配置数据
                const dataResponse = await fetch(`${API_BASE}/roi-config/list`);
                if (dataResponse.ok) {
                    const data = await dataResponse.json();
                    log(`✅ ROI配置数据获取成功: 共 ${data.total} 条记录`, 'success');

                    if (data.data && data.data.length > 0) {
                        // 分析数据结构
                        const sampleRecord = data.data[0];
                        const fields = Object.keys(sampleRecord);
                        log(`📋 数据字段: ${fields.join(', ')}`, 'info');

                        // 统计不同类型的ROI
                        const yazhuCount = data.data.filter(roi => roi.attribute === 'yazhu').length;
                        const pailiaoCount = data.data.filter(roi => roi.attribute === 'pailiao').length;
                        log(`📈 ROI类型统计: yazhu(${yazhuCount}), pailiao(${pailiaoCount})`, 'info');

                        // 显示前3条记录
                        data.data.slice(0, 3).forEach((roi, index) => {
                            log(`📄 记录${index + 1}: ${roi.roi_id} - ${roi.name} (${roi.attribute}/${roi.roi_type})`, 'info');
                        });
                    } else {
                        log(`📭 表为空，没有ROI配置记录`, 'warning');
                    }
                } else {
                    const errorText = await dataResponse.text();
                    log(`❌ 获取ROI数据失败: HTTP ${dataResponse.status} - ${errorText}`, 'error');
                }

            } catch (error) {
                log(`❌ 查看表错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            log('🚀 ROI API 测试工具已启动', 'info');
            log('💡 建议先点击"测试连接"确认后端服务状态', 'warning');
        };
    </script>
</body>
</html>

-- 创建ROI配置表
CREATE TABLE IF NOT EXISTS roi_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    roi_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    attribute VARCHAR(50),
    roi_type VARCHAR(50),
    coordinates TEXT,
    algorithm_type VARCHAR(50),
    algorithm_params TEXT,
    video_source_id VARCHAR(255),
    video_source_path VARCHAR(500),
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_roi_configs_roi_id ON roi_configs(roi_id);
CREATE INDEX IF NOT EXISTS idx_roi_configs_video_source_id ON roi_configs(video_source_id);
CREATE INDEX IF NOT EXISTS idx_roi_configs_is_active ON roi_configs(is_active);

-- 插入示例数据（可选）
INSERT OR IGNORE INTO roi_configs (
    roi_id, name, attribute, roi_type, coordinates, algorithm_type, algorithm_params,
    video_source_id, video_source_path, is_active
) VALUES (
    'example_yazhu_roi',
    '示例压铸机ROI',
    'yazhu',
    'polygon',
    '[[100,100],[200,100],[200,200],[100,200]]',
    'direction',
    '{"type":"direction","前置背景检测":{"enabled":true,"backgroundUpdateRate":0.01,"motionThreshold":50,"minArea":500},"后置方向检测":{"consecutiveDetectionThreshold":3,"minDisplacement":2,"maxPatience":3}}',
    'default_video_source',
    '/default/video/path',
    1
);

INSERT OR IGNORE INTO roi_configs (
    roi_id, name, attribute, roi_type, coordinates, algorithm_type, algorithm_params,
    video_source_id, video_source_path, is_active
) VALUES (
    'example_pailiao_roi',
    '示例排料口ROI',
    'pailiao',
    'polygon',
    '[[300,300],[400,300],[400,400],[300,400]]',
    'motion',
    '{"type":"motion","运动检测":{"algorithm":"帧差法","learningRate":0.01,"detectionThreshold":50,"shadowRemoval":0.5,"threshold":30,"frameInterval":2,"minArea":300}}',
    'default_video_source',
    '/default/video/path',
    1
);

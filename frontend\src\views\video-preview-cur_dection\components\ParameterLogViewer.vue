<template>
  <div class="parameter-log-viewer">
    <el-card class="log-card">
      <template #header>
        <div class="card-header">
          <h3>参数传递日志</h3>
          <div class="header-actions">
            <el-button size="small" @click="clearLogs">清空日志</el-button>
            <el-button size="small" @click="exportLogs">导出日志</el-button>
            <el-switch
              v-model="autoScroll"
              active-text="自动滚动"
              inactive-text="手动滚动"
            />
          </div>
        </div>
      </template>
      
      <div class="log-content" ref="logContainer">
        <div class="log-filters">
          <el-checkbox-group v-model="selectedLogTypes" @change="filterLogs">
            <el-checkbox label="CONFIG">配置</el-checkbox>
            <el-checkbox label="ROI-CONFIG">ROI配置</el-checkbox>
            <el-checkbox label="WS-SEND">前端发送</el-checkbox>
            <el-checkbox label="FRAME-SEND">帧发送</el-checkbox>
            <el-checkbox label="CONFIG-RECV">后端接收</el-checkbox>
            <el-checkbox label="FRAME-RECV">帧接收</el-checkbox>
          </el-checkbox-group>
        </div>
        
        <div class="log-list" ref="logList">
          <div
            v-for="(log, index) in filteredLogs"
            :key="index"
            :class="['log-item', `log-${log.type}`]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-type">{{ log.type }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'

interface LogEntry {
  time: string
  type: string
  message: string
  timestamp: number
}

// 日志数据
const logs = ref<LogEntry[]>([])
const filteredLogs = ref<LogEntry[]>([])

// 过滤选项
const selectedLogTypes = ref(['CONFIG', 'ROI-CONFIG', 'WS-SEND', 'CONFIG-RECV'])
const autoScroll = ref(true)

// DOM引用
const logContainer = ref<HTMLElement>()
const logList = ref<HTMLElement>()

// 添加日志条目
const addLog = (type: string, message: string) => {
  const logEntry: LogEntry = {
    time: new Date().toLocaleTimeString(),
    type,
    message,
    timestamp: Date.now()
  }
  
  logs.value.push(logEntry)
  
  // 限制日志数量，避免内存溢出
  if (logs.value.length > 1000) {
    logs.value.splice(0, 100) // 删除最旧的100条
  }
  
  filterLogs()
  
  // 自动滚动到底部
  if (autoScroll.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}

// 过滤日志
const filterLogs = () => {
  filteredLogs.value = logs.value.filter(log => 
    selectedLogTypes.value.includes(log.type)
  )
}

// 滚动到底部
const scrollToBottom = () => {
  if (logList.value) {
    logList.value.scrollTop = logList.value.scrollHeight
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
  filteredLogs.value = []
  ElMessage.success('日志已清空')
}

// 导出日志
const exportLogs = () => {
  const logText = filteredLogs.value
    .map(log => `[${log.time}] ${log.type}: ${log.message}`)
    .join('\n')
  
  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `parameter-logs-${new Date().toISOString().slice(0, 19)}.txt`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('日志已导出')
}

// 监听操作日志，提取参数相关的日志
const handleOperationLog = (message: string) => {
  // 解析日志类型
  let logType = 'INFO'
  
  if (message.includes('[CONFIG]')) {
    logType = 'CONFIG'
  } else if (message.includes('[ROI-CONFIG]')) {
    logType = 'ROI-CONFIG'
  } else if (message.includes('[WS-SEND]')) {
    logType = 'WS-SEND'
  } else if (message.includes('[FRAME-SEND]')) {
    logType = 'FRAME-SEND'
  } else if (message.includes('[CONFIG-RECV]')) {
    logType = 'CONFIG-RECV'
  } else if (message.includes('[FRAME-RECV]')) {
    logType = 'FRAME-RECV'
  }
  
  // 只记录参数相关的日志
  if (logType !== 'INFO') {
    addLog(logType, message)
  }
}

// 暴露给父组件的方法
defineExpose({
  addLog,
  handleOperationLog
})

// 监听过滤选项变化
watch(selectedLogTypes, filterLogs, { deep: true })

// 组件挂载
onMounted(() => {
  filterLogs()
})
</script>

<style scoped>
.parameter-log-viewer {
  margin-top: 20px;
}

.log-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.log-content {
  max-height: 400px;
  overflow: hidden;
}

.log-filters {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.log-list {
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-item {
  display: flex;
  padding: 4px 8px;
  border-bottom: 1px solid var(--border-color, #f0f0f0);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.log-item:hover {
  background-color: var(--bg-color-soft, #f8f9fa);
}

.log-time {
  width: 80px;
  color: var(--text-color-mute, #666);
  flex-shrink: 0;
  transition: color 0.3s ease;
}

.log-type {
  width: 120px;
  font-weight: bold;
  flex-shrink: 0;
}

.log-message {
  flex: 1;
  word-break: break-all;
}

/* 不同日志类型的颜色 */
.log-CONFIG .log-type {
  color: var(--primary-color, #409eff);
}

.log-ROI-CONFIG .log-type {
  color: var(--success-color, #67c23a);
}

.log-WS-SEND .log-type {
  color: var(--warning-color, #e6a23c);
}

.log-FRAME-SEND .log-type {
  color: var(--danger-color, #f56c6c);
}

.log-CONFIG-RECV .log-type {
  color: var(--info-color, #909399);
}

.log-FRAME-RECV .log-type {
  color: var(--text-color-soft, #606266);
}

.log-type {
  transition: color 0.3s ease;
}

.log-message {
  color: var(--text-color, #303133);
  transition: color 0.3s ease;
}

/* 暗色模式增强支持 */
.dark-theme .log-item {
  border-bottom-color: var(--border-color);
}

.dark-theme .log-item:hover {
  background-color: var(--bg-color-hover);
}

.dark-theme .log-time {
  color: var(--text-color-mute);
}

.dark-theme .log-message {
  color: var(--text-color);
}
</style>

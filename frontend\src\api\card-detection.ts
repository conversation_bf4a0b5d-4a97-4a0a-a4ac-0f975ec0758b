import { request } from './request'

// 卡料检测结果接口
export interface CardDetectionResult {
  id?: number
  detection_group_id: number
  is_normal: boolean
  detection_time: number
  timestamp: string
  undetected_rois?: string[]
  detected_rois?: string[]
  trigger_roi_id?: string
  result_details?: {
    total_pailiao_rois?: number
    detected_pailiao_count?: number
    notes?: string
    [key: string]: any
  }
  created_at?: string
}

// 简化的卡料检测结果接口 - 只包含核心字段
export interface CardDetectionResultSimple {
  id: number
  detection_group_id: number
  timestamp: string
  is_normal: boolean
  undetected_rois?: string[]
}

// 创建卡料检测结果的数据
export interface CardDetectionResultCreate {
  detection_group_id: number
  is_normal: boolean
  detection_time: number
  timestamp: string
  undetected_rois?: string[]
  detected_rois?: string[]
  trigger_roi_id?: string
  result_details?: {
    total_pailiao_rois?: number
    detected_pailiao_count?: number
    notes?: string
    [key: string]: any
  }
}

// 卡料检测统计信息
export interface CardDetectionStatistics {
  total_detections: number
  normal_count: number
  jam_count: number
  success_rate: number
  avg_detection_time: number
  recent_results: CardDetectionResult[]
}

// 卡料检测看板数据
export interface CardDetectionDashboard {
  today_total: number
  today_normal: number
  today_jam: number
  today_success_rate: number
  week_total: number
  week_normal: number
  week_jam: number
  week_success_rate: number
  avg_detection_time: number
  recent_results: CardDetectionResult[]
}

// 卡料检测趋势数据
export interface CardDetectionTrend {
  date: string
  total_detections: number
  normal_count: number
  jam_count: number
  success_rate: number
  avg_detection_time: number
}

// 查询参数
export interface CardDetectionQuery {
  detection_group_id?: number
  start_time?: string
  end_time?: string
  is_normal?: boolean
  page?: number
  page_size?: number
}

// 卡料检测API服务
export const cardDetectionApi = {
  // 创建卡料检测结果
  async createResult(data: CardDetectionResultCreate): Promise<CardDetectionResult> {
    const response = await request({
      url: '/card-detection/',
      method: 'POST',
      data
    })
    return response.data
  },

  // 获取卡料检测结果列表
  async getResults(params?: CardDetectionQuery): Promise<{
    total: number
    page: number
    page_size: number
    items: CardDetectionResult[]
  }> {
    const response = await request({
      url: '/card-detection/',
      method: 'GET',
      params
    })
    return response.data
  },

  // 获取单个卡料检测结果
  async getResult(id: number): Promise<CardDetectionResult> {
    const response = await request({
      url: `/card-detection/${id}`,
      method: 'GET'
    })
    return response.data
  },

  // 更新卡料检测结果
  async updateResult(id: number, data: Partial<CardDetectionResultCreate>): Promise<CardDetectionResult> {
    const response = await request({
      url: `/card-detection/${id}`,
      method: 'PUT',
      data
    })
    return response.data
  },

  // 删除卡料检测结果
  async deleteResult(id: number): Promise<{ message: string }> {
    const response = await request({
      url: `/card-detection/${id}`,
      method: 'DELETE'
    })
    return response.data
  },

  // 获取卡料检测统计信息
  async getStatistics(detectionGroupId: number, params?: {
    start_time?: string
    end_time?: string
    recent_limit?: number
  }): Promise<CardDetectionStatistics> {
    const response = await request({
      url: `/card-detection/statistics/${detectionGroupId}`,
      method: 'GET',
      params
    })
    return response.data
  },

  // 获取卡料检测看板数据
  async getDashboard(detectionGroupId?: number): Promise<CardDetectionDashboard> {
    const response = await request({
      url: '/card-detection/dashboard/overview',
      method: 'GET',
      params: detectionGroupId ? { detection_group_id: detectionGroupId } : undefined
    })
    return response.data
  },

  // 获取卡料检测趋势数据
  async getTrends(params?: {
    detection_group_id?: number
    days?: number
  }): Promise<CardDetectionTrend[]> {
    const response = await request({
      url: '/card-detection/trends/daily',
      method: 'GET',
      params
    })
    return response.data
  },

  // 清理历史数据
  async cleanupHistory(params?: {
    detection_group_id?: number
    days_to_keep?: number
  }): Promise<{ message: string; deleted_count: number }> {
    const response = await request({
      url: '/card-detection/cleanup',
      method: 'DELETE',
      params
    })
    return response.data
  },

  // 获取最新的卡料检测结果（简化版本）
  async getRecentResults(params?: {
    limit?: number
    detection_group_id?: number
  }): Promise<CardDetectionResultSimple[]> {
    const response = await request({
      url: '/card-detection/recent',
      method: 'GET',
      params
    })
    return response.data
  }
}

// 看板概览数据接口（符合API规范）
export interface DashboardOverview {
  total_detections_today: number
  normal_detections_today: number
  jam_detections_today: number
  success_rate_today: number
  total_detections_week: number
  normal_detections_week: number
  jam_detections_week: number
  success_rate_week: number
  avg_detection_time: number
  recent_results: CardDetectionResult[]
}

// 获取卡料检测看板概览数据（直接调用API）
export async function getCardDetectionOverview(): Promise<DashboardOverview> {
  const response = await request({
    url: '/card-detection/dashboard/overview',
    method: 'GET'
  })
  return response.data
}

// 获取最新的卡料检测结果
export async function getRecentCardDetectionResults(params?: {
  limit?: number
  detection_group_id?: number
}): Promise<CardDetectionResult[]> {
  const response = await request({
    url: '/card-detection/recent',
    method: 'GET',
    params
  })
  return response.data
}

export default cardDetectionApi
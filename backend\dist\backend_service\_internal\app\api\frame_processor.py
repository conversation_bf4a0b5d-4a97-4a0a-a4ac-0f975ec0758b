from typing import Dict, Any
import json
import base64
import io
import os
import copy
import numpy as np
import cv2
from PIL import Image
from datetime import datetime

from app.services.websocket_manager import WebSocketConnectionManager

class FrameProcessor:
    """视频帧处理器"""
    
    def __init__(self, connection_manager: WebSocketConnectionManager, detection_instances: Dict[str, Any]):
        self.connection_manager = connection_manager
        self.detection_instances = detection_instances
        
        # 确保ROI截图保存目录存在
        self.roi_save_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'static', 'roi')
        os.makedirs(self.roi_save_dir, exist_ok=True)
    
    async def save_roi_image(self, roi_frame: np.ndarray, roi_id: str):
        """保存ROI截图到static/roi文件夹"""
        try:
            # 生成文件名：时间戳_ROI_ID.png
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精确到毫秒
            filename = f"{timestamp}_{roi_id}.png"
            filepath = os.path.join(self.roi_save_dir, filename)
            
            # 保存图像
            cv2.imwrite(filepath, roi_frame)
            print(f"[ROI-SAVE] ROI截图已保存: {filename}")
            
        except Exception as e:
            print(f"[ROI-SAVE] 保存ROI截图失败: {str(e)}")
    
    async def process_detection_frame(self, message: Dict[str, Any], websocket, client_id: str):
        """处理检测帧"""
        if client_id not in self.detection_instances:
            return

        detection_instance = self.detection_instances[client_id]
        detection_mode = message.get("detection_mode", detection_instance["mode"])
        detection_algorithm = message.get("detection_algorithm", detection_instance["detection_algorithm"])
        
        # 检查消息类型
        message_type = message.get("type", "frame")
        
        try:
            if message_type == "roi_frames":
                # 处理新的ROI截图数据格式
                await self.process_roi_frames(message, websocket, client_id, detection_instance)
            else:
                # 处理传统的整帧数据格式（向后兼容）
                await self.process_legacy_frame(message, websocket, client_id, detection_instance)
                    
        except Exception as e:
            await self.connection_manager.send_personal_message(
                json.dumps({
                    "type": "detection_error",
                    "message": f"Detection error: {str(e)}"
                }),
                websocket
            )
    
    async def process_roi_frames(self, message: Dict[str, Any], websocket, client_id: str, detection_instance: Dict[str, Any]):
        """处理ROI截图数据"""
        roi_list = message.get("roi_list", [])
        video_info = message.get("video_info", {})
        
        print(f"[ROI-FRAMES] 接收到 {len(roi_list)} 个ROI截图")
        
        # 创建结果容器
        all_results = {
            "type": "detection_result",
            "motion_detected": False,
            "contours": [],
            "roi_results": [],
            "timestamp": datetime.now().isoformat()
        }
        
        # 处理每个ROI截图
        for roi in roi_list:
            roi_result = await self.process_roi_image_detection(roi, detection_instance)
            if roi_result:
                all_results["roi_results"].append(roi_result)
                
                # 更新全局检测结果
                if roi_result.get("motion_detected", False):
                    all_results["motion_detected"] = True
                    all_results["contours"].extend(roi_result.get("contours", []))
                    
                    # 如果有方向信息，添加到全局结果
                    if "direction" in roi_result:
                        all_results["direction"] = roi_result["direction"]
        
        # 将检测结果写入文件
        await self.log_detection_results(all_results)
            
        # 返回所有结果
        await self.connection_manager.send_personal_message(
            json.dumps(all_results),
            websocket
        )
    
    async def process_legacy_frame(self, message: Dict[str, Any], websocket, client_id: str, detection_instance: Dict[str, Any]):
        """处理传统的整帧数据格式（向后兼容）"""
        roi_list = message.get("roi_list", [])
        
        # 解码Base64图像
        frame_data = message.get("frame", "")
        
        # 移除可能的前缀
        if "," in frame_data:
            frame_data = frame_data.split(",", 1)[1]
        
        # 解码
        frame_bytes = base64.b64decode(frame_data)
        frame_pil = Image.open(io.BytesIO(frame_bytes))
        
        # 转换为OpenCV格式
        frame = np.array(frame_pil)
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        
        # 检查图像是否有效
        if frame.size == 0 or frame.shape[0] == 0 or frame.shape[1] == 0:
            return
            
        # 创建结果容器
        all_results = {
            "type": "detection_result",
            "motion_detected": False,
            "contours": [],
            "roi_results": [],
            "timestamp": datetime.now().isoformat()
        }
        
        # 处理每个ROI
        for roi in roi_list:
            roi_result = await self.process_roi_detection(roi, frame, detection_instance)
            if roi_result:
                all_results["roi_results"].append(roi_result)
                
                # 更新全局检测结果
                if roi_result.get("motion_detected", False):
                    all_results["motion_detected"] = True
                    all_results["contours"].extend(roi_result.get("contours", []))
                    
                    # 如果有方向信息，添加到全局结果
                    if "direction" in roi_result:
                        all_results["direction"] = roi_result["direction"]

        # 多ROI检测处理已移除（原卡料检测功能）
        # 如果需要多ROI协同检测功能，可在此处添加新的实现

        # 将检测结果写入文件（排除contours字段）
        await self.log_detection_results(all_results)
            
        # 返回所有结果
        await self.connection_manager.send_personal_message(
            json.dumps(all_results),
            websocket
        )
    
    async def process_roi_detection(self, roi: Dict[str, Any], frame: np.ndarray, detection_instance: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个ROI的检测"""
        roi_id = roi.get("id", "")
        roi_name = roi.get("name", f"ROI-{roi_id}")
        roi_params = roi.get("params", {})
        roi_detection_type = roi_params.get("type", "motion")  # 默认为运动检测

        # 确定使用哪个检测器
        detector = None
        detector_source = ""
        current_roi_mode = roi_detection_type  # 每个ROI独立的检测模式

        # 检查是否有ROI专用检测器
        if roi_id in detection_instance["roi_detectors"]:
            detector = detection_instance["roi_detectors"][roi_id]
            detector_source = f"专用检测器({type(detector).__name__})"
        else:
            # 根据ROI的检测类型选择检测器
            if roi_detection_type == "direction":
                # 方向检测使用方向检测器
                detector = detection_instance["direction_detector"]
                detector_source = f"方向检测器({type(detector).__name__})"
            elif detection_instance["detection_algorithm"] == "frame_difference":
                detector = detection_instance["frame_differencer"]
                detector_source = f"全局帧差检测器({type(detector).__name__})"
            else:
                detector = detection_instance["motion_detector"]
                detector_source = f"全局运动检测器({type(detector).__name__})"
        
        # 创建ROI掩码
        roi_mask = self.create_roi_mask(roi, frame.shape[:2])
        if roi_mask is None:
            return None
        
        # 执行检测
        roi_result = {
            "roi_id": roi_id,
            "roi_name": roi_name,
            "motion_detected": False,
            "contours": [],
            # 增加调试信息
            "attribute": roi.get('attribute', 'unknown'),
            "detector_type": current_roi_mode,
            "detector_source": detector_source,
            "roi_params": roi_params
        }
        
        # 根据ROI的检测类型执行相应的检测算法
        if current_roi_mode == 'motion':
            await self.process_motion_detection(detector, frame, roi_mask, roi_result)
        elif current_roi_mode == 'direction':
            await self.process_direction_detection(detector, frame, roi_mask, roi_result, detection_instance)
        
        return roi_result
    
    async def process_roi_image_detection(self, roi: Dict[str, Any], detection_instance: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个ROI截图的检测"""
        roi_id = roi.get("id", "")
        roi_name = roi.get("name", f"ROI-{roi_id}")
        roi_params = roi.get("params", {})
        roi_detection_type = roi_params.get("type", "motion")  # 默认为运动检测
        image_data = roi.get("image_data", "")
        bounding_box = roi.get("bounding_box", {})
        relative_coordinates = roi.get("relative_coordinates", [])
        original_coordinates = roi.get("original_coordinates", [])
        
        print(f"[ROI-IMAGE] 处理ROI {roi_id}, 类型: {roi_detection_type}, 图像大小: {len(image_data)} bytes")
        
        if not image_data:
            print(f"[ROI-IMAGE] ROI {roi_id} 没有图像数据")
            return None
        
        try:
            # 解码ROI图像
            if "," in image_data:
                image_data = image_data.split(",", 1)[1]
            
            image_bytes = base64.b64decode(image_data)
            image_pil = Image.open(io.BytesIO(image_bytes))
            roi_frame = np.array(image_pil)
            roi_frame = cv2.cvtColor(roi_frame, cv2.COLOR_RGB2BGR)
            
            # 检查图像是否有效
            if roi_frame.size == 0 or roi_frame.shape[0] == 0 or roi_frame.shape[1] == 0:
                print(f"[ROI-IMAGE] ROI {roi_id} 图像无效")
                return None
            
            print(f"[ROI-IMAGE] ROI {roi_id} 图像尺寸: {roi_frame.shape}")
            
            # 确定使用哪个检测器
            detector = None
            detector_source = ""
            current_roi_mode = roi_detection_type
            
            # 检查是否有ROI专用检测器
            if roi_id in detection_instance["roi_detectors"]:
                detector = detection_instance["roi_detectors"][roi_id]
                detector_source = f"专用检测器({type(detector).__name__})"
            else:
                # 根据ROI的检测类型选择检测器
                if roi_detection_type == "direction":
                    detector = detection_instance["direction_detector"]
                    detector_source = f"方向检测器({type(detector).__name__})"
                elif detection_instance["detection_algorithm"] == "frame_difference":
                    detector = detection_instance["frame_differencer"]
                    detector_source = f"全局帧差检测器({type(detector).__name__})"
                else:
                    detector = detection_instance["motion_detector"]
                    detector_source = f"全局运动检测器({type(detector).__name__})"
            
            # 执行检测
            roi_result = {
                "roi_id": roi_id,
                "roi_name": roi_name,
                "motion_detected": False,
                "contours": [],
                "attribute": roi.get('attribute', 'unknown'),
                "detector_type": current_roi_mode,
                "detector_source": detector_source,
                "roi_params": roi_params,
                "bounding_box": bounding_box,
                "relative_coordinates": relative_coordinates,
                "original_coordinates": original_coordinates
            }
            
            # 根据ROI的检测类型执行相应的检测算法
            if current_roi_mode == 'motion':
                await self.process_motion_detection_on_roi_image(detector, roi_frame, roi_result)
            elif current_roi_mode == 'direction':
                await self.process_direction_detection_on_roi_image(detector, roi_frame, roi_result, detection_instance)
            
            print(f"[ROI-IMAGE] ROI {roi_id} 检测完成, 运动检测: {roi_result['motion_detected']}")
            return roi_result
            
        except Exception as e:
            print(f"[ROI-IMAGE] ROI {roi_id} 处理失败: {str(e)}")
            return None
    
    async def process_motion_detection_on_roi_image(self, detector, roi_frame: np.ndarray, roi_result: Dict[str, Any]):
        """在ROI图像上处理运动检测"""
        # 保存ROI截图
        # await self.save_roi_image(roi_frame, roi_result.get('roi_id', 'unknown'))
        
        # 运动检测（前端已经截取多边形区域，直接处理）
        motion_detected, contours = detector.detect(roi_frame)
        
        # 将轮廓转换为前端可用的格式，并转换坐标为绝对坐标
        contours_json = self.convert_roi_contours_to_json(contours, roi_result.get("bounding_box", {}))
        
        roi_result["motion_detected"] = motion_detected
        roi_result["contours"] = contours_json
        
        # 添加检测器参数信息用于调试
        if hasattr(detector, 'params'):
            roi_result["detector_params"] = detector.params
    
    async def process_direction_detection_on_roi_image(self, detector, roi_frame: np.ndarray, roi_result: Dict[str, Any], detection_instance: Dict[str, Any]):
        """在ROI图像上处理方向检测"""
        # 保存ROI截图
        # await self.save_roi_image(roi_frame, roi_result.get('roi_id', 'unknown'))
        
        # 方向检测（前端已经截取多边形区域，直接处理）
        direction_state, contours, direction_info = detection_instance["direction_detector"].detect_roi(roi_frame)
        
        # 获取ROI边界框信息用于坐标转换
        bounding_box = roi_result.get("bounding_box", {})
        offset_x = bounding_box.get("x", 0)
        offset_y = bounding_box.get("y", 0)
        
        # 将轮廓转换为前端可用的格式，并转换坐标为绝对坐标
        contours_json = []
        
        for i, contour in enumerate(contours):
            try:
                # 处理OpenCV轮廓格式
                points = []
                
                # 检查轮廓是否为空或格式错误
                if contour is None or len(contour) == 0:
                    continue
                
                # 判断是否是箭头轮廓（通过轮廓点数和形状特征）
                is_arrow = False
                if len(contour) == 7:  # 箭头轮廓有7个点
                    is_arrow = True
                
                for j, point in enumerate(contour):
                    try:
                        # 检查点的格式
                        if point is None:
                            continue
                        
                        # 安全的OpenCV轮廓点格式处理
                        if hasattr(point, '__len__') and len(point) > 0:
                            try:
                                if hasattr(point[0], '__len__') and len(point[0]) >= 2:
                                    # 标准OpenCV格式 [[x, y]]
                                    x, y = int(point[0][0]), int(point[0][1])
                                elif len(point) >= 2:
                                    # 简化格式 [x, y]
                                    x, y = int(point[0]), int(point[1])
                                else:
                                    continue
                                
                                # 转换为绝对坐标
                                abs_x = x + offset_x
                                abs_y = y + offset_y
                                points.append({"x": abs_x, "y": abs_y})
                                
                            except (IndexError, TypeError, ValueError):
                                continue
                        else:
                            continue
                    
                    except (ValueError, TypeError, IndexError):
                        continue
                
                if points:
                    contour_data = {
                        "points": points,
                        "type": "direction_arrow" if is_arrow else "normal",
                        "color": "red" if is_arrow else "green"
                    }
                    
                    # 如果是箭头，添加方向信息
                    if is_arrow and direction_info:
                        contour_data["direction"] = direction_info.get('direction', '')
                    
                    contours_json.append(contour_data)
            
            except Exception:
                continue
        
        roi_result["motion_detected"] = direction_state != 'STATIONARY'
        roi_result["contours"] = contours_json
        roi_result["direction"] = direction_info
        
        # 添加检测器参数信息用于调试
        if hasattr(detector, 'params'):
            roi_result["detector_params"] = detector.params
        elif hasattr(detector, 'motion_detect_params'):
            roi_result["detector_params"] = detector.motion_detect_params
    
    def create_roi_mask(self, roi: Dict[str, Any], frame_shape: tuple) -> np.ndarray:
        """创建ROI掩码"""
        roi_mask = None
        
        if roi.get('type') == 'polygon' and 'points' in roi:
            points = np.array([[p.get('x', 0), p.get('y', 0)] for p in roi['points']], np.int32)
            points = points.reshape((-1, 1, 2))
            roi_mask = np.zeros(frame_shape, dtype=np.uint8)
            cv2.fillPoly(roi_mask, [points], (255,))
        
        elif roi.get('type') == 'rectangle' and 'points' in roi:
            points = roi['points']
            if len(points) >= 2:
                x1, y1 = int(points[0].get('x', 0)), int(points[0].get('y', 0))
                x2, y2 = int(points[1].get('x', 0)), int(points[1].get('y', 0))
                roi_mask = np.zeros(frame_shape, dtype=np.uint8)
                cv2.rectangle(roi_mask, (x1, y1), (x2, y2), 255, -1)
        
        return roi_mask
    
    async def process_motion_detection(self, detector, frame: np.ndarray, roi_mask: np.ndarray, roi_result: Dict[str, Any]):
        """处理运动检测"""
        # 运动检测
        motion_detected, contours = detector.detect(frame, roi_mask)
        
        # 将轮廓转换为前端可用的格式
        contours_json = self.convert_contours_to_json(contours)
        
        roi_result["motion_detected"] = motion_detected
        roi_result["contours"] = contours_json

        # 添加检测器参数信息用于调试
        if hasattr(detector, 'params'):
            roi_result["detector_params"] = detector.params
    
    async def process_direction_detection(self, detector, frame: np.ndarray, roi_mask: np.ndarray, roi_result: Dict[str, Any], detection_instance: Dict[str, Any]):
        """处理方向检测"""
        # 方向检测
        # 提取ROI区域
        roi_frame = cv2.bitwise_and(frame, frame, mask=roi_mask)
        
        # 方向检测
        direction_state, contours, direction_info = detection_instance["direction_detector"].detect_roi(roi_frame)
        
        # 将轮廓转换为前端可用的格式
        contours_json = []

        for i, contour in enumerate(contours):
            try:
                # 处理OpenCV轮廓格式
                points = []

                # 检查轮廓是否为空或格式错误
                if contour is None or len(contour) == 0:
                    continue

                # 判断是否是箭头轮廓（通过轮廓点数和形状特征）
                is_arrow = False
                if len(contour) == 7:  # 箭头轮廓有7个点
                    is_arrow = True

                for j, point in enumerate(contour):
                    try:
                        # 检查点的格式
                        if point is None:
                            continue

                        # 🔥 修复：安全的OpenCV轮廓点格式处理
                        if hasattr(point, '__len__') and len(point) > 0:
                            try:
                                if hasattr(point[0], '__len__') and len(point[0]) >= 2:
                                    # 标准OpenCV格式 [[x, y]]
                                    x, y = int(point[0][0]), int(point[0][1])
                                elif len(point) >= 2:
                                    # 简化格式 [x, y]
                                    x, y = int(point[0]), int(point[1])
                                else:
                                    continue

                                points.append({"x": x, "y": y})

                            except (IndexError, TypeError, ValueError):
                                continue
                        else:
                            continue

                    except (ValueError, TypeError, IndexError):
                        continue

                if points:
                    contour_data = {
                        "points": points,
                        "type": "direction_arrow" if is_arrow else "normal",
                        "color": "red" if is_arrow else "green"
                    }

                    # 如果是箭头，添加方向信息
                    if is_arrow and direction_info:
                        contour_data["direction"] = direction_info.get('direction', '')

                    contours_json.append(contour_data)

            except Exception:
                continue
        
        roi_result["motion_detected"] = direction_state != 'STATIONARY'
        roi_result["contours"] = contours_json
        roi_result["direction"] = direction_info

        # 添加检测器参数信息用于调试
        if hasattr(detector, 'params'):
            roi_result["detector_params"] = detector.params
        elif hasattr(detector, 'motion_detect_params'):
            roi_result["detector_params"] = detector.motion_detect_params
    
    def convert_contours_to_json(self, contours) -> list:
        """将轮廓转换为JSON格式（用于传统整帧检测）"""
        contours_json = []
        for contour in contours:
            points = []
            for point in contour:
                try:
                    # 🔥 修复：安全的轮廓点格式转换
                    if hasattr(point, '__len__') and len(point) > 0:
                        if hasattr(point[0], '__len__') and len(point[0]) >= 2:
                            # 标准OpenCV格式 [[x, y]]
                            x, y = int(point[0][0]), int(point[0][1])
                        elif len(point) >= 2:
                            # 简化格式 [x, y]
                            x, y = int(point[0]), int(point[1])
                        else:
                            continue  # 跳过无效点
                    else:
                        continue  # 跳过无效点

                    points.append({"x": x, "y": y})
                except (IndexError, TypeError, ValueError):
                    # 跳过无效的轮廓点
                    continue

            if points:  # 只添加有效的轮廓
                contours_json.append({"points": points})
        
        return contours_json
    
    def convert_roi_contours_to_json(self, contours, bounding_box: Dict[str, Any]) -> list:
        """将ROI轮廓转换为JSON格式，并转换坐标为绝对坐标"""
        contours_json = []
        offset_x = bounding_box.get("x", 0)
        offset_y = bounding_box.get("y", 0)
        
        for contour in contours:
            points = []
            for point in contour:
                try:
                    # 安全的轮廓点格式转换
                    if hasattr(point, '__len__') and len(point) > 0:
                        if hasattr(point[0], '__len__') and len(point[0]) >= 2:
                            # 标准OpenCV格式 [[x, y]]
                            x, y = int(point[0][0]), int(point[0][1])
                        elif len(point) >= 2:
                            # 简化格式 [x, y]
                            x, y = int(point[0]), int(point[1])
                        else:
                            continue  # 跳过无效点
                    else:
                        continue  # 跳过无效点

                    # 转换为绝对坐标
                    abs_x = x + offset_x
                    abs_y = y + offset_y
                    points.append({"x": abs_x, "y": abs_y})
                except (IndexError, TypeError, ValueError):
                    # 跳过无效的轮廓点
                    continue

            if points:  # 只添加有效的轮廓
                contours_json.append({"points": points})
        
        return contours_json
    
    async def log_detection_results(self, all_results: Dict[str, Any]):
        """记录检测结果到文件"""
        try:
            log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "logs")
            os.makedirs(log_dir, exist_ok=True)
            log_file = os.path.join(log_dir, "检测结果.txt")
            
            # 创建结果的副本，移除contours字段
            log_results = copy.deepcopy(all_results)
            
            # 处理全局contours
            if "contours" in log_results and log_results["contours"]:
                log_results["contours"] = f"[已省略 {len(log_results['contours'])} 个轮廓]"
            
            # 处理各ROI结果中的contours
            for roi_result in log_results.get("roi_results", []):
                if "contours" in roi_result and roi_result["contours"]:
                    roi_result["contours"] = f"[已省略 {len(roi_result['contours'])} 个轮廓]"
                # 如果contours为空，保留原始空列表
            
            with open(log_file, "a", encoding="utf-8") as f:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                f.write(f"[{timestamp}] {json.dumps(log_results, ensure_ascii=False)}\n")
        except Exception as write_err:
            print(f"写入检测结果到文件失败: {write_err}")
import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def get_auth_token(username="admin", password="123456"):
    """获取认证token"""
    login_url = f"{API_BASE}/auth/login/json"
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data["access_token"]
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_api_with_fresh_token(method, url, data=None):
    """使用新token测试API接口"""
    token = get_auth_token()
    if not token:
        print(f"无法获取token，跳过 {method} {url}")
        return None
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers)
        
        print(f"{method} {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code < 400:
            print("✅ 成功")
        else:
            print("❌ 失败")
            
        try:
            response_data = response.json()
            print(f"响应: {response_data}")
        except:
            print(f"响应: {response.text}")
        print("-" * 50)
        
        return response
    except Exception as e:
        print(f"请求失败: {e}")
        print("-" * 50)
        return None

def main():
    print("修复执行预设检测计划的问题...\n")
    
    # 1. 首先获取计划状态
    print("1. 获取计划1的当前状态:")
    response = test_api_with_fresh_token("GET", f"{API_BASE}/preset-schedules/1")
    
    if response and response.status_code == 200:
        schedule_data = response.json()
        is_enabled = schedule_data.get('isEnabled', False)
        print(f"当前启用状态: {is_enabled}")
        
        # 2. 如果未启用，则启用它
        if not is_enabled:
            print("\n2. 启用预设检测计划:")
            test_api_with_fresh_token("PUT", f"{API_BASE}/preset-schedules/1/toggle", {})
        else:
            print("\n2. 计划已启用，无需切换")
    
    # 3. 再次检查状态
    print("\n3. 再次检查计划状态:")
    test_api_with_fresh_token("GET", f"{API_BASE}/preset-schedules/1")
    
    # 4. 尝试执行
    print("\n4. 执行预设检测计划:")
    response = test_api_with_fresh_token("POST", f"{API_BASE}/preset-schedules/1/execute", {})
    
    if response and response.status_code == 200:
        print("🎉 执行成功！预设检测计划API完全正常工作！")
    else:
        print("⚠️ 执行仍然失败，可能需要进一步调试")
    
    # 5. 停止执行（清理）
    print("\n5. 停止预设检测计划（清理）:")
    test_api_with_fresh_token("POST", f"{API_BASE}/preset-schedules/1/stop", {})
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
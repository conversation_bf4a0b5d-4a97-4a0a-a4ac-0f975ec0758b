/**
 * ROI控制管理器
 * 用于处理ROI动态启停控制
 * 根据后端状态机的指令，动态控制canvas截图和参数传递
 */

import { ref, reactive } from 'vue'

interface ROIControlCommand {
  action: 'start_batch' | 'stop_batch'
  roi_ids: string[]
  reason: string
  timestamp: number
}

interface ROIControlState {
  enabledROIs: Set<string>
  disabledROIs: Set<string>
  lastCommand: ROIControlCommand | null
  commandHistory: ROIControlCommand[]
}

export function useROIControl() {
  // ROI控制状态
  const controlState = reactive<ROIControlState>({
    enabledROIs: new Set(),
    disabledROIs: new Set(),
    lastCommand: null,
    commandHistory: []
  })

  // 调试信息
  const debugInfo = ref({
    totalCommands: 0,
    lastAction: '',
    lastReason: '',
    enabledCount: 0,
    disabledCount: 0
  })

  /**
   * 处理ROI控制命令
   * @param commands ROI控制命令列表
   */
  const processROIControlCommands = (commands: ROIControlCommand[]) => {
    if (!commands || commands.length === 0) return

    console.log(`[ROI-CONTROL] 处理 ${commands.length} 个ROI控制命令`)

    commands.forEach(command => {
      processROIControlCommand(command)
    })

    updateDebugInfo()
  }

  /**
   * 处理单个ROI控制命令
   * @param command ROI控制命令
   */
  const processROIControlCommand = (command: ROIControlCommand) => {
    console.log(`[ROI-CONTROL] 执行命令: ${command.action}, ROI: ${command.roi_ids}, 原因: ${command.reason}`)

    // 更新控制状态
    controlState.lastCommand = command
    controlState.commandHistory.push(command)

    // 保持历史记录合理大小
    if (controlState.commandHistory.length > 50) {
      controlState.commandHistory = controlState.commandHistory.slice(-50)
    }

    // 根据命令类型更新ROI状态
    if (command.action === 'start_batch') {
      // 启用指定ROI的检测
      command.roi_ids.forEach(roiId => {
        controlState.enabledROIs.add(roiId)
        controlState.disabledROIs.delete(roiId)
      })
      
      console.log(`[ROI-CONTROL] 启用ROI检测: ${command.roi_ids.join(', ')}`)
      
    } else if (command.action === 'stop_batch') {
      // 禁用指定ROI的检测
      command.roi_ids.forEach(roiId => {
        controlState.disabledROIs.add(roiId)
        controlState.enabledROIs.delete(roiId)
      })
      
      console.log(`[ROI-CONTROL] 禁用ROI检测: ${command.roi_ids.join(', ')}`)
    }

    // 🔥 关键：通知ROI控制器更新检测状态
    notifyROIControllers(command)
  }

  /**
   * 通知ROI控制器更新检测状态
   * @param command ROI控制命令
   */
  const notifyROIControllers = (command: ROIControlCommand) => {
    // 发送自定义事件，通知ROI管理组件
    const event = new CustomEvent('roi-control-update', {
      detail: {
        action: command.action,
        roi_ids: command.roi_ids,
        reason: command.reason,
        enabledROIs: Array.from(controlState.enabledROIs),
        disabledROIs: Array.from(controlState.disabledROIs)
      }
    })
    
    window.dispatchEvent(event)
  }

  /**
   * 检查ROI是否启用
   * @param roiId ROI ID
   * @returns 是否启用
   */
  const isROIEnabled = (roiId: string): boolean => {
    return controlState.enabledROIs.has(roiId)
  }

  /**
   * 检查ROI是否禁用
   * @param roiId ROI ID
   * @returns 是否禁用
   */
  const isROIDisabled = (roiId: string): boolean => {
    return controlState.disabledROIs.has(roiId)
  }

  /**
   * 获取ROI控制状态
   * @param roiId ROI ID
   * @returns 控制状态
   */
  const getROIControlStatus = (roiId: string): 'enabled' | 'disabled' | 'unknown' => {
    if (controlState.enabledROIs.has(roiId)) return 'enabled'
    if (controlState.disabledROIs.has(roiId)) return 'disabled'
    return 'unknown'
  }

  /**
   * 重置ROI控制状态
   */
  const resetROIControl = () => {
    controlState.enabledROIs.clear()
    controlState.disabledROIs.clear()
    controlState.lastCommand = null
    controlState.commandHistory = []
    
    updateDebugInfo()
    
    console.log('[ROI-CONTROL] ROI控制状态已重置')
  }

  /**
   * 更新调试信息
   */
  const updateDebugInfo = () => {
    debugInfo.value = {
      totalCommands: controlState.commandHistory.length,
      lastAction: controlState.lastCommand?.action || '',
      lastReason: controlState.lastCommand?.reason || '',
      enabledCount: controlState.enabledROIs.size,
      disabledCount: controlState.disabledROIs.size
    }
  }

  /**
   * 获取ROI控制统计信息
   */
  const getROIControlStats = () => {
    const stats = {
      totalCommands: controlState.commandHistory.length,
      enabledROIs: Array.from(controlState.enabledROIs),
      disabledROIs: Array.from(controlState.disabledROIs),
      lastCommand: controlState.lastCommand,
      recentCommands: controlState.commandHistory.slice(-10)
    }
    
    return stats
  }

  /**
   * 获取ROI控制日志
   */
  const getROIControlLog = () => {
    return controlState.commandHistory.map(cmd => ({
      timestamp: new Date(cmd.timestamp * 1000).toLocaleTimeString(),
      action: cmd.action,
      roi_ids: cmd.roi_ids.join(', '),
      reason: cmd.reason
    }))
  }

  return {
    // 状态
    controlState,
    debugInfo,
    
    // 方法
    processROIControlCommands,
    isROIEnabled,
    isROIDisabled,
    getROIControlStatus,
    resetROIControl,
    getROIControlStats,
    getROIControlLog
  }
}

// 全局ROI控制实例
let globalROIControl: ReturnType<typeof useROIControl> | null = null

/**
 * 获取全局ROI控制实例
 */
export function getGlobalROIControl() {
  if (!globalROIControl) {
    globalROIControl = useROIControl()
  }
  return globalROIControl
}

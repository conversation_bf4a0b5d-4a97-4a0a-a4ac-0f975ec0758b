<template>
  <div class="roi-config-container">
    <div class="page-header">
      <h2>ROI配置管理</h2>
      <p class="page-description">
        在视频画面上配置感兴趣区域（ROI），支持矩形和多边形绘制，用于压铸机和排料口的智能检测配置
      </p>
    </div>

    <div class="roi-config-content">
      <VideoROIConfigTest />
    </div>
  </div>
</template>

<script setup lang="ts">
import VideoROIConfigTest from '@/components/video-test/VideoROIConfigTest.vue'
</script>

<style scoped>
.roi-config-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: var(--text-color);
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: var(--text-color-soft);
  font-size: 14px;
  line-height: 1.5;
}

.roi-config-content {
  flex: 1;
  min-height: 0;
}
</style>

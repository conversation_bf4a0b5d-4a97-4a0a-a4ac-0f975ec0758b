<template>
  <div class="preset-schedule-view">
    <!-- 头部操作栏 -->
    <div class="header-section">
      <div class="header-left">
        <h3>预设检测计划</h3>
        <p class="subtitle">查看和管理自动化检测模板切换计划</p>
      </div>
      <div class="header-actions">
        <el-button 
          type="primary" 
          size="small"
          :icon="Plus" 
          @click="showCreateDialog = true"
        >
          新建计划
        </el-button>
        <el-button 
          size="small"
          :icon="Refresh" 
          @click="loadSchedules"
          :loading="loading"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 快速统计 -->
    <div class="quick-stats">
      <div class="stat-item">
        <span class="stat-number">{{ schedules.length }}</span>
        <span class="stat-label">总计划</span>
      </div>
      <div class="stat-item">
        <span class="stat-number">{{ activeSchedules.length }}</span>
        <span class="stat-label">活跃中</span>
      </div>
      <div class="stat-item">
        <span class="stat-number">{{ todaySchedules.length }}</span>
        <span class="stat-label">今日计划</span>
      </div>
    </div>

    <!-- 今日计划 -->
    <div v-if="todaySchedules.length > 0" class="today-section">
      <h4 class="section-title">
        <el-icon><Clock /></el-icon>
        今日计划
      </h4>
      <div class="today-schedules">
        <div 
          v-for="schedule in todaySchedules" 
          :key="schedule.id"
          class="schedule-item today-item"
          :class="{
            'active': schedule.status === 'active',
            'pending': schedule.status === 'pending',
            'completed': schedule.status === 'completed'
          }"
        >
          <div class="schedule-time">
            {{ schedule.startTime }} - {{ schedule.endTime }}
            <div v-if="formatCountdown(schedule)" class="countdown-text">
              {{ formatCountdown(schedule) }}
            </div>
          </div>
          <div class="schedule-content">
            <div class="schedule-name">{{ schedule.name }}</div>
            <div class="schedule-template">{{ schedule.templateName }}</div>
          </div>
          <div class="schedule-status">
            <el-tag 
              :type="getStatusTagType(schedule.status)" 
              size="small"
            >
              {{ getStatusText(schedule.status) }}
            </el-tag>
          </div>
          <div class="schedule-actions">
            <el-button 
              v-if="schedule.status === 'pending'"
              type="primary" 
              size="small" 
              @click="executeSchedule(schedule.id)"
            >
              执行
            </el-button>
            <el-button 
              v-if="schedule.status === 'active'"
              type="danger" 
              size="small" 
              @click="stopSchedule(schedule.id)"
            >
              停止
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 即将到来的计划 -->
    <div v-if="upcomingSchedules.length > 0" class="upcoming-section">
      <h4 class="section-title">
        <el-icon><Bell /></el-icon>
        即将执行
      </h4>
      <div class="upcoming-schedules">
        <div 
          v-for="schedule in upcomingSchedules.slice(0, 3)" 
          :key="schedule.id"
          class="schedule-item upcoming-item"
        >
          <div class="schedule-date">{{ formatDate(schedule.date) }}</div>
          <div class="schedule-time">
            {{ schedule.startTime }}
            <div v-if="formatCountdown(schedule)" class="countdown-text">
              {{ formatCountdown(schedule) }}
            </div>
          </div>
          <div class="schedule-content">
            <div class="schedule-name">{{ schedule.name }}</div>
            <div class="schedule-template">{{ schedule.templateName }}</div>
          </div>
          <div class="schedule-actions">
            <el-switch 
              v-model="schedule.isEnabled"
              size="small"
              @change="toggleSchedule(schedule.id, schedule.isEnabled)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 所有计划列表 -->
    <div class="all-schedules-section">
      <div class="section-header">
        <h4 class="section-title">
          <el-icon><Calendar /></el-icon>
          所有计划
        </h4>
        <el-button 
          type="text" 
          size="small"
          @click="openFullManager"
        >
          查看全部 <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
      
      <div class="schedules-list">
        <el-table 
          :data="recentSchedules" 
          size="small"
          stripe
          style="width: 100%"
          max-height="300"
        >
          <el-table-column prop="name" label="计划名称" min-width="120" />
          <el-table-column prop="date" label="日期" width="100" />
          <el-table-column label="时间" width="120">
            <template #default="{ row }">
              {{ row.startTime }}-{{ row.endTime }}
            </template>
          </el-table-column>
          <el-table-column prop="templateName" label="模板" min-width="100" />
          <el-table-column label="状态" width="80">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusTagType(row.status)" 
                size="small"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button 
                v-if="row.status === 'pending' && row.isEnabled"
                type="primary" 
                size="small" 
                @click="showExecuteConfirm(row)"
              >
                执行
              </el-button>
              <el-button 
                v-else-if="row.status === 'active'"
                type="danger" 
                size="small" 
                @click="stopSchedule(row.id)"
              >
                停止
              </el-button>
              <span v-else class="no-action">-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 创建计划对话框 -->
    <el-dialog 
      v-model="showCreateDialog" 
      title="新建预设计划"
      width="500px"
      @close="resetForm"
    >
      <PresetScheduleForm 
        @submit="handleFormSubmit"
        @cancel="showCreateDialog = false"
      />
    </el-dialog>

    <!-- 执行确认弹窗 -->
    <ExecuteConfirmDialog
      v-model="showExecuteDialog"
      :schedule="executingSchedule"
      :loading="executeLoading"
      @confirm="handleExecuteConfirm"
      @cancel="handleExecuteCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Refresh, Clock, Bell, Calendar, ArrowRight } from '@element-plus/icons-vue'
import { usePresetSchedule } from '@/composables/usePresetSchedule'
import PresetScheduleForm from '@/views/preset-schedule/components/PresetScheduleForm.vue'
import ExecuteConfirmDialog from '@/components/ExecuteConfirmDialog.vue'
import type { PresetSchedule } from '@/types/preset-schedule'

// 使用预设计划组合式函数
const {
  schedules,
  loading,
  todaySchedules,
  activeSchedules,
  upcomingSchedules,
  loadSchedules,
  createSchedule,
  toggleSchedule,
  executeSchedule,
  stopSchedule
} = usePresetSchedule()

// 响应式数据
const showCreateDialog = ref(false)
const currentTime = ref(new Date())
const countdownTimer = ref<NodeJS.Timeout | null>(null)
const showExecuteDialog = ref(false)
const executingSchedule = ref<PresetSchedule | null>(null)
const executeLoading = ref(false)

// 计算属性
const recentSchedules = computed(() => {
  return schedules.value
    .sort((a, b) => {
      const dateCompare = b.date.localeCompare(a.date)
      if (dateCompare !== 0) return dateCompare
      return a.startTime.localeCompare(b.startTime)
    })
    .slice(0, 5)
})

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'pending': return 'warning'
    case 'active': return 'success'
    case 'running': return 'success'
    case 'completed': return 'info'
    case 'stopped': return 'primary'  // 修改为primary显示蓝色
    case 'failed': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待执行'
    case 'active': return '执行中'
    case 'running': return '执行中'
    case 'completed': return '已完成'
    case 'stopped': return '已停止'
    case 'failed': return '失败'
    default: return '未知'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(today.getDate() + 1)
  
  if (dateString === today.toISOString().split('T')[0]) {
    return '今天'
  } else if (dateString === tomorrow.toISOString().split('T')[0]) {
    return '明天'
  } else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

// 计算倒计时
const getCountdown = (schedule: any) => {
  const now = currentTime.value
  const today = now.toISOString().split('T')[0]
  
  // 只为今日的待执行计划显示倒计时
  if (schedule.date !== today || schedule.status !== 'pending' || !schedule.isEnabled) {
    return null
  }
  
  const [hours, minutes] = schedule.startTime.split(':').map(Number)
  const scheduleTime = new Date(now)
  scheduleTime.setHours(hours, minutes, 0, 0)
  
  const diff = scheduleTime.getTime() - now.getTime()
  
  if (diff <= 0) {
    return null // 已经到时间或过期
  }
  
  const totalMinutes = Math.floor(diff / (1000 * 60))
  const remainingHours = Math.floor(totalMinutes / 60)
  const remainingMinutes = totalMinutes % 60
  
  if (remainingHours > 0) {
    return `${remainingHours}小时${remainingMinutes}分钟`
  } else {
    return `${remainingMinutes}分钟`
  }
}

// 格式化倒计时显示
const formatCountdown = (schedule: any) => {
  const countdown = getCountdown(schedule)
  return countdown ? `还有 ${countdown}` : ''
}

// 处理表单提交
const handleFormSubmit = async (formData: any) => {
  const success = await createSchedule(formData)
  if (success) {
    showCreateDialog.value = false
    resetForm()
  }
}

// 重置表单
const resetForm = () => {
  // 表单重置逻辑
}

// 显示执行确认弹窗
const showExecuteConfirm = (schedule: PresetSchedule) => {
  executingSchedule.value = schedule
  showExecuteDialog.value = true
}

// 处理执行确认
const handleExecuteConfirm = async (scheduleId: number) => {
  executeLoading.value = true
  try {
    const success = await executeSchedule(scheduleId)
    if (success) {
      showExecuteDialog.value = false
      executingSchedule.value = null
      ElMessage.success('计划执行成功')
    }
  } catch (error) {
    console.error('执行失败:', error)
    ElMessage.error('计划执行失败')
  } finally {
    executeLoading.value = false
  }
}

// 处理执行取消
const handleExecuteCancel = () => {
  showExecuteDialog.value = false
  executingSchedule.value = null
}

// 打开完整管理器
const openFullManager = () => {
  // 这里可以跳转到完整的预设计划管理页面
  // 或者发出事件让父组件处理
  ElMessage.info('跳转到完整的预设计划管理页面')
}

// 启动倒计时定时器
const startCountdownTimer = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
  
  // 每秒更新一次当前时间
  countdownTimer.value = setInterval(() => {
    currentTime.value = new Date()
  }, 1000)
}

// 停止倒计时定时器
const stopCountdownTimer = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
}

// 组件挂载时启动定时器
onMounted(() => {
  startCountdownTimer()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopCountdownTimer()
})
</script>

<style scoped>
.preset-schedule-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  overflow-y: auto;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.header-left h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.subtitle {
  margin: 0;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.quick-stats {
  display: flex;
  gap: 20px;
  padding: 12px 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: var(--el-color-primary);
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.today-schedules,
.upcoming-schedules {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.schedule-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
  border-left: 3px solid var(--el-color-info);
  transition: all 0.3s ease;
}

.today-item.pending {
  border-left-color: var(--el-color-warning);
  background: var(--el-color-warning-light-9);
}

.today-item.active {
  border-left-color: var(--el-color-success);
  background: var(--el-color-success-light-9);
}

.today-item.completed {
  border-left-color: var(--el-color-info);
}

.upcoming-item {
  border-left-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.schedule-time {
  min-width: 80px;
  font-size: 12px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.countdown-text {
  font-size: 10px;
  color: var(--el-color-warning);
  font-weight: 500;
  margin-top: 2px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.schedule-date {
  min-width: 40px;
  font-size: 12px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-right: 8px;
}

.schedule-content {
  flex: 1;
  margin-left: 12px;
}

.schedule-name {
  font-size: 13px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.schedule-template {
  font-size: 11px;
  color: var(--el-text-color-regular);
}

.schedule-status {
  margin: 0 8px;
}

.schedule-actions {
  display: flex;
  gap: 4px;
}

.schedules-list {
  background: var(--el-bg-color);
  border-radius: 6px;
  overflow: hidden;
}

.no-action {
  color: var(--el-text-color-placeholder);
  font-size: 12px;
}

/* 滚动条样式 */
.preset-schedule-view::-webkit-scrollbar {
  width: 6px;
}

.preset-schedule-view::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.preset-schedule-view::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 3px;
}

.preset-schedule-view::-webkit-scrollbar-thumb:hover {
  background: var(--el-fill-color-darker);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    gap: 12px;
  }
  
  .quick-stats {
    justify-content: space-around;
  }
  
  .schedule-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .schedule-content {
    margin-left: 0;
    width: 100%;
  }
  
  .schedule-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * ROI加载器接口
 */
export interface ROIData {
  id?: number                                    // 数据库主键ID
  roi_id: string                                // ROI唯一标识
  name?: string                                 // ROI名称
  attribute?: string                            // ROI属性：yazhu/pailiao
  roi_type?: string                            // ROI类型：rectangle/polygon
  color?: string                               // ROI颜色
  coordinates: Array<{ x: number; y: number }> // ROI坐标点
  algorithm_type?: string                      // 算法类型：direction/motion
  algorithm_params?: any                       // 算法参数
  video_source_id?: string                     // 视频源ID
  video_source_path?: string                   // 视频源路径
  is_active?: boolean                          // 是否激活
  created_at?: string                          // 创建时间
  updated_at?: string                          // 更新时间
}

/**
 * 视频源接口
 */
export interface VideoSource {
  id: string | number
  name: string
  path?: string
  device_ip?: string
  device_port?: number
}

/**
 * ROI加载逻辑的Composable
 */
export function useROILoader() {
  // 响应式状态
  const showLoadDialog = ref(false)
  const loading = ref(false)
  const roiList = ref<ROIData[]>([])
  const selectedROIs = ref<ROIData[]>([])
  
  // 搜索和筛选状态
  const searchKeyword = ref('')
  const filterType = ref('')
  const filterAlgorithm = ref('')

  /**
   * 过滤后的ROI列表
   */
  const filteredROIList = computed(() => {
    let filtered = [...roiList.value]

    // 关键词搜索
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      filtered = filtered.filter(roi =>
        roi.roi_id?.toLowerCase().includes(keyword) ||
        roi.name?.toLowerCase().includes(keyword)
      )
    }

    // 类型筛选
    if (filterType.value) {
      filtered = filtered.filter(roi => roi.attribute === filterType.value)
    }

    // 算法筛选
    if (filterAlgorithm.value) {
      filtered = filtered.filter(roi => {
        const algorithmType = roi.algorithm_type
        if (filterAlgorithm.value === 'direction') {
          return algorithmType === 'direction'
        } else if (filterAlgorithm.value === 'motion') {
          return algorithmType === 'motion' ||
                 algorithmType === 'background_subtraction' ||
                 algorithmType === 'frame_difference'
        }
        return true
      })
    }

    return filtered
  })

  /**
   * 从数据库加载ROI列表
   */
  const loadROIList = async (videoSource: VideoSource): Promise<boolean> => {
    if (!videoSource) {
      ElMessage.warning('请先选择视频源')
      return false
    }

    loading.value = true
    try {
      const response = await fetch('/api/roi-config/load-by-video-source', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          video_source_id: videoSource.id,
          video_source_path: videoSource.path || ''
        })
      })

      if (response.ok) {
        const result = await response.json()
        roiList.value = result.data || []
        ElMessage.success(`加载到 ${roiList.value.length} 个已保存的ROI`)
        return true
      } else {
        const error = await response.json()
        ElMessage.error(`加载ROI列表失败: ${error.detail}`)
        roiList.value = []
        return false
      }
    } catch (error) {
      ElMessage.error(`加载ROI列表异常: ${error}`)
      roiList.value = []
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 刷新ROI列表
   */
  const refreshROIList = async (videoSource: VideoSource): Promise<boolean> => {
    return await loadROIList(videoSource)
  }

  /**
   * 处理选择变化
   */
  const handleSelectionChange = (selection: ROIData[]) => {
    selectedROIs.value = selection
  }

  /**
   * 全选ROI
   */
  const selectAll = () => {
    selectedROIs.value = [...filteredROIList.value]
  }

  /**
   * 清空选择
   */
  const clearSelection = () => {
    selectedROIs.value = []
  }

  /**
   * 按类型选择ROI
   */
  const selectByType = (type: string) => {
    selectedROIs.value = filteredROIList.value.filter(roi => roi.attribute === type)
  }

  /**
   * 按算法选择ROI
   */
  const selectByAlgorithm = (algorithm: string) => {
    selectedROIs.value = filteredROIList.value.filter(roi => {
      const algorithmType = roi.algorithm_type
      if (algorithm === 'direction') {
        return algorithmType === 'direction'
      } else if (algorithm === 'motion') {
        return algorithmType === 'motion' ||
               algorithmType === 'background_subtraction' ||
               algorithmType === 'frame_difference'
      }
      return false
    })
  }

  /**
   * 转换ROI数据格式为前端绘制格式
   */
  const convertROIDataToDrawFormat = (roiData: ROIData) => {
    return {
      roi_id: roiData.roi_id, // 🔥 使用roi_id字段保持一致性
      name: roiData.name || `ROI_${roiData.roi_id}`,
      attribute: roiData.attribute || 'pailiao',
      roi_type: roiData.roi_type || 'polygon',
      coordinates: roiData.coordinates || [],
      points: roiData.coordinates || [], // 兼容性字段，绘制器需要
      color: roiData.color || (roiData.attribute === 'yazhu' ? '#ff0000' : '#00ffff'),
      isActive: roiData.is_active !== false,
      // 保留原始数据用于参数恢复
      _originalData: roiData
    }
  }

  /**
   * 批量转换ROI数据
   */
  const convertSelectedROIsToDrawFormat = () => {
    return selectedROIs.value.map(roi => convertROIDataToDrawFormat(roi))
  }

  /**
   * 确认加载选中的ROI
   */
  const confirmLoadSelectedROIs = async (): Promise<ROIData[]> => {
    if (selectedROIs.value.length === 0) {
      ElMessage.warning('请先选择要加载的ROI')
      return []
    }

    try {
      await ElMessageBox.confirm(
        `确定要加载选中的 ${selectedROIs.value.length} 个ROI吗？这将清除当前的ROI配置。`,
        '确认加载',
        {
          type: 'warning',
          confirmButtonText: '确定加载',
          cancelButtonText: '取消'
        }
      )

      return [...selectedROIs.value]
    } catch (error) {
      // 用户取消操作
      return []
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    selectedROIs.value = []
    searchKeyword.value = ''
    filterType.value = ''
    filterAlgorithm.value = ''
    roiList.value = []
  }

  /**
   * 显示加载对话框
   */
  const showLoadDialogModal = () => {
    showLoadDialog.value = true
  }

  /**
   * 隐藏加载对话框
   */
  const hideLoadDialog = () => {
    showLoadDialog.value = false
    resetState()
  }

  /**
   * 获取ROI统计信息
   */
  const getROIStats = () => {
    const total = filteredROIList.value.length
    const selected = selectedROIs.value.length
    const yazhuCount = filteredROIList.value.filter(roi => roi.attribute === 'yazhu').length
    const pailiaoCount = filteredROIList.value.filter(roi => roi.attribute === 'pailiao').length

    return {
      total,
      selected,
      yazhuCount,
      pailiaoCount
    }
  }

  return {
    // 响应式状态
    showLoadDialog,
    loading,
    roiList,
    selectedROIs,
    searchKeyword,
    filterType,
    filterAlgorithm,
    
    // 计算属性
    filteredROIList,
    
    // 方法
    loadROIList,
    refreshROIList,
    handleSelectionChange,
    selectAll,
    clearSelection,
    selectByType,
    selectByAlgorithm,
    convertROIDataToDrawFormat,
    convertSelectedROIsToDrawFormat,
    confirmLoadSelectedROIs,
    resetState,
    showLoadDialogModal,
    hideLoadDialog,
    getROIStats
  }
}

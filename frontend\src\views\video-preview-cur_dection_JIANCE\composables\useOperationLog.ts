import { ref, nextTick } from 'vue'

/**
 * 操作日志管理Hook
 * 负责处理页面的操作日志记录与管理
 */
export function useOperationLog() {
  // 操作日志列表
  const operationInfos = ref<Array<{ time: string; message: string }>>([])
  
  // 操作日志容器DOM引用
  const infoContent = ref<HTMLElement>()

  /**
   * 添加操作日志
   * @param message 日志消息
   */
  const addOperationInfo = (message: string) => {
    const now = new Date()
    const timeStr = now.getHours().toString().padStart(2, '0') + ':' + 
                    now.getMinutes().toString().padStart(2, '0') + ':' + 
                    now.getSeconds().toString().padStart(2, '0')
    
    operationInfos.value.push({
      time: timeStr,
      message
    })
    
    // 自动滚动到底部
    nextTick(() => {
      if (infoContent.value) {
        infoContent.value.scrollTop = infoContent.value.scrollHeight
      }
    })
  }

  /**
   * 清空操作日志
   */
  const clearOperationInfo = () => {
    operationInfos.value = []
  }

  return {
    operationInfos,
    infoContent,
    addOperationInfo,
    clearOperationInfo
  }
} 
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Optional
import json
import logging

from app.db.session import get_db
from app.models.models import ROIConfig

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/roi-config", tags=["ROI配置"])

@router.post("/save")
async def save_roi_config(roi_data: dict, db: Session = Depends(get_db)):
    """保存ROI配置"""
    try:
        roi_id = roi_data.get('roi_id')
        if not roi_id:
            raise HTTPException(status_code=400, detail="ROI ID不能为空")
        
        # 查找现有配置
        existing_config = db.query(ROIConfig).filter(ROIConfig.roi_id == roi_id).first()
        
        if existing_config:
            # 更新现有配置
            existing_config.name = roi_data.get('name', existing_config.name)
            existing_config.attribute = roi_data.get('attribute', existing_config.attribute)
            existing_config.roi_type = roi_data.get('roi_type', existing_config.roi_type)
            existing_config.color = roi_data.get('color', existing_config.color)
            existing_config.coordinates = json.dumps(roi_data.get('coordinates', []))
            existing_config.algorithm_type = roi_data.get('algorithm_type', existing_config.algorithm_type)
            existing_config.algorithm_params = json.dumps(roi_data.get('algorithm_params', {}))
            existing_config.video_source_id = roi_data.get('video_source_id', existing_config.video_source_id)
            existing_config.video_source_path = roi_data.get('video_source_path', existing_config.video_source_path)
            existing_config.is_active = roi_data.get('is_active', existing_config.is_active)
            
            db.commit()
            db.refresh(existing_config)
            
            logger.info(f"Updated ROI config: {roi_id}")
            return {"message": "ROI配置更新成功", "data": existing_config.to_dict()}
        else:
            # 创建新配置
            new_config = ROIConfig.from_dict(roi_data)
            db.add(new_config)
            db.commit()
            db.refresh(new_config)
            
            logger.info(f"Created new ROI config: {roi_id}")
            return {"message": "ROI配置保存成功", "data": new_config.to_dict()}
            
    except Exception as e:
        logger.error(f"Error saving ROI config: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"保存ROI配置失败: {str(e)}")

@router.get("/list")
async def get_roi_configs(
    video_source_id: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    """获取ROI配置列表"""
    try:
        query = db.query(ROIConfig)
        
        if video_source_id:
            query = query.filter(ROIConfig.video_source_id == video_source_id)
        
        if is_active is not None:
            query = query.filter(ROIConfig.is_active == is_active)
        
        configs = query.order_by(ROIConfig.created_at.desc()).all()
        
        return {
            "message": "获取ROI配置成功",
            "data": [config.to_dict() for config in configs],
            "total": len(configs)
        }
        
    except Exception as e:
        logger.error(f"Error getting ROI configs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取ROI配置失败: {str(e)}")

@router.get("/{roi_id}")
async def get_roi_config(roi_id: str, db: Session = Depends(get_db)):
    """获取单个ROI配置"""
    try:
        config = db.query(ROIConfig).filter(ROIConfig.roi_id == roi_id).first()
        
        if not config:
            raise HTTPException(status_code=404, detail="ROI配置不存在")
        
        return {"message": "获取ROI配置成功", "data": config.to_dict()}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting ROI config {roi_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取ROI配置失败: {str(e)}")

@router.delete("/{roi_id}")
async def delete_roi_config(roi_id: str, db: Session = Depends(get_db)):
    """删除ROI配置"""
    try:
        config = db.query(ROIConfig).filter(ROIConfig.roi_id == roi_id).first()
        
        if not config:
            raise HTTPException(status_code=404, detail="ROI配置不存在")
        
        db.delete(config)
        db.commit()
        
        logger.info(f"Deleted ROI config: {roi_id}")
        return {"message": "ROI配置删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting ROI config {roi_id}: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除ROI配置失败: {str(e)}")

@router.post("/batch-save")
async def batch_save_roi_configs(roi_configs: List[dict], db: Session = Depends(get_db)):
    """批量保存ROI配置"""
    try:
        saved_configs = []
        
        for roi_data in roi_configs:
            roi_id = roi_data.get('roi_id')
            if not roi_id:
                continue
            
            # 查找现有配置
            existing_config = db.query(ROIConfig).filter(ROIConfig.roi_id == roi_id).first()
            
            if existing_config:
                # 更新现有配置
                existing_config.name = roi_data.get('name', existing_config.name)
                existing_config.attribute = roi_data.get('attribute', existing_config.attribute)
                existing_config.roi_type = roi_data.get('roi_type', existing_config.roi_type)
                existing_config.color = roi_data.get('color', existing_config.color)
                existing_config.coordinates = json.dumps(roi_data.get('coordinates', []))
                existing_config.algorithm_type = roi_data.get('algorithm_type', existing_config.algorithm_type)
                existing_config.algorithm_params = json.dumps(roi_data.get('algorithm_params', {}))
                existing_config.video_source_id = roi_data.get('video_source_id', existing_config.video_source_id)
                existing_config.video_source_path = roi_data.get('video_source_path', existing_config.video_source_path)
                existing_config.is_active = roi_data.get('is_active', existing_config.is_active)
                
                saved_configs.append(existing_config.to_dict())
            else:
                # 创建新配置
                new_config = ROIConfig.from_dict(roi_data)
                db.add(new_config)
                saved_configs.append(roi_data)
        
        db.commit()
        
        logger.info(f"Batch saved {len(saved_configs)} ROI configs")
        return {
            "message": f"批量保存{len(saved_configs)}个ROI配置成功",
            "data": saved_configs
        }
        
    except Exception as e:
        logger.error(f"Error batch saving ROI configs: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"批量保存ROI配置失败: {str(e)}")

@router.post("/load-by-video-source")
async def load_roi_configs_by_video_source(
    video_source_data: dict,
    db: Session = Depends(get_db)
):
    """根据视频源加载ROI配置"""
    try:
        video_source_id = video_source_data.get('video_source_id')
        video_source_path = video_source_data.get('video_source_path')
        
        query = db.query(ROIConfig).filter(ROIConfig.is_active == True)
        
        if video_source_id:
            query = query.filter(ROIConfig.video_source_id == video_source_id)
        elif video_source_path:
            query = query.filter(ROIConfig.video_source_path == video_source_path)
        else:
            raise HTTPException(status_code=400, detail="需要提供video_source_id或video_source_path")
        
        configs = query.order_by(ROIConfig.created_at.desc()).all()
        
        return {
            "message": "加载ROI配置成功",
            "data": [config.to_dict() for config in configs],
            "total": len(configs)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error loading ROI configs by video source: {str(e)}")
        raise HTTPException(status_code=500, detail=f"加载ROI配置失败: {str(e)}")

import { ref, onUnmounted, computed } from 'vue'
import { VideoDetectionWebSocket } from '@/services/websocket'
import { getGlobalROIControl } from '@/composables/useROIControl'



/**
 * 运动检测Hook
 * 负责处理ROI区域的截图并通过WebSocket发送到后端进行检测
 * @param addOperationInfo 操作日志记录函数
 * @param addAlgorithmLog 算法参数日志记录函数（可选）
 * @param debugEnabled 调试开关，控制是否输出控制台日志
 */
export function useMotionDetection(
  addOperationInfo: (message: string) => void,
  addAlgorithmLog?: (logData: any) => void,
  debugEnabled: { value: boolean } = { value: false }
) {
  // WebSocket实例
  const wsClient = ref<VideoDetectionWebSocket | null>(null)
  
  // 检测状态
  const isDetectionActive = ref(false)
  const isConnected = ref(false)
  // 注意：检测模式和算法现在由ROI参数决定，这里保留用于向后兼容
  const currentDetectionMode = ref<'motion' | 'direction'>('motion')
  const currentDetectionAlgorithm = ref<'background_subtraction' | 'frame_difference'>('background_subtraction')
  
  // 检测结果
  const detectionResult = ref<{
    motion_detected: boolean;
    contours: Array<{points: Array<{x: number, y: number}>}>;
    direction?: {
      direction: string;
      center: [number, number];
      confidence: number;
    };
    roi_results: Array<{
      roi_id: string;
      roi_name: string;
      motion_detected: boolean;
      contours: Array<{points: Array<{x: number, y: number}>}>;
      direction?: {
        direction: string;
        center: [number, number];
        confidence: number;
      };
    }>;
    roi_violations: Array<{
      roi_id: string;
      roi_name: string;
      violation_type: string;
    }>;
  }>({
    motion_detected: false,
    contours: [],
    roi_results: [],
    roi_violations: []
  })



  // 检测统计
  const detectionStats = ref({
    totalDetections: 0,
    motionDetections: 0,
    motionDetectionRate: 0,
    startTime: 0,
    runningTime: 0
  })

  // 视频捕获相关
  let captureInterval: number | null = null
  const captureDelay = 200 // 每200ms捕获一次

  // ROI活跃状态管理（临时实现，后续会被ROI控制器替代）
  const roiActiveStates = ref<Record<string, boolean>>({})

  // 获取ROI活跃状态
  const getROIActiveStatus = (roiId: string): boolean => {
    // 默认情况下，所有ROI都是活跃的（向后兼容）
    const isActive = roiActiveStates.value[roiId] !== false

    // 添加调试日志
    if (debugEnabled.value) {
      console.log(`🔍 [ROI-ACTIVE] 检查ROI活跃状态: ${roiId} = ${isActive}`, {
        roiActiveStates: roiActiveStates.value,
        hasKey: roiId in roiActiveStates.value,
        value: roiActiveStates.value[roiId]
      })
    }

    return isActive
  }

  // 设置ROI活跃状态
  const setROIActiveStatus = (roiId: string, isActive: boolean) => {
    roiActiveStates.value[roiId] = isActive
  }

  // 算法配置
  const algorithmConfig = ref({
    // 背景减除法参数
    motion_params: {
      minArea: 100,
      detectionThreshold: 40,
      learningRate: 0.005,
      shadowsThreshold: 0.5
    },
    // 帧差法参数
    frame_difference_params: {
      minArea: 100,
      threshold: 30,
      frameInterval: 2
    },
    // 方向检测参数
    direction_params: {
      detector_type: 'background_subtraction',
      minDisplacement: 2,
      maxPatience: 3,
      consecutiveThreshold: 3,
      motion_params: {
        minArea: 100,
        detectionThreshold: 30,
        learningRate: 0.003,
        shadowsThreshold: 0.5
      }
    },
    // ROI特定检测器
    roi_detectors: {},
    // 全局设置
    global_settings: {}
  })
  
  /**
   * 初始化WebSocket连接
   */
  const initWebSocket = async () => {
    try {
      wsClient.value = new VideoDetectionWebSocket()
      
      // 设置WebSocket回调
      wsClient.value.onConnectionStatus((status) => {
        isConnected.value = status === 'connected'
        addOperationInfo(`[WS] WebSocket连接状态: ${status}`)

        // 🔍 调试：记录连接详细信息
        if (status === 'connected') {
          addOperationInfo(`[WS-DEBUG] 连接成功`)
          addOperationInfo(`[WS-DEBUG] WebSocket URL: ws://localhost:8000/ws/video-detection/[client_id]`)
        }
      })
      
      wsClient.value.onDetectionResult((result) => {
        detectionResult.value = {
          motion_detected: result.motion_detected,
          contours: result.contours || [],
          direction: result.direction || undefined,
          roi_results: result.roi_results || [],
          roi_violations: result.roi_violations || []
        }



        // 🔥 处理ROI控制命令
        if (result.roi_control_commands && result.roi_control_commands.length > 0) {
          const roiControl = getGlobalROIControl()
          roiControl.processROIControlCommands(result.roi_control_commands)

          // 记录ROI控制日志
          result.roi_control_commands.forEach(cmd => {
            addOperationInfo(`[ROI控制] ${cmd.action}: ${cmd.roi_ids.join(', ')} (${cmd.reason})`)
          })
        }

        // 更新统计信息
        detectionStats.value.totalDetections++
        if (result.motion_detected) {
          detectionStats.value.motionDetections++
        }
        detectionStats.value.motionDetectionRate = 
          detectionStats.value.motionDetections / detectionStats.value.totalDetections
        detectionStats.value.runningTime = (Date.now() - detectionStats.value.startTime) / 1000
        
        // 记录运动检测结果 - 增强版调试信息
        if (result.motion_detected) {
          const dirInfo = result.direction
            ? `，方向: ${result.direction.direction}`
            : ''
          addOperationInfo(`[检测] 检测到运动${dirInfo}`)

          // 详细的ROI检测结果信息
          if (result.roi_results && result.roi_results.length > 0) {
            addOperationInfo(`[检测] ROI检测详情:`)
            result.roi_results.forEach((roiResult: any, index: number) => {
              if (roiResult.motion_detected) {
                const roiInfo = [
                  `ROI${index + 1}: ${roiResult.roi_name || roiResult.roi_id}`,
                  `属性: ${roiResult.attribute || '未知'}`,
                  `检测器: ${roiResult.detector_type || '未知'}`,
                  `轮廓数: ${roiResult.contours?.length || 0}`
                ].join(', ')

                addOperationInfo(`[检测]   - ${roiInfo}`)

                // 如果有方向信息，显示详细的方向数据
                if (roiResult.direction) {
                  const directionDetail = [
                    `方向: ${roiResult.direction.direction}`,
                    `置信度: ${(roiResult.direction.confidence * 100).toFixed(1)}%`,
                    `连续计数: ${roiResult.direction.consecutive_count}`,
                    `历史长度: ${roiResult.direction.history_length}`
                  ].join(', ')
                  addOperationInfo(`[检测]     方向详情: ${directionDetail}`)
                }

                // 显示检测参数信息（如果可用）
                if (roiResult.detector_params) {
                  const paramInfo = Object.entries(roiResult.detector_params)
                    .map(([key, value]) => `${key}=${value}`)
                    .join(', ')
                  addOperationInfo(`[检测]     参数: ${paramInfo}`)
                }
                
                // 记录到算法参数日志
                if (addAlgorithmLog) {
                  // 获取当前算法类型
                  const isDirection = roiResult.detector_type === 'direction' || 
                                     (roiResult.direction !== undefined);
                  const isPreMotion = isDirection && roiResult.detector_step === 'pre_motion';
                  
                  // 创建日志对象
                  addAlgorithmLog({
                    timestamp: Date.now(),
                    roi_id: roiResult.roi_id,
                    roi_name: roiResult.roi_name,
                    attribute: roiResult.attribute,
                    type: isDirection ? 'direction' : 'motion',
                    is_pre_motion: isPreMotion,
                    enabled: roiResult.enabled !== false, // 默认为true，除非明确设置为false
                    input_params: roiResult.detector_params || {},
                    results: {
                      motion_detected: roiResult.motion_detected,
                      contours_count: roiResult.contours?.length || 0,
                      direction: roiResult.direction,
                      algorithm: roiResult.detector_type,
                      detector_step: roiResult.detector_step
                    }
                  });
                }
              }
            })
          }

          // 全局检测统计
          addOperationInfo(`[检测] 统计: 总检测=${detectionStats.value.totalDetections}, 运动检测=${detectionStats.value.motionDetections}, 检测率=${(detectionStats.value.motionDetectionRate * 100).toFixed(1)}%`)
        }
      })
      
      wsClient.value.onError((error) => {
        addOperationInfo(`[ERROR] WebSocket错误: ${error}`)
      })
      
      // 连接WebSocket
      await wsClient.value.connect()
      isConnected.value = true
      addOperationInfo('[WS] WebSocket连接成功')

      // 连接成功后发送全局设置和ROI配置
      if (algorithmConfig.value.global_settings) {
        wsClient.value.updateConfig({
          global_settings: algorithmConfig.value.global_settings
        })
        addOperationInfo('[WS] 已发送全局设置到后端')
      }

      // 🔥 关键修复：WebSocket连接成功后重新同步ROI配置
      // 这里不能直接访问roiList，需要通过回调来处理
      addOperationInfo('[WS] WebSocket连接成功，等待ROI配置同步')

      return true
    } catch (error) {
      if (debugEnabled.value) {
        console.error('WebSocket初始化失败:', error)
      }
      addOperationInfo(`[ERROR] WebSocket初始化失败: ${error}`)
      return false
    }
  }

  /**
   * 截取ROI区域图像并发送到后端
   * @param videoElement 视频元素
   * @param roiList ROI区域列表
   */
  /**
   * 计算ROI区域的边界框
   * @param points ROI坐标点数组
   * @returns 边界框信息 {x, y, width, height}
   */
  const calculateROIBoundingBox = (points: any[]) => {
    if (!points || points.length === 0) {
      return null
    }

    const xCoords = points.map(p => p.x)
    const yCoords = points.map(p => p.y)
    
    const minX = Math.min(...xCoords)
    const maxX = Math.max(...xCoords)
    const minY = Math.min(...yCoords)
    const maxY = Math.max(...yCoords)
    
    return {
      x: Math.max(0, Math.floor(minX)),
      y: Math.max(0, Math.floor(minY)),
      width: Math.ceil(maxX - minX),
      height: Math.ceil(maxY - minY)
    }
  }

  /**
   * 截取单个ROI区域的图像
   * @param videoElement 视频元素
   * @param roi ROI信息
   * @returns ROI区域的图像数据
   */
  const captureROIRegion = (videoElement: HTMLVideoElement, roi: any) => {
    const coordinates = roi.coordinates || roi.points || []
    const boundingBox = calculateROIBoundingBox(coordinates)
    
    if (!boundingBox || coordinates.length < 3) {
      addOperationInfo(`[ROI-CAPTURE] ❌ ROI ${roi.roi_id} 无有效坐标，跳过截图`)
      return null
    }

    // 确保边界框在视频范围内
    const clampedBox = {
      x: Math.max(0, Math.min(boundingBox.x, videoElement.videoWidth - 1)),
      y: Math.max(0, Math.min(boundingBox.y, videoElement.videoHeight - 1)),
      width: Math.min(boundingBox.width, videoElement.videoWidth - boundingBox.x),
      height: Math.min(boundingBox.height, videoElement.videoHeight - boundingBox.y)
    }

    // 创建临时canvas截取ROI区域
    const roiCanvas = document.createElement('canvas')
    const roiCtx = roiCanvas.getContext('2d')
    
    if (!roiCtx) {
      addOperationInfo(`[ROI-CAPTURE] ❌ 无法获取ROI Canvas上下文`)
      return null
    }

    // 设置canvas大小为ROI区域大小
    roiCanvas.width = clampedBox.width
    roiCanvas.height = clampedBox.height
    
    // 🔥 新增：多边形截图逻辑
    // 1. 先绘制完整的矩形区域
    roiCtx.drawImage(
      videoElement,
      clampedBox.x, clampedBox.y, clampedBox.width, clampedBox.height,
      0, 0, clampedBox.width, clampedBox.height
    )
    
    // 2. 创建多边形掩码，只保留多边形内的像素
    const imageData = roiCtx.getImageData(0, 0, clampedBox.width, clampedBox.height)
    const data = imageData.data
    
    // 创建多边形路径用于点在多边形内的判断
    const relativeCoords = coordinates.map(point => ({
      x: point.x - clampedBox.x,
      y: point.y - clampedBox.y
    }))
    
    // 遍历每个像素，如果不在多边形内则设为透明
    for (let y = 0; y < clampedBox.height; y++) {
      for (let x = 0; x < clampedBox.width; x++) {
        const index = (y * clampedBox.width + x) * 4
        
        // 判断点是否在多边形内
        if (!isPointInPolygon(x, y, relativeCoords)) {
          // 设为透明黑色
          data[index] = 0     // R
          data[index + 1] = 0 // G
          data[index + 2] = 0 // B
          data[index + 3] = 0 // A (透明)
        }
      }
    }
    
    // 将处理后的图像数据重新绘制到canvas
    roiCtx.putImageData(imageData, 0, 0)
    
    // 获取ROI区域图像数据
    const roiImageData = roiCanvas.toDataURL('image/png') // 使用PNG保持透明度
    
    if (debugEnabled.value) {
      console.log(`🔍 [ROI-CAPTURE] ROI ${roi.roi_id} 多边形截图完成:`, {
        boundingBox: clampedBox,
        polygonPoints: relativeCoords.length,
        imageSize: Math.round(roiImageData.length / 1024) + 'KB'
      })
    }
    
    return {
      roi_id: roi.roi_id,
      image_data: roiImageData,
      bounding_box: clampedBox,
      // 相对坐标（相对于截图区域）
      relative_coordinates: relativeCoords,
      // 原始坐标（相对于整个视频画布）
      original_coordinates: coordinates
    }
  }

  // 🔥 新增：判断点是否在多边形内的函数（射线法）
  const isPointInPolygon = (x: number, y: number, polygon: Array<{x: number, y: number}>) => {
    let inside = false
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const xi = polygon[i].x
      const yi = polygon[i].y
      const xj = polygon[j].x
      const yj = polygon[j].y
      
      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside
      }
    }
    return inside
  }

  const captureAndSendROI = (videoElement: HTMLVideoElement, roiList: any[]) => {
    // 添加详细的条件检查日志
    if (!videoElement) {
      addOperationInfo('[CAPTURE-DEBUG] ❌ videoElement为空，跳过截图')
      return
    }
    if (!wsClient.value) {
      addOperationInfo('[CAPTURE-DEBUG] ❌ wsClient为空，跳过截图')
      return
    }
    if (!isConnected.value) {
      addOperationInfo('[CAPTURE-DEBUG] ❌ WebSocket未连接，跳过截图')
      return
    }
    if (!isDetectionActive.value) {
      addOperationInfo('[CAPTURE-DEBUG] ❌ 检测未激活，跳过截图')
      return
    }

    // 添加视频状态检查
    if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
      addOperationInfo(`[CAPTURE-DEBUG] ❌ 视频尺寸异常: ${videoElement.videoWidth}x${videoElement.videoHeight}，跳过截图`)
      return
    }

    addOperationInfo(`[CAPTURE-DEBUG] ✅ 开始ROI区域截图，视频尺寸: ${videoElement.videoWidth}x${videoElement.videoHeight}，ROI数量: ${roiList.length}`)

    try {
      
      // 🔥 新逻辑：截取每个ROI区域并发送
      const roiCaptures: any[] = []
      
      // 🔧 根据ROI控制状态过滤活跃的ROI
      if (debugEnabled.value) {
        console.log(`🔍 [ROI-FILTER] 开始过滤ROI，原始数量: ${roiList.length}`)
        roiList.forEach((roi, index) => {
          const roiId = roi.roi_id || roi.id
          console.log(`🔍 [ROI-FILTER] ROI${index + 1}: ${roiId} (${roi.name})`)
        })
      }

      const activeROIs = roiList
        .filter(roi => {
          // 检查ROI是否处于活跃状态
          const roiId = roi.roi_id || roi.id
          const isActive = getROIActiveStatus(roiId)

          if (debugEnabled.value) {
            if (!isActive) {
              console.log(`🔍 [ROI-CTRL] ❌ 跳过非活跃ROI: ${roiId}`)
            } else {
              console.log(`🔍 [ROI-CTRL] ✅ 保留活跃ROI: ${roiId}`)
            }
          }

          return isActive
        })
       
      if (activeROIs.length === 0) {
        addOperationInfo('[ROI-CAPTURE] ⚠️ 没有活跃的ROI，跳过截图')
        return
      }
       
      // 截取每个活跃ROI的区域
      for (const roi of activeROIs) {
        const roiCapture = captureROIRegion(videoElement, roi)
        if (roiCapture) {
          roiCaptures.push(roiCapture)
        }
      }
       
      if (roiCaptures.length === 0) {
        addOperationInfo('[ROI-CAPTURE] ❌ 所有ROI截图失败，跳过发送')
        return
      }

      if (debugEnabled.value) {
        console.log(`🔍 [ROI-CAPTURE] ROI截图完成，成功数量: ${roiCaptures.length}`)
      }

      // 🔥 新逻辑：处理ROI截图数据，丰富检测参数
      const finalROIList = roiCaptures.map(roiCapture => {
        // 从原始ROI列表中找到对应的ROI信息
        const originalROI = activeROIs.find(roi => roi.roi_id === roiCapture.roi_id)
        
        if (debugEnabled.value) {
          console.log('🔍 [DEBUG] 处理ROI截图数据:', {
            roi_id: roiCapture.roi_id,
            hasOriginalROI: !!originalROI,
            boundingBox: roiCapture.bounding_box,
            imageSize: Math.round(roiCapture.image_data.length / 1024) + 'KB'
          })
        }

        if (!originalROI) {
          addOperationInfo(`[ROI-CAPTURE] ⚠️ 找不到ROI ${roiCapture.roi_id} 的原始数据`)
          return null
        }

        // 🔥 关键修复：确保ROI参数格式正确
        let processedParams = originalROI.params

        // 如果没有参数，使用默认参数（新格式）
        if (!processedParams || Object.keys(processedParams).length === 0) {
          if (originalROI.attribute === 'yazhu') {
            processedParams = {
              type: 'direction',
              motion_detection: {
                enabled: true,
                backgroundUpdateRate: 0.01,
                motionThreshold: 50,
                minArea: 500
              },
              direction_detection: {
                consecutiveDetectionThreshold: 3,
                minDisplacement: 2,
                maxPatience: 3
              }
            }
          } else {
            processedParams = {
              type: 'motion',
              motion_detection: {
                algorithm: 'frame_difference',
                threshold: 30,
                frameInterval: 2,
                minArea: 300
              }
            }
          }
        }

        return {
          // 🔥 关键修复：使用后端期望的字段名
          id: roiCapture.roi_id,                                    // 后端期望 'id' 字段
          roi_id: roiCapture.roi_id,                               // 保留roi_id字段用于兼容性
          name: originalROI.name || `ROI_${roiCapture.roi_id}`,   // 确保有名称
          attribute: originalROI.attribute,                        // yazhu 或 pailiao
          type: originalROI.roi_type || 'polygon',                 // 后端期望 'type' 字段
          
          // 🔥 新增：ROI截图相关数据
          image_data: roiCapture.image_data,                       // ROI区域截图
          bounding_box: roiCapture.bounding_box,                   // 边界框信息
          relative_coordinates: roiCapture.relative_coordinates,   // 相对坐标
          original_coordinates: roiCapture.original_coordinates,   // 原始坐标（用于结果映射）
          
          // 保留原有字段用于兼容性
          points: originalROI.coordinates || originalROI.points || [],     // 后端期望 'points' 字段
          coordinates: originalROI.coordinates || originalROI.points || [], // 保留coordinates字段用于兼容性
          color: originalROI.color || '#ff0000',                   // 确保有颜色
          params: processedParams                                  // 处理后的参数
        }
      }).filter(roi => roi !== null) // 过滤掉无效的ROI

      // 根据ROI参数动态确定检测模式（支持混合模式）
      const directionROIs = finalROIList.filter(roi => roi.params?.type === 'direction')
      const motionROIs = finalROIList.filter(roi => roi.params?.type === 'motion')

      // 支持混合检测模式：每个ROI可以独立使用不同的检测算法
      const dynamicDetectionMode = (directionROIs.length > 0 && motionROIs.length > 0) ? 'mixed' :
                                   (directionROIs.length > 0 ? 'direction' : 'motion')

      addOperationInfo(`[FRAME-SEND] 检测模式分析: 方向检测ROI=${directionROIs.length}个, 运动检测ROI=${motionROIs.length}个, 选择模式=${dynamicDetectionMode}`)

      // 🔥 关键修复：详细记录发送的ROI数据
      if (debugEnabled.value) {
        console.log('🔍 [FRAME-SEND-DEBUG] 准备发送的ROI数据详情:')
        finalROIList.forEach((roi, index) => {
          console.log(`🔍 [FRAME-SEND-DEBUG] ROI${index + 1}:`, {
            id: roi.id,
            roi_id: roi.roi_id,
            name: roi.name,
            attribute: roi.attribute,
            type: roi.type,
            pointsCount: roi.points?.length || 0,
            hasParams: !!roi.params,
            paramsType: roi.params?.type
          })
        })
      }

      // 获取全局设置
      const globalSettings = {}

      // 🔥 新数据结构：发送ROI截图数据而不是整个视频帧
      const data = {
        type: "roi_frames",  // 🔥 新消息类型：ROI区域截图
        roi_list: finalROIList,  // 包含每个ROI的截图数据
        detection_mode: dynamicDetectionMode,
        detection_algorithm: currentDetectionAlgorithm.value, // 保留用于向后兼容
        global_settings: globalSettings,  // 🔥 新增：动态全局参数
        timestamp: Date.now(),
        video_info: {
          width: videoElement.videoWidth,
          height: videoElement.videoHeight
        },
        // 🔥 新增：添加调试信息
        debug_info: {
          roi_count: finalROIList.length,
          yazhu_count: finalROIList.filter(r => r.attribute === 'yazhu').length,
          pailiao_count: finalROIList.filter(r => r.attribute === 'pailiao').length,
          total_image_size: finalROIList.reduce((sum, roi) => sum + (roi.image_data?.length || 0), 0)
        }
      }
      
      // 记录发送的ROI参数到算法日志
      if (addAlgorithmLog) {
        finalROIList.forEach(roi => {
          // 确定算法类型
          const isDirection = roi.params?.type === 'direction';
          
          if (roi.params) {
            // 标准化参数
            const inputParams = {};
            
            if (isDirection) {
              // 方向检测
              const motionDetection = roi.params.motion_detection || roi.params.前置背景检测 || {};
              const directionDetection = roi.params.direction_detection || roi.params.后置方向检测 || {};
              
              // 记录前置运动检测参数
              if (motionDetection.enabled !== false) {
                addAlgorithmLog({
                  timestamp: Date.now(),
                  roi_id: roi.roi_id || roi.id,
                  roi_name: roi.name,
                  attribute: roi.attribute,
                  type: 'direction',
                  is_pre_motion: true,
                  enabled: motionDetection.enabled !== false,
                  input_params: {
                    backgroundUpdateRate: motionDetection.backgroundUpdateRate,
                    motionThreshold: motionDetection.motionThreshold,
                    minArea: motionDetection.minArea
                  },
                  results: {
                    algorithm: 'direction_pre_motion'
                  }
                });
              }
              
              // 记录方向检测参数
              addAlgorithmLog({
                timestamp: Date.now(),
                roi_id: roi.roi_id || roi.id,
                roi_name: roi.name,
                attribute: roi.attribute,
                type: 'direction',
                is_pre_motion: false,
                enabled: true,
                input_params: {
                  consecutiveDetectionThreshold: directionDetection.consecutiveDetectionThreshold,
                  minDisplacement: directionDetection.minDisplacement,
                  maxPatience: directionDetection.maxPatience
                },
                results: {
                  algorithm: 'direction'
                }
              });
            } else {
              // 运动检测
              const motionDetection = roi.params.motion_detection || roi.params.运动检测 || {};
              const algorithm = motionDetection.algorithm || '帧差法';
              
              let algorithmType = 'frame_difference';
              if (algorithm === 'background_subtraction' || algorithm === '背景去除法') {
                algorithmType = 'background_subtraction';
              }
              
              // 根据算法类型记录不同参数
              if (algorithmType === 'background_subtraction') {
                addAlgorithmLog({
                  timestamp: Date.now(),
                  roi_id: roi.roi_id || roi.id,
                  roi_name: roi.name,
                  attribute: roi.attribute,
                  type: 'motion',
                  input_params: {
                    algorithm: 'background_subtraction',
                    learningRate: motionDetection.learningRate,
                    detectionThreshold: motionDetection.detectionThreshold,
                    shadowRemoval: motionDetection.shadowRemoval,
                    minArea: motionDetection.minArea
                  },
                  results: {
                    algorithm: 'background_subtraction'
                  }
                });
              } else {
                addAlgorithmLog({
                  timestamp: Date.now(),
                  roi_id: roi.roi_id || roi.id,
                  roi_name: roi.name,
                  attribute: roi.attribute,
                  type: 'motion',
                  input_params: {
                    algorithm: 'frame_difference',
                    threshold: motionDetection.threshold,
                    frameInterval: motionDetection.frameInterval,
                    minArea: motionDetection.minArea
                  },
                  results: {
                    algorithm: 'frame_difference'
                  }
                });
              }
            }
          }
        });
      }

      // 🔍 调试：记录发送的ROI数据结构
      const debugFrameCount2 = (Date.now() / 1000) % 30
      if (Math.floor(debugFrameCount2) === 0 && debugEnabled.value) {
        console.log('🔍 [DEBUG] 发送到后端的ROI数据:', {
          roiCount: finalROIList.length,
          roiSample: finalROIList[0] ? {
            id: finalROIList[0].id,
            roi_id: finalROIList[0].roi_id,
            name: finalROIList[0].name,
            attribute: finalROIList[0].attribute,
            hasParams: !!finalROIList[0].params,
            paramsType: finalROIList[0].params?.type
          } : null
        })
      }

      // 记录ROI截图发送信息（每30帧记录一次，避免日志过多）
      const frameCount = (Date.now() / 1000) % 30
      if (Math.floor(frameCount) === 0) {
        const totalImageSize = finalROIList.reduce((sum, roi) => sum + (roi.image_data?.length || 0), 0)
        addOperationInfo(`[ROI-SEND] 发送ROI截图数据:`)
        addOperationInfo(`[ROI-SEND] - 动态检测模式: ${dynamicDetectionMode}`)
        addOperationInfo(`[ROI-SEND] - 原始ROI数量: ${roiList.length}`)
        addOperationInfo(`[ROI-SEND] - 活跃ROI数量: ${activeROIs.length}`)
        addOperationInfo(`[ROI-SEND] - 成功截图ROI数量: ${finalROIList.length}`)
        addOperationInfo(`[ROI-SEND] - 总图像大小: ${Math.round(totalImageSize / 1024)}KB`)
        addOperationInfo(`[ROI-SEND] - 平均单ROI大小: ${Math.round(totalImageSize / finalROIList.length / 1024)}KB`)

        // 🔥 关键修复：详细记录每个ROI的截图传输数据
        finalROIList.forEach((roi, index) => {
          const roiImageSize = roi.image_data?.length || 0
          addOperationInfo(`[ROI-SEND]   ROI${index + 1}: ${roi.id} (${roi.attribute || 'unknown'})`)
          addOperationInfo(`[ROI-SEND]     - 名称: ${roi.name}`)
          addOperationInfo(`[ROI-SEND]     - 类型: ${roi.type}`)
          addOperationInfo(`[ROI-SEND]     - 截图大小: ${Math.round(roiImageSize / 1024)}KB`)
          addOperationInfo(`[ROI-SEND]     - 边界框: ${roi.bounding_box?.width}x${roi.bounding_box?.height} at (${roi.bounding_box?.x},${roi.bounding_box?.y})`)
          addOperationInfo(`[ROI-SEND]     - 原始坐标点数: ${roi.points?.length || 0}`)
          addOperationInfo(`[ROI-SEND]     - 相对坐标点数: ${roi.relative_coordinates?.length || 0}`)

          // 显示ROI中包含的检测参数
          if (roi.params) {
            addOperationInfo(`[FRAME-SEND]     - 参数类型: ${roi.params.type}`)
            if (roi.params.type === 'direction') {
              // 🔥 修复：支持新格式和旧格式参数显示
              const motionDetection = roi.params.motion_detection || roi.params.前置背景检测 || {}
              const directionDetection = roi.params.direction_detection || roi.params.后置方向检测 || {}
              addOperationInfo(`[FRAME-SEND]     - 方向检测参数: minArea=${motionDetection.minArea}, threshold=${motionDetection.motionThreshold}, displacement=${directionDetection.minDisplacement}`)
            } else if (roi.params.type === 'motion') {
              // 🔥 修复：支持新格式和旧格式参数显示
              const motionDetection = roi.params.motion_detection || roi.params.运动检测 || {}
              const algorithm = motionDetection.algorithm || '未知'
              const minArea = motionDetection.minArea || 0
              addOperationInfo(`[FRAME-SEND]     - 运动检测参数: 算法=${algorithm}, minArea=${minArea}`)
            } else {
              addOperationInfo(`[FRAME-SEND]     - 其他检测参数: ${JSON.stringify(roi.params)}`)
            }
          } else {
            addOperationInfo(`[FRAME-SEND]     - 参数: ❌ 无参数配置`)
          }
        })

        // ROI统计
        const yazhuCount = finalROIList.filter(r => r.attribute === 'yazhu').length
        const pailiaoCount = finalROIList.filter(r => r.attribute === 'pailiao').length
        addOperationInfo(`[FRAME-SEND] ROI统计: 压铸机ROI=${yazhuCount}个, 排料口ROI=${pailiaoCount}个`)
      }

      // 🔍 调试：检查WebSocket客户端状态
      const debugFrameCount = (Date.now() / 1000) % 30
      if (Math.floor(debugFrameCount) === 0) {
        const totalImageSize = finalROIList.reduce((sum, roi) => sum + (roi.image_data?.length || 0), 0)
        addOperationInfo(`[WS-DEBUG] 准备发送ROI截图数据`)
        addOperationInfo(`[WS-DEBUG] - wsClient存在: ${!!wsClient.value}`)
        addOperationInfo(`[WS-DEBUG] - 连接状态: ${isConnected.value ? '已连接' : '未连接'}`)
        addOperationInfo(`[WS-DEBUG] - 数据大小: ${JSON.stringify(data).length}字节`)
        addOperationInfo(`[WS-DEBUG] - 总图像大小: ${Math.round(totalImageSize / 1024)}KB`)
        addOperationInfo(`[WS-DEBUG] - ROI截图数据完整性检查:`)

        // 🔥 关键修复：检查每个ROI截图数据的完整性
        finalROIList.forEach((roi, index) => {
          const hasId = !!roi.id
          const hasImageData = !!roi.image_data
          const hasBoundingBox = !!roi.bounding_box
          const hasRelativeCoords = roi.relative_coordinates && roi.relative_coordinates.length > 0
          const hasOriginalCoords = roi.original_coordinates && roi.original_coordinates.length > 0
          const hasParams = !!roi.params
          const hasAttribute = !!roi.attribute

          addOperationInfo(`[WS-DEBUG]   ROI${index + 1}: id=${hasId}, image=${hasImageData}, bbox=${hasBoundingBox}, coords=${hasRelativeCoords}, params=${hasParams}, attr=${hasAttribute}`)

          if (!hasId) addOperationInfo(`[WS-DEBUG]     ❌ 缺少ID字段`)
          if (!hasImageData) addOperationInfo(`[WS-DEBUG]     ❌ 缺少截图数据`)
          if (!hasBoundingBox) addOperationInfo(`[WS-DEBUG]     ❌ 缺少边界框信息`)
          if (!hasRelativeCoords) addOperationInfo(`[WS-DEBUG]     ❌ 缺少相对坐标数据`)
          if (!hasOriginalCoords) addOperationInfo(`[WS-DEBUG]     ❌ 缺少原始坐标数据`)
          if (!hasParams) addOperationInfo(`[WS-DEBUG]     ❌ 缺少参数配置`)
          if (!hasAttribute) addOperationInfo(`[WS-DEBUG]     ❌ 缺少属性标识`)
        })
      }

      // 发送到WebSocket
      if (wsClient.value && isConnected.value) {
        try {
          wsClient.value.sendFrame(data)
          if (Math.floor(debugFrameCount) === 0) {
            const totalImageSize = finalROIList.reduce((sum, roi) => sum + (roi.image_data?.length || 0), 0)
            addOperationInfo(`[WS-DEBUG] ✅ ROI截图数据已发送到WebSocket`)
            addOperationInfo(`[WS-DEBUG] ✅ 发送的ROI数量: ${finalROIList.length}`)
            addOperationInfo(`[WS-DEBUG] ✅ 总图像大小: ${Math.round(totalImageSize / 1024)}KB`)
          }
        } catch (error) {
          addOperationInfo(`[WS-DEBUG] ❌ 发送ROI截图数据失败: ${error}`)
          if (debugEnabled.value) {
            console.error('WebSocket发送失败:', error)
          }
        }
      } else {
        if (Math.floor(debugFrameCount) === 0) {
          addOperationInfo(`[WS-DEBUG] ❌ 无法发送ROI截图数据: wsClient=${!!wsClient.value}, connected=${isConnected.value}`)
          if (!wsClient.value) {
            addOperationInfo(`[WS-DEBUG] ❌ WebSocket客户端未初始化`)
          }
          if (!isConnected.value) {
            addOperationInfo(`[WS-DEBUG] ❌ WebSocket连接已断开`)
          }
        }
      }
    } catch (error) {
      if (debugEnabled.value) {
        console.error('ROI截图发送失败:', error)
      }
      addOperationInfo(`[ERROR] ROI截图发送失败: ${error}`)
    }
  }

  /**
   * 启动视频检测
   * @param videoElement 视频元素
   * @param roiList ROI区域列表
   * @param config 可选的配置参数
   * @returns 是否成功启动检测
   */
  const startDetection = async (
    videoElement: HTMLVideoElement,
    roiList: any[],
    config?: any
  ): Promise<boolean> => {
    if (!videoElement) {
      addOperationInfo('[ERROR] 无法获取视频元素')
      return false
    }

    if (!wsClient.value || !isConnected.value) {
      const connected = await initWebSocket()
      if (!connected) {
        addOperationInfo('[ERROR] 无法连接到WebSocket服务器')
        return false
      }
    }

    // 重置统计信息
    detectionStats.value = {
      totalDetections: 0,
      motionDetections: 0,
      motionDetectionRate: 0,
      startTime: Date.now(),
      runningTime: 0
    }

    // 开始定时捕获
    isDetectionActive.value = true
    captureInterval = window.setInterval(() => {
      captureAndSendROI(videoElement, roiList)
    }, captureDelay)

    // 只发送必要的配置（全局设置）
    if (config && config.global_settings) {
      updateConfig({
        global_settings: config.global_settings
      })
    }

    addOperationInfo(`[检测] 开始ROI检测，检测模式将根据ROI参数动态确定`)
    return true
  }
  
  /**
   * 停止视频检测
   */
  const stopDetection = () => {
    if (captureInterval !== null) {
      clearInterval(captureInterval)
      captureInterval = null
    }
    
    isDetectionActive.value = false
    
    // 清除检测结果
    detectionResult.value = {
      motion_detected: false,
      contours: [],
      roi_results: [],
      roi_violations: []
    }
    
    addOperationInfo('[检测] 停止检测')
  }
  
  /**
   * 切换检测模式
   * @param mode 检测模式
   */
  const setDetectionMode = (mode: 'motion' | 'direction') => {
    currentDetectionMode.value = mode
    addOperationInfo(`[检测] 设置检测模式: ${mode === 'motion' ? '运动检测' : '方向检测'}`)
    
    // 如果正在检测中，更新配置
    if (isDetectionActive.value && wsClient.value) {
      wsClient.value.updateConfig({
        detection_mode: mode
      })
    }
  }
  
  /**
   * 设置检测算法
   * @param algorithm 检测算法
   */
  const setDetectionAlgorithm = (algorithm: 'background_subtraction' | 'frame_difference') => {
    currentDetectionAlgorithm.value = algorithm
    addOperationInfo(`[检测] 设置检测算法: ${algorithm === 'background_subtraction' ? '背景减除法' : '帧差法'}`)
    
    // 如果正在检测中，更新配置
    if (isDetectionActive.value && wsClient.value) {
      wsClient.value.updateConfig({
        detection_algorithm: algorithm
      })
    }
  }

  /**
   * 更新检测配置
   * @param config 配置对象
   */
  const updateConfig = (config: any) => {
    // 记录发送的配置详情
    addOperationInfo(`[CONFIG] 准备发送配置更新:`)

    // 只处理和发送必要的配置
    const configToSend: any = {}

    // 处理ROI检测器配置
    if (config.roi_detectors) {
      addOperationInfo(`[CONFIG] - ROI检测器配置:`)
      Object.entries(config.roi_detectors).forEach(([roiId, detectorConfig]) => {
        addOperationInfo(`[CONFIG]   ROI ${roiId}: ${JSON.stringify(detectorConfig)}`)
      })
      algorithmConfig.value.roi_detectors = {
        ...algorithmConfig.value.roi_detectors,
        ...config.roi_detectors
      }
      configToSend.roi_detectors = config.roi_detectors
    }

    // 处理全局设置参数
    if (config.global_settings) {
      addOperationInfo(`[CONFIG] - 全局设置: ${JSON.stringify(config.global_settings)}`)
      algorithmConfig.value.global_settings = {
        ...algorithmConfig.value.global_settings,
        ...config.global_settings
      }
      configToSend.global_settings = config.global_settings
    }

    // 处理检测模式和算法（用于帧发送时的参数）
    if (config.detection_algorithm) {
      addOperationInfo(`[CONFIG] - 检测算法: ${config.detection_algorithm}`)
      currentDetectionAlgorithm.value = config.detection_algorithm
      // 不发送到后端，只用于前端状态管理
    }

    if (config.detection_mode) {
      addOperationInfo(`[CONFIG] - 检测模式: ${config.detection_mode}`)
      // 不发送到后端，只用于前端状态管理
    }

    // 只有当有实际需要发送的配置时才发送
    if (Object.keys(configToSend).length > 0) {
      if (wsClient.value && isConnected.value) {
        addOperationInfo(`[WS-SEND] 发送配置到后端: ${JSON.stringify(configToSend, null, 2)}`)
        wsClient.value.updateConfig(configToSend)
        addOperationInfo('[CONFIG] ✅ 配置已发送到后端')
      } else {
        addOperationInfo('[CONFIG] ⚠️ WebSocket未连接，配置将在连接后发送')
      }
    } else {
      addOperationInfo('[CONFIG] ℹ️ 无需发送配置到后端')
    }
  }
  
  /**
   * 为特定ROI设置检测算法
   * @param roiId ROI ID
   * @param algorithm 检测算法类型或ROI属性
   * @param params 算法参数
   */
  const setRoiDetector = (roiId: string, algorithm: 'background_subtraction' | 'frame_difference' | 'direction', params: any = {}) => {
    // 更新ROI检测器配置
    algorithmConfig.value.roi_detectors = {
      ...algorithmConfig.value.roi_detectors,
      [roiId]: {
        type: algorithm,
        ...params
      }
    }

    // 构建标准化的参数格式
    let standardizedParams: any = {
      type: algorithm
    }

    // 根据算法类型处理参数
    if (algorithm === 'direction') {
      // 方向检测算法参数 - 支持新格式和旧格式
      const motionDetection = params.motion_detection || params.前置背景检测 || {}
      const directionDetection = params.direction_detection || params.后置方向检测 || {}

      // 🔥 修改为后端期望的参数格式
      standardizedParams = {
        detector_type: 'background_subtraction', // 方向检测内部使用的检测器类型
        // 前置运动检测参数
        motion_params: {
          enabled: motionDetection.enabled !== false,  // 确保enabled状态正确传递
          minArea: motionDetection.minArea || 500,
          detectionThreshold: motionDetection.motionThreshold || 50,
          learningRate: motionDetection.backgroundUpdateRate || 0.01,
          shadowsThreshold: motionDetection.shadowsThreshold || 0.5
        },
        // 方向检测特有参数
        minDisplacement: directionDetection.minDisplacement || 2,
        maxPatience: directionDetection.maxPatience || 3,
        consecutiveThreshold: directionDetection.consecutiveDetectionThreshold || 3
      }

      addOperationInfo(`[检测] 为ROI ${roiId} 设置方向检测算法`)
    } else if (algorithm === 'background_subtraction') {
      // 背景减除法参数 - 支持新格式和旧格式
      const motionDetection = params.motion_detection || params.运动检测 || {}

      standardizedParams = {
        type: 'background_subtraction',
        minArea: motionDetection.minArea || 100,
        detectionThreshold: motionDetection.detectionThreshold || 40,
        learningRate: motionDetection.learningRate || 0.005,
        shadowsThreshold: motionDetection.shadowRemoval || 0.5
      }

      addOperationInfo(`[检测] 为ROI ${roiId} 设置背景减除法`)
    } else if (algorithm === 'frame_difference') {
      // 帧差法参数 - 支持新格式和旧格式
      const motionDetection = params.motion_detection || params.运动检测 || {}

      standardizedParams = {
        type: 'frame_difference',
        minArea: motionDetection.minArea || 100,
        threshold: motionDetection.threshold || 30,
        frameInterval: motionDetection.frameInterval || 2
      }

      addOperationInfo(`[检测] 为ROI ${roiId} 设置帧差法`)
    } else if (algorithm === 'motion') {
      // 🔥 关键修复：处理motion类型，根据algorithm字段映射到具体检测器
      const motionDetection = params.motion_detection || params.运动检测 || {}
      const detectionAlgorithm = motionDetection.algorithm || 'frame_difference'

      if (detectionAlgorithm === 'background_subtraction') {
        // 背景减除法
        standardizedParams = {
          type: 'background_subtraction',
          minArea: motionDetection.minArea || 100,
          detectionThreshold: motionDetection.detectionThreshold || 40,
          learningRate: motionDetection.learningRate || 0.005,
          shadowsThreshold: motionDetection.shadowRemoval || 0.5
        }
        addOperationInfo(`[检测] 为ROI ${roiId} 设置运动检测(背景减除法)`)
      } else {
        // 帧差法（默认）
        standardizedParams = {
          type: 'frame_difference',
          minArea: motionDetection.minArea || 100,
          threshold: motionDetection.threshold || 30,
          frameInterval: motionDetection.frameInterval || 2
        }
        addOperationInfo(`[检测] 为ROI ${roiId} 设置运动检测(帧差法)`)
      }
    }

    // 详细记录ROI配置信息
    addOperationInfo(`[ROI-CONFIG] 设置ROI ${roiId} 检测器:`)
    addOperationInfo(`[ROI-CONFIG] - 算法类型: ${algorithm}`)
    addOperationInfo(`[ROI-CONFIG] - 原始参数: ${JSON.stringify(params, null, 2)}`)
    addOperationInfo(`[ROI-CONFIG] - 标准化参数: ${JSON.stringify(standardizedParams, null, 2)}`)

    // 发送配置到服务器（无论是否正在检测都发送，以便后端保存配置）
    // 详细的连接状态检查
    if (debugEnabled.value) {
      console.log(`🔍 [ROI-CONFIG-DEBUG] WebSocket状态检查:`)
      console.log(`🔍 [ROI-CONFIG-DEBUG]   wsClient存在: ${!!wsClient.value}`)
      console.log(`🔍 [ROI-CONFIG-DEBUG]   连接状态: ${isConnected.value}`)
      console.log(`🔍 [ROI-CONFIG-DEBUG]   ROI ID: ${roiId}`)
      console.log(`🔍 [ROI-CONFIG-DEBUG]   算法类型: ${algorithm}`)
      console.log(`🔍 [ROI-CONFIG-DEBUG]   原始参数:`, params)
      console.log(`🔍 [ROI-CONFIG-DEBUG]   标准化参数:`, standardizedParams)
    }

    if (wsClient.value && isConnected.value) {
      const configToSend = {
        roi_detectors: {
          [roiId]: standardizedParams
        }
      }

      if (debugEnabled.value) {
        console.log(`🚀 [WS-SEND] 准备发送ROI配置:`, configToSend)
      }
      addOperationInfo(`[WS-SEND] 发送ROI配置: ${JSON.stringify(configToSend, null, 2)}`)

      try {
        wsClient.value.updateConfig(configToSend)
        if (debugEnabled.value) {
          console.log(`✅ [WS-SEND] ROI配置发送成功`)
        }
        addOperationInfo(`[ROI-CONFIG] ✅ ROI ${roiId} 配置已发送到后端`)
      } catch (error) {
        if (debugEnabled.value) {
          console.error(`❌ [WS-SEND] ROI配置发送失败:`, error)
        }
        addOperationInfo(`[ROI-CONFIG] ❌ ROI ${roiId} 配置发送失败: ${error}`)
      }
    } else {
      if (debugEnabled.value) {
        console.warn(`⚠️ [ROI-CONFIG] WebSocket未连接，无法发送ROI配置`)
      }
      addOperationInfo(`[ROI-CONFIG] ⚠️ WebSocket未连接，ROI ${roiId} 配置将在连接后发送`)
    }
  }

  // 发送消息到WebSocket服务器
  const sendMessageToServer = (message: any) => {
    if (wsClient.value && isConnected.value) {
      // 使用updateConfig方法发送消息
      updateConfig(message)
      return true
    }
    return false
  }

  /**
   * 更新全局设置
   * 将全局设置发送到后端
   * @param settings 全局设置对象
   */
  const updateGlobalSettings = (settings: {
    delay_time?: number;
    pause_threshold?: number;
    cooldown_time?: number;
  }) => {
    if (!wsClient.value || !isConnected.value) {
      return false
    }

    // 更新本地配置
    algorithmConfig.value.global_settings = {
      ...algorithmConfig.value.global_settings,
      ...settings
    }

    // 发送全局设置更新消息
    updateConfig({
      global_settings: algorithmConfig.value.global_settings
    })

    if (addOperationInfo) {
      const updates = Object.entries(settings)
        .map(([key, value]) => `${key}=${value}`)
        .join(', ')
      addOperationInfo(`[PARAM] 全局参数已更新: ${updates}`)
    }

    return true
  }

  /**
   * 动态获取全局设置
   */
  const getDynamicGlobalSettings = () => {
    return algorithmConfig.value.global_settings
  }

  /**
   * 动态更新ROI参数
   */
  const updateROIParams = (roiId: string, params: any) => {
    if (!wsClient.value || !isConnected.value) {
      return false
    }

    // 更新本地配置
    if (!algorithmConfig.value.roi_detectors) {
      algorithmConfig.value.roi_detectors = {}
    }

    algorithmConfig.value.roi_detectors[roiId] = {
      ...algorithmConfig.value.roi_detectors[roiId],
      ...params
    }

    // 发送ROI参数更新消息
    updateConfig({
      roi_detectors: {
        [roiId]: algorithmConfig.value.roi_detectors[roiId]
      }
    })

    if (addOperationInfo) {
      addOperationInfo(`[PARAM] ROI ${roiId} 参数已更新`)
    }

    return true
  }

  /**
   * 动态获取ROI参数
   */
  const getDynamicROIParams = (roiId: string) => {
    return algorithmConfig.value.roi_detectors?.[roiId] || null
  }

  /**
   * 获取全局设置
   * 从后端获取当前的全局设置
   */
  const getGlobalSettings = async (): Promise<any> => {
    if (!wsClient.value || !isConnected.value) {
      return null
    }

    // 使用已有的updateConfig方法发送配置请求
    updateConfig({
      request_global_settings: true
    })

    // 注意：这里需要在WebSocket消息处理中处理返回的设置
    // 可以使用Promise和事件机制实现异步获取

    return null // 目前简单返回null，实际实现需要等待WebSocket响应
  }

  /**
   * 同步所有ROI配置到后端
   * @param roiList ROI列表
   * @param roiDetectors ROI检测器配置
   */
  const syncAllROIConfigs = (roiList: any[], roiDetectors: Record<string, any>) => {
    if (debugEnabled.value) {
      console.log('🔍 [DEBUG] syncAllROIConfigs调用:', {
        roiListLength: roiList.length,
        roiDetectorsKeys: Object.keys(roiDetectors),
        wsConnected: isConnected.value
      })
    }

    if (!wsClient.value || !isConnected.value) {
      addOperationInfo('[WARNING] WebSocket未连接，无法同步ROI配置')
      return false
    }

    if (!roiList || roiList.length === 0) {
      addOperationInfo('[ROI-SYNC] 没有ROI需要同步')
      return true
    }

    addOperationInfo(`[ROI-SYNC] 开始同步 ${roiList.length} 个ROI配置`)

    // 🔥 关键修复：详细检查每个ROI的数据完整性
    const validROIs = roiList.filter(roi => {
      const hasId = !!roi.roi_id
      const hasAttribute = !!roi.attribute
      const hasCoordinates = roi.coordinates && roi.coordinates.length > 0

      if (!hasId) {
        addOperationInfo(`[ROI-SYNC] ❌ ROI缺少ID字段: ${JSON.stringify(roi)}`)
        return false
      }
      if (!hasAttribute) {
        addOperationInfo(`[ROI-SYNC] ❌ ROI ${roi.roi_id} 缺少attribute字段`)
        return false
      }
      if (!hasCoordinates) {
        addOperationInfo(`[ROI-SYNC] ❌ ROI ${roi.roi_id} 缺少坐标数据`)
        return false
      }

      return true
    })

    if (validROIs.length !== roiList.length) {
      addOperationInfo(`[ROI-SYNC] ⚠️ 过滤后有效ROI数量: ${validROIs.length}/${roiList.length}`)
    }

    // 构建所有ROI的检测器配置
    const allROIDetectors: Record<string, any> = {}

    validROIs.forEach(roi => {
      const roiId = roi.roi_id  // 🔥 修复：使用正确的字段名
      const roiConfig = roiDetectors[roiId]

      if (debugEnabled.value) {
        console.log('🔍 [DEBUG] 处理ROI:', {
          roiId,
          roiName: roi.name,
          roiAttribute: roi.attribute,
          hasConfig: !!roiConfig,
          configKeys: roiConfig ? Object.keys(roiConfig) : [],
          coordinatesCount: roi.coordinates?.length || 0
        })
      }

      if (roiConfig && Object.keys(roiConfig).length > 0) {
        // 需要将新格式参数转换为后端期望的标准化格式
        let standardizedParams: any = {}

        // 确定算法类型和后端检测器类型
        let backendDetectorType = roiConfig.type
        if (roi.attribute === 'pailiao' && roiConfig.type === 'motion') {
          // 对于pailiao类型的ROI，需要根据运动检测算法确定具体的检测器类型
          const algorithm = roiConfig.motion_detection?.algorithm
          if (algorithm === 'background_subtraction') {
            backendDetectorType = 'background_subtraction'
          } else if (algorithm === 'frame_difference') {
            backendDetectorType = 'frame_difference'
          }
        }

        // 使用setRoiDetector的逻辑进行标准化处理
        if (backendDetectorType === 'direction') {
          // 方向检测算法参数 - 支持新格式和旧格式
          const motionDetection = roiConfig.motion_detection || roiConfig.前置背景检测 || {}
          const directionDetection = roiConfig.direction_detection || roiConfig.后置方向检测 || {}

          standardizedParams = {
            type: 'direction',
            detector_type: 'background_subtraction', // 方向检测内部使用的检测器类型
            // 前置背景检测参数
            motion_params: {
              minArea: motionDetection.minArea || 500,
              detectionThreshold: motionDetection.motionThreshold || 50,
              learningRate: motionDetection.backgroundUpdateRate || 0.01,
              shadowsThreshold: 0.5,
              enabled: motionDetection.enabled !== false  // 确保enabled状态正确传递
            },
            // 方向检测特有参数
            minDisplacement: directionDetection.minDisplacement || 2,
            maxPatience: directionDetection.maxPatience || 3,
            consecutiveThreshold: directionDetection.consecutiveDetectionThreshold || 3
          }
        } else if (backendDetectorType === 'background_subtraction') {
          // 背景减除法参数 - 支持新格式和旧格式
          const motionDetection = roiConfig.motion_detection || roiConfig.运动检测 || {}

          standardizedParams = {
            type: 'background_subtraction',
            minArea: motionDetection.minArea || 100,
            detectionThreshold: motionDetection.detectionThreshold || 40,
            learningRate: motionDetection.learningRate || 0.005,
            shadowsThreshold: motionDetection.shadowRemoval || 0.5
          }
        } else if (backendDetectorType === 'frame_difference') {
          // 帧差法参数 - 支持新格式和旧格式
          const motionDetection = roiConfig.motion_detection || roiConfig.运动检测 || {}

          standardizedParams = {
            type: 'frame_difference',
            minArea: motionDetection.minArea || 100,
            threshold: motionDetection.threshold || 30,
            frameInterval: motionDetection.frameInterval || 2
          }
        }

        allROIDetectors[roiId] = standardizedParams
        addOperationInfo(`[ROI-SYNC] ROI ${roiId} 使用已配置参数，转换为标准化格式`)
      } else {
        // 为没有配置的ROI设置默认配置（标准化格式）
        const defaultType = roi.attribute === 'yazhu' ? 'direction' : 'frame_difference'

        if (defaultType === 'direction') {
          // 方向检测默认配置（标准化格式）
          allROIDetectors[roiId] = {
            type: 'direction',
            detector_type: 'background_subtraction',
            motion_params: {
              enabled: true,
              minArea: 500,
              detectionThreshold: 50,
              learningRate: 0.01,
              shadowsThreshold: 0.5
            },
            minDisplacement: 2,
            maxPatience: 3,
            consecutiveThreshold: 3
          }
        } else {
          // 帧差法默认配置（标准化格式）
          allROIDetectors[roiId] = {
            type: 'frame_difference',
            minArea: 300,
            threshold: 30,
            frameInterval: 2
          }
        }

        addOperationInfo(`[ROI-SYNC] ROI ${roiId} (${roi.attribute}) 使用默认${defaultType}配置（标准化格式）`)
      }
    })

    // 只发送ROI配置，不发送其他参数
    const configToSend = {
      roi_detectors: allROIDetectors
    }

    addOperationInfo(`[ROI-SYNC] 准备同步${Object.keys(allROIDetectors).length}个ROI配置:`)
    Object.entries(allROIDetectors).forEach(([roiId, config]) => {
      addOperationInfo(`[ROI-SYNC] - ROI ${roiId}: ${JSON.stringify(config)}`)
    })

    // 🔥 关键修复：添加错误处理
    try {
      wsClient.value.updateConfig(configToSend)
      addOperationInfo(`[ROI-SYNC] ✅ 已同步${Object.keys(allROIDetectors).length}个ROI配置到后端`)
      return true
    } catch (error) {
      addOperationInfo(`[ROI-SYNC] ❌ 同步ROI配置失败: ${error}`)
      if (debugEnabled.value) {
        console.error('ROI配置同步失败:', error)
      }
      return false
    }
  }
  
  // 组件卸载时清理资源
  onUnmounted(() => {
    stopDetection()
    
    if (wsClient.value) {
      wsClient.value.disconnect()
    }
  })

  // 返回可用函数
  return {
    isDetectionActive,
    isConnected,
    detectionResult,
    detectionStats,
    currentDetectionMode,
    currentDetectionAlgorithm,
    algorithmConfig,
    startDetection,
    stopDetection,
    setDetectionMode,
    setDetectionAlgorithm,
    updateConfig,
    setRoiDetector,
    sendMessageToServer,
    updateGlobalSettings,
    getGlobalSettings,
    syncAllROIConfigs,
    // ROI控制相关
    getROIActiveStatus,
    setROIActiveStatus,
    roiActiveStates,
    // 动态参数管理
    getDynamicGlobalSettings,
    updateROIParams,
    getDynamicROIParams
  }
}
import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 15000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    // 直接返回响应数据，不包含axios的响应结构
    return response.data
  },
  error => {
    // 错误处理
    const status = error.response?.status
    const message = error.response?.data?.detail || error.message || '请求失败'
    
    // 401未授权，清除token并跳转到登录页
    if (status === 401) {
      localStorage.removeItem('token')
      if (router.currentRoute.value.path !== '/login') {
        ElMessage.error('登录已过期，请重新登录')
        router.push({
          path: '/login',
          query: { redirect: router.currentRoute.value.fullPath }
        })
      }
    } else if (status === 403) {
      ElMessage.error('权限不足，无法访问')
    } else if (status === 404) {
      ElMessage.error('请求的资源不存在')
    } else if (status === 500) {
      ElMessage.error('服务器错误，请稍后再试')
    } else {
      ElMessage.error(message)
    }
    
    return Promise.reject(error)
  }
)

// 定义泛型API函数
export const get = <T = any>(url: string, params?: any, config?: any): Promise<T> => {
  return api.get(url, { params, ...config })
}

export const post = <T = any>(url: string, data?: any, config?: any): Promise<T> => {
  return api.post(url, data, config)
}

export const put = <T = any>(url: string, data?: any, config?: any): Promise<T> => {
  return api.put(url, data, config)
}

export const del = <T = any>(url: string, config?: any): Promise<T> => {
  return api.delete(url, config)
}

export default api 
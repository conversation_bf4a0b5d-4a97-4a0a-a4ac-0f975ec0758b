import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'

// 解析Cookie的辅助函数
function getCookie(cookieString: string, name: string): string | null {
  const value = `; ${cookieString}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
}

// https://vite.dev/config/
export default defineConfig({
  build: {
    sourcemap: false,
  },
  plugins: [
    vue(),
    vueJsx(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    host: '0.0.0.0', // 允许外部网络访问
    port: 5173,
    // 根据WebSDK编程指南：https下需要开启跨域隔离头
    // 但在开发环境下，这些头可能会阻止WebSDK正常工作
    // 暂时注释掉，如果需要HTTPS环境再启用
    // headers: {
    //   'Cross-Origin-Embedder-Policy': 'require-corp',
    //   'Cross-Origin-Opener-Policy': 'same-origin'
    // },
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
      // WebSDK ISAPI代理 - 动态代理到当前登录的设备
      '/ISAPI': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // 从Cookie中获取当前设备信息
            const cookies = req.headers.cookie || '';
            const ip = getCookie(cookies, 'websdk_ip') || '************'; // 默认IP
            const port = getCookie(cookies, 'websdk_port') || '80';     // 默认端口
            
            // 动态重写路径
            const newPath = `/api/websdk-proxy/${ip}/${port}${proxyReq.path}`;
            
            console.log(`[Vite Proxy] 重写ISAPI路径: ${proxyReq.path} -> ${newPath}`);
            
            proxyReq.path = newPath;
          });
        }
      }
    }
  }
})

<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>每日检测统计详情</h3>
        <button class="close-btn" @click="$emit('close')">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      
      <div class="modal-body">
        <!-- 日期筛选器 -->
        <div class="date-filter">
          <label for="date-picker">选择日期：</label>
          <input 
            id="date-picker"
            type="date" 
            v-model="selectedDate" 
            @change="fetchStatistics"
            class="date-input"
          />
          <button @click="refreshData" class="refresh-btn" :disabled="loading">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="23 4 23 10 17 10"></polyline>
              <polyline points="1 20 1 14 7 14"></polyline>
              <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
            </svg>
            刷新
          </button>
          <button @click="exportData" class="export-btn" :disabled="loading || !statistics">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
            导出
          </button>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="spinner"></div>
          <p>正在加载统计数据...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-state">
          <p>{{ error }}</p>
          <button @click="fetchStatistics" class="retry-btn">重试</button>
        </div>

        <!-- 统计数据展示 -->
        <div v-else-if="statistics" class="statistics-content">
          <div class="summary-info">
            <h4>{{ statistics.date }} 统计概览</h4>
            <div class="summary-cards">
              <div class="summary-card">
                <span class="label">检测模板数</span>
                <span class="value">{{ statistics.templates.length }}</span>
              </div>
              <div class="summary-card">
                <span class="label">总检测次数</span>
                <span class="value">{{ totalDetections }}</span>
              </div>
              <div class="summary-card">
                <span class="label">总卡料次数</span>
                <span class="value">{{ totalJamDetections }}</span>
              </div>
              <div class="summary-card">
                <span class="label">整体成功率</span>
                <span class="value">{{ overallSuccessRate }}%</span>
              </div>
            </div>
          </div>

          <!-- 详细统计数据 -->
          <div class="detailed-statistics">
            <div v-for="template in statistics.templates" :key="template.template_id" class="template-section">
              <div class="template-header" @click="toggleTemplate(template.template_id)">
                <h5>
                  <svg 
                    :class="['expand-icon', { expanded: expandedTemplates.includes(template.template_id) }]" 
                    width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                  >
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                  {{ template.template_name }} (ID: {{ template.template_id }})
                </h5>
                <div class="template-stats">
                  <span>检测: {{ template.total_detections }}</span>
                  <span>卡料: {{ template.jam_detections }}</span>
                  <span>成功率: {{ (template.success_rate * 100).toFixed(2) }}%</span>
                </div>
              </div>
              
              <div v-if="expandedTemplates.includes(template.template_id)" class="template-details">
                <div v-for="dieCaster in template.die_casters" :key="dieCaster.die_caster_id" class="die-caster-section">
                  <div class="die-caster-header" @click="toggleDieCaster(dieCaster.die_caster_id)">
                    <h6>
                      <svg 
                        :class="['expand-icon', { expanded: expandedDieCasters.includes(dieCaster.die_caster_id) }]" 
                        width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                      >
                        <polyline points="9 18 15 12 9 6"></polyline>
                      </svg>
                      {{ dieCaster.die_caster_name }} (ID: {{ dieCaster.die_caster_id }})
                    </h6>
                    <div class="die-caster-stats">
                      <span>检测: {{ dieCaster.total_detections }}</span>
                      <span>卡料: {{ dieCaster.jam_detections }}</span>
                      <span>成功率: {{ (dieCaster.success_rate * 100).toFixed(2) }}%</span>
                    </div>
                  </div>
                  
                  <div v-if="expandedDieCasters.includes(dieCaster.die_caster_id)" class="detection-groups">
                    <div v-for="group in dieCaster.detection_groups" :key="group.group_id" class="group-item">
                      <div class="group-info">
                        <span class="group-name">{{ group.group_name }} (ID: {{ group.group_id }})</span>
                        <div class="group-stats">
                          <span>检测: {{ group.total_detections }}</span>
                          <span>卡料: {{ group.jam_detections }}</span>
                          <span>成功率: {{ (group.success_rate * 100).toFixed(2) }}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 无数据状态 -->
        <div v-else class="no-data-state">
          <p>暂无统计数据</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { DailyDetectionStatistics } from '@/types/daily-statistics'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'close'): void
}

const props = defineProps<Props>()
defineEmits<Emits>()

// 响应式数据
const selectedDate = ref(new Date().toISOString().split('T')[0])
const statistics = ref<DailyDetectionStatistics | null>(null)
const loading = ref(false)
const error = ref('')
const expandedTemplates = ref<number[]>([])
const expandedDieCasters = ref<number[]>([])

// 计算属性
const totalDetections = computed(() => {
  if (!statistics.value) return 0
  return statistics.value.templates.reduce((sum, template) => sum + template.total_detections, 0)
})

const totalJamDetections = computed(() => {
  if (!statistics.value) return 0
  return statistics.value.templates.reduce((sum, template) => sum + template.jam_detections, 0)
})

const overallSuccessRate = computed(() => {
  if (!statistics.value || totalDetections.value === 0) return '0.00'
  const rate = ((totalDetections.value - totalJamDetections.value) / totalDetections.value) * 100
  return rate.toFixed(2)
})

// 方法
const fetchStatistics = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const response = await fetch(`/api/card-detection/daily-statistics?date=${selectedDate.value}`)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    statistics.value = data
  } catch (err) {
    error.value = err instanceof Error ? err.message : '获取统计数据失败'
    statistics.value = null
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  fetchStatistics()
}

// 导出数据为CSV格式
const exportData = () => {
  if (!statistics.value) {
    return
  }

  try {
    // 准备CSV数据
    const csvData = []
    
    // 添加标题行
    csvData.push([
      '日期',
      '模板名称',
      '模板ID',
      '压铸机名称',
      '压铸机ID',
      '检测组名称',
      '检测组ID',
      '总检测次数',
      '卡料次数',
      '正常次数',
      '成功率(%)'
    ])

    // 添加数据行
    statistics.value.templates.forEach(template => {
      template.die_casters.forEach(dieCaster => {
        dieCaster.detection_groups.forEach(group => {
          csvData.push([
            statistics.value!.date,
            template.template_name,
            template.template_id.toString(),
            dieCaster.die_caster_name,
            dieCaster.die_caster_id.toString(),
            group.group_name,
            group.group_id.toString(),
            group.total_detections.toString(),
            group.jam_detections.toString(),
            (group.total_detections - group.jam_detections).toString(),
            (group.success_rate * 100).toFixed(2)
          ])
        })
      })
    })

    // 转换为CSV字符串
    const csvContent = csvData.map(row => 
      row.map(field => `"${field}"`).join(',')
    ).join('\n')

    // 添加BOM以支持中文
    const BOM = '\uFEFF'
    const csvWithBOM = BOM + csvContent

    // 创建下载链接
    const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    
    link.setAttribute('href', url)
    link.setAttribute('download', `检测统计_${statistics.value.date}.csv`)
    link.style.visibility = 'hidden'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
  } catch (err) {
    console.error('导出数据失败:', err)
    // 这里可以添加错误提示
  }
}

const toggleTemplate = (templateId: number) => {
  const index = expandedTemplates.value.indexOf(templateId)
  if (index > -1) {
    expandedTemplates.value.splice(index, 1)
  } else {
    expandedTemplates.value.push(templateId)
  }
}

const toggleDieCaster = (dieCasterId: number) => {
  const index = expandedDieCasters.value.indexOf(dieCasterId)
  if (index > -1) {
    expandedDieCasters.value.splice(index, 1)
  } else {
    expandedDieCasters.value.push(dieCasterId)
  }
}

const handleOverlayClick = () => {
  // 点击遮罩层关闭弹窗
}

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    fetchStatistics()
  }
})

// 组件挂载时获取数据
onMounted(() => {
  if (props.visible) {
    fetchStatistics()
  }
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--el-bg-color);
  border-radius: 6px;
  width: 90vw;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--el-border-color);
  box-shadow: var(--el-box-shadow-dark);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: var(--el-text-color-regular);
  transition: all 0.2s;
}

.close-btn:hover {
  background-color: var(--el-fill-color);
  color: var(--el-text-color-primary);
}

.modal-body {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
}

.date-filter label {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 12px;
}

.date-input {
  padding: 6px 8px;
  border: 1px solid var(--el-border-color);
  border-radius: 3px;
  font-size: 12px;
  background: var(--el-bg-color);
  color: var(--el-text-color-primary);
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  background-color: var(--el-color-primary);
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  background-color: var(--el-color-primary-dark-2);
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  background-color: var(--el-color-success);
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.export-btn:hover:not(:disabled) {
  background-color: var(--el-color-success-dark-2);
}

.export-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  color: var(--el-text-color-regular);
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--el-fill-color);
  border-top: 2px solid var(--el-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state {
  text-align: center;
  padding: 32px;
  color: var(--el-color-danger);
}

.retry-btn {
  margin-top: 8px;
  padding: 6px 12px;
  background-color: var(--el-color-danger);
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.no-data-state {
  text-align: center;
  padding: 32px;
  color: var(--el-text-color-regular);
}

.summary-info {
  margin-bottom: 16px;
}

.summary-info h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 12px;
}

.summary-card {
  background-color: var(--el-fill-color-lighter);
  padding: 12px;
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-card .label {
  font-size: 10px;
  color: var(--el-text-color-regular);
  margin-bottom: 2px;
}

.summary-card .value {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.detailed-statistics {
  space-y: 12px;
}

.template-section {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  margin-bottom: 12px;
}

.template-header {
  padding: 12px;
  background-color: var(--el-fill-color-lighter);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s;
}

.template-header:hover {
  background-color: var(--el-fill-color);
}

.template-header h5 {
  margin: 0;
  font-size: 13px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 6px;
}

.expand-icon {
  transition: transform 0.2s;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.template-stats {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: var(--el-text-color-regular);
}

.template-details {
  padding: 0 12px 12px 12px;
}

.die-caster-section {
  margin-left: 16px;
  margin-bottom: 8px;
  border-left: 2px solid var(--el-border-color-lighter);
  padding-left: 12px;
}

.die-caster-header {
  padding: 8px;
  background-color: var(--el-bg-color);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 3px;
  transition: background-color 0.2s;
  border: 1px solid var(--el-border-color-lighter);
}

.die-caster-header:hover {
  background-color: var(--el-fill-color-lighter);
}

.die-caster-header h6 {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 4px;
}

.die-caster-stats {
  display: flex;
  gap: 8px;
  font-size: 10px;
  color: var(--el-text-color-regular);
}

.detection-groups {
  margin-top: 6px;
  margin-left: 16px;
}

.group-item {
  padding: 6px 8px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 3px;
  margin-bottom: 4px;
  border-left: 2px solid var(--el-color-primary);
  border: 1px solid var(--el-border-color-lighter);
}

.group-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-name {
  font-size: 11px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.group-stats {
  display: flex;
  gap: 6px;
  font-size: 9px;
  color: var(--el-text-color-regular);
}

/* 滚动条样式 */
.modal-body::-webkit-scrollbar {
  width: 3px;
}

.modal-body::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
}

.modal-body::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 2px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 暗色模式支持 */
.dark-theme .modal-content {
  background: var(--bg-color-soft);
  border-color: var(--border-color);
}

.dark-theme .modal-header {
  border-bottom-color: var(--border-color);
}

.dark-theme .date-filter {
  background-color: var(--bg-color-mute);
  border-color: var(--border-color);
}

.dark-theme .date-input {
  background-color: var(--bg-color-mute);
  border-color: var(--border-color);
  color: var(--text-color);
}

.dark-theme .template-header {
  background-color: var(--bg-color-mute);
}

.dark-theme .template-header:hover {
  background-color: var(--bg-color-hover);
}

.dark-theme .summary-card {
  background-color: var(--bg-color-mute);
  border-color: var(--border-color);
}

.dark-theme .die-caster-header {
  background-color: var(--bg-color-mute);
  border-color: var(--border-color);
}

.dark-theme .die-caster-header:hover {
  background-color: var(--bg-color-hover);
}

.dark-theme .group-item {
  background-color: var(--bg-color-mute);
  border-color: var(--border-color);
  border-left-color: var(--primary-color);
}
</style>
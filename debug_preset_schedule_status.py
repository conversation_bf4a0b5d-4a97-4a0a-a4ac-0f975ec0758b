#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试预设计划状态显示问题
检查后端API返回的数据结构和状态字段
"""

import requests
import json
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8000"
LOGIN_URL = f"{BASE_URL}/api/auth/login/json/"
SCHEDULES_URL = f"{BASE_URL}/api/preset-schedules/"

def login():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "123456"
    }
    
    try:
        response = requests.post(LOGIN_URL, json=login_data)
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print(f"✅ 登录成功，token: {token[:20]}...")
            return token
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def get_preset_schedules(token):
    """获取预设计划列表"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(SCHEDULES_URL, headers=headers)
        print(f"\n📡 API请求: GET {SCHEDULES_URL}")
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📦 响应数据类型: {type(data)}")
            print(f"📝 数据长度: {len(data) if isinstance(data, list) else 'N/A'}")
            
            if isinstance(data, list) and len(data) > 0:
                print("\n🔍 分析第一个预设计划的数据结构:")
                first_schedule = data[0]
                print(json.dumps(first_schedule, indent=2, ensure_ascii=False))
                
                # 检查状态字段
                print("\n🏷️ 状态字段分析:")
                if 'status' in first_schedule:
                    print(f"  - status: {first_schedule['status']}")
                if 'executionStatus' in first_schedule:
                    print(f"  - executionStatus: {first_schedule['executionStatus']}")
                
                # 检查所有计划的状态值
                print("\n📋 所有计划的状态值:")
                status_values = set()
                execution_status_values = set()
                
                for i, schedule in enumerate(data):
                    print(f"  计划 {i+1}: {schedule.get('name', 'Unknown')}")
                    if 'status' in schedule:
                        status_val = schedule['status']
                        status_values.add(status_val)
                        print(f"    - status: {status_val}")
                    if 'executionStatus' in schedule:
                        exec_status_val = schedule['executionStatus']
                        execution_status_values.add(exec_status_val)
                        print(f"    - executionStatus: {exec_status_val}")
                
                print(f"\n📊 发现的 status 值: {list(status_values)}")
                print(f"📊 发现的 executionStatus 值: {list(execution_status_values)}")
                
                # 模拟前端状态映射
                print("\n🎯 前端状态映射测试:")
                for status_val in status_values:
                    mapped_text = get_status_text_simulation(status_val)
                    print(f"  {status_val} -> {mapped_text}")
                
                for exec_status_val in execution_status_values:
                    mapped_text = get_status_text_simulation(exec_status_val)
                    print(f"  {exec_status_val} -> {mapped_text}")
            else:
                print("📭 没有预设计划数据")
                
        else:
            print(f"❌ 获取预设计划失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def get_status_text_simulation(status):
    """模拟前端的 getStatusText 函数"""
    status_map = {
        'pending': '待执行',
        'active': '执行中',
        'completed': '已完成',
        'failed': '执行失败',
        'running': '执行中',  # 添加可能缺失的映射
        'stopped': '已停止'   # 添加可能缺失的映射
    }
    return status_map.get(status, '未知')

def main():
    print("🔍 开始调试预设计划状态显示问题...")
    print(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 登录
    token = login()
    if not token:
        return
    
    # 获取预设计划数据
    get_preset_schedules(token)
    
    print("\n💡 可能的问题原因:")
    print("1. 后端返回的字段名与前端期望不一致 (status vs executionStatus)")
    print("2. 前端 getStatusText 函数缺少某些状态值的映射")
    print("3. 数据绑定时使用了错误的字段名")
    print("\n🔧 建议检查:")
    print("1. 确认后端返回的状态字段名称")
    print("2. 更新前端 getStatusText 函数以处理所有可能的状态值")
    print("3. 确保模板中使用正确的字段名 (row.status 或 row.executionStatus)")

if __name__ == "__main__":
    main()
<template>
  <div class="preset-schedule-form">
    <el-form 
      ref="formRef" 
      :model="formData" 
      :rules="formRules" 
      label-width="90px"
      @submit.prevent="handleSubmit"
    >
      <!-- 基本信息和时间设置 - 横向布局 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8">
            <el-form-item label="计划名称" prop="name">
              <el-input 
                v-model="formData.name" 
                placeholder="请输入计划名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8">
            <el-form-item label="执行日期" prop="date">
              <el-date-picker
                v-model="formData.date"
                type="date"
                placeholder="选择执行日期"
                style="width: 100%"
                :disabled-date="disabledDate"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="8">
            <el-form-item label="检测模板" prop="templateId">
              <el-select 
                v-model="formData.templateId" 
                placeholder="请选择检测模板"
                style="width: 100%"
                filterable
                :loading="templatesLoading"
              >
                <el-option
                  v-for="template in availableTemplates"
                  :key="template.id"
                  :label="template.name"
                  :value="template.id"
                >
                  <div class="template-option">
                    <span class="template-name">{{ template.name }}</span>
                    <span class="template-desc">{{ template.description || '无描述' }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="8" :md="6">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker
                v-model="formData.startTime"
                placeholder="开始时间"
                style="width: 100%"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="6">
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker
                v-model="formData.endTime"
                placeholder="结束时间"
                style="width: 100%"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="12">
            <el-form-item label="描述" prop="description">
              <el-input 
                v-model="formData.description" 
                placeholder="请输入计划描述（可选）"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row v-if="timeConflictMessage">
          <el-col :span="24">
            <el-alert
              :title="timeConflictMessage"
              type="warning"
              :closable="false"
              show-icon
            />
          </el-col>
        </el-row>
      </div>

      <!-- 模板预览和高级设置 - 横向布局 -->
      <div class="form-section">
        <h3 class="section-title">设置选项</h3>
        
        <el-row :gutter="20">
          <!-- 模板预览 -->
          <el-col :xs="24" :sm="24" :md="14" v-if="selectedTemplate">
            <div class="template-preview-compact">
              <div class="preview-header">
                <span class="preview-title">模板预览</span>
              </div>
              <div class="template-info-grid">
                <div class="info-item">
                  <span class="label">模板:</span>
                  <span class="value">{{ selectedTemplate.name }}</span>
                </div>
                <div class="info-item">
                  <span class="label">ROI:</span>
                  <span class="value">{{ selectedTemplate.roiCount || 0 }} 个</span>
                </div>
                <div class="info-item">
                  <span class="label">创建:</span>
                  <span class="value">{{ formatDate(selectedTemplate.createdAt) }}</span>
                </div>
                <div v-if="selectedTemplate.description" class="info-item full-width">
                  <span class="label">描述:</span>
                  <span class="value">{{ selectedTemplate.description }}</span>
                </div>
              </div>
            </div>
          </el-col>
          
          <!-- 高级设置 -->
          <el-col :xs="24" :sm="24" :md="10">
            <div class="advanced-settings">
              <div class="settings-grid">
                <div class="setting-item">
                  <el-form-item label="自动启用">
                    <el-switch 
                      v-model="formData.isEnabled" 
                      active-text="启用" 
                      inactive-text="禁用"
                      size="small"
                    />
                  </el-form-item>
                </div>
                
                <div class="setting-item">
                  <el-form-item label="执行确认">
                    <el-switch 
                      v-model="formData.requireConfirmation" 
                      active-text="需要" 
                      inactive-text="自动"
                      size="small"
                    />
                  </el-form-item>
                </div>
                
                <div class="setting-item">
                  <el-form-item label="重试次数">
                    <el-input-number 
                      v-model="formData.retryCount" 
                      :min="0" 
                      :max="5" 
                      controls-position="right"
                      size="small"
                      style="width: 100px"
                    />
                  </el-form-item>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          {{ schedule ? '更新计划' : '创建计划' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { PresetSchedule, PresetScheduleCreate } from '@/types/preset-schedule'
import { getDetectionTemplates } from '@/api/detection-templates'
import { checkScheduleConflict } from '@/api/preset-schedules'

// Props
interface Props {
  schedule?: PresetSchedule | null
}

const props = withDefaults(defineProps<Props>(), {
  schedule: null
})

// Emits
interface Emits {
  submit: [data: PresetScheduleCreate]
  cancel: []
}

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)
const templatesLoading = ref(false)
const availableTemplates = ref<any[]>([])
const timeConflictMessage = ref('')

// 表单数据
const formData = ref<PresetScheduleCreate>({
  name: '',
  description: '',
  date: '',
  startTime: '',
  endTime: '',
  templateId: '',
  isEnabled: true,
  requireConfirmation: false,
  retryCount: 1
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入计划名称', trigger: 'blur' },
    { min: 2, max: 50, message: '计划名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  date: [
    { required: true, message: '请选择执行日期', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' }
  ],
  templateId: [
    { required: true, message: '请选择检测模板', trigger: 'change' }
  ]
}

// 计算属性
const selectedTemplate = computed(() => {
  return availableTemplates.value.find(t => t.id === formData.value.templateId)
})

// 验证结束时间
function validateEndTime(rule: any, value: string, callback: Function) {
  if (!value) {
    callback(new Error('请选择结束时间'))
    return
  }
  
  if (!formData.value.startTime) {
    callback()
    return
  }
  
  if (value <= formData.value.startTime) {
    callback(new Error('结束时间必须晚于开始时间'))
    return
  }
  
  callback()
}

// 禁用过去的日期
const disabledDate = (date: Date) => {
  return date < new Date(new Date().setHours(0, 0, 0, 0))
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 加载检测模板
const loadTemplates = async () => {
  try {
    templatesLoading.value = true
    availableTemplates.value = await getDetectionTemplates()
  } catch (error) {
    console.error('加载检测模板失败:', error)
    ElMessage.error('加载检测模板失败')
  } finally {
    templatesLoading.value = false
  }
}

// 检查时间冲突
const checkTimeConflict = async () => {
  if (!formData.value.date || !formData.value.startTime || !formData.value.endTime) {
    timeConflictMessage.value = ''
    return
  }
  
  try {
    const result = await checkScheduleConflict({
      date: formData.value.date,
      startTime: formData.value.startTime,
      endTime: formData.value.endTime,
      excludeId: props.schedule?.id
    })
    
    timeConflictMessage.value = result.hasConflict ? result.message : ''
  } catch (error) {
    console.error('检查时间冲突失败:', error)
  }
}

// 监听时间变化，检查冲突
watch(
  () => [formData.value.date, formData.value.startTime, formData.value.endTime],
  () => {
    checkTimeConflict()
  },
  { deep: true }
)

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (timeConflictMessage.value) {
      ElMessage.warning('存在时间冲突，请调整时间设置')
      return
    }
    
    submitting.value = true
    emit('submit', { ...formData.value })
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}

// 初始化表单数据
const initFormData = () => {
  if (props.schedule) {
    formData.value = {
      name: props.schedule.name,
      description: props.schedule.description || '',
      date: props.schedule.date,
      startTime: props.schedule.startTime,
      endTime: props.schedule.endTime,
      templateId: props.schedule.templateId,
      isEnabled: props.schedule.isEnabled,
      requireConfirmation: props.schedule.requireConfirmation || false,
      retryCount: props.schedule.retryCount || 1
    }
  } else {
    // 重置为默认值
    formData.value = {
      name: '',
      description: '',
      date: '',
      startTime: '',
      endTime: '',
      templateId: '',
      isEnabled: true,
      requireConfirmation: false,
      retryCount: 1
    }
  }
}

// 监听 props 变化
watch(
  () => props.schedule,
  () => {
    initFormData()
  },
  { immediate: true }
)

// 组件挂载时加载数据
onMounted(() => {
  loadTemplates()
})
</script>

<style scoped>
.preset-schedule-form {
  max-width: 100%;
}

.form-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.form-section:last-of-type {
  border-bottom: none;
  margin-bottom: 16px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 15px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 3px;
  height: 14px;
  background: var(--el-color-primary);
  margin-right: 8px;
  border-radius: 2px;
}

.template-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.template-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.template-desc {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 紧凑型模板预览 */
.template-preview-compact {
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
  padding: 12px;
  border: 1px solid var(--el-border-color-light);
}

.preview-header {
  margin-bottom: 8px;
}

.preview-title {
  font-size: 13px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.template-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  font-size: 12px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item .label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  font-size: 11px;
}

.info-item .value {
  color: var(--el-text-color-primary);
  font-size: 12px;
  word-break: break-all;
}

/* 高级设置 */
.advanced-settings {
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
  padding: 12px;
  border: 1px solid var(--el-border-color-light);
}

.settings-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.setting-item {
  display: flex;
  align-items: center;
}

.setting-item .el-form-item {
  margin-bottom: 0;
  flex: 1;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 表单项间距调整 */
.el-form-item {
  margin-bottom: 16px;
}

/* 时间选择器样式 */
.el-time-picker {
  width: 100%;
}

/* 警告信息样式 */
.el-alert {
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .template-info-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .form-actions .el-button {
    width: 100%;
  }
  
  .template-info-grid {
    grid-template-columns: 1fr;
  }
  
  .settings-grid {
    gap: 8px;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 576px) {
  .section-title {
    font-size: 14px;
  }
  
  .el-form-item {
    margin-bottom: 12px;
  }
  
  .form-section {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
}
</style>
<template>
  <div class="roi-config-test">
    <!-- 视频源配置区域 -->
    <div class="config-section">
      <el-card class="config-card">
        <template #header>
          <div class="card-header">
            <span>视频源配置</span>
            <el-button
              type="primary"
              size="small"
              @click="startPreview"
              :loading="isConnecting"
              :disabled="!canStartPreview"
            >
              {{ isPreviewActive ? '停止预览' : '开始预览' }}
            </el-button>
          </div>
        </template>

        <el-form :model="videoConfig" label-width="100px" size="small">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="视频源">
                <el-select
                  v-model="videoConfig.sourceId"
                  placeholder="请选择视频源"
                  style="width: 100%"
                  @change="onVideoSourceChange"
                >
                  <el-option
                    v-for="source in videoSources"
                    :key="source.id"
                    :label="source.name"
                    :value="source.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="通道号">
                <el-input-number
                  v-model="videoConfig.channel"
                  :min="1"
                  :max="64"
                  placeholder="通道号"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="码流类型">
                <el-select v-model="videoConfig.streamType" style="width: 100%">
                  <el-option label="主码流" value="1" />
                  <el-option label="子码流" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="协议">
                <el-select v-model="videoConfig.protocol" style="width: 100%">
                  <el-option label="HTTP" :value="1" />
                  <el-option label="HTTPS" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>

    <!-- ROI配置和视频预览区域 -->
    <div class="roi-section">
      <el-row :gutter="20">
        <!-- 视频显示和ROI绘制区域 -->
        <el-col :span="16">
          <el-card class="video-card">
            <template #header>
              <div class="card-header">
                <span>视频预览与ROI配置</span>
                <div class="video-controls">
                  <el-button
                    type="info"
                    size="small"
                    @click="toggleSound"
                    :disabled="!isPreviewActive"
                  >
                    {{ soundEnabled ? '关闭声音' : '开启声音' }}
                  </el-button>
                </div>
              </div>
            </template>
            
            <div class="video-container" :style="videoContainerStyle">
              <div
                id="divPlugin"
                ref="divPlugin"
                class="video-plugin"
                :style="{ width: '100%', height: '100%' }"
              >
                <div v-if="!isPreviewActive" class="video-placeholder">
                  <el-icon size="64" color="#ccc"><VideoCamera /></el-icon>
                  <p>{{ isLoggedIn ? '点击开始预览按钮开始视频预览' : '请先登录设备' }}</p>
                </div>
                
                <!-- ROI绘制层 - Canvas -->
                <canvas
                  ref="roiCanvas"
                  class="roi-canvas"
                  :width="640"
                  :height="360"
                  :style="{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    pointerEvents: 'auto',
                    cursor: isDrawing ? 'crosshair' : 'default',
                    zIndex: 10
                  }"
                ></canvas>
              </div>
            </div>
            
            <!-- ROI绘制控制面板 -->
            <div class="roi-controls">
              <div class="roi-type-selector">
                <span>ROI类型：</span>
                <el-radio-group v-model="currentROIType" size="small">
                  <el-radio-button label="yazhu">压铸机</el-radio-button>
                  <el-radio-button label="pailiao">排料口</el-radio-button>
                </el-radio-group>
              </div>

              <div class="roi-shape-controls">
                <el-button
                  type="primary"
                  size="small"
                  @click="startDrawingROI('Rectangle')"
                  :disabled="isDrawing"
                >
                  绘制矩形
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  @click="startDrawingROI('Polygon')"
                  :disabled="isDrawing"
                >
                  绘制多边形
                </el-button>
                <el-button
                  v-if="isDrawing"
                  type="success"
                  size="small"
                  @click="completeDrawing"
                  :disabled="!canCompleteDrawing"
                >
                  完成绘制
                </el-button>
                <el-button
                  v-if="isDrawing"
                  type="warning"
                  size="small"
                  @click="cancelDrawing"
                >
                  取消绘制
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="clearAllROI"
                  :disabled="roiList.length === 0"
                >
                  清空ROI
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- ROI管理和状态区域 -->
        <el-col :span="8">
          <!-- ROI列表 -->
          <el-card class="roi-list-card">
            <template #header>
              <div class="card-header">
                <span>ROI列表 ({{ roiStats.total }}个)</span>
                <div>
                  <el-button size="small" @click="exportROI">导出</el-button>
                  <el-button size="small" @click="showImportDialog = true">导入</el-button>
                </div>
              </div>
            </template>

            <div class="roi-stats">
              <div class="stat-item">
                <span class="stat-label">压铸机:</span>
                <el-tag type="danger" size="small">{{ roiStats.yazhu }}个</el-tag>
              </div>
              <div class="stat-item">
                <span class="stat-label">排料口:</span>
                <el-tag type="info" size="small">{{ roiStats.pailiao }}个</el-tag>
              </div>
            </div>

            <div class="roi-list">
              <div
                v-for="roi in roiList"
                :key="roi.roi_id"
                class="roi-item"
                :class="{ 'roi-item-selected': selectedROI?.roi_id === roi.roi_id }"
                @click="selectROI(roi)"
              >
                <div class="roi-item-header">
                  <div class="roi-name">
                    <span
                      class="roi-color-indicator"
                      :style="{ backgroundColor: getROIColor(roi.roi_type) }"
                    ></span>
                    {{ roi.name }}
                  </div>
                  <div class="roi-actions">
                    <el-button
                      type="primary"
                      size="small"
                      @click.stop="editROI(roi)"
                    >
                      编辑
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click.stop="deleteROI(roi.roi_id)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
                <div class="roi-item-info">
                  <span class="roi-type">{{ roi.attribute === 'yazhu' ? '压铸机' : '排料口' }}</span>
                  <span class="roi-shape">{{ roi.roi_type === 'rectangle' ? '矩形' : '多边形' }}</span>
                  <span class="roi-points">{{ roi.coordinates.length }}个点</span>
                </div>
              </div>

              <div v-if="roiList.length === 0" class="no-roi">
                暂无ROI配置
              </div>
            </div>
          </el-card>

          <!-- 系统状态 -->
          <el-card class="info-card" style="margin-top: 20px;">
            <template #header>
              <span>系统状态</span>
            </template>

            <div class="status-info">
              <div class="status-item">
                <span class="label">视频源:</span>
                <el-tag :type="selectedVideoSource ? 'success' : 'info'" size="small">
                  {{ selectedVideoSource ? selectedVideoSource.name : '未选择' }}
                </el-tag>
              </div>

              <div class="status-item">
                <span class="label">预览状态:</span>
                <el-tag :type="isPreviewActive ? 'success' : 'info'" size="small">
                  {{ isPreviewActive ? '预览中' : '未预览' }}
                </el-tag>
              </div>

              <div class="status-item">
                <span class="label">声音状态:</span>
                <el-tag :type="soundEnabled ? 'success' : 'info'" size="small">
                  {{ soundEnabled ? '已开启' : '已关闭' }}
                </el-tag>
              </div>
            </div>
          </el-card>

          <!-- ROI参数配置 -->
          <el-card v-if="selectedROI" class="roi-params-card" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>ROI参数配置 - {{ selectedROI.name }}</span>
                <el-tag :type="selectedROI.roi_type === 'yazhu' ? 'danger' : 'info'" size="small">
                  {{ selectedROI.roi_type === 'yazhu' ? '压铸机' : '排料口' }}
                </el-tag>
              </div>
            </template>

            <div class="roi-params-content">
              <!-- 基础参数 -->
              <div class="param-section">
                <h4>基础参数</h4>
                <el-form :model="selectedROI" label-width="120px" size="small">
                  <el-form-item label="ROI名称">
                    <el-input v-model="selectedROI.name" placeholder="请输入ROI名称" />
                  </el-form-item>
                  <el-form-item label="检测阈值">
                    <el-slider
                      v-model="selectedROI.params.threshold"
                      :min="0"
                      :max="1"
                      :step="0.01"
                      show-input
                      :format-tooltip="(val) => `${(val * 100).toFixed(0)}%`"
                    />
                  </el-form-item>
                  <el-form-item label="敏感度">
                    <el-slider
                      v-model="selectedROI.params.sensitivity"
                      :min="0"
                      :max="1"
                      :step="0.01"
                      show-input
                      :format-tooltip="(val) => `${(val * 100).toFixed(0)}%`"
                    />
                  </el-form-item>
                </el-form>
              </div>

              <!-- 算法参数 -->
              <div class="param-section">
                <h4>算法参数</h4>
                <el-form :model="selectedROI.algorithmParams" label-width="120px" size="small">
                  <!-- 压铸机算法参数 -->
                  <template v-if="selectedROI.roi_type === 'yazhu'">
                    <el-form-item>
                      <template #label>
                        <span>运动检测</span>
                        <el-tooltip content="压铸机默认启用运动检测算法" placement="top">
                          <el-icon style="margin-left: 4px;"><QuestionFilled /></el-icon>
                        </el-tooltip>
                      </template>
                      <el-switch v-model="selectedROI.algorithmParams.motionDetection.enabled" />
                    </el-form-item>
                    <template v-if="selectedROI.algorithmParams.motionDetection.enabled">
                      <el-form-item label="运动阈值">
                        <el-slider
                          v-model="selectedROI.algorithmParams.motionDetection.threshold"
                          :min="0"
                          :max="255"
                          :step="1"
                          show-input
                        />
                      </el-form-item>
                      <el-form-item label="最小面积">
                        <el-slider
                          v-model="selectedROI.algorithmParams.motionDetection.minArea"
                          :min="10"
                          :max="1000"
                          :step="10"
                          show-input
                        />
                      </el-form-item>
                      <el-form-item label="历史帧数">
                        <el-slider
                          v-model="selectedROI.algorithmParams.motionDetection.history"
                          :min="10"
                          :max="100"
                          :step="5"
                          show-input
                        />
                      </el-form-item>
                    </template>
                  </template>

                  <!-- 排料口算法参数 -->
                  <template v-if="selectedROI.roi_type === 'pailiao'">
                    <el-form-item>
                      <template #label>
                        <span>方向检测</span>
                        <el-tooltip content="排料口默认启用方向检测算法" placement="top">
                          <el-icon style="margin-left: 4px;"><QuestionFilled /></el-icon>
                        </el-tooltip>
                      </template>
                      <el-switch v-model="selectedROI.algorithmParams.directionDetection.enabled" />
                    </el-form-item>
                    <template v-if="selectedROI.algorithmParams.directionDetection.enabled">
                      <el-form-item label="方向阈值">
                        <el-slider
                          v-model="selectedROI.algorithmParams.directionDetection.threshold"
                          :min="0"
                          :max="180"
                          :step="1"
                          show-input
                          :format-tooltip="(val) => `${val}°`"
                        />
                      </el-form-item>
                      <el-form-item label="检测角度">
                        <el-slider
                          v-model="selectedROI.algorithmParams.directionDetection.angle"
                          :min="0"
                          :max="360"
                          :step="5"
                          show-input
                          :format-tooltip="(val) => `${val}°`"
                        />
                      </el-form-item>
                      <el-form-item label="容差范围">
                        <el-slider
                          v-model="selectedROI.algorithmParams.directionDetection.tolerance"
                          :min="1"
                          :max="45"
                          :step="1"
                          show-input
                          :format-tooltip="(val) => `±${val}°`"
                        />
                      </el-form-item>
                    </template>
                  </template>
                </el-form>
              </div>

              <!-- 操作按钮 -->
              <div class="param-actions">
                <el-button type="primary" @click="saveROIParams">保存参数</el-button>
                <el-button @click="resetROIParams">重置参数</el-button>
                <el-button type="info" @click="testROIAlgorithm">测试算法</el-button>
              </div>
            </div>
          </el-card>

          <!-- 操作日志 -->
          <el-card class="log-card" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>操作日志</span>
                <el-button size="small" @click="clearLogs">清空</el-button>
              </div>
            </template>

            <div class="log-container">
              <div
                v-for="(log, index) in operationLogs"
                :key="index"
                class="log-item"
                :class="log.type"
              >
                <span class="log-time">{{ log.time }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
              <div v-if="operationLogs.length === 0" class="no-logs">
                暂无操作日志
              </div>
            </div>
          </el-card>

          <!-- 操作日志 -->
          <el-card class="log-card" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>操作日志</span>
                <el-button size="small" @click="clearLogs">清空</el-button>
              </div>
            </template>

            <div class="log-container">
              <div
                v-for="(log, index) in operationLogs"
                :key="index"
                class="log-item"
                :class="log.type"
              >
                <span class="log-time">{{ log.time }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
              <div v-if="operationLogs.length === 0" class="no-logs">
                暂无操作日志
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- ROI编辑对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑ROI" width="800px">
      <el-form v-if="editingROI" :model="editingROI" label-width="120px">
        <el-form-item label="ROI名称">
          <el-input v-model="editingROI.name" placeholder="请输入ROI名称" />
        </el-form-item>
        <el-form-item label="ROI类型">
          <el-radio-group v-model="editingROI.roi_type">
            <el-radio label="yazhu">压铸机</el-radio>
            <el-radio label="pailiao">排料口</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="形状类型">
          <el-tag :type="editingROI.shape === 'Rectangle' ? 'success' : 'info'">
            {{ editingROI.shape === 'Rectangle' ? '矩形' : '多边形' }}
          </el-tag>
        </el-form-item>
        
        <!-- 基础参数 -->
        <el-divider content-position="left">基础参数</el-divider>
        <el-form-item label="检测阈值">
          <el-slider
            v-model="editingROI.params.threshold"
            :min="0"
            :max="1"
            :step="0.01"
            show-input
            :format-tooltip="(val) => `${(val * 100).toFixed(0)}%`"
          />
        </el-form-item>
        <el-form-item label="敏感度">
          <el-slider
            v-model="editingROI.params.sensitivity"
            :min="0"
            :max="1"
            :step="0.01"
            show-input
            :format-tooltip="(val) => `${(val * 100).toFixed(0)}%`"
          />
        </el-form-item>
        
        <!-- 算法参数 -->
        <el-divider content-position="left">算法参数</el-divider>
        <template v-if="editingROI.roi_type === 'yazhu'">
          <el-form-item label="运动检测">
            <el-switch v-model="editingROI.algorithmParams.motionDetection.enabled" />
          </el-form-item>
          <template v-if="editingROI.algorithmParams.motionDetection.enabled">
            <el-form-item label="运动阈值">
              <el-slider
                v-model="editingROI.algorithmParams.motionDetection.threshold"
                :min="0"
                :max="255"
                :step="1"
                show-input
              />
            </el-form-item>
            <el-form-item label="最小面积">
              <el-slider
                v-model="editingROI.algorithmParams.motionDetection.minArea"
                :min="10"
                :max="1000"
                :step="10"
                show-input
              />
            </el-form-item>
          </template>
        </template>
        
        <template v-if="editingROI.roi_type === 'pailiao'">
          <el-form-item label="方向检测">
            <el-switch v-model="editingROI.algorithmParams.directionDetection.enabled" />
          </el-form-item>
          <template v-if="editingROI.algorithmParams.directionDetection.enabled">
            <el-form-item label="方向阈值">
              <el-slider
                v-model="editingROI.algorithmParams.directionDetection.threshold"
                :min="0"
                :max="180"
                :step="1"
                show-input
                :format-tooltip="(val) => `${val}°`"
              />
            </el-form-item>
            <el-form-item label="检测角度">
              <el-slider
                v-model="editingROI.algorithmParams.directionDetection.angle"
                :min="0"
                :max="360"
                :step="5"
                show-input
                :format-tooltip="(val) => `${val}°`"
              />
            </el-form-item>
          </template>
        </template>
        
        <el-form-item label="坐标点">
          <div class="coordinates-display">
            <div v-for="(point, index) in editingROI.coordinates" :key="index" class="coordinate-item">
              点{{ index + 1 }}: ({{ Math.round(point[0]) }}, {{ Math.round(point[1]) }})
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="saveROIEdit">保存</el-button>
      </template>
    </el-dialog>

    <!-- ROI导入对话框 -->
    <el-dialog v-model="showImportDialog" title="导入ROI配置" width="500px">
      <el-input
        v-model="importData"
        type="textarea"
        :rows="10"
        placeholder="请粘贴ROI配置JSON数据"
      />
      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button type="primary" @click="importROI">导入</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { VideoCamera, QuestionFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useWebSDK } from '@/composables/useWebSDK'
import { getVideoSource } from '@/api/video-sources'

// Props - 接收外部传入的视频源信息
interface Props {
  videoSource?: {
    id: number
    video_source_id?: number
  } | null
}

const props = withDefaults(defineProps<Props>(), {
  videoSource: null
})

// 视频源接口定义
interface VideoSource {
  id: number
  name: string
  ip: string
  port: number
  username: string
  password: string
  protocol: number
}

// 视频配置
const videoConfig = ref({
  sourceId: null as number | null,
  channel: 1,
  streamType: '1',
  protocol: 1
})

// 视频源数据
const videoSources = ref<VideoSource[]>([])

// 获取视频源详细信息 - 根据props传入的视频源ID
const fetchVideoSourceDetails = async (videoSourceId: number) => {
  if (!videoSourceId) return

  try {
    console.log(`正在获取视频源详细信息: ID=${videoSourceId}`)
    const videoSourceData = await getVideoSource(videoSourceId)

    // 检查视频源类型
    if (videoSourceData.source_type !== 'websdk_device') {
      ElMessage.error(`不支持的视频源类型: ${videoSourceData.source_type}，仅支持 websdk_device 类型`)
      console.error(`视频源类型不支持: ${videoSourceData.source_type}`)
      return
    }

    // 验证必要字段
    if (!videoSourceData.device_ip || !videoSourceData.device_port ||
        !videoSourceData.device_username || !videoSourceData.device_password) {
      ElMessage.error('视频源配置不完整，缺少设备连接信息')
      console.error('视频源配置不完整')
      return
    }

    // 转换数据格式以适配组件需求
    const formattedSource: VideoSource = {
      id: videoSourceData.id,
      name: videoSourceData.name || (videoSourceData.description + ' - ' + videoSourceData.device_ip),
      ip: videoSourceData.device_ip,
      port: videoSourceData.device_port || 80,
      username: videoSourceData.device_username || 'admin',
      password: videoSourceData.device_password || '',
      channel: videoSourceData.channel_id || 1,
      streamType: videoSourceData.stream_type || 1,
      protocol: videoSourceData.device_protocol || 1
    }

    // 设置为当前视频源
    videoSources.value = [formattedSource]
    videoConfig.value.sourceId = formattedSource.id

    console.log(`视频源信息获取成功: ${formattedSource.name}`)

  } catch (error: any) {
    ElMessage.error(`获取视频源信息失败: ${error.message}`)
    console.error(`获取视频源信息失败: ${error.message}`)
  }
}

// 监听props中的视频源变化
const watchVideoSource = () => {
  if (props.videoSource) {
    // 优先使用video_source_id，其次使用id
    const videoSourceId = props.videoSource.video_source_id || props.videoSource.id
    if (videoSourceId) {
      fetchVideoSourceDetails(videoSourceId)
    }
  } else {
    // 如果没有传入视频源，使用默认的模拟数据
    videoSources.value = [
      {
        id: 1,
        name: '摄像头01',
        ip: '************',
        port: 80,
        username: 'admin',
        password: 'Qq112233',
        protocol: 1
      }
    ]
  }
}

// 选中的视频源
const selectedVideoSource = computed(() => {
  return videoSources.value.find(source => source.id === videoConfig.value.sourceId) || null
})

// 使用WebSDK composable
const {
  // 状态
  isConnecting,
  isLoggedIn,
  isPreviewActive,
  soundEnabled,
  operationLogs,

  // 方法
  addLog,
  clearLogs,
  initWebSDK,
  loginDevice: webSDKLoginDevice,
  startPreview: webSDKStartPreview,
  stopPreview,
  openSound,
  closeSound,
  cleanup
} = useWebSDK()

// ROI相关（第一步暂时不使用）
// const { ... } = useROI()

// 视频相关
const divPlugin = ref<HTMLElement>()

// ROI相关状态（第三步TypeScript实现）
const currentROIType = ref<'yazhu' | 'pailiao'>('yazhu')
const isDrawing = ref(false)
const canCompleteDrawing = ref(false)
const roiList = ref<any[]>([])
const selectedROI = ref<any>(null)
const showEditDialog = ref(false)
const showImportDialog = ref(false)
const editingROI = ref<any>(null)
const importData = ref('')

// 绘制相关状态
const currentDrawingShape = ref<'Rectangle' | 'Polygon' | null>(null)
const drawingPoints = ref<[number, number][]>([])
const roiCanvas = ref<HTMLCanvasElement>()
const canvasContext = ref<CanvasRenderingContext2D | null>(null)



// ROI统计
const roiStats = computed(() => ({
  total: roiList.value.length,
  yazhu: roiList.value.filter(roi => roi.roi_type === 'yazhu').length,
  pailiao: roiList.value.filter(roi => roi.roi_type === 'pailiao').length
}))

// 计算属性
const canStartPreview = computed(() => {
  return selectedVideoSource.value && !isConnecting.value
})

const videoContainerStyle = computed(() => ({
  width: '100%',
  aspectRatio: '16/9',
  minHeight: '300px',
  maxHeight: '600px',
  backgroundColor: 'var(--bg-color-mute)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  position: 'relative' as const
}))

// 视频源变化处理
const onVideoSourceChange = () => {
  if (isPreviewActive.value) {
    stopPreview()
  }
  console.log(`选择视频源: ${selectedVideoSource.value?.name || '未知'}`)
}

// 开始/停止预览
const startPreview = async () => {
  if (!selectedVideoSource.value) {
    ElMessage.error('请先选择视频源')
    return
  }

  if (isPreviewActive.value) {
    stopPreview()
    return
  }

  try {
    // 先登录设备
    if (!isLoggedIn.value) {
      await webSDKLoginDevice({
        ip: selectedVideoSource.value.ip,
        port: selectedVideoSource.value.port,
        username: selectedVideoSource.value.username,
        password: selectedVideoSource.value.password,
        channel: videoConfig.value.channel,
        streamType: videoConfig.value.streamType,
        protocol: videoConfig.value.protocol
      })
    }

    // 开始预览
    if (isLoggedIn.value) {
      webSDKStartPreview({
        ip: selectedVideoSource.value.ip,
        port: selectedVideoSource.value.port,
        username: selectedVideoSource.value.username,
        password: selectedVideoSource.value.password,
        channel: videoConfig.value.channel,
        streamType: videoConfig.value.streamType,
        protocol: videoConfig.value.protocol
      })
    }
  } catch (error: any) {
    ElMessage.error(`预览失败: ${error.message}`)
  }
}

// 切换声音
const toggleSound = () => {
  if (soundEnabled.value) {
    closeSound()
  } else {
    openSound()
  }
}



// ROI相关功能（第三步TypeScript实现）
const startDrawingROI = (shape: 'Rectangle' | 'Polygon') => {
  // 确保Canvas已初始化
  if (!roiCanvas.value || !canvasContext.value) {
    addLog('Canvas未初始化，正在重新初始化...', 'warning')
    const success = initCanvas()
    if (!success) {
      addLog('Canvas初始化失败，无法开始绘制', 'error')
      return
    }
  }

  // 强制开启ROI绘制功能，移除预览状态检查
  addLog(`开始绘制${shape === 'Rectangle' ? '矩形' : '多边形'}ROI，类型：${currentROIType.value}`, 'info')
  isDrawing.value = true
  canCompleteDrawing.value = false

  // 初始化绘制状态
  currentDrawingShape.value = shape
  drawingPoints.value = []

  // 确保事件监听器已绑定
  addCanvasEventListeners()
  
  // 设置Canvas为绘制模式
  if (roiCanvas.value) {
    roiCanvas.value.style.cursor = 'crosshair'
  }

  addLog(`开始绘制${shape === 'Rectangle' ? '矩形' : '多边形'}ROI，请在Canvas上点击绘制`, 'info')
}

const completeDrawing = () => {
  if (drawingPoints.value.length < 2) {
    ElMessage.warning('至少需要2个点才能完成绘制')
    return
  }

  // 创建新的ROI
  const newROI = createNewROI()
  roiList.value.push(newROI)

  addLog(`完成ROI绘制: ${newROI.name}`, 'success')
  ElMessage.success(`成功创建ROI: ${newROI.name}`)

  // 重置绘制状态
  resetDrawingState()
}

const cancelDrawing = () => {
  addLog('取消ROI绘制', 'info')
  resetDrawingState()
}

const clearAllROI = () => {
  roiList.value = []
  selectedROI.value = null
  addLog('清空所有ROI', 'info')
}

const selectROI = (roi: any) => {
  selectedROI.value = roi
  addLog(`选择ROI: ${roi.name}`, 'info')
}

const editROI = (roi: any) => {
  editingROI.value = { ...roi }
  
  // 确保算法参数结构完整
  if (!editingROI.value.algorithmParams) {
    editingROI.value.algorithmParams = {
      motionDetection: {
        enabled: editingROI.value.roi_type === 'yazhu',
        threshold: 30,
        minArea: 100,
        history: 50
      },
      directionDetection: {
        enabled: editingROI.value.roi_type === 'pailiao',
        threshold: 45,
        angle: 90,
        tolerance: 15
      }
    }
  }
  
  showEditDialog.value = true
  addLog(`编辑ROI: ${roi.name}`, 'info')
}

const deleteROI = (roiId: string) => {
  const index = roiList.value.findIndex(roi => roi.roi_id === roiId)
  if (index > -1) {
    const roi = roiList.value[index]
    roiList.value.splice(index, 1)
    if (selectedROI.value?.roi_id === roiId) {
      selectedROI.value = null
    }
    addLog(`删除ROI: ${roi.name}`, 'info')
  }
}

const saveROIEdit = () => {
  if (!editingROI.value) return

  // 确保算法参数结构完整
  if (!editingROI.value.algorithmParams) {
    editingROI.value.algorithmParams = {
      motionDetection: {
        enabled: editingROI.value.roi_type === 'yazhu',
        threshold: 30,
        minArea: 100,
        history: 50
      },
      directionDetection: {
        enabled: editingROI.value.roi_type === 'pailiao',
        threshold: 45,
        angle: 90,
        tolerance: 15
      }
    }
  }

  const index = roiList.value.findIndex(roi => roi.roi_id === editingROI.value.roi_id)
  if (index > -1) {
    roiList.value[index] = { ...editingROI.value }
    addLog(`ROI "${editingROI.value.name}" 更新成功，算法参数已保存`, 'success')
  }

  showEditDialog.value = false
  editingROI.value = null
}

const exportROI = () => {
  const data = JSON.stringify(roiList.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `roi_config_${new Date().getTime()}.json`
  a.click()
  URL.revokeObjectURL(url)
  addLog('导出ROI配置', 'success')
}

const importROI = () => {
  if (!importData.value.trim()) {
    ElMessage.error('请输入ROI配置数据')
    return
  }

  try {
    const data = JSON.parse(importData.value)
    if (Array.isArray(data)) {
      roiList.value = data
      addLog(`导入${data.length}个ROI配置`, 'success')
      ElMessage.success(`导入${data.length}个ROI配置`)
    } else {
      ElMessage.error('ROI配置数据格式错误')
    }
  } catch (error) {
    ElMessage.error('ROI配置数据解析失败')
  }

  showImportDialog.value = false
  importData.value = ''
}

const getROIColor = (roiType: 'yazhu' | 'pailiao') => {
  return roiType === 'yazhu' ? '#ff4444' : '#00cccc'
}

// 绘制相关辅助方法
const resetDrawingState = () => {
  isDrawing.value = false
  canCompleteDrawing.value = false
  currentDrawingShape.value = null
  drawingPoints.value = []
  removeCanvasEventListeners()
  clearCanvas()
}

const createNewROI = () => {
  const roiId = `roi_${Date.now()}`
  const roiName = `${currentROIType.value}_${roiId.slice(-4)}`

  // 根据ROI类型设置默认算法参数
  const getDefaultAlgorithmParams = (roiType: 'yazhu' | 'pailiao') => {
    if (roiType === 'yazhu') {
      // 压铸机默认开启运动检测
      return {
        motionDetection: {
          enabled: true,
          threshold: 30,
          minArea: 100,
          history: 50
        },
        directionDetection: {
          enabled: false,
          threshold: 45,
          angle: 0,
          tolerance: 10
        }
      }
    } else {
      // 排料口默认开启方向检测
      return {
        motionDetection: {
          enabled: false,
          threshold: 30,
          minArea: 100,
          history: 50
        },
        directionDetection: {
          enabled: true,
          threshold: 45,
          angle: 90,
          tolerance: 15
        }
      }
    }
  }

  return {
    id: roiId,
    name: roiName,
    roi_type: currentROIType.value,
    shape: currentDrawingShape.value,
    coordinates: [...drawingPoints.value],
    params: {
      threshold: 0.5,
      sensitivity: 0.8
    },
    algorithmParams: getDefaultAlgorithmParams(currentROIType.value)
  }
}

const addCanvasEventListeners = () => {
  const canvas = roiCanvas.value
  if (!canvas) {
    addLog('Canvas元素不存在，无法绑定事件监听器', 'error')
    return
  }

  // 移除可能存在的旧事件监听器
  removeCanvasEventListeners()
  
  canvas.addEventListener('click', handleCanvasClick)
  canvas.addEventListener('mousemove', handleCanvasMouseMove)
  canvas.addEventListener('contextmenu', handleCanvasRightClick)
  
  // 添加测试事件来验证事件绑定
  const testHandler = (e: MouseEvent) => {
    addLog(`Canvas鼠标事件测试成功: (${e.clientX}, ${e.clientY})`, 'info')
    canvas.removeEventListener('mousedown', testHandler)
  }
  canvas.addEventListener('mousedown', testHandler)
  
  addLog('Canvas事件监听器绑定完成', 'success')
}

const removeCanvasEventListeners = () => {
  const canvas = roiCanvas.value
  if (!canvas) return

  try {
    canvas.removeEventListener('click', handleCanvasClick)
    canvas.removeEventListener('mousemove', handleCanvasMouseMove)
    canvas.removeEventListener('contextmenu', handleCanvasRightClick)
    addLog('Canvas事件监听器已移除', 'info')
  } catch (error) {
    addLog('移除Canvas事件监听器时出错', 'warning')
  }
}

const handleCanvasClick = (event: MouseEvent) => {
  addLog(`Canvas点击事件触发: isDrawing=${isDrawing.value}`, 'info')
  
  if (!isDrawing.value) {
    addLog('当前不在绘制状态，忽略点击事件', 'warning')
    return
  }
  
  const canvas = roiCanvas.value
  if (!canvas) {
    addLog('Canvas元素不存在', 'error')
    return
  }

  const rect = canvas.getBoundingClientRect()
  const scaleX = canvas.width / rect.width
  const scaleY = canvas.height / rect.height
  const x = (event.clientX - rect.left) * scaleX
  const y = (event.clientY - rect.top) * scaleY

  addLog(`Canvas尺寸: ${canvas.width}x${canvas.height}, 显示尺寸: ${rect.width.toFixed(2)}x${rect.height.toFixed(2)}`, 'info')
  addLog(`鼠标位置: (${event.clientX}, ${event.clientY}), Canvas位置: (${rect.left.toFixed(2)}, ${rect.top.toFixed(2)})`, 'info')
  addLog(`计算坐标: (${x.toFixed(2)}, ${y.toFixed(2)})`, 'info')

  currentROI.value.coordinates.push({ x, y })
  addLog(`添加绘制点: (${x.toFixed(2)}, ${y.toFixed(2)})`, 'info')

  if (currentROI.value.shape === 'Rectangle' && currentROI.value.coordinates.length === 2) {
    completeDrawing()
    addLog('矩形绘制完成', 'success')
  } else if (currentROI.value.shape === 'Polygon' && currentROI.value.coordinates.length >= 3) {
    canCompleteDrawing.value = true
    addLog('多边形已有足够点数，可以点击"完成"按钮或右键结束', 'success')
  }

  drawCanvas()
}

const handleCanvasMouseMove = (event: MouseEvent) => {
  // 鼠标移动时的预览绘制（可选实现）
}

const handleCanvasRightClick = (event: MouseEvent) => {
  event.preventDefault()
  if (currentROI.value.shape === 'Polygon' && currentROI.value.coordinates.length >= 3) {
    // 右键完成多边形绘制
    completeDrawing()
  }
}

const clearCanvas = () => {
  const canvas = roiCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (ctx) {
    ctx.clearRect(0, 0, canvas.width, canvas.height)
  }
}

const drawCanvas = () => {
  const canvas = roiCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 绘制现有ROI
  drawExistingROIs(ctx)

  // 绘制当前正在绘制的ROI
  if (isDrawing.value && currentROI.value.coordinates.length > 0) {
    drawCurrentROI(ctx)
  }
}

const drawExistingROIs = (ctx: CanvasRenderingContext2D) => {
  roiList.value.forEach(roi => {
    ctx.strokeStyle = getROIColor(roi.roi_type)
    ctx.lineWidth = 2
    ctx.beginPath()

    if (roi.roi_type === 'rectangle' && roi.coordinates.length >= 2) {
      const { x: x1, y: y1 } = roi.coordinates[0]
      const { x: x2, y: y2 } = roi.coordinates[1]
      ctx.rect(x1, y1, x2 - x1, y2 - y1)
    } else if (roi.roi_type === 'polygon' && roi.coordinates.length >= 3) {
      const { x: startX, y: startY } = roi.coordinates[0]
      ctx.moveTo(startX, startY)
      for (let i = 1; i < roi.coordinates.length; i++) {
        const { x, y } = roi.coordinates[i]
        ctx.lineTo(x, y)
      }
      ctx.closePath()
    }

    ctx.stroke()
  })
}

const drawCurrentROI = (ctx: CanvasRenderingContext2D) => {
  if (!currentROI.value.coordinates.length) return
  
  ctx.strokeStyle = getROIColor(currentROIType.value)
  ctx.lineWidth = 2
  ctx.setLineDash([5, 5]) // 虚线表示正在绘制

  ctx.beginPath()

  if (currentROI.value.shape === 'Rectangle' && currentROI.value.coordinates.length >= 1) {
    const { x: x1, y: y1 } = currentROI.value.coordinates[0]
    if (currentROI.value.coordinates.length === 2) {
      const { x: x2, y: y2 } = currentROI.value.coordinates[1]
      ctx.rect(x1, y1, x2 - x1, y2 - y1)
    }
  } else if (currentROI.value.shape === 'Polygon' && currentROI.value.coordinates.length >= 1) {
    const { x: startX, y: startY } = currentROI.value.coordinates[0]
    ctx.moveTo(startX, startY)
    for (let i = 1; i < currentROI.value.coordinates.length; i++) {
      const { x, y } = currentROI.value.coordinates[i]
      ctx.lineTo(x, y)
    }
  }

  ctx.stroke()
  ctx.setLineDash([]) // 重置虚线

  // 绘制点
  currentROI.value.coordinates.forEach(({ x, y }, index) => {
    ctx.fillStyle = getROIColor(currentROIType.value)
    ctx.beginPath()
    ctx.arc(x, y, 4, 0, 2 * Math.PI)
    ctx.fill()

    // 绘制点的序号
    ctx.fillStyle = '#fff'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'
    ctx.fillText((index + 1).toString(), x, y + 4)
  })
}

// ROI参数配置相关方法
const saveROIParams = () => {
  if (!selectedROI.value) return

  const index = roiList.value.findIndex(roi => roi.roi_id === selectedROI.value.roi_id)
  if (index > -1) {
    roiList.value[index] = { ...selectedROI.value }
    addLog(`保存ROI参数: ${selectedROI.value.name}`, 'success')
    ElMessage.success('ROI参数保存成功')
  }
}

const resetROIParams = () => {
  if (!selectedROI.value) return

  // 重置为默认参数
  const defaultParams = {
    threshold: 0.5,
    sensitivity: 0.8
  }

  const getDefaultAlgorithmParams = (roiType: 'yazhu' | 'pailiao') => {
    if (roiType === 'yazhu') {
      return {
        motionDetection: {
          enabled: true,
          threshold: 30,
          minArea: 100,
          history: 50
        },
        directionDetection: {
          enabled: false,
          threshold: 45,
          angle: 0,
          tolerance: 10
        }
      }
    } else {
      return {
        motionDetection: {
          enabled: false,
          threshold: 30,
          minArea: 100,
          history: 50
        },
        directionDetection: {
          enabled: true,
          threshold: 45,
          angle: 90,
          tolerance: 15
        }
      }
    }
  }

  selectedROI.value.params = { ...defaultParams }
  selectedROI.value.algorithmParams = getDefaultAlgorithmParams(selectedROI.value.roi_type)

  addLog(`重置ROI参数: ${selectedROI.value.name}`, 'info')
  ElMessage.info('ROI参数已重置为默认值')
}

const testROIAlgorithm = () => {
  if (!selectedROI.value) return

  const algorithmType = selectedROI.value.roi_type === 'yazhu' ? '运动检测' : '方向检测'
  addLog(`测试${algorithmType}算法: ${selectedROI.value.name}`, 'info')
  ElMessage.info(`正在测试${algorithmType}算法...`)

  // 模拟算法测试
  setTimeout(() => {
    addLog(`${algorithmType}算法测试完成`, 'success')
    ElMessage.success(`${algorithmType}算法测试完成`)
  }, 2000)
}

// 添加测试ROI数据
const addTestROIData = () => {
  const testROI1 = {
    id: 'roi_001',
    name: 'yazhu_001',
    roi_type: 'yazhu',
    shape: 'Rectangle',
    coordinates: [[100, 100], [200, 100], [200, 200], [100, 200]],
    params: { threshold: 0.7, sensitivity: 0.9 },
    algorithmParams: {
      motionDetection: {
        enabled: true,
        threshold: 30,
        minArea: 100,
        history: 50
      },
      directionDetection: {
        enabled: false,
        threshold: 45,
        angle: 0,
        tolerance: 10
      }
    }
  }

  const testROI2 = {
    id: 'roi_002',
    name: 'pailiao_001',
    roi_type: 'pailiao',
    shape: 'Polygon',
    coordinates: [[300, 150], [400, 120], [450, 200], [380, 250], [320, 220]],
    params: { threshold: 0.6, sensitivity: 0.8 },
    algorithmParams: {
      motionDetection: {
        enabled: false,
        threshold: 30,
        minArea: 100,
        history: 50
      },
      directionDetection: {
        enabled: true,
        threshold: 45,
        angle: 90,
        tolerance: 15
      }
    }
  }

  roiList.value = [testROI1, testROI2]
  addLog('添加测试ROI数据', 'info')
}

// 初始化Canvas
const initCanvas = () => {
  const canvas = roiCanvas.value
  if (!canvas) {
    addLog('Canvas元素未找到，等待DOM渲染', 'warning')
    return false
  }

  // 设置Canvas尺寸
  canvas.width = 640
  canvas.height = 360

  // 设置Canvas样式
  canvas.style.position = 'absolute'
  canvas.style.top = '0'
  canvas.style.left = '0'
  canvas.style.width = '100%'
  canvas.style.height = '100%'
  canvas.style.pointerEvents = 'auto'
  canvas.style.zIndex = '10'
  canvas.style.cursor = 'crosshair'

  // 获取绘制上下文
  canvasContext.value = canvas.getContext('2d')
  
  if (!canvasContext.value) {
    addLog('Canvas上下文获取失败', 'error')
    return false
  }

  // 强制启用Canvas交互
  canvas.style.display = 'block'
  canvas.style.visibility = 'visible'
  
  // 绑定Canvas事件监听器
  addCanvasEventListeners()
  
  // 绘制已有的ROI
  drawCanvas()
  
  addLog('Canvas初始化完成，事件监听器已绑定，ROI绘制功能已强制启用', 'success')
  return true
}

// 组件挂载时初始化WebSDK
onMounted(async () => {
  await initWebSDK()

  // 初始化视频源
  watchVideoSource()

  addLog('ROI配置页面初始化完成', 'success')

  // 等待DOM渲染完成后初始化Canvas
  await nextTick()
  
  // 使用重试机制确保Canvas正确初始化
  const tryInitCanvas = (retries = 5) => {
    if (retries <= 0) {
      addLog('Canvas初始化失败，已达到最大重试次数', 'error')
      return
    }
    
    const success = initCanvas()
    if (success) {
      addTestROIData()
      addLog('ROI绘制功能已强制启用，可以直接开始绘制', 'success')
    } else {
      addLog(`Canvas初始化失败，${retries - 1}次重试后重新尝试`, 'warning')
      setTimeout(() => tryInitCanvas(retries - 1), 500)
    }
  }
  
  setTimeout(() => tryInitCanvas(), 500)
})

// 监听props变化
watch(() => props.videoSource, () => {
  watchVideoSource()
}, { immediate: true, deep: true })



// 组件卸载时清理资源
onUnmounted(() => {
  // 清理ROI绘制相关的事件监听器
  removeCanvasEventListeners()
  
  // 清理WebSDK资源
  cleanup()
  
  addLog('ROI配置页面已卸载', 'info')
})

</script>

<style scoped>
@import './styles/video-roi-config.css';
</style>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
压铸检测系统后端部署包创建工具
"""

import os
import shutil
import zipfile
from datetime import datetime
from pathlib import Path

def create_deployment_package():
    """创建完整的部署包"""
    
    print("============================================================")
    print("📦 压铸检测系统后端部署包创建工具")
    print("============================================================")
    
    # 获取当前时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 创建部署包目录
    deployment_dir = Path(f"压铸检测系统_部署包_{timestamp}")
    deployment_dir.mkdir(exist_ok=True)
    
    print(f"📁 创建部署目录: {deployment_dir}")
    
    # 1. 复制可执行文件
    print("1️⃣ 复制可执行文件...")
    backend_dir = deployment_dir / "backend"
    backend_dir.mkdir(exist_ok=True)
    
    # 复制整个dist/backend_service目录
    dist_dir = Path("dist/backend_service")
    if dist_dir.exists():
        shutil.copytree(dist_dir, backend_dir / "backend_service", dirs_exist_ok=True)
        print(f"✅ 已复制可执行文件到: {backend_dir / 'backend_service'}")
    else:
        print("❌ 错误: 找不到构建的可执行文件")
        return
    
    # 2. 创建数据库目录并复制数据库
    print("2️⃣ 准备数据库文件...")
    database_dir = deployment_dir / "database"
    database_dir.mkdir(exist_ok=True)
    
    # 复制数据库文件
    db_file = Path("die_casting_detection.db")
    if db_file.exists():
        shutil.copy2(db_file, database_dir / "die_casting_detection.db")
        print(f"✅ 已复制数据库文件到: {database_dir}")
    else:
        print("⚠️ 警告: 未找到数据库文件，将在首次运行时创建")
    
    # 3. 复制配置文件
    print("3️⃣ 复制配置文件...")
    config_files = [
        "alembic.ini",
        "init_database.py",
        "app_launcher_standalone.py"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            shutil.copy2(config_file, backend_dir / config_file)
            print(f"✅ 已复制: {config_file}")
    
    # 4. 创建启动脚本
    print("4️⃣ 创建启动脚本...")
    
    # Windows启动脚本
    start_script = deployment_dir / "启动系统.bat"
    with open(start_script, 'w', encoding='utf-8') as f:
        f.write("""@echo off
chcp 65001 > nul
echo ============================================================
echo 🚀 启动压铸检测系统后端服务
echo ============================================================
echo.

cd /d "%~dp0"

echo 📍 当前目录: %CD%
echo.

echo 🔍 检查数据库文件...
if not exist "database\die_casting_detection.db" (
    echo ⚠️  数据库文件不存在，将在首次启动时自动创建
    echo.
)

echo 🚀 启动后端服务...
echo 📝 日志将显示在此窗口中
echo 💡 按 Ctrl+C 可停止服务
echo.

cd backend\backend_service
backend_service.exe

echo.
echo 🛑 服务已停止
pause
""")
    
    # Windows停止脚本
    stop_script = deployment_dir / "停止系统.bat"
    with open(stop_script, 'w', encoding='utf-8') as f:
        f.write("""@echo off
chcp 65001 > nul
echo ============================================================
echo 🛑 停止压铸检测系统后端服务
echo ============================================================
echo.

taskkill /f /im backend_service.exe 2>nul
if %errorlevel% == 0 (
    echo ✅ 后端服务已停止
) else (
    echo ℹ️  后端服务未在运行
)

echo.
pause
""")
    
    print(f"✅ 已创建启动脚本: {start_script}")
    print(f"✅ 已创建停止脚本: {stop_script}")
    
    # 5. 创建README文件
    print("5️⃣ 创建说明文档...")
    readme_file = deployment_dir / "README.md"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(f"""# 压铸检测系统后端服务部署包

## 📦 部署包信息
- 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 版本: 独立可执行版本
- 数据库: 外置SQLite数据库

## 📁 目录结构
```
压铸检测系统_部署包_{timestamp}/
├── backend/                    # 后端服务文件
│   ├── backend_service/        # 可执行文件目录
│   │   ├── backend_service.exe # 主程序
│   │   └── _internal/          # 依赖文件
│   ├── alembic.ini            # 数据库迁移配置
│   ├── init_database.py       # 数据库初始化脚本
│   └── app_launcher_standalone.py # 独立启动器
├── database/                   # 数据库目录
│   └── die_casting_detection.db # SQLite数据库文件
├── 启动系统.bat               # Windows启动脚本
├── 停止系统.bat               # Windows停止脚本
└── README.md                  # 本说明文件
```

## 🚀 快速开始

### Windows系统
1. 双击 `启动系统.bat` 启动服务
2. 等待服务启动完成（通常需要10-30秒）
3. 服务启动后可通过浏览器访问: http://localhost:8000
4. 双击 `停止系统.bat` 停止服务

### 手动启动
1. 打开命令提示符
2. 进入 `backend/backend_service` 目录
3. 运行 `backend_service.exe`

## ⚙️ 配置说明

### 数据库配置
- 数据库文件位置: `database/die_casting_detection.db`
- 首次运行时会自动创建数据库和表结构
- 数据库备份建议定期进行

### 端口配置
- 默认端口: 8000
- 如需修改端口，请编辑启动脚本或直接运行可执行文件时指定参数

### 日志文件
- 日志文件会在运行目录下的 `logs` 文件夹中生成
- 包括应用日志、API日志、WebSocket日志等

## 🔧 故障排除

### 常见问题
1. **端口被占用**: 检查8000端口是否被其他程序占用
2. **数据库错误**: 检查database目录权限，确保程序有读写权限
3. **启动失败**: 查看控制台输出的错误信息

### 重置数据库
如需重置数据库，可以删除 `database/die_casting_detection.db` 文件，重新启动服务时会自动创建新的数据库。

## 📞 技术支持
如遇到问题，请联系技术支持团队。
""")
    
    print(f"✅ 已创建说明文档: {readme_file}")
    
    # 6. 创建压缩包（可选）
    print("6️⃣ 创建压缩包...")
    zip_file = f"压铸检测系统_部署包_{timestamp}.zip"
    
    with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(deployment_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(deployment_dir.parent)
                zipf.write(file_path, arc_path)
    
    print(f"✅ 已创建压缩包: {zip_file}")
    
    print("\n============================================================")
    print("🎉 部署包创建完成！")
    print("============================================================")
    print(f"📁 部署目录: {deployment_dir.absolute()}")
    print(f"📦 压缩包: {Path(zip_file).absolute()}")
    print("\n📋 下一步操作:")
    print("1. 将部署包复制到目标服务器")
    print("2. 解压部署包")
    print("3. 双击'启动系统.bat'启动服务")
    print("4. 通过浏览器访问 http://localhost:8000 验证服务")
    
if __name__ == "__main__":
    try:
        create_deployment_package()
    except Exception as e:
        print(f"❌ 创建部署包时发生错误: {e}")
        input("按回车键退出...")
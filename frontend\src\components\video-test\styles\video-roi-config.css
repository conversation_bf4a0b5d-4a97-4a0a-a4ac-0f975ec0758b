/* ROI配置页面样式 */
.roi-config-test {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0;
  margin: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
}

/* 配置区域 */
.config-section {
  flex-shrink: 0;
}

/* ROI配置区域 */
.roi-section {
  flex: 1;
  min-height: 0;
}

/* 卡片样式 */
.config-card,
.video-card,
.info-card,
.roi-list-card,
.log-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(var(--text-color-rgb, 0, 0, 0), 0.1);
  transition: box-shadow 0.3s ease;
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
}

.config-card:hover,
.video-card:hover,
.info-card:hover,
.roi-list-card:hover,
.log-card:hover {
  box-shadow: 0 4px 16px rgba(var(--text-color-rgb, 0, 0, 0), 0.15);
  border-color: var(--primary-color-light);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: var(--text-color);
  padding: 0;
  margin: 0;
}

/* 视频控制按钮组 */
.video-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.video-controls .el-button {
  min-width: 80px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.video-controls .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.3);
}

/* 视频容器 */
.video-container {
  border: 2px solid var(--border-color);
  border-radius: 6px;
  overflow: hidden;
  background: linear-gradient(135deg, var(--bg-color-mute) 0%, var(--bg-color-soft) 100%);
  position: relative;
  transition: border-color 0.3s ease;
  contain: layout;
}

.video-container:hover {
  border-color: var(--primary-color);
}

/* 视频插件 */
.video-plugin {
  background-color: var(--bg-color-mute);
  width: 100%;
  height: 100%;
  position: relative;
  object-fit: contain;
}

/* WebSDK插件容器适配 */
.video-plugin object,
.video-plugin embed,
.video-plugin canvas,
.video-plugin video {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain;
}

/* 视频占位符 */
.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-color-mute);
  text-align: center;
  background: radial-gradient(circle at center, var(--bg-color-soft) 0%, var(--bg-color-mute) 70%);
}

.video-placeholder .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--text-color-mute);
  opacity: 0.6;
  animation: pulse 2s infinite;
}

.video-placeholder p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.5;
  opacity: 0.8;
}

/* ROI绘制层 - Canvas */
.roi-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: auto;
  z-index: 10;
  cursor: crosshair;
}

/* ROI绘制模式下的鼠标样式 */
.video-container.drawing-mode {
  cursor: crosshair;
}

.video-container.drawing-mode .roi-canvas {
  cursor: crosshair;
}

/* ROI控制区域 */
.roi-controls {
  margin-top: 16px;
  padding: 16px;
  background-color: var(--bg-color-soft);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.roi-type-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.roi-type-selector span {
  font-weight: 500;
  color: var(--text-color);
}

.roi-shape-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.roi-shape-controls .el-button {
  min-width: 80px;
  font-size: 12px;
}

/* 状态信息区域 */
.status-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background-color: var(--bg-color-soft);
  border-radius: 6px;
  margin-bottom: 16px;
  border: 1px solid var(--border-color);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: background-color 0.2s ease;
}

.status-item:hover {
  background-color: var(--el-fill-color-lighter);
  margin: 0 -12px;
  padding: 10px 12px;
  border-radius: 4px;
}

.status-item:last-child {
  border-bottom: none;
}

.status-item .label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  font-size: 13px;
}

/* ROI统计 */
.roi-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-label {
  font-size: 13px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

/* ROI列表 */
.roi-list {
  max-height: 400px;
  overflow-y: auto;
}

.roi-list::-webkit-scrollbar {
  width: 6px;
}

.roi-list::-webkit-scrollbar-track {
  background: var(--el-fill-color-light);
  border-radius: 3px;
}

.roi-list::-webkit-scrollbar-thumb {
  background: var(--el-border-color-dark);
  border-radius: 3px;
}

.roi-list::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-darker);
}

.roi-item {
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: var(--bg-color-soft);
  cursor: pointer;
  transition: all 0.2s ease;
}

.roi-item:hover {
  border-color: var(--primary-color-light);
  background-color: var(--el-fill-color-lighter);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.1);
}

.roi-item-selected {
  border-color: var(--primary-color) !important;
  background-color: var(--primary-color-light-9) !important;
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.roi-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.roi-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-color);
}

.roi-color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.roi-item-info {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--text-color-soft);
}

.roi-type,
.roi-shape,
.roi-points {
  padding: 2px 6px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.roi-actions {
  display: flex;
  gap: 8px;
}

.roi-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

/* ROI编辑对话框样式 */
.coordinates-display {
  max-height: 150px;
  overflow-y: auto;
  padding: 8px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-light);
}

.coordinate-item {
  padding: 4px 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.coordinate-item:not(:last-child) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.no-roi {
  text-align: center;
  color: var(--el-text-color-placeholder);
  padding: 40px 20px;
  font-size: 14px;
  font-style: italic;
}

/* 日志容器 */
.log-container {
  height: 200px;
  overflow-y: auto;
  background-color: var(--bg-color-mute);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-container::-webkit-scrollbar {
  width: 6px;
}

.log-container::-webkit-scrollbar-track {
  background: var(--el-fill-color-light);
  border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb {
  background: var(--el-border-color-dark);
  border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-darker);
}

/* 日志项 */
.log-item {
  margin-bottom: 4px;
  padding: 2px 0;
  word-break: break-all;
  color: var(--text-color-soft);
}

.log-item:last-child {
  margin-bottom: 0;
}

.log-item.error {
  color: var(--error-color);
}

.log-item.success {
  color: var(--success-color);
}

.log-item.warning {
  color: var(--warning-color);
}

/* 无日志提示 */
.no-logs {
  text-align: center;
  color: var(--el-text-color-placeholder);
  padding: 40px 20px;
  font-size: 14px;
  font-style: italic;
}

/* ROI参数配置区域样式 */
.roi-params-card {
  border: 1px solid var(--primary-color-light);
  background: linear-gradient(135deg, var(--bg-color-soft) 0%, var(--primary-color-light-9) 100%);
}

.roi-params-content {
  padding: 16px;
}

.param-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--bg-color-soft);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.param-section h4 {
  margin: 0 0 16px 0;
  color: var(--text-color);
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--primary-color-light);
}

.param-section .el-form-item {
  margin-bottom: 16px;
}

.param-section .el-form-item__label {
  font-weight: 500;
  color: var(--text-color);
}

.param-section .el-slider {
  margin: 8px 0;
}

.param-section .el-switch {
  margin-left: 8px;
}

/* 参数操作按钮 */
.param-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.param-actions .el-button {
  min-width: 100px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.param-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

/* 算法参数特殊样式 */
.param-section .el-form-item .el-tooltip {
  color: var(--primary-color);
}

.param-section .el-slider__runway {
  background-color: var(--el-fill-color-light);
}

.param-section .el-slider__bar {
  background-color: var(--primary-color);
}

.param-section .el-slider__button {
  border-color: var(--primary-color);
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .roi-section .el-col {
    margin-bottom: 20px;
  }
  
  .video-controls,
  .roi-shape-controls {
    justify-content: center;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .video-container {
    min-height: 250px;
    max-height: 500px;
  }
  
  .param-actions {
    flex-direction: column;
  }
  
  .param-actions .el-button {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .roi-config-test {
    gap: 16px;
  }
  
  .roi-section .el-row {
    flex-direction: column;
  }
  
  .roi-section .el-col {
    margin-bottom: 16px;
  }
  
  .video-controls,
  .roi-shape-controls {
    justify-content: center;
    gap: 6px;
  }
  
  .video-controls .el-button,
  .roi-shape-controls .el-button {
    min-width: 70px;
    font-size: 11px;
  }
  
  .status-info {
    padding: 12px;
  }
  
  .log-container {
    height: 150px;
  }
  
  .status-item {
    padding: 8px 0;
  }
  
  .video-container {
    min-height: 200px;
    max-height: 400px;
  }
  
  .roi-controls {
    padding: 12px;
  }
  
  .roi-type-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .param-section {
    padding: 12px;
  }
  
  .param-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .param-actions .el-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .roi-config-test {
    gap: 12px;
  }
  
  .config-section .el-form-item {
    margin-bottom: 16px;
  }
  
  .video-controls,
  .roi-shape-controls {
    gap: 6px;
  }
  
  .video-controls .el-button,
  .roi-shape-controls .el-button {
    min-width: 60px;
    font-size: 10px;
    padding: 6px 8px;
  }
  
  .status-info {
    padding: 10px;
  }
  
  .log-container {
    height: 120px;
    font-size: 11px;
  }
  
  .video-container {
    min-height: 180px;
    max-height: 320px;
    aspect-ratio: 16/9;
  }
  
  .roi-controls {
    padding: 10px;
  }
  
  .roi-item {
    padding: 10px;
  }
  
  .roi-item-info {
    gap: 8px;
    font-size: 11px;
  }
}

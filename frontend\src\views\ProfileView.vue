<template>
  <div class="profile-container">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <span>个人信息</span>
        </div>
      </template>
      
      <div class="profile-content">
        <!-- 用户头像区域 -->
        <div class="avatar-section">
          <el-avatar :size="80" class="user-avatar">
            {{ userInitials }}
          </el-avatar>
          <div class="user-basic-info">
            <h3>{{ currentUser?.username || '用户' }}</h3>
            <p class="user-role">{{ getRoleText(currentUser?.role) }}</p>
            <p class="user-status">
              <el-tag :type="currentUser?.is_active ? 'success' : 'danger'" size="small">
                {{ currentUser?.is_active ? '活跃' : '禁用' }}
              </el-tag>
            </p>
          </div>
        </div>

        <!-- 用户信息编辑表单 -->
        <el-divider />
        
        <el-form
          ref="profileFormRef"
          :model="profileForm"
          :rules="profileRules"
          label-width="100px"
          class="profile-form"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名" prop="username">
                <el-input v-model="profileForm.username" placeholder="请输入用户名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="profileForm.email" placeholder="请输入邮箱" type="email" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="角色">
                <el-input :value="getRoleText(currentUser?.role)" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态">
                <el-switch
                  v-model="profileForm.is_active"
                  active-text="活跃"
                  inactive-text="禁用"
                  :disabled="!canEditStatus"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">密码修改</el-divider>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="新密码" prop="password">
                <el-input
                  v-model="profileForm.password"
                  type="password"
                  placeholder="留空则不修改密码"
                  show-password
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="确认密码" prop="confirmPassword">
                <el-input
                  v-model="profileForm.confirmPassword"
                  type="password"
                  placeholder="请再次输入新密码"
                  show-password
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="updateProfile" :loading="loading">
              保存修改
            </el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 账户信息卡片 -->
    <el-card class="account-info-card">
      <template #header>
        <div class="card-header">
          <span>账户信息</span>
        </div>
      </template>
      
      <div class="account-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ currentUser?.id }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(currentUser?.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后更新">
            {{ formatDate(currentUser?.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="超级用户">
            <el-tag :type="currentUser?.is_superuser ? 'success' : 'info'" size="small">
              {{ currentUser?.is_superuser ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { updateUser, type UpdateUserRequest } from '@/api/users'
import { getCurrentUser } from '@/api/auth'
import type { User } from '@/types'

const authStore = useAuthStore()
const profileFormRef = ref<FormInstance>()
const loading = ref(false)

// 当前用户信息
const currentUser = ref<User | null>(null)

// 表单数据
const profileForm = reactive({
  username: '',
  email: '',
  is_active: true,
  password: '',
  confirmPassword: ''
})

// 表单验证规则
const profileRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    {
      validator: (rule, value, callback) => {
        if (profileForm.password && value !== profileForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const userInitials = computed(() => {
  return currentUser.value?.username ? currentUser.value.username.charAt(0).toUpperCase() : 'U'
})

const canEditStatus = computed(() => {
  return currentUser.value?.is_superuser || false
})

// 获取角色文本
const getRoleText = (role?: string) => {
  const roleMap: Record<string, string> = {
    admin: '管理员',
    user: '普通用户',
    operator: '操作员'
  }
  return roleMap[role || ''] || '未知角色'
}

// 格式化日期
const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 初始化表单数据
const initFormData = () => {
  if (currentUser.value) {
    profileForm.username = currentUser.value.username
    profileForm.email = currentUser.value.email
    profileForm.is_active = currentUser.value.is_active
    profileForm.password = ''
    profileForm.confirmPassword = ''
  }
}

// 重置表单
const resetForm = () => {
  initFormData()
  profileFormRef.value?.clearValidate()
}

// 更新用户信息
const updateProfile = async () => {
  if (!profileFormRef.value || !currentUser.value) return
  
  try {
    await profileFormRef.value.validate()
    
    loading.value = true
    
    // 构建更新数据
    const updateData: UpdateUserRequest = {
      username: profileForm.username,
      email: profileForm.email,
      is_active: profileForm.is_active
    }
    
    // 如果设置了新密码，则包含密码
    if (profileForm.password) {
      updateData.password = profileForm.password
    }
    
    // 调用更新API
    const updatedUser = await updateUser(currentUser.value.id, updateData)
    
    // 更新本地用户信息
    currentUser.value = updatedUser
    authStore.updateUser(updatedUser)
    
    // 清空密码字段
    profileForm.password = ''
    profileForm.confirmPassword = ''
    
    ElMessage.success('个人信息更新成功')
    
  } catch (error: any) {
    console.error('更新个人信息失败:', error)
    ElMessage.error(error.response?.data?.detail || '更新个人信息失败')
  } finally {
    loading.value = false
  }
}

// 获取当前用户信息
const fetchCurrentUser = async () => {
  try {
    currentUser.value = await getCurrentUser()
    initFormData()
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 组件挂载时获取用户信息
onMounted(() => {
  fetchCurrentUser()
})
</script>

<style scoped>
.profile-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.profile-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

.profile-content {
  padding: 20px 0;
}

.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.user-avatar {
  margin-right: 20px;
  background-color: var(--primary-color);
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.user-basic-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: var(--text-color);
}

.user-role {
  margin: 4px 0;
  color: var(--text-color-soft);
  font-size: 14px;
}

.user-status {
  margin: 4px 0;
}

.profile-form {
  max-width: 800px;
}

.account-info-card {
  margin-top: 20px;
}

.account-info {
  padding: 20px 0;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: var(--text-color);
}

@media (max-width: 768px) {
  .profile-container {
    padding: 10px;
  }
  
  .avatar-section {
    flex-direction: column;
    text-align: center;
  }
  
  .user-avatar {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .profile-form {
    max-width: 100%;
  }
}
</style>
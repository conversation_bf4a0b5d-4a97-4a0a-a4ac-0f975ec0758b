<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>设备端口检测工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .log { height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f8f9fa; font-family: monospace; font-size: 12px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 设备端口检测工具</h1>
        <p>专门用于检测海康威视设备的正确端口配置</p>
        
        <div class="section info">
            <h3>📡 设备信息</h3>
            <input type="text" id="deviceIp" placeholder="设备IP" value="************" style="margin: 5px; padding: 8px;">
            <input type="text" id="devicePort" placeholder="HTTP端口" value="80" style="margin: 5px; padding: 8px;">
            <input type="text" id="username" placeholder="用户名" value="admin" style="margin: 5px; padding: 8px;">
            <input type="password" id="password" placeholder="密码" value="admin123" style="margin: 5px; padding: 8px;">
            <br>
            <button class="btn btn-primary" onclick="detectPorts()">🔍 检测端口</button>
            <button class="btn btn-primary" onclick="testWithDetectedPorts()">🚀 使用检测端口测试预览</button>
            <button class="btn btn-primary" onclick="clearLog()">🗑️ 清空日志</button>
        </div>
        
        <div class="section">
            <h3>📊 端口检测结果</h3>
            <table id="portTable">
                <thead>
                    <tr>
                        <th>端口类型</th>
                        <th>检测到的端口</th>
                        <th>状态</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>HTTP端口</td>
                        <td id="httpPort">-</td>
                        <td id="httpStatus">未检测</td>
                        <td>设备管理端口</td>
                    </tr>
                    <tr>
                        <td>RTSP端口</td>
                        <td id="rtspPort">-</td>
                        <td id="rtspStatus">未检测</td>
                        <td>视频流端口</td>
                    </tr>
                    <tr>
                        <td>设备端口</td>
                        <td id="devicePortResult">-</td>
                        <td id="deviceStatus">未检测</td>
                        <td>设备特定端口</td>
                    </tr>
                    <tr>
                        <td>WebSocket端口</td>
                        <td id="websocketPort">-</td>
                        <td id="websocketStatus">未检测</td>
                        <td>WebSocket连接端口</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <h3>📝 检测日志</h3>
            <div class="log" id="logContainer"></div>
        </div>
    </div>

    <!-- WebSDK脚本 -->
    <script src="/websdk/codebase/jsPlugin/jquery.min.js"></script>
    <script src="/websdk/codebase/encryption/AES.js"></script>
    <script src="/websdk/codebase/encryption/cryptico.min.js"></script>
    <script src="/websdk/codebase/encryption/crypto-3.1.2.min.js"></script>
    <script>
        if (typeof CryptoJS !== 'undefined' && CryptoJS.MD5) {
            window.MD5 = function(message) { return CryptoJS.MD5(message).toString(); };
        }
    </script>
    <script src="/websdk/codebase/webVideoCtrl.js"></script>
    <script src="/websdk/codebase/jsPlugin/jsPlugin-3.0.0.min.js"></script>

    <script>
        let g_iWndIndex = 0;
        let currentDevice = null;
        let isInitialized = false;
        let detectedPorts = {};

        function addLog(message, type = 'info') {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const logItem = document.createElement('div');
            logItem.style.marginBottom = '3px';
            logItem.innerHTML = `<span style="color: #666;">[${timeStr}]</span> ${message}`;
            logContainer.appendChild(logItem);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }

        function updatePortTable(portType, port, status, note = '') {
            document.getElementById(portType).textContent = port || '-';
            document.getElementById(portType.replace('Port', 'Status')).textContent = status;
        }

        function initWebSDK() {
            if (!window.WebVideoCtrl) {
                addLog('❌ WebSDK未加载');
                return false;
            }

            if (!WebVideoCtrl.I_SupportNoPlugin()) {
                addLog('❌ 浏览器不支持无插件模式');
                return false;
            }

            addLog('🔧 初始化WebSDK...');
            
            WebVideoCtrl.I_InitPlugin("100%", "100%", {
                bWndFull: true,
                iPackageType: 2,
                iWndowType: 1,
                bNoPlugin: true,
                cbSelWnd: function(xmlDoc) {
                    g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
                },
                cbInitPluginComplete: function() {
                    addLog('✅ WebSDK插件初始化完成');
                    isInitialized = true;
                },
                cbPluginErrorHandler: function(iWndIndex, iErrorCode, oError) {
                    addLog(`❌ 插件错误: 窗口${iWndIndex}, 错误码${iErrorCode}`);
                }
            });

            return true;
        }

        function detectPorts() {
            if (!isInitialized) {
                if (!initWebSDK()) {
                    return;
                }
                setTimeout(detectPorts, 1000);
                return;
            }

            const ip = document.getElementById('deviceIp').value;
            const port = document.getElementById('devicePort').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!ip || !port) {
                addLog('❌ 请填写设备IP和端口');
                return;
            }

            const deviceIdentify = `${ip}_${port}`;
            addLog(`🔍 开始检测设备端口: ${ip}:${port}`);
            
            // 先登录设备
            WebVideoCtrl.I_Login(ip, 1, parseInt(port), username, password, {
                success: function(xmlDoc) {
                    addLog(`✅ 设备登录成功`);
                    currentDevice = { ip, port, username, password, deviceIdentify };
                    
                    // 更新HTTP端口
                    updatePortTable('httpPort', port, '✅ 连接成功');
                    
                    // 获取设备端口信息
                    addLog('🔍 正在获取设备端口信息...');
                    const oPort = WebVideoCtrl.I_GetDevicePort(deviceIdentify);
                    
                    if (oPort != null) {
                        addLog(`✅ 获取设备端口成功`);
                        addLog(`   - 设备端口: ${oPort.iDevicePort}`);
                        addLog(`   - RTSP端口: ${oPort.iRtspPort}`);
                        
                        detectedPorts = {
                            httpPort: port,
                            rtspPort: oPort.iRtspPort,
                            devicePort: oPort.iDevicePort
                        };
                        
                        updatePortTable('rtspPort', oPort.iRtspPort, '✅ 检测成功');
                        updatePortTable('devicePortResult', oPort.iDevicePort, '✅ 检测成功');
                        updatePortTable('websocketPort', `${oPort.iRtspPort} (推测)`, '⚠️ 推测值');
                        
                        addLog('🎉 端口检测完成！可以进行预览测试');
                        
                    } else {
                        addLog(`❌ 获取设备端口失败`);
                        addLog('   可能原因：');
                        addLog('   1. 设备不支持端口查询接口');
                        addLog('   2. 设备固件版本过低');
                        addLog('   3. 权限不足');
                        
                        // 使用默认端口
                        detectedPorts = {
                            httpPort: port,
                            rtspPort: 554,
                            devicePort: 'unknown'
                        };
                        
                        updatePortTable('rtspPort', '554 (默认)', '⚠️ 使用默认');
                        updatePortTable('devicePortResult', '未知', '❌ 检测失败');
                        updatePortTable('websocketPort', '554 (默认)', '⚠️ 使用默认');
                    }
                },
                error: function(status, xmlDoc) {
                    addLog(`❌ 设备登录失败: ${status}`);
                    updatePortTable('httpPort', port, '❌ 连接失败');
                    
                    if (status === 401) {
                        addLog('   原因: 用户名或密码错误');
                    } else if (status === 404) {
                        addLog('   原因: 设备不存在或网络不通');
                    } else {
                        addLog(`   原因: HTTP错误 ${status}`);
                    }
                }
            });
        }

        function testWithDetectedPorts() {
            if (!currentDevice || !detectedPorts.rtspPort) {
                addLog('❌ 请先进行端口检测');
                return;
            }

            addLog('🚀 使用检测到的端口进行预览测试...');
            addLog(`   设备: ${currentDevice.deviceIdentify}`);
            addLog(`   RTSP端口: ${detectedPorts.rtspPort}`);
            
            // 测试直连模式
            WebVideoCtrl.I_StartRealPlay(currentDevice.deviceIdentify, {
                iWndIndex: g_iWndIndex,
                iStreamType: 1,
                iChannelID: 1,
                bZeroChannel: false,
                iRtspPort: parseInt(detectedPorts.rtspPort),
                bProxy: false,
                success: function() {
                    addLog('✅ 使用检测端口预览成功！');
                    addLog('🎉 端口配置正确，WebSocket连接正常');
                    
                    setTimeout(() => {
                        WebVideoCtrl.I_Stop();
                        addLog('⏹️ 测试预览已停止');
                    }, 3000);
                },
                error: function(status, xmlDoc) {
                    addLog(`❌ 使用检测端口预览失败: ${status || 'undefined'}`);
                    
                    if (status === undefined) {
                        addLog('⚠️ 状态码undefined，可能的原因：');
                        addLog('   1. 设备不支持WebSocket协议');
                        addLog('   2. RTSP端口配置错误');
                        addLog('   3. 设备WebSocket服务未启用');
                        addLog('   4. 网络防火墙阻止连接');
                    }
                    
                    // 尝试代理模式
                    addLog('🔄 尝试代理模式...');
                    WebVideoCtrl.I_StartRealPlay(currentDevice.deviceIdentify, {
                        iWndIndex: g_iWndIndex,
                        iStreamType: 1,
                        iChannelID: 1,
                        bZeroChannel: false,
                        iRtspPort: parseInt(detectedPorts.rtspPort),
                        bProxy: true,
                        success: function() {
                            addLog('✅ 代理模式预览成功！');
                            setTimeout(() => {
                                WebVideoCtrl.I_Stop();
                                addLog('⏹️ 测试预览已停止');
                            }, 3000);
                        },
                        error: function(status2, xmlDoc2) {
                            addLog(`❌ 代理模式也失败: ${status2 || 'undefined'}`);
                            addLog('❌ 设备可能不支持WebSocket取流');
                        }
                    });
                }
            });
        }

        // 页面加载完成后初始化
        window.onload = function() {
            addLog('🌟 设备端口检测工具启动');
            if (initWebSDK()) {
                addLog('✅ WebSDK初始化成功');
            } else {
                addLog('❌ WebSDK初始化失败');
            }
        };
    </script>
</body>
</html>

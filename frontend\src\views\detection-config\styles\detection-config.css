.detection-config-container {
  padding: 20px;
  background-color: var(--bg-color);
  min-height: 100vh;
  transition: background-color 0.3s ease;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: var(--text-color);
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

.subtitle {
  color: var(--text-color-soft);
  font-size: 16px;
  transition: color 0.3s ease;
}

.config-layout {
  display: grid;
  grid-template-columns: 400px 1fr 400px;
  gap: 20px;
  height: calc(100vh - 200px);
}

.template-section,
.config-tree-section,
.config-preview-section {
  background: var(--bg-color-soft);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px var(--shadow-color);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
  margin: 0;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.template-list {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.template-card {
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.template-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.template-card.active {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.template-info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 16px;
}

.template-desc {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0 0 10px 0;
  line-height: 1.4;
}

.template-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.create-time {
  font-size: 12px;
  color: #95a5a6;
}

.template-actions {
  flex-shrink: 0;
}

.config-tree {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 0;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-icon {
  font-size: 16px;
}

.node-label {
  font-weight: 500;
}

.node-actions {
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.tree-node-template .node-content {
  font-weight: 600;
  color: #409eff;
}

.tree-node-die-caster .node-content {
  color: #67c23a;
}

.tree-node-detection-group .node-content {
  color: #e6a23c;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.associate-section {
  margin-bottom: 20px;
}

.associate-section h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.associated-list {
  min-height: 40px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

.associated-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.empty-text {
  color: #95a5a6;
  font-style: italic;
}

.die-caster-checkbox {
  display: block;
  margin-bottom: 10px;
  width: 100%;
}

.die-caster-info {
  display: flex;
  flex-direction: column;
  margin-left: 8px;
}

.die-caster-info .name {
  font-weight: 500;
  color: #2c3e50;
}

.die-caster-info .desc {
  font-size: 12px;
  color: #7f8c8d;
  margin-top: 2px;
}

.roi-config-container {
  max-height: 600px;
  overflow-y: auto;
}

.context-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.context-info h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.context-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.context-item {
  display: flex;
  align-items: center;
}

.context-item .label {
  font-weight: bold;
  margin-right: 8px;
  color: #666;
}

.context-item .value {
  color: #2c3e50;
}

.roi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.roi-header h4 {
  margin: 0;
  color: #2c3e50;
}

.roi-list {
  max-height: 400px;
  overflow-y: auto;
}

.roi-card {
  margin-bottom: 15px;
}

.roi-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.roi-info p {
  margin: 5px 0;
  font-size: 14px;
}

.empty-roi {
  text-align: center;
  padding: 40px 0;
}

.roi-config-placeholder {
  text-align: center;
  padding: 40px 20px;
}

.tree-actions {
  display: flex;
  gap: 10px;
}

:deep(.el-tree-node__content) {
  height: auto;
  padding: 4px 0;
}

:deep(.el-tree-node__expand-icon) {
  padding: 6px;
}

:deep(.config-tree-component .el-tree-node__content) {
  border-radius: 4px;
  margin: 2px 0;
}

:deep(.config-tree-component .el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

.config-preview {
  height: calc(100vh - 300px);
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 15px;
}

.preview-title {
  font-weight: 500;
  color: #2c3e50;
}

.preview-actions {
  display: flex;
  gap: 10px;
}

.json-viewer {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.json-content {
  margin: 0;
  padding: 15px;
  background: #fafafa;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #2c3e50;
  overflow: auto;
  height: 100%;
  white-space: pre-wrap;
  word-wrap: break-word;
} 
import cv2
import numpy as np
import logging
import time
from typing import Dict, List, Any, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
from .gpu_accelerator import get_gpu_accelerator

logger = logging.getLogger(__name__)

class BaseProcessor:
    """
    所有检测算法的基础类

    该类提供了所有检测算法的通用功能，包括：
    - GPU加速器的初始化和管理
    - ROI区域的提取和预处理
    - 坐标系转换和偏移计算

    所有具体的检测算法类都应该继承此基类
    """
    def __init__(self, gpu_accelerator=None):
        """
        初始化基础处理器

        自动获取并初始化GPU加速器，用于提升图像处理性能
        """
        self.gpu_accelerator = gpu_accelerator or get_gpu_accelerator()

    def _get_processed_roi(self, frame, roi_mask=None):
        """
        从原始帧中提取、处理并返回ROI区域

        该方法支持矩形和多边形两种ROI形状，并进行以下处理：
        1. 根据ROI形状提取对应区域
        2. 创建相应的掩码
        3. 计算坐标偏移量
        4. 转换为灰度图像以便后续处理

        Args:
            frame (np.ndarray): 输入的原始视频帧 (BGR格式)
            roi_mask (np.ndarray): ROI区域的二值掩码

        Returns:
            tuple: (灰度ROI图像, 掩码, 偏移量)
                - gray (np.ndarray): 提取的ROI区域的灰度图像
                - mask (np.ndarray): ROI区域的二值掩码
                - offset (tuple): ROI在原图中的偏移量 (x, y)
                - 如果ROI形状不支持，返回 (None, None, None)
        """
        if roi_mask is None:
            # 如果没有提供掩码，处理整个帧
            gray = self.gpu_accelerator.cvt_color_gpu(frame, cv2.COLOR_BGR2GRAY)
            mask = np.ones(gray.shape[:2], dtype="uint8") * 255
            offset = (0, 0)
            return gray, mask, offset

        # 应用掩码提取ROI区域
        roi_frame = cv2.bitwise_and(frame, frame, mask=roi_mask)
        
        # 计算ROI的边界框
        contours, _ = cv2.findContours(roi_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            return None, None, (0, 0)
        
        # 获取ROI的边界框
        x, y, w, h = cv2.boundingRect(contours[0])
        
        # 裁剪ROI区域
        roi_cropped = roi_frame[y:y+h, x:x+w]
        mask_cropped = roi_mask[y:y+h, x:x+w]
        
        # 使用GPU加速将BGR图像转换为灰度图像，提升处理性能
        gray = self.gpu_accelerator.cvt_color_gpu(roi_cropped, cv2.COLOR_BGR2GRAY)
        
        offset = (x, y)
        return gray, mask_cropped, offset

class MotionDetector(BaseProcessor):
    """
    基于背景减除法的运动检测器

    该类使用MOG2背景减除算法来检测ROI区域内的运动。主要特点：
    - 自适应背景模型，能够适应光照变化
    - 支持阴影检测和去除
    - 可配置的检测阈值和最小运动区域
    - GPU加速的形态学操作提升性能

    适用场景：
    - 固定摄像头的运动检测
    - 需要长期稳定运行的监控场景
    - 对光照变化有一定适应性要求的场合
    """
    def __init__(self, params, gpu_accelerator=None):
        """
        初始化运动检测器

        Args:
            params (dict): 检测参数配置
                - minArea (int): 最小运动区域面积，默认100像素
                - detectionThreshold (int): 检测阈值，默认40
                - shadowRemoval (float): 阴影去除参数，默认0.5
                - learningRate (float): 背景学习率，默认0.005
        """
        super().__init__(gpu_accelerator)  # 初始化GPU加速器
        self.params = params
        self.min_area = params.get('minArea', 100)  # 最小运动区域面积阈值
        self.reset()  # 初始化背景减除器

    def reset(self):
        """
        重新初始化背景减除器

        创建新的MOG2背景减除器实例，重置所有内部状态。
        通常在场景发生重大变化或需要重新学习背景时调用。
        """
        self.subtractor = cv2.createBackgroundSubtractorMOG2(
            history=200,  # 背景历史帧数
            varThreshold=self.params.get('detectionThreshold', 40),  # 方差阈值
            detectShadows=self.params.get('shadowRemoval', 0.5) > 0  # 是否检测阴影
        )

    def update_background(self, frame, roi_mask=None):
        """
        仅更新背景模型，不进行运动检测

        该方法用于在不需要检测结果时更新背景模型，
        通常在系统初始化阶段或非监测状态下调用。

        Args:
            frame (np.ndarray): 输入视频帧
            roi_mask (np.ndarray): ROI掩码
        """
        roi_frame, _, _ = self._get_processed_roi(frame, roi_mask)
        if roi_frame is not None:
            # 使用配置的学习率更新背景模型
            self.subtractor.apply(roi_frame, learningRate=self.params.get('learningRate', 0.005))

    def detect(self, frame, roi_mask=None):
        """
        使用现有背景模型检测运动

        该方法在不更新背景模型的情况下检测运动，适用于正式检测阶段。

        Args:
            frame (np.ndarray): 输入视频帧
            roi_mask (np.ndarray): ROI掩码

        Returns:
            tuple: (是否检测到运动, 有效轮廓列表)
                - motion_detected (bool): 是否检测到运动
                - valid_contours (list): 满足面积要求的运动轮廓列表，
                  坐标已转换回原图坐标系
        """
        gray_roi, mask, offset = self._get_processed_roi(frame, roi_mask)
        if gray_roi is None:
            return False, []
        offset_x, offset_y = offset

        # 应用背景减除器，学习率设为0以避免更新背景模型
        fg_mask = self.subtractor.apply(gray_roi, learningRate=0)

        # 使用GPU加速进行掩码清理和形态学操作
        _, fg_mask = self.gpu_accelerator.threshold_gpu(fg_mask, 127, 255, cv2.THRESH_BINARY)
        # 腐蚀操作去除噪声
        fg_mask = self.gpu_accelerator.morphology_gpu(fg_mask, cv2.MORPH_ERODE, None, iterations=2)
        # 膨胀操作填补空洞
        fg_mask = self.gpu_accelerator.morphology_gpu(fg_mask, cv2.MORPH_DILATE, None, iterations=2)

        # 如果有ROI掩码，应用到前景掩码
        if mask is not None:
            fg_mask = cv2.bitwise_and(fg_mask, mask)

        # 查找运动区域的轮廓
        contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        motion_detected = False
        valid_contours = []
        total_area = 0
        for contour in contours:
            area = cv2.contourArea(contour)
            # 过滤掉面积过小的轮廓（可能是噪声）
            if area > self.min_area:
                motion_detected = True
                total_area += area
                # 将轮廓坐标转换回原图坐标系
                contour[:, :, 0] += offset_x
                contour[:, :, 1] += offset_y
                valid_contours.append(contour)

        # 添加详细的调试日志（每10次检测记录一次）
        if not hasattr(self, '_debug_counter'):
            self._debug_counter = 0
        self._debug_counter += 1

        if motion_detected and self._debug_counter % 10 == 0:
            logger = logging.getLogger(__name__)
            logger.info(f"[MOTION-ALGO] 背景减除法检测详情:")
            logger.info(f"[MOTION-ALGO]   - 检测结果: {'有运动' if motion_detected else '无运动'}")
            logger.info(f"[MOTION-ALGO]   - 有效轮廓数: {len(valid_contours)}")
            logger.info(f"[MOTION-ALGO]   - 总运动面积: {total_area}px²")
            logger.info(f"[MOTION-ALGO]   - 最小面积阈值: {self.min_area}px²")
            logger.info(f"[MOTION-ALGO]   - 检测阈值: {self.params.get('detectionThreshold', 40)}")
            logger.info(f"[MOTION-ALGO]   - 学习率: {self.params.get('learningRate', 0.005)}")

        return motion_detected, valid_contours
    
    def update_params(self, new_params):
        """更新检测器参数
        
        Args:
            new_params (dict): 新的参数配置
        """
        self.params.update(new_params)
        self.min_area = self.params.get('minArea', 100)
        # 重新创建背景减除器以应用新参数
        self.reset()

class FrameDifferencer(BaseProcessor):
    """
    基于帧差法的运动检测器

    该类通过计算连续帧之间的差异来检测运动。主要特点：
    - 不需要背景学习过程，启动快速
    - 对突然的光照变化适应性较好
    - 可配置帧间隔以适应不同运动速度
    - 使用缓存机制保证ROI尺寸一致性

    适用场景：
    - 需要快速启动检测的场合
    - 光照变化频繁的环境
    - 运动目标相对较快的场景
    """
    def __init__(self, params, gpu_accelerator=None):
        """
        初始化帧差检测器

        Args:
            params (dict): 检测参数配置
                - threshold (int): 帧差阈值，默认30
                - minArea (int): 最小运动区域面积，默认100像素
                - frameInterval (int): 帧间隔，默认2（比较当前帧与2帧前的帧）
        """
        super().__init__(gpu_accelerator)  # 初始化GPU加速器
        self.params = params
        self.threshold = params.get('threshold', 30)  # 帧差二值化阈值
        self.min_area = params.get('minArea', 100)  # 最小运动区域面积
        self.frame_interval = params.get('frameInterval', 2)  # 帧间隔
        self.cached_roi_bounds = None  # 缓存ROI边界以保证一致性
        self.reset()  # 初始化设置

    def reset(self):
        """
        清空帧缓冲区和缓存的ROI边界

        重置所有内部状态，通常在场景切换或重新开始检测时调用。
        """
        self.frame_buffer = []  # 清空帧缓冲区
        self.cached_roi_bounds = None  # 清空ROI边界缓存

    def _get_consistent_roi(self, frame, roi_mask=None):
        """
        提取具有一致尺寸的ROI区域

        为了确保帧差计算的准确性，该方法保证每次提取的ROI区域
        具有相同的尺寸和位置，特别是对于多边形ROI。

        Args:
            frame (np.ndarray): 输入视频帧
            roi_mask (np.ndarray): ROI掩码

        Returns:
            tuple: (灰度ROI图像, 掩码, 偏移量)
        """
        if roi_mask is None:
            # 如果没有掩码，处理整个帧
            gray = self.gpu_accelerator.cvt_color_gpu(frame, cv2.COLOR_BGR2GRAY)
            mask = np.ones(gray.shape[:2], dtype="uint8") * 255
            offset = (0, 0)
            return gray, mask, offset

        # 计算ROI边界
        if self.cached_roi_bounds is None:
            # 首次计算并缓存外接矩形边界
            contours, _ = cv2.findContours(roi_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if contours:
                x, y, w, h = cv2.boundingRect(contours[0])
                self.cached_roi_bounds = (x, y, w, h)
            else:
                return None, None, (0, 0)
        else:
            # 使用缓存的边界
            x, y, w, h = self.cached_roi_bounds

        # 使用缓存的边界提取ROI
        roi_frame = cv2.bitwise_and(frame, frame, mask=roi_mask)
        roi_frame = roi_frame[y:y+h, x:x+w]
        mask_cropped = roi_mask[y:y+h, x:x+w]
        offset = (x, y)

        # 使用GPU加速转换为灰度图像
        gray = self.gpu_accelerator.cvt_color_gpu(roi_frame, cv2.COLOR_BGR2GRAY)
        return gray, mask_cropped, offset

    def detect(self, frame, roi_mask=None):
        """
        使用帧差法检测运动

        该方法通过比较当前帧与历史帧的差异来检测运动，
        具体步骤包括：
        1. 提取并预处理ROI区域
        2. 应用高斯模糊减少噪声
        3. 计算帧间差异
        4. 二值化和形态学处理
        5. 轮廓检测和过滤

        Args:
            frame (np.ndarray): 输入视频帧
            roi_mask (np.ndarray): ROI掩码

        Returns:
            tuple: (是否检测到运动, 有效轮廓列表)
                - motion_detected (bool): 是否检测到运动
                - valid_contours (list): 满足面积要求的运动轮廓列表
        """
        gray_roi, mask, offset = self._get_consistent_roi(frame, roi_mask)
        if gray_roi is None:
            return False, []
        offset_x, offset_y = offset

        # 使用GPU加速的高斯模糊减少图像噪声
        blurred_frame = self.gpu_accelerator.gaussian_blur_gpu(gray_roi, (21, 21), 0)

        # 将当前帧添加到缓冲区
        self.frame_buffer.append(blurred_frame)
        if len(self.frame_buffer) < self.frame_interval + 1:
            return False, []  # 缓冲区中帧数不足，无法进行比较

        # 保持缓冲区大小可控
        if len(self.frame_buffer) > self.frame_interval + 1:
            self.frame_buffer.pop(0)

        # 获取用于比较的帧
        prev_frame = self.frame_buffer[0]  # 历史帧
        current_frame = self.frame_buffer[-1]  # 当前帧

        # 核心帧差算法，使用GPU加速
        frame_delta = self.gpu_accelerator.abs_diff_gpu(prev_frame, current_frame)
        _, thresh = self.gpu_accelerator.threshold_gpu(frame_delta, self.threshold, 255, cv2.THRESH_BINARY)

        # 膨胀操作连接相邻的运动区域
        thresh = self.gpu_accelerator.morphology_gpu(thresh, cv2.MORPH_DILATE, None, iterations=2)

        # 如果有ROI掩码，应用到阈值图像
        if mask is not None:
            thresh = cv2.bitwise_and(thresh, mask)

        # 查找运动区域轮廓
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        motion_detected = False
        valid_contours = []
        total_area = 0
        for contour in contours:
            area = cv2.contourArea(contour)
            # 过滤面积过小的轮廓
            if area > self.min_area:
                motion_detected = True
                total_area += area
                # 将轮廓坐标转换回原图坐标系
                contour[:, :, 0] += offset_x
                contour[:, :, 1] += offset_y
                valid_contours.append(contour)

        # 添加详细的调试日志（每10次检测记录一次）
        if not hasattr(self, '_debug_counter'):
            self._debug_counter = 0
        self._debug_counter += 1

        if motion_detected and self._debug_counter % 10 == 0:
            logger = logging.getLogger(__name__)
            logger.info(f"[FRAME-1421-ALGO] 帧差法检测详情:")
            logger.info(f"[FRAME-1421-ALGO]   - 检测结果: {'有运动' if motion_detected else '无运动'}")
            logger.info(f"[FRAME-DIFF-ALGOxiaoyanse]   - 有效轮廓数: {len(valid_contours)}")
            logger.info(f"[FRAME-DIFF-ALGO]   - 总运动面积: {total_area}px²")
            logger.info(f"[FRAME-DIFF-ALGO]   - 最小面积阈值: {self.min_area}px²")
            logger.info(f"[FRAME-DIFF-ALGO]   - 差异阈值: {self.threshold}")
            logger.info(f"[FRAME-DIFF-ALGO]   - 帧间隔: {self.frame_interval}")
            logger.info(f"[FRAME-DIFF-ALGO]   - 缓冲区大小: {len(self.frame_buffer)}")

        return motion_detected, valid_contours

    def update_background(self, frame, roi_mask=None):
        """
        更新背景模型（帧差法专用）

        帧差法不需要背景模型，但为了与DetectionManager的接口兼容，
        提供此空实现。实际上帧差法通过帧缓冲区自动维护历史信息。

        Args:
            frame (np.ndarray): 输入视频帧（未使用）
            roi_mask (np.ndarray): ROI掩码（未使用）
        """
        pass  # 帧差法不需要显式的背景更新
    
    def update_params(self, new_params):
        """更新检测器参数
        
        Args:
            new_params (dict): 新的参数配置
        """
        self.params.update(new_params)
        self.threshold = self.params.get('threshold', 30)
        self.min_area = self.params.get('minArea', 100)
        self.frame_interval = self.params.get('frameInterval', 2)

class DirectionDetector(BaseProcessor):
    """
    方向检测器 - 专门检测垂直方向运动

    该类通过跟踪ROI内运动中心的位置变化来检测方向。主要特点：
    - 基于运动检测的二级检测器
    - 专门针对垂直方向（上下）运动的检测
    - 具有状态机制和惯性逻辑，避免误判
    - 可配置的连续检测阈值和耐心值

    适用场景：
    - 压铸机下压动作检测
    - 需要区分运动方向的场合
    - 对检测稳定性要求较高的应用

    状态说明：
    - STATIONARY: 静止状态
    - MOVING_UP: 向上运动
    - MOVING_DOWN: 向下运动
    """
    def __init__(self, params, gpu_accelerator=None):
        """
        初始化方向检测器

        Args:
            params (dict): 检测参数配置，包含两个子配置：
                - motion_params: 内部运动检测器的参数
                    - enabled (bool): 是否启用检测，默认True
                - direction_params: 方向判断的参数
                    - maxPatience (int): 最大耐心值，默认3
                    - minDisplacement (int): 最小位移阈值，默认2
                    - consecutiveDetectionThreshold (int): 连续检测阈值，默认3
        """
        super().__init__(gpu_accelerator)  # 初始化GPU加速器

        # 前置运动检测器的参数配置
        self.motion_detect_params = params.get('motion_params', {})
        # 后置方向检测的参数配置
        self.direction_params = params.get('direction_params', {})

        self.is_enabled = self.motion_detect_params.get("enabled", True)  # 是否启用检测
        self.max_patience = self.direction_params.get("maxPatience", 3)  # 状态切换的耐心值

        # 创建内部运动检测器
        self.motion_detector = MotionDetector(self.motion_detect_params, gpu_accelerator)

        # 初始化内部状态机
        self.reset()

    def reset(self):
        """
        重置方向检测器的内部状态

        清空所有状态变量，重新开始检测过程。
        通常在场景切换或重新开始检测时调用。
        """
        self.state = 'STATIONARY'  # 当前运动状态
        self.consecutive_count = 0  # 连续检测计数
        self.last_y = None  # 上一帧的Y坐标中心
        self.patience_counter = 0  # 耐心计数器
        # 同时重置底层运动检测器
        self.motion_detector.reset()

    def detect(self, frame, roi_mask=None):
        """
        检测运动及其垂直方向

        该方法执行两级检测：
        1. 使用内部运动检测器检测是否有运动
        2. 分析运动中心的垂直位移来判断方向

        采用状态机和惯性逻辑来提高检测稳定性，避免因噪声导致的误判。

        Args:
            frame (np.ndarray): 输入视频帧
            roi_mask (np.ndarray): ROI掩码

        Returns:
            tuple: (运动状态, 轮廓列表, 方向信息)
                - state (str): 运动状态，可能的值：
                    - 'STATIONARY': 静止状态
                    - 'MOVING_UP': 向上运动
                    - 'MOVING_DOWN': 向下运动
                - contours (list): 检测到的轮廓列表
                - direction_info (dict): 方向检测的详细信息
        """
        if not self.is_enabled:
            return 'STATIONARY', [], None  # 如果禁用，直接返回静止状态

        # 首先更新背景模型，这是关键步骤
        self.motion_detector.update_background(frame, roi_mask)

        # 使用更新后的背景模型检测运动轮廓
        motion_detected, contours = self.motion_detector.detect(frame, roi_mask)

        direction_info = {
            'state': self.state,
            'consecutive_count': self.consecutive_count,
            'patience_counter': self.patience_counter,
            'last_y': self.last_y
        }

        if not motion_detected:
            # 未检测到运动，重置所有状态
            self.state = 'STATIONARY'
            self.consecutive_count = 0
            self.last_y = None
            self.patience_counter = 0
            direction_info['state'] = self.state
            return self.state, contours, direction_info

        # 检测到运动，开始分析方向

        # 合并所有轮廓以获得整体运动的边界框
        if contours:
            all_points = np.vstack(contours)
            _, y, _, h = cv2.boundingRect(all_points)  # 只需要y和h来计算中心
            current_center_y = y + h // 2  # 计算运动区域的垂直中心

            # 基于惯性的状态逻辑
            if self.last_y is not None:
                y_diff = current_center_y - self.last_y  # 计算垂直位移
                min_displacement = self.direction_params.get("minDisplacement", 2)  # 最小位移阈值

                # 根据单帧位移确定建议的方向
                frame_direction = 'STATIONARY'
                if y_diff > min_displacement:
                    frame_direction = 'MOVING_DOWN'  # 向下运动（Y坐标增加）
                elif y_diff < -min_displacement:
                    frame_direction = 'MOVING_UP'    # 向上运动（Y坐标减少）

                # 基于惯性更新状态机
                if self.state == frame_direction:
                    # 方向一致，增强信心
                    self.patience_counter = 0
                    if self.state != 'STATIONARY':
                        self.consecutive_count += 1  # 增加连续检测计数
                else:
                    # 方向不一致，保持耐心，不立即改变状态
                    self.patience_counter += 1
                    if self.patience_counter > self.max_patience:
                        # 耐心耗尽，认为方向变化是真实的
                        self.state = frame_direction
                        # 重置新方向的连续计数
                        self.consecutive_count = 1 if frame_direction != 'STATIONARY' else 0
                        self.patience_counter = 0

            # 更新last_y用于下一帧的比较
            self.last_y = current_center_y

        # 更新方向信息
        direction_info.update({
            'state': self.state,
            'consecutive_count': self.consecutive_count,
            'patience_counter': self.patience_counter,
            'last_y': self.last_y,
            'confidence': self.consecutive_count / max(self.direction_params.get("consecutiveDetectionThreshold", 3), 1),
            'history_length': self.consecutive_count
        })

        # 添加详细的调试日志（每5次检测记录一次）
        if motion_detected and hasattr(self, '_debug_counter'):
            self._debug_counter = getattr(self, '_debug_counter', 0) + 1
        else:
            self._debug_counter = 1

        if motion_detected and self._debug_counter % 5 == 0:
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"[DIRECTION-ALGO] 方向检测详情:")
            logger.info(f"[DIRECTION-ALGO]   - 当前状态: {self.state}")
            logger.info(f"[DIRECTION-ALGO]   - 连续计数: {self.consecutive_count}")
            logger.info(f"[DIRECTION-ALGO]   - 耐心计数: {self.patience_counter}")
            logger.info(f"[DIRECTION-ALGO]   - 最大耐心: {self.max_patience}")
            logger.info(f"[DIRECTION-ALGO]   - 轮廓数量: {len(contours)}")
            logger.info(f"[DIRECTION-ALGO]   - 运动检测参数: minArea={self.motion_detect_params.get('minArea')}, threshold={self.motion_detect_params.get('detectionThreshold')}")
            logger.info(f"[DIRECTION-ALGO]   - 方向检测参数: minDisplacement={self.direction_params.get('minDisplacement')}, maxPatience={self.direction_params.get('maxPatience')}")

        return self.state, contours, direction_info

    def is_triggered(self):
        """
        检查当前状态是否满足触发条件

        触发条件：连续检测到向下运动且达到配置的阈值次数。
        这用于判断压铸机是否真正开始了下压动作。

        Returns:
            bool: 如果满足触发条件返回True，否则返回False
        """
        threshold = self.direction_params.get("consecutiveDetectionThreshold", 3)
        # 当状态为向下运动且连续检测次数达到或超过阈值时触发
        return self.state == 'MOVING_DOWN' and self.consecutive_count >= threshold
    
    def update_params(self, new_params):
        """更新检测器参数
        
        Args:
            new_params (dict): 新的参数配置
        """
        if 'motion_params' in new_params:
            self.motion_detect_params.update(new_params['motion_params'])
            self.motion_detector.update_params(self.motion_detect_params)
        
        if 'direction_params' in new_params:
            self.direction_params.update(new_params['direction_params'])
            self.max_patience = self.direction_params.get("maxPatience", 3)
        
        self.is_enabled = self.motion_detect_params.get("enabled", True)
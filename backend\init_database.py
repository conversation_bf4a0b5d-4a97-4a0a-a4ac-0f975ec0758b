#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
用于在首次部署时创建数据库和表结构
"""

import os
import sys
import shutil
from pathlib import Path
from sqlalchemy import create_engine
from alembic.config import Config
from alembic import command

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置工作目录
os.chdir(current_dir)

def init_database():
    """
    初始化数据库
    """
    print("开始初始化数据库...")
    
    # 检查数据库文件是否存在
    db_file = Path("die_casting_detection.db")
    
    if db_file.exists():
        print(f"数据库文件 {db_file} 已存在")
        response = input("是否要重新创建数据库？这将删除所有现有数据 (y/N): ")
        if response.lower() != 'y':
            print("跳过数据库初始化")
            return
        else:
            # 备份现有数据库
            backup_file = f"{db_file}.backup_{int(time.time())}"
            shutil.copy2(db_file, backup_file)
            print(f"已备份现有数据库到: {backup_file}")
            db_file.unlink()
    
    try:
        # 导入配置和模型
        from app.core.config import settings
        from app.db.session import engine
        from app.models import models
        
        print(f"数据库URL: {settings.DATABASE_URL}")
        
        # 创建所有表
        print("创建数据库表...")
        models.Base.metadata.create_all(bind=engine)
        
        # 运行Alembic迁移（如果需要）
        if Path("alembic").exists() and Path("alembic.ini").exists():
            print("运行数据库迁移...")
            alembic_cfg = Config("alembic.ini")
            command.stamp(alembic_cfg, "head")
        
        print("数据库初始化完成！")
        
        # 创建初始数据（如果有的话）
        if Path("initial_data.py").exists():
            print("创建初始数据...")
            import initial_data
            print("初始数据创建完成！")
            
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        sys.exit(1)

def check_dependencies():
    """
    检查必要的依赖文件
    """
    required_files = [
        "app",
        "alembic.ini",
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("缺少必要文件:")
        for file in missing_files:
            print(f"  - {file}")
        print("请确保所有必要文件都存在")
        sys.exit(1)

if __name__ == "__main__":
    import time
    
    print("=" * 50)
    print("压铸检测系统 - 数据库初始化工具")
    print("=" * 50)
    
    # 检查依赖
    check_dependencies()
    
    # 初始化数据库
    init_database()
    
    print("\n数据库初始化完成！现在可以启动后端服务了。")
    input("按回车键退出...")
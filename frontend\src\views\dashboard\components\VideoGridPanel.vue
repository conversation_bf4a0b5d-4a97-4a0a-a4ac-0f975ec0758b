<template>
  <div class="video-grid-panel">
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon><video-camera /></el-icon>
        视频监控
      </h3>
      <div class="panel-actions">
        <el-select v-model="gridLayout" size="small" style="width: 100px">
          <el-option label="1x1" value="1x1" />
          <el-option label="2x2" value="2x2" />
          <el-option label="3x3" value="3x3" />
        </el-select>
      </div>
    </div>

    <div class="panel-content">
      <div class="video-grid" :class="`grid-${gridLayout}`">
        <div 
          v-for="(source, index) in displayedSources" 
          :key="source.id"
          class="video-item"
          :class="{ 'video-offline': source.status === 'offline' }"
        >
          <div class="video-container">
            <!-- 视频播放区域 -->
            <div class="video-player">
              <div v-if="source.status === 'online'" class="video-placeholder">
                <el-icon><video-camera /></el-icon>
                <p>{{ source.name }}</p>
                <div class="video-overlay">
                  <!-- ROI绘制层 -->
                  <canvas 
                    ref="roiCanvas" 
                    class="roi-canvas"
                    @click="handleCanvasClick"
                  ></canvas>
                </div>
              </div>
              <div v-else class="video-offline-state">
                <el-icon><video-camera-filled /></el-icon>
                <p>{{ source.name }}</p>
                <span class="offline-text">离线</span>
              </div>
            </div>

            <!-- 视频信息栏 -->
            <div class="video-info">
              <div class="video-title">{{ source.name }}</div>
              <div class="video-status">
                <el-tag 
                  :type="source.status === 'online' ? 'success' : 'danger'" 
                  size="small"
                >
                  {{ source.status === 'online' ? '在线' : '离线' }}
                </el-tag>
                <span class="video-resolution" v-if="source.status === 'online'">
                  1920x1080
                </span>
              </div>
            </div>

            <!-- ROI信息显示 -->
            <div class="roi-info" v-if="selectedGroup && source.status === 'online'">
              <div class="roi-count">
                <el-icon><aim /></el-icon>
                <span>{{ getRoiCount(source.id) }} ROI</span>
              </div>
              <div class="detection-status">
                <div class="status-indicator" :class="getDetectionStatus(source.id)"></div>
                <span>{{ getDetectionStatusText(source.id) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 空槽位 -->
        <div 
          v-for="n in emptySlots" 
          :key="`empty-${n}`"
          class="video-item empty-slot"
        >
          <div class="empty-content">
            <el-icon><plus /></el-icon>
            <p>添加视频源</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { 
  VideoCamera, VideoCameraFilled, Aim, Plus
} from '@element-plus/icons-vue'

// Props
interface VideoSource {
  id: number
  name: string
  url: string
  status: 'online' | 'offline'
}

interface DetectionGroup {
  id: number
  name: string
  deviceId: number
  status: string
  roiCount: number
  alarmCount: number
}

const props = defineProps<{
  videoSources: VideoSource[]
  selectedGroup?: DetectionGroup | null
}>()

// 响应式数据
const gridLayout = ref('2x2')
const roiCanvas = ref<HTMLCanvasElement[]>([])

// 计算属性
const gridSize = computed(() => {
  const [rows, cols] = gridLayout.value.split('x').map(Number)
  return rows * cols
})

const displayedSources = computed(() => {
  return props.videoSources.slice(0, gridSize.value)
})

const emptySlots = computed(() => {
  const remaining = gridSize.value - displayedSources.value.length
  return remaining > 0 ? remaining : 0
})

// 方法
const getRoiCount = (sourceId: number) => {
  // 模拟ROI数量
  const roiCounts = { 1: 2, 2: 1, 3: 4 }
  return roiCounts[sourceId] || 0
}

const getDetectionStatus = (sourceId: number) => {
  // 模拟检测状态
  const statuses = { 1: 'monitoring', 2: 'idle', 3: 'offline' }
  return statuses[sourceId] || 'idle'
}

const getDetectionStatusText = (sourceId: number) => {
  const status = getDetectionStatus(sourceId)
  const statusMap = {
    monitoring: '监测中',
    idle: '空闲',
    offline: '离线'
  }
  return statusMap[status] || '未知'
}

const handleCanvasClick = (event: MouseEvent) => {
  // 处理ROI绘制点击事件
  console.log('Canvas clicked:', event)
}

// 绘制ROI
const drawROI = () => {
  nextTick(() => {
    roiCanvas.value.forEach((canvas, index) => {
      if (canvas) {
        const ctx = canvas.getContext('2d')
        if (ctx) {
          // 清空画布
          ctx.clearRect(0, 0, canvas.width, canvas.height)
          
          // 绘制示例ROI
          ctx.strokeStyle = '#ff4444'
          ctx.lineWidth = 2
          ctx.strokeRect(50, 50, 100, 80)
          
          ctx.strokeStyle = '#44ff44'
          ctx.strokeRect(200, 100, 120, 90)
        }
      }
    })
  })
}

onMounted(() => {
  drawROI()
})
</script>

<style scoped>
.video-grid-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-color);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.panel-content {
  flex: 1;
  padding: 16px 20px;
}

.video-grid {
  display: grid;
  gap: 16px;
  height: 100%;
}

.video-grid.grid-1x1 {
  grid-template-columns: 1fr;
}

.video-grid.grid-2x2 {
  grid-template-columns: repeat(2, 1fr);
}

.video-grid.grid-3x3 {
  grid-template-columns: repeat(3, 1fr);
}

.video-item {
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.video-item:hover {
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.1);
}

.video-item.video-offline {
  opacity: 0.7;
}

.video-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-player {
  flex: 1;
  position: relative;
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.video-placeholder {
  text-align: center;
  color: #fff;
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.video-placeholder .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.7;
}

.video-placeholder p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.roi-canvas {
  width: 100%;
  height: 100%;
  pointer-events: auto;
}

.video-offline-state {
  text-align: center;
  color: #666;
}

.video-offline-state .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
  color: var(--danger-color);
}

.video-offline-state p {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.offline-text {
  font-size: 12px;
  color: var(--danger-color);
}

.video-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--bg-color-soft);
  border-top: 1px solid var(--border-color);
}

.video-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
}

.video-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-resolution {
  font-size: 12px;
  color: var(--text-color-mute);
}

.roi-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: var(--bg-color-mute);
  font-size: 12px;
}

.roi-count {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-color-soft);
}

.detection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--text-color-soft);
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.status-indicator.monitoring {
  background-color: var(--success-color);
  animation: pulse 2s infinite;
}

.status-indicator.idle {
  background-color: var(--info-color);
}

.status-indicator.offline {
  background-color: var(--danger-color);
}

.empty-slot {
  border: 2px dashed var(--border-color);
  background-color: transparent;
}

.empty-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-color-mute);
  cursor: pointer;
  transition: all 0.3s ease;
}

.empty-content:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.empty-content .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.empty-content p {
  margin: 0;
  font-size: 14px;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>

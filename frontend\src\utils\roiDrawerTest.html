<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROI绘制器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .video-area {
            position: relative;
            width: 640px;
            height: 360px;
            background: #000;
            margin: 20px auto;
            border: 2px solid #ddd;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 0 5px;
            border: 1px solid #ddd;
            background: #fff;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .btn:hover {
            background: #f0f0f0;
        }
        
        .btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .roi-list {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .roi-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            margin: 5px 0;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        .roi-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .roi-info {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .delete-btn:hover {
            background: #c82333;
        }
        
        .log {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ROI绘制器测试</h1>
        
        <div class="controls">
            <button id="enableBtn" class="btn">启用绘制</button>
            <button id="rectangleBtn" class="btn">矩形模式</button>
            <button id="polygonBtn" class="btn">多边形模式</button>
            <button id="clearBtn" class="btn">清空ROI</button>
            <div style="margin-left: 20px;">
                <label>ROI属性:</label>
                <label><input type="radio" name="attribute" value="pailiao"> 排料口(青色)</label>
                <label><input type="radio" name="attribute" value="yazhu"> 压铸机(红色)</label>
            </div>
        </div>
        
        <div class="video-area">
            <canvas id="roiCanvas"></canvas>
        </div>
        
        <div class="roi-list">
            <h3>ROI列表</h3>
            <div id="roiListContainer">
                <p>暂无ROI区域</p>
            </div>
        </div>
        
        <div class="log">
            <h3>操作日志</h3>
            <div id="logContainer"></div>
        </div>
    </div>

    <script type="module">
        import { ROIDrawer } from './roiDrawer.js'
        
        // 获取DOM元素
        const canvas = document.getElementById('roiCanvas')
        const enableBtn = document.getElementById('enableBtn')
        const rectangleBtn = document.getElementById('rectangleBtn')
        const polygonBtn = document.getElementById('polygonBtn')
        const clearBtn = document.getElementById('clearBtn')
        const roiListContainer = document.getElementById('roiListContainer')
        const logContainer = document.getElementById('logContainer')
        
        // 初始化ROI绘制器
        const roiDrawer = new ROIDrawer(canvas, {
            videoWidth: 640,
            videoHeight: 360,
            debugMode: true  // 启用调试模式
        })

        // 绑定鼠标事件到画布
        canvas.addEventListener('mousedown', (e) => {
            if (e.button === 0) {
                log(`左键按下: (${e.clientX}, ${e.clientY})`)
            } else if (e.button === 2) {
                log(`右键按下: (${e.clientX}, ${e.clientY}) - 完成多边形`)
            }
            roiDrawer.onMouseDown(e)
        })
        canvas.addEventListener('mousemove', (e) => roiDrawer.onMouseMove(e))
        canvas.addEventListener('mouseup', (e) => {
            if (e.button === 0) {
                log(`左键抬起: (${e.clientX}, ${e.clientY})`)
            }
            roiDrawer.onMouseUp(e)
        })
        canvas.addEventListener('dblclick', (e) => {
            log(`双击: (${e.clientX}, ${e.clientY})`)
            roiDrawer.onDoubleClick(e)
        })
        canvas.addEventListener('contextmenu', (e) => {
            roiDrawer.onContextMenu(e)
        })
        
        // 状态变量
        let isEnabled = false
        let currentMode = null
        
        // 日志函数
        function log(message) {
            const time = new Date().toLocaleTimeString()
            const logEntry = document.createElement('div')
            logEntry.textContent = `[${time}] ${message}`
            logContainer.appendChild(logEntry)
            logContainer.scrollTop = logContainer.scrollHeight
        }
        
        // 更新ROI列表显示
        function updateROIList() {
            const rois = roiDrawer.getROIs()
            
            if (rois.length === 0) {
                roiListContainer.innerHTML = '<p>暂无ROI区域</p>'
                return
            }
            
            roiListContainer.innerHTML = rois.map((roi, index) => `
                <div class="roi-item">
                    <div class="roi-info">
                        <div class="roi-color" style="background-color: ${roi.color}"></div>
                        <div>
                            <strong>${roi.name}</strong><br>
                            <small>${roi.type === 'rectangle' ? '矩形' : '多边形'} - ${roi.points.length} 个点</small>
                        </div>
                    </div>
                    <button class="delete-btn" onclick="deleteROI(${index})">删除</button>
                </div>
            `).join('')
        }
        
        // 删除ROI
        window.deleteROI = function(index) {
            roiDrawer.deleteROI(index)
            updateROIList()
            log(`删除ROI ${index}`)
        }
        
        // 设置ROI绘制器回调
        roiDrawer.onROIAdded = (roi) => {
            log(`添加${roi.roi_type === 'rectangle' ? '矩形' : '多边形'}ROI: ${roi.name}`)
            updateROIList()
        }
        
        roiDrawer.onROIDeleted = (roi, index) => {
            log(`删除ROI: ${roi.name}`)
            updateROIList()
        }
        
        // 按钮事件处理
        enableBtn.addEventListener('click', () => {
            isEnabled = !isEnabled
            roiDrawer.setEnabled(isEnabled)
            enableBtn.textContent = isEnabled ? '禁用绘制' : '启用绘制'
            enableBtn.classList.toggle('active', isEnabled)
            log(`绘制模式${isEnabled ? '已启用' : '已禁用'}`)
        })
        
        rectangleBtn.addEventListener('click', () => {
            if (!isEnabled) {
                isEnabled = true
                roiDrawer.setEnabled(true)
                enableBtn.textContent = '禁用绘制'
                enableBtn.classList.add('active')
                log('自动启用绘制模式')
            }

            currentMode = 'rectangle'
            roiDrawer.setDrawMode('rectangle')
            rectangleBtn.classList.add('active')
            polygonBtn.classList.remove('active')
            log(`切换到矩形绘制模式 - 绘制状态: ${roiDrawer.isEnabled}, 模式: ${roiDrawer.drawMode}`)
        })
        
        polygonBtn.addEventListener('click', () => {
            if (!isEnabled) {
                isEnabled = true
                roiDrawer.setEnabled(true)
                enableBtn.textContent = '禁用绘制'
                enableBtn.classList.add('active')
                log('自动启用绘制模式')
            }

            currentMode = 'polygon'
            roiDrawer.setDrawMode('polygon')
            polygonBtn.classList.add('active')
            rectangleBtn.classList.remove('active')
            log(`切换到多边形绘制模式 - 绘制状态: ${roiDrawer.isEnabled}, 模式: ${roiDrawer.drawMode}`)
        })
        
        clearBtn.addEventListener('click', () => {
            roiDrawer.clearROIs()
            updateROIList()
            log('清空所有ROI')
        })

        // 属性选择事件
        document.querySelectorAll('input[name="attribute"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked) {
                    roiDrawer.setCurrentAttribute(e.target.value)
                    log(`选择ROI属性: ${e.target.value === 'pailiao' ? '排料口(青色)' : '压铸机(红色)'}`)
                }
            })
        })
        
        // 初始化日志
        log('ROI绘制器测试页面已加载')
        log(`画布尺寸: ${canvas.width}x${canvas.height}`)
        log(`画布样式: pointer-events=${canvas.style.pointerEvents}, cursor=${canvas.style.cursor}`)
        log('使用说明：')
        log('1. 点击"启用绘制"按钮')
        log('2. 选择"矩形模式"或"多边形模式"')
        log('3. 在黑色区域绘制ROI')
        log('4. 矩形：左键拖拽绘制')
        log('5. 多边形：左键点击顶点，右键完成绘制')
    </script>
</body>
</html>

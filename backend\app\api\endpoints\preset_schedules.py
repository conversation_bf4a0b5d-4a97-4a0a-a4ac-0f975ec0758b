from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date, time
import logging

from app.db.session import get_db
from app.api.deps import get_current_active_user
from app.models.models import User, PresetSchedule, DetectionTemplate, DetectionGroup

logger = logging.getLogger(__name__)

router = APIRouter(tags=["预设检测计划"])

@router.get("/")
@router.get("")
async def get_preset_schedules(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取所有预设检测计划"""
    try:
        schedules = db.query(PresetSchedule).order_by(PresetSchedule.date.desc(), PresetSchedule.start_time).all()
        return [schedule.to_dict() for schedule in schedules]
    except Exception as e:
        logger.error(f"获取预设检测计划失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取预设检测计划失败")

@router.get("/{schedule_id}")
@router.get("/{schedule_id}/")
async def get_preset_schedule(
    schedule_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取特定预设检测计划"""
    try:
        schedule = db.query(PresetSchedule).filter(PresetSchedule.id == int(schedule_id)).first()
        if not schedule:
            raise HTTPException(status_code=404, detail="预设检测计划不存在")
        return schedule.to_dict()
    except HTTPException:
        raise
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的计划ID")
    except Exception as e:
        logger.error(f"获取预设检测计划失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取预设检测计划失败")

@router.post("/")
@router.post("")
async def create_preset_schedule(
    schedule_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """创建预设检测计划"""
    try:
        # 验证模板是否存在
        template_id = schedule_data.get("templateId")
        if template_id:
            template = db.query(DetectionTemplate).filter(DetectionTemplate.id == int(template_id)).first()
            if not template:
                raise HTTPException(status_code=400, detail="指定的检测模板不存在")
        
        # 创建新的预设计划
        new_schedule = PresetSchedule.from_dict(schedule_data)
        db.add(new_schedule)
        db.commit()
        db.refresh(new_schedule)
        
        return new_schedule.to_dict()
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"数据格式错误: {str(e)}")
    except Exception as e:
        db.rollback()
        logger.error(f"创建预设检测计划失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建预设检测计划失败")

@router.put("/{schedule_id}")
@router.put("/{schedule_id}/")
async def update_preset_schedule(
    schedule_id: str,
    schedule_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """更新预设检测计划"""
    try:
        schedule = db.query(PresetSchedule).filter(PresetSchedule.id == int(schedule_id)).first()
        if not schedule:
            raise HTTPException(status_code=404, detail="预设检测计划不存在")
        
        # 验证模板是否存在（如果提供了新的模板ID）
        template_id = schedule_data.get("templateId")
        if template_id and template_id != schedule.template_id:
            template = db.query(DetectionTemplate).filter(DetectionTemplate.id == int(template_id)).first()
            if not template:
                raise HTTPException(status_code=400, detail="指定的检测模板不存在")
        
        # 字段名映射：前端驼峰命名 -> 后端下划线命名
        field_mapping = {
            'templateId': 'template_id',
            'startTime': 'start_time',
            'endTime': 'end_time',
            'isEnabled': 'is_enabled'
        }
        
        # 更新字段
        for key, value in schedule_data.items():
            # 映射字段名
            db_field = field_mapping.get(key, key)
            
            # 检查字段是否存在且不是受保护字段
            if hasattr(schedule, db_field) and db_field not in ['id', 'created_at']:
                setattr(schedule, db_field, value)
            else:
                logger.warning(f"忽略未知或受保护的字段: {key} -> {db_field}")
        
        schedule.updated_at = datetime.now()
        db.commit()
        db.refresh(schedule)
        
        return schedule.to_dict()
    except HTTPException:
        raise
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的计划ID")
    except Exception as e:
        db.rollback()
        logger.error(f"更新预设检测计划失败: {str(e)}")
        raise HTTPException(status_code=500, detail="更新预设检测计划失败")

@router.delete("/{schedule_id}")
@router.delete("/{schedule_id}/")
async def delete_preset_schedule(
    schedule_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """删除预设检测计划"""
    try:
        schedule = db.query(PresetSchedule).filter(PresetSchedule.id == int(schedule_id)).first()
        if not schedule:
            raise HTTPException(status_code=404, detail="预设检测计划不存在")
        
        deleted_schedule_data = schedule.to_dict()
        db.delete(schedule)
        db.commit()
        
        return {"message": "预设检测计划删除成功", "deleted_schedule": deleted_schedule_data}
    except HTTPException:
        raise
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的计划ID")
    except Exception as e:
        db.rollback()
        logger.error(f"删除预设检测计划失败: {str(e)}")
        raise HTTPException(status_code=500, detail="删除预设检测计划失败")

@router.put("/{schedule_id}/toggle")
@router.put("/{schedule_id}/toggle/")
async def toggle_preset_schedule(
    schedule_id: str,
    toggle_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """启用/禁用预设检测计划"""
    try:
        schedule = db.query(PresetSchedule).filter(PresetSchedule.id == int(schedule_id)).first()
        if not schedule:
            raise HTTPException(status_code=404, detail="预设检测计划不存在")
        
        schedule.is_enabled = not schedule.is_enabled
        
        # 根据启用状态更新执行状态
        if schedule.is_enabled:
            # 启用时，如果当前状态是stopped，则改为pending
            if schedule.status == "stopped":
                schedule.status = "pending"
        else:
            # 禁用时，如果当前状态是running，则停止执行；否则设为stopped
            if schedule.status == "running":
                schedule.status = "stopped"
            elif schedule.status == "pending":
                schedule.status = "stopped"
        
        schedule.updated_at = datetime.now()
        db.commit()
        db.refresh(schedule)
        
        return schedule.to_dict()
    except HTTPException:
        raise
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的计划ID")
    except Exception as e:
        db.rollback()
        logger.error(f"切换预设检测计划状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="切换预设检测计划状态失败")

@router.post("/check-conflict")
async def check_schedule_conflict(
    conflict_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """检查预设检测计划时间冲突"""
    try:
        date_str = conflict_data.get("date")
        start_time_str = conflict_data.get("startTime")
        end_time_str = conflict_data.get("endTime")
        exclude_id = conflict_data.get("excludeId")  # 更新时排除自己
        
        # 转换日期和时间格式
        target_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        start_time = datetime.strptime(start_time_str, "%H:%M").time()
        end_time = datetime.strptime(end_time_str, "%H:%M").time()
        
        # 查询同一天的启用计划
        query = db.query(PresetSchedule).filter(
            PresetSchedule.date == date_str,  # 使用字符串格式比较
            PresetSchedule.is_enabled == True
        )
        
        if exclude_id:
            query = query.filter(PresetSchedule.id != int(exclude_id))
        
        schedules = query.all()
        
        conflicts = []
        for schedule in schedules:
            # 将数据库中的时间字符串转换为time对象进行比较
            schedule_start_time = datetime.strptime(schedule.start_time, "%H:%M").time()
            schedule_end_time = datetime.strptime(schedule.end_time, "%H:%M").time()
            
            # 检查时间重叠
            if not (end_time <= schedule_start_time or start_time >= schedule_end_time):
                conflicts.append(schedule.to_dict())
        
        return {
            "hasConflict": len(conflicts) > 0,
            "conflicts": conflicts
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"日期或时间格式错误: {str(e)}")
    except Exception as e:
        logger.error(f"检查预设检测计划冲突失败: {str(e)}")
        raise HTTPException(status_code=500, detail="检查预设检测计划冲突失败")

@router.post("/{schedule_id}/execute")
async def execute_preset_schedule(
    schedule_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """执行预设检测计划 - 自动控制检测看板和启动检测模板"""
    try:
        schedule = db.query(PresetSchedule).filter(PresetSchedule.id == int(schedule_id)).first()
        if not schedule:
            raise HTTPException(status_code=404, detail="预设检测计划不存在")
        
        if not schedule.is_enabled:
            raise HTTPException(status_code=400, detail="预设检测计划未启用")
        
        # 更新状态为执行中
        schedule.status = "running"
        schedule.updated_at = datetime.now()
        db.commit()
        db.refresh(schedule)
        
        # 获取检测模板信息
        template = None
        detection_groups = []
        dashboard_url = None
        
        if schedule.template_id:
            template = db.query(DetectionTemplate).filter(DetectionTemplate.id == schedule.template_id).first()
            if template:
                # 获取模板关联的检测组
                detection_groups = db.query(DetectionGroup).filter(
                    DetectionGroup.template_id == schedule.template_id
                ).all()
                
                # 构建检测看板URL
                dashboard_url = "/detection-dashboard"
                
                logger.info(f"找到检测模板: {template.name}, 关联检测组数量: {len(detection_groups)}")
            else:
                logger.warning(f"预设计划 {schedule.name} 关联的检测模板 {schedule.template_id} 不存在")
        
        logger.info(f"开始执行预设检测计划: {schedule.name}")
        
        return {
            "message": "预设检测计划开始执行",
            "schedule": schedule.to_dict(),
            "template": {
                "id": template.id if template else None,
                "name": template.name if template else None,
                "detectionGroups": [{
                    "id": group.id,
                    "name": group.name,
                    "status": group.status
                } for group in detection_groups]
            } if template else None,
            "dashboardUrl": dashboard_url,
            "autoStart": True  # 标识需要自动启动检测看板
        }
    except HTTPException:
        raise
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的计划ID")
    except Exception as e:
        db.rollback()
        logger.error(f"执行预设检测计划失败: {str(e)}")
        raise HTTPException(status_code=500, detail="执行预设检测计划失败")

@router.post("/{schedule_id}/stop")
async def stop_preset_schedule(
    schedule_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """停止正在执行的预设检测计划"""
    try:
        schedule = db.query(PresetSchedule).filter(PresetSchedule.id == int(schedule_id)).first()
        if not schedule:
            raise HTTPException(status_code=404, detail="预设检测计划不存在")
        
        # 更新状态为已停止
        schedule.status = "stopped"
        schedule.updated_at = datetime.now()
        db.commit()
        db.refresh(schedule)
        
        # 这里应该调用停止检测的逻辑
        logger.info(f"停止预设检测计划: {schedule.name}")
        
        return {
            "message": "预设检测计划已停止",
            "schedule": schedule.to_dict()
        }
    except HTTPException:
        raise
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的计划ID")
    except Exception as e:
        db.rollback()
        logger.error(f"停止预设检测计划失败: {str(e)}")
        raise HTTPException(status_code=500, detail="停止预设检测计划失败")

@router.get("/{schedule_id}/status")
async def get_schedule_execution_status(
    schedule_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取预设检测计划执行状态"""
    try:
        schedule = db.query(PresetSchedule).filter(PresetSchedule.id == int(schedule_id)).first()
        if not schedule:
            raise HTTPException(status_code=404, detail="预设检测计划不存在")
        
        return {
            "scheduleId": str(schedule.id),
            "status": schedule.status,
            "name": schedule.name,
            "updatedAt": schedule.updated_at.isoformat() if schedule.updated_at else None
        }
    except HTTPException:
        raise
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的计划ID")
    except Exception as e:
        logger.error(f"获取预设检测计划状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取预设检测计划状态失败")

@router.get("/by-date/{target_date}")
async def get_schedules_by_date(
    target_date: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """按日期获取预设检测计划"""
    try:
        # 转换日期格式
        date_obj = datetime.strptime(target_date, "%Y-%m-%d").date()
        
        schedules = db.query(PresetSchedule).filter(
            PresetSchedule.date == date_obj
        ).order_by(PresetSchedule.start_time).all()
        
        return [schedule.to_dict() for schedule in schedules]
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误，请使用 YYYY-MM-DD 格式")
    except Exception as e:
        logger.error(f"按日期获取预设检测计划失败: {str(e)}")
        raise HTTPException(status_code=500, detail="按日期获取预设检测计划失败")

@router.get("/by-date-range/")
async def get_schedules_by_date_range(
    start_date: str,
    end_date: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """按日期范围获取预设检测计划"""
    try:
        # 转换日期格式
        start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
        
        schedules = db.query(PresetSchedule).filter(
            PresetSchedule.date >= start_date_obj,
            PresetSchedule.date <= end_date_obj
        ).order_by(PresetSchedule.date, PresetSchedule.start_time).all()
        
        return [schedule.to_dict() for schedule in schedules]
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误，请使用 YYYY-MM-DD 格式")
    except Exception as e:
        logger.error(f"按日期范围获取预设检测计划失败: {str(e)}")
        raise HTTPException(status_code=500, detail="按日期范围获取预设检测计划失败")
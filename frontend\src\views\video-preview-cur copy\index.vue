<!-- 视频预览页面主入口 -->
<template>
  <div class="video-preview-page">
    <div class="page-header">
      <div>
        <h2>视频预览（自动化）</h2>
        <p class="page-description">选择视频源后将自动登录设备、选择模拟通道、使用主码流并开始预览</p>
      </div>
    </div>
    
    <div class="preview-content">
      <!-- 视频源选择区域 -->
      <VideoSourceSelector 
        :sources="videoSources" 
        :selected-source="selectedVideoSource"
        :is-connecting="isConnecting"
        :is-preview-active="isPreviewActive"
        @source-change="onVideoSourceChange"
        @refresh="refreshVideoSources"
      />
      
      <!-- 主要内容区域 - 左右布局 -->
      <div class="main-content">
        <!-- 左侧视频区域 - 60% -->
        <div class="video-section">
          <!-- ROI绘制工具栏 -->
          <ROIToolbar 
            :selected-attribute="selectedROIAttribute"
            :is-drawing-enabled="isDrawingEnabled"
            :roi-count="roiCount"
            @attribute-change="onROIAttributeChange"
            @toggle-draw="toggleDrawMode"
            @clear="clearROIs"
            @save="saveROIs"
            @export="exportROIs"
            @import="importROIs"
            @load="loadROIListToVideo"
            @test="testROILoad"
            @init="manualInitROI"
          />
          
          <!-- 视频预览区域 -->
          <VideoPlayer
            ref="videoPlayer"
            :is-preview-active="isPreviewActive"
            :is-connecting="isConnecting"
            :show-motion-detection="showMotionDetection"
            :detection-result="detectionResult"
            :video-container-style="videoContainerStyle"
            :roi-list="roiList"
            :highlighted-roi-id="highlightedROIId"
            @start-preview="startPreview"
            @stop-preview="stopPreview"
            @toggle-motion="toggleMotionDetection"
            @roi-drawer-ready="onROIDrawerReady"
            @video-size-changed="onVideoSizeChanged"
          />
          
          <!-- 调试区域 -->
          <div class="debug-section">
            <button @click="forceInitROI" class="debug-btn">
              强制初始化ROI绘制器
            </button>
            <button @click="checkROIStatus" class="debug-btn">
              检查ROI状态
            </button>
          </div>
        </div>

        <!-- 右侧面板区域 - 40% -->
        <div class="right-panel">
          <!-- ROI列表和管理面板 -->
          <ROIManagement 
            :roi-list="roiList"
            :selected-attribute="selectedROIAttribute"
            :is-drawing-enabled="isDrawingEnabled"
            :highlighted-roi-id="highlightedROIId"
            @edit="editROI"
            @delete="deleteROI"
            @highlight="toggleROIHighlight"
            @clear-attribute="clearAttributeROIs"
          />
          
          <AlgorithmConfig />
        </div>
      </div>
      
      <!-- 操作信息框 -->
      <OperationLog 
        :logs="operationInfos"
        @clear="clearOperationInfo"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// 导入组件
import VideoSourceSelector from './components/VideoSourceSelector.vue'
import VideoPlayer from './components/VideoPlayer.vue'
import ROIToolbar from './components/ROIToolbar.vue'
import ROIManagement from './components/ROIManagement.vue'
import AlgorithmConfig from './components/AlgorithmConfig.vue'
import OperationLog from './components/OperationLog.vue'

// 导入可复用逻辑
import { useWebSDK } from './composables/useWebSDK'
import { useROIDrawer } from './composables/useROIDrawer'
// 移除运动检测导入
// import { useMotionDetection } from './composables/useMotionDetection'
import { useOperationLog } from './composables/useOperationLog'

// 操作日志逻辑
const { operationInfos, addOperationInfo, clearOperationInfo } = useOperationLog()

// WebSDK相关逻辑
const { 
  webVideoCtrl,
  videoSources, 
  selectedVideoSource, 
  isConnecting,
  isPreviewActive,
  videoContainerStyle,
  initWebSDK,
  refreshVideoSources,
  onVideoSourceChange,
  startPreview,
  stopPreview
} = useWebSDK(addOperationInfo)

// 创建空的运动检测状态，保持接口兼容
const showMotionDetection = ref(false)
const detectionResult = ref<Record<string, any>>({
  motion_detected: false,
  contours: [],
  roi_violations: []
})

// 空的切换运动检测函数
const toggleMotionDetection = () => {
  addOperationInfo('[INFO] 运动检测功能已禁用')
  ElMessage.info('运动检测功能已禁用，请使用其他算法')
}

// ROI绘制相关逻辑
const {
  roiDrawerInstance,
  roiList,
  roiCount,
  selectedROIAttribute,
  isDrawingEnabled,
  highlightedROIId,
  initROIDrawer,
  onROIAttributeChange,
  toggleDrawMode,
  clearROIs,
  saveROIs,
  exportROIs,
  importROIs,
  loadROIListToVideo,
  testROILoad,
  manualInitROI,
  editROI,
  deleteROI,
  toggleROIHighlight,
  clearAttributeROIs,
  onROIDrawerReady,
  updateROIDrawerSize
} = useROIDrawer(addOperationInfo)

// 视频播放器组件引用
const videoPlayer = ref<any>(null)

// 视频尺寸变化处理
const onVideoSizeChanged = ({ width, height, skipReinitialization = false }: { width: number, height: number, skipReinitialization?: boolean }) => {
  addOperationInfo(`[VIDEO] 视频实际尺寸: ${width}x${height}`)
  
  // 检查视频尺寸与默认尺寸差异
  if (width !== 640 || height !== 360) {
    if (width === 1920 && height === 1080) {
      addOperationInfo(`[VIDEO] ✅ 检测到高清视频分辨率: ${width}x${height}`)
    } else {
      addOperationInfo(`[VIDEO] ℹ️ 注意：检测到非标准尺寸: ${width}x${height}`)
    }
  } else {
    addOperationInfo(`[VIDEO] 检测到标准视频尺寸: ${width}x${height}`)
  }
  
  // 如果是定时检测触发的尺寸变化，且指定了跳过重新初始化，则不更新ROI绘制器
  if (skipReinitialization) {
    return
  }
  
  // 使用updateROIDrawerSize方法更新尺寸
  if (roiDrawerInstance.value) {
    const success = updateROIDrawerSize(width, height)
    if (success) {
      addOperationInfo(`[ROI] 成功将ROI绘制器尺寸更新为: ${width}x${height}`)
    } else {
      addOperationInfo(`[WARNING] ROI绘制器尺寸更新失败，请尝试手动初始化`)
    }
  }
}

// 强制初始化ROI绘制器
const forceInitROI = () => {
  addOperationInfo('[DEBUG] 强制初始化ROI绘制器')
  
  // 使用VideoPlayer组件的方法获取当前视频尺寸
  if (videoPlayer.value && videoPlayer.value.updateVideoSize) {
    const { width, height } = videoPlayer.value.updateVideoSize()
    addOperationInfo(`[DEBUG] 获取到当前视频尺寸: ${width}x${height}`)
    
    // 使用querySelector直接获取canvas元素
    const canvasElement = document.querySelector('.roi-display-canvas') as HTMLCanvasElement
    
    if (canvasElement) {
      addOperationInfo(`[DEBUG] 使用querySelector找到Canvas元素: ${canvasElement.width}x${canvasElement.height}`)
      
      // 确保Canvas元素准备就绪，使用实际视频尺寸
      canvasElement.width = width
      canvasElement.height = height
      canvasElement.style.display = 'block'
      canvasElement.style.position = 'absolute'
      canvasElement.style.top = '0'
      canvasElement.style.left = '0'
      canvasElement.style.pointerEvents = 'auto'
      
      // 添加视频尺寸信息到日志
      if (width === 1920 && height === 1080) {
        addOperationInfo(`[VIDEO] ✅ 高清视频: ${width}x${height}`)
      } else {
        addOperationInfo(`[VIDEO] 当前视频尺寸: ${width}x${height}`)
      }
      
      // 直接初始化ROI绘制器
      onROIDrawerReady(canvasElement)
      
      addOperationInfo('[DEBUG] Canvas已设置并传递给ROI绘制器')
    } else {
      // 尝试通过ref获取
      addOperationInfo('[DEBUG] 尝试通过ref获取Canvas元素')
      
      nextTick(() => {
        const canvas = videoPlayer.value?.roiDisplayCanvas
        if (canvas) {
          addOperationInfo(`[DEBUG] 通过ref找到Canvas元素: ${canvas.width}x${canvas.height}`)
          
          // 确保Canvas元素准备就绪，使用实际视频尺寸
          canvas.width = width
          canvas.height = height
          canvas.style.display = 'block'
          canvas.style.position = 'absolute'
          canvas.style.top = '0'
          canvas.style.left = '0'
          canvas.style.pointerEvents = 'auto'
          
          // 添加视频尺寸信息到日志
          addOperationInfo(`[VIDEO] 当前视频尺寸: ${width}x${height}`)
          
          // 直接初始化ROI绘制器
          onROIDrawerReady(canvas)
        } else {
          addOperationInfo('[ERROR] 无法找到Canvas元素')
          ElMessage.error('无法找到Canvas元素')
        }
      })
    }
  } else {
    // 如果无法获取视频尺寸，使用默认尺寸
    addOperationInfo('[WARNING] 无法获取视频尺寸，使用默认尺寸(640x360)')
    
    // 尝试通过querySelector获取canvas元素
    const canvasElement = document.querySelector('.roi-display-canvas') as HTMLCanvasElement
    if (canvasElement) {
      canvasElement.width = 640
      canvasElement.height = 360
      onROIDrawerReady(canvasElement)
      addOperationInfo('[DEBUG] 使用默认尺寸初始化ROI绘制器')
    } else {
      // 如果找不到canvas元素，使用manualInitROI
      addOperationInfo('[INFO] 找不到Canvas元素，尝试使用manualInitROI')
      manualInitROI()
    }
  }
}

// 检查ROI绘制器状态
const checkROIStatus = () => {
  addOperationInfo('[DEBUG] 检查ROI绘制器状态')
  addOperationInfo(`[DEBUG] ROI绘制器实例: ${roiDrawerInstance.value ? '已初始化' : '未初始化'}`)
  addOperationInfo(`[DEBUG] 已有ROI: ${roiList.value.length}个`)
  addOperationInfo(`[DEBUG] 当前属性: ${selectedROIAttribute.value || '未选择'}`)
  addOperationInfo(`[DEBUG] 绘制状态: ${isDrawingEnabled.value ? '启用' : '禁用'}`)
  
  // 检查ref获取的canvas
  if (videoPlayer.value?.roiDisplayCanvas) {
    const canvas = videoPlayer.value.roiDisplayCanvas
    addOperationInfo(`[DEBUG] Canvas通过ref: ${canvas.width}x${canvas.height}`)
  } else {
    addOperationInfo('[DEBUG] Canvas元素通过ref未找到')
  }
  
  // 直接使用DOM查询查找canvas
  const canvasElement = document.querySelector('.roi-display-canvas') as HTMLCanvasElement
  if (canvasElement) {
    addOperationInfo(`[DEBUG] Canvas通过DOM查询: ${canvasElement.width}x${canvasElement.height}`)
    addOperationInfo(`[DEBUG] Canvas样式: display=${canvasElement.style.display}, position=${canvasElement.style.position}`)
    addOperationInfo(`[DEBUG] Canvas事件: pointerEvents=${canvasElement.style.pointerEvents}`)
  } else {
    addOperationInfo('[DEBUG] Canvas元素通过DOM查询未找到')
  }
}

// 组件生命周期钩子
onMounted(async () => {
  try {
    // 检查WebSDK脚本是否已加载
    if (!(window as any).WebVideoCtrl) {
      addOperationInfo('[ERROR] WebSDK脚本未加载，请检查index.html中的脚本引用')
      ElMessage.error('WebSDK脚本未加载，请检查配置')
      return
    }

    // 检查jQuery是否已加载
    if (typeof $ === 'undefined') {
      addOperationInfo('[WARNING] jQuery未加载，将使用原生DOM解析XML')
    } else {
      addOperationInfo('[INFO] jQuery已加载，将使用jQuery解析XML')
    }

    await initWebSDK()
    await refreshVideoSources()
    
    // 确保在DOM渲染完成后初始化ROI绘制器
    nextTick(() => {
      setTimeout(() => {
        addOperationInfo('[INFO] 初始组件挂载完成，预检查ROI画布状态')
        if (videoPlayer.value?.roiDisplayCanvas) {
          const canvas = videoPlayer.value.roiDisplayCanvas
          addOperationInfo(`[INFO] 找到ROI画布: ${canvas.width}x${canvas.height}`)
        } else {
          addOperationInfo('[WARNING] 组件挂载后未找到ROI画布')
        }
      }, 1000)
    })
  } catch (error) {
    console.error('初始化失败:', error)
    addOperationInfo(`[ERROR] 初始化失败: ${error instanceof Error ? error.message : String(error)}`)
    ElMessage.error('初始化失败，请检查WebSDK配置')
  }
})

// 组件卸载
onUnmounted(() => {

})
</script>

<style>
@import './styles/video-preview.css';

/* 调试区域样式 */
.debug-section {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.debug-btn {
  padding: 5px 10px;
  background-color: #444;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.debug-btn:hover {
  background-color: #666;
}
</style> 
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.models import User
from app.core.security import get_password_hash, verify_password

def check_users():
    """检查数据库中的用户"""
    db: Session = SessionLocal()
    try:
        users = db.query(User).all()
        print(f"数据库中共有 {len(users)} 个用户:")
        
        for user in users:
            print(f"ID: {user.id}")
            print(f"用户名: {user.username}")
            print(f"邮箱: {user.email}")
            print(f"是否激活: {user.is_active}")
            print(f"是否超级用户: {user.is_superuser}")
            print(f"密码哈希: {user.hashed_password[:50]}...")
            print("-" * 40)
            
        # 如果没有用户，创建一个默认用户
        if len(users) == 0:
            print("没有找到用户，创建默认用户...")
            default_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("admin123"),
                is_active=True,
                is_superuser=True
            )
            db.add(default_user)
            db.commit()
            print("默认用户创建成功: admin / admin123")
        else:
            # 测试密码验证
            print("\n测试密码验证:")
            test_passwords = ["admin", "admin123", "password", "123456"]
            for user in users:
                print(f"\n用户: {user.username}")
                for pwd in test_passwords:
                    is_valid = verify_password(pwd, user.hashed_password)
                    print(f"  密码 '{pwd}': {'✓' if is_valid else '✗'}")
                    
    except Exception as e:
        print(f"检查用户时出错: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_users()
import cv2
import numpy as np
import time
from typing import Dict, List, Any, Tuple, Optional

class BaseProcessor:
    """所有检测算法的基础类"""
    def __init__(self):
        """初始化基础处理器"""
        pass
    
    def _get_processed_roi(self, frame, roi_mask=None):
        """从原始帧中提取、处理并返回ROI区域
        
        Args:
            frame (np.ndarray): 输入的原始视频帧 (BGR格式)
            roi_mask (np.ndarray): ROI掩码
            
        Returns:
            tuple: (灰度ROI图像, 掩码, 偏移量)
        """
        if roi_mask is None:
            # 如果没有提供掩码，处理整个帧
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            return gray, None, (0, 0)
        
        # 应用掩码提取ROI区域
        roi_frame = cv2.bitwise_and(frame, frame, mask=roi_mask)
        
        # 计算ROI的边界框
        contours, _ = cv2.findContours(roi_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            return None, None, (0, 0)
        
        # 获取ROI的边界框
        x, y, w, h = cv2.boundingRect(contours[0])
        
        # 裁剪ROI区域
        roi_cropped = roi_frame[y:y+h, x:x+w]
        
        # 转换为灰度图
        gray_roi = cv2.cvtColor(roi_cropped, cv2.COLOR_BGR2GRAY)
        
        # 创建裁剪后的掩码
        mask_cropped = roi_mask[y:y+h, x:x+w]
        
        return gray_roi, mask_cropped, (x, y)
    
    def _apply_mask_to_contours(self, contours, offset_x=0, offset_y=0):
        """将轮廓调整回原始坐标系
        
        Args:
            contours (list): 轮廓列表
            offset_x (int): X轴偏移量
            offset_y (int): Y轴偏移量
            
        Returns:
            list: 调整后的轮廓列表
        """
        if not contours:
            return []
        
        adjusted_contours = []
        for contour in contours:
            # 调整每个轮廓点的坐标
            adjusted_contour = contour + np.array([[offset_x, offset_y]])
            adjusted_contours.append(adjusted_contour)
        
        return adjusted_contours

class MotionDetector(BaseProcessor):
    """运动检测器 - 使用背景减除法"""
    def __init__(self, params=None):
        """初始化运动检测器
        
        Args:
            params (dict): 检测参数
                - minArea: 最小检测面积
                - detectionThreshold: 检测阈值
                - learningRate: 背景学习速率
                - shadowsThreshold: 阴影检测阈值 (0-1，0表示不检测阴影)
        """
        super().__init__()
        
        # 设置默认参数
        self.params = {
            'minArea': 100,
            'detectionThreshold': 40,
            'learningRate': 0.005,
            'shadowsThreshold': 0.5
        }
        
        # 更新自定义参数
        if params:
            self.params.update(params)
        
        # 创建背景减除器
        self.bg_subtractor = cv2.createBackgroundSubtractorMOG2(
            history=500,
            varThreshold=self.params['detectionThreshold'],
            detectShadows=self.params['shadowsThreshold'] > 0
        )
        
        # 设置阴影阈值
        if self.params['shadowsThreshold'] > 0:
            self.bg_subtractor.setShadowThreshold(self.params['shadowsThreshold'])
    
    def detect(self, frame, roi_mask=None):
        """检测运动
        
        Args:
            frame (np.ndarray): 输入帧
            roi_mask (np.ndarray): ROI掩码
            
        Returns:
            tuple: (是否检测到运动, 轮廓列表)
        """
        # 获取处理后的ROI
        gray_roi, mask_roi, offset = self._get_processed_roi(frame, roi_mask)
        if gray_roi is None:
            return False, []
        
        # 应用背景减除
        fg_mask = self.bg_subtractor.apply(gray_roi, learningRate=self.params['learningRate'])
            
        # 🔥 修复：正确处理阴影和前景像素
        if self.params['shadowsThreshold'] > 0:
            # 当启用阴影检测时：
            # - 0: 背景像素
            # - 127: 阴影像素 (需要根据shadowsThreshold决定是否保留)
            # - 255: 前景像素 (始终保留)

            # 创建掩码：保留前景像素(255)和部分阴影像素(127)
            # 这里我们保留所有非背景像素，因为阴影也可能是运动的一部分
            fg_mask = cv2.threshold(fg_mask, 50, 255, cv2.THRESH_BINARY)[1]
        else:
            # 如果不检测阴影，所有非背景像素都被视为前景
            fg_mask = cv2.threshold(fg_mask, 50, 255, cv2.THRESH_BINARY)[1]
        
        # 应用形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, kernel)
            
        # 如果有ROI掩码，应用到前景掩码
        if mask_roi is not None:
            fg_mask = cv2.bitwise_and(fg_mask, mask_roi)

        # 查找轮廓
        contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤小轮廓
        filtered_contours = [cnt for cnt in contours if cv2.contourArea(cnt) >= self.params['minArea']]
        
        # 调整轮廓坐标
        adjusted_contours = self._apply_mask_to_contours(filtered_contours, offset[0], offset[1])
        
        # 判断是否检测到运动
        motion_detected = len(filtered_contours) > 0
        
        return motion_detected, adjusted_contours

class FrameDifferencer(BaseProcessor):
    """帧差法运动检测器"""
    def __init__(self, params=None):
        """初始化帧差法检测器
        
        Args:
            params (dict): 检测参数
                - threshold: 差异阈值
                - minArea: 最小检测面积
                - frameInterval: 帧间隔
        """
        super().__init__()
        
        # 设置默认参数
        self.params = {
            'threshold': 30,
            'minArea': 100,
            'frameInterval': 2
        }
        
        # 更新自定义参数
        if params:
            self.params.update(params)
        
        # 初始化历史帧
        self.prev_frames = []
        self.max_frames = max(2, self.params['frameInterval'])
    
    def detect(self, frame, roi_mask=None):
        """检测运动
        
        Args:
            frame (np.ndarray): 输入帧
            roi_mask (np.ndarray): ROI掩码
            
        Returns:
            tuple: (是否检测到运动, 轮廓列表)
        """
        # 获取处理后的ROI
        gray_roi, mask_roi, offset = self._get_processed_roi(frame, roi_mask)
        if gray_roi is None:
            return False, []
        
        # 存储当前帧
        self.prev_frames.append(gray_roi.copy())
        
        # 保持固定数量的历史帧
        if len(self.prev_frames) > self.max_frames:
            self.prev_frames.pop(0)
        
        # 如果历史帧不足，无法进行差分
        if len(self.prev_frames) < 2:
            return False, []
        
        # 获取要比较的历史帧
        prev_frame = self.prev_frames[0]
        curr_frame = self.prev_frames[-1]
        
        # 计算帧差
        frame_diff = cv2.absdiff(prev_frame, curr_frame)
            
        # 应用阈值
        _, thresh = cv2.threshold(frame_diff, self.params['threshold'], 255, cv2.THRESH_BINARY)
        
        # 应用形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        # 如果有ROI掩码，应用到阈值图像
        if mask_roi is not None:
            thresh = cv2.bitwise_and(thresh, mask_roi)
        
        # 查找轮廓
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
        # 过滤小轮廓
        filtered_contours = [cnt for cnt in contours if cv2.contourArea(cnt) >= self.params['minArea']]
        
        # 调整轮廓坐标
        adjusted_contours = self._apply_mask_to_contours(filtered_contours, offset[0], offset[1])
        
        # 判断是否检测到运动
        motion_detected = len(filtered_contours) > 0
        
        return motion_detected, adjusted_contours

class DirectionDetector(BaseProcessor):
    """方向检测器"""
    def __init__(self, params=None):
        """初始化方向检测器

        Args:
            params (dict): 检测参数
                - detector_type: 检测器类型 ('background_subtraction' 或 'frame_difference')
                - motion_params: 运动检测参数
                - minDisplacement: 最小位移
                - maxPatience: 最大耐心值
                - consecutiveThreshold: 连续检测阈值
        """
        super().__init__()

        # 设置默认参数
        self.params = {
            'detector_type': 'background_subtraction',
            'motion_params': {
                'minArea': 100,
                'detectionThreshold': 30,
                'learningRate': 0.003,
                'shadowsThreshold': 0.5,
                'enabled': True  # 前置运动检测是否启用
            },
            'minDisplacement': 2,
            'maxPatience': 3,
            'consecutiveThreshold': 3
        }

        # 更新自定义参数
        if params:
            self.update_params(params)

        # 提取前置运动检测启用状态
        self.is_enabled = self.params['motion_params'].get('enabled', True)

        # 创建运动检测器
        self._create_motion_detector()

        # 跟踪状态
        self.prev_center = None
        self.direction_history = []
        self.patience = 0
        self.state = 'STATIONARY'  # 'STATIONARY', 'MOVING_UP', 'MOVING_DOWN'
        self.consecutive_count = 0  # 连续检测计数
    
    def update_params(self, params):
        """更新参数

        Args:
            params (dict): 新参数
        """
        # 更新顶层参数
        for key, value in params.items():
            if key != 'motion_params':
                self.params[key] = value

        # 更新运动检测参数
        if 'motion_params' in params:
            self.params['motion_params'].update(params['motion_params'])

        # 更新前置运动检测启用状态
        self.is_enabled = self.params['motion_params'].get('enabled', True)

        # 重新创建运动检测器
        self._create_motion_detector()
    
    def _create_motion_detector(self):
        """创建运动检测器"""
        if self.params['detector_type'] == 'frame_difference':
            self.motion_detector = FrameDifferencer(self.params['motion_params'])
        else:
            self.motion_detector = MotionDetector(self.params['motion_params'])
    
    def detect(self, frame, roi_info):
        """检测运动方向
        
        Args:
            frame (np.ndarray): 输入帧
            roi_info (dict): ROI信息
                - type: ROI类型 ('rectangle' 或 'polygon')
                - coords: 坐标 (对于矩形: [x, y, w, h], 对于多边形: [[x1, y1], [x2, y2], ...])
            
        Returns:
            tuple: (方向状态, 轮廓列表, 方向信息)
        """
        # 创建ROI掩码
        height, width = frame.shape[:2]
        roi_mask = np.zeros((height, width), dtype=np.uint8)
        
        if roi_info['type'] == 'rectangle':
            # 🔥 修复：处理不同格式的矩形坐标
            coords = roi_info['coords']
            if coords and isinstance(coords[0], dict):
                # 字典格式: [{"x": x1, "y": y1}, {"x": x2, "y": y2}] -> 转换为 [x, y, w, h]
                x1, y1 = coords[0]["x"], coords[0]["y"]
                x2, y2 = coords[1]["x"], coords[1]["y"]
                x, y, w, h = x1, y1, x2-x1, y2-y1
            else:
                # 数组格式: [x, y, w, h]
                x, y, w, h = coords[:4]
            cv2.rectangle(roi_mask, (int(x), int(y)), (int(x+w), int(y+h)), 255, -1)
        elif roi_info['type'] == 'polygon':
            # 🔥 修复：处理不同格式的坐标数据
            coords = roi_info['coords']
            if coords and isinstance(coords[0], dict):
                # 字典格式: [{"x": 100, "y": 100}, ...]
                points = np.array([[coord["x"], coord["y"]] for coord in coords], np.int32)
            else:
                # 数组格式: [[100, 100], ...]
                points = np.array(coords, np.int32)
            points = points.reshape((-1, 1, 2))
            cv2.fillPoly(roi_mask, [points], (255,))
        
        return self.detect_roi(frame, roi_mask)
    
    def detect_roi(self, frame, roi_mask=None):
        """检测ROI区域内的运动方向

        Args:
            frame (np.ndarray): 输入帧
            roi_mask (np.ndarray): ROI掩码

        Returns:
            tuple: (方向状态, 轮廓列表, 方向信息)
        """
        # 检查前置运动检测是否启用
        if not self.is_enabled:
            return 'STATIONARY', [], self._get_direction_info()

        # 检测运动
        motion_detected, contours = self.motion_detector.detect(frame, roi_mask)
        
        # 如果没有检测到运动
        if not motion_detected or not contours:
            # 增加耐心计数
            self.patience += 1

            # 如果超过最大耐心值，重置状态
            if self.patience > self.params['maxPatience']:
                self.state = 'STATIONARY'
                self.prev_center = None
                self.direction_history = []
                self.consecutive_count = 0

            return self.state, contours, self._get_direction_info()
        
        # 重置耐心计数
        self.patience = 0
        
        # 找到最大轮廓
        largest_contour = max(contours, key=cv2.contourArea)
        
        # 计算质心
        M = cv2.moments(largest_contour)
        if M["m00"] == 0:
            return self.state, contours, self._get_direction_info()
        
        cx = int(M["m10"] / M["m00"])
        cy = int(M["m01"] / M["m00"])
        current_center = (cx, cy)
        
        # 🔥 修复：改进方向计算逻辑
        if self.prev_center is None:
            # 第一次检测，记录中心点并假设静止
            self.prev_center = current_center
            direction = 'STATIONARY'
        else:
            # 计算垂直位移
            dy = current_center[1] - self.prev_center[1]

            # 🔥 降低最小位移阈值，使检测更敏感
            min_displacement = max(1, self.params.get('minDisplacement', 2))

            # 如果位移小于最小阈值，认为是静止
            if abs(dy) < min_displacement:
                direction = 'STATIONARY'
            else:
                direction = 'MOVING_UP' if dy < 0 else 'MOVING_DOWN'

            # 更新上一次中心点
            self.prev_center = current_center
        

        # 添加到方向历史
        self.direction_history.append(direction)

        # 保持固定长度的历史
        if len(self.direction_history) > self.params['consecutiveThreshold']:
            self.direction_history.pop(0)
        
        # 如果历史记录长度不足，保持当前状态
        if len(self.direction_history) < self.params['consecutiveThreshold']:
            return self.state, contours, self._get_direction_info()
        
        # 计算主导方向
        up_count = self.direction_history.count('MOVING_UP')
        down_count = self.direction_history.count('MOVING_DOWN')
        stationary_count = self.direction_history.count('STATIONARY')
            
        # 计算置信度
        total = len(self.direction_history)
        
        if up_count > down_count and up_count > stationary_count:
            self.state = 'MOVING_UP'
            confidence = up_count / total
        elif down_count > up_count and down_count > stationary_count:
            self.state = 'MOVING_DOWN'
            confidence = down_count / total
        else:
            self.state = 'STATIONARY'
            confidence = stationary_count / total
        
        # 绘制方向箭头到轮廓上
        enhanced_contours = self._draw_direction_arrows(contours, current_center, confidence)

        return self.state, enhanced_contours, self._get_direction_info(confidence)
    
    def _draw_direction_arrows(self, contours, center, confidence):
        """绘制方向箭头到轮廓上

        Args:
            contours (list): 原始轮廓列表
            center (tuple): 运动中心点 (x, y)
            confidence (float): 置信度

        Returns:
            list: 包含方向箭头的增强轮廓列表
        """
        if self.state == 'STATIONARY' or center is None:
            return contours

        # 创建方向箭头轮廓
        arrow_contours = []

        # 箭头参数
        arrow_length = 50  # 箭头长度
        arrow_width = 20   # 箭头宽度

        cx, cy = center

        if self.state == 'MOVING_UP':
            # 向上箭头
            arrow_points = np.array([
                [cx, cy - arrow_length],      # 箭头顶点
                [cx - arrow_width//2, cy],    # 左下角
                [cx - arrow_width//4, cy],    # 左中
                [cx - arrow_width//4, cy + arrow_length//2],  # 左下
                [cx + arrow_width//4, cy + arrow_length//2],  # 右下
                [cx + arrow_width//4, cy],    # 右中
                [cx + arrow_width//2, cy],    # 右下角
            ], dtype=np.int32)
        elif self.state == 'MOVING_DOWN':
            # 向下箭头
            arrow_points = np.array([
                [cx, cy + arrow_length],      # 箭头顶点
                [cx - arrow_width//2, cy],    # 左上角
                [cx - arrow_width//4, cy],    # 左中
                [cx - arrow_width//4, cy - arrow_length//2],  # 左上
                [cx + arrow_width//4, cy - arrow_length//2],  # 右上
                [cx + arrow_width//4, cy],    # 右中
                [cx + arrow_width//2, cy],    # 右上角
            ], dtype=np.int32)
        else:
            return contours

        # 将箭头轮廓添加到结果中（直接作为OpenCV轮廓格式）
        arrow_contours.append(arrow_points.reshape(-1, 1, 2))

        # 返回原始轮廓 + 箭头轮廓
        return contours + arrow_contours

    def _get_direction_info(self, confidence=0.0):
        """获取方向信息

        Args:
            confidence (float): 置信度

        Returns:
            dict: 方向信息
        """
        if self.prev_center is None:
            return None

        return {
            'direction': self.state,
            'center': self.prev_center,
            'confidence': confidence
        }

class VideoDetectionManager:
    """视频检测管理器"""
    def __init__(self):
        """初始化视频检测管理器"""
        self.detectors = {}
        self.roi_detectors = {}
        self.global_settings = {}


    
    def get_detector(self, detector_id, detector_type='motion', params=None):
        """获取检测器
        
        Args:
            detector_id (str): 检测器ID
            detector_type (str): 检测器类型 ('motion', 'direction', 'frame_difference')
            params (dict): 检测参数
            
        Returns:
            object: 检测器实例
        """
        # 如果检测器已存在，返回现有检测器
        if detector_id in self.detectors:
            # 如果提供了参数，更新检测器参数
            if params and hasattr(self.detectors[detector_id], 'update_params'):
                self.detectors[detector_id].update_params(params)
            return self.detectors[detector_id]
        
        # 创建新检测器
        if detector_type == 'motion':
            detector = MotionDetector(params)
        elif detector_type == 'direction':
            detector = DirectionDetector(params)
        elif detector_type == 'frame_difference':
            detector = FrameDifferencer(params)
        else:
            raise ValueError(f"不支持的检测器类型: {detector_type}")
        
        # 存储检测器
        self.detectors[detector_id] = detector
        
        return detector
    
    def remove_detector(self, detector_id):
        """移除检测器
        
        Args:
            detector_id (str): 检测器ID
            
        Returns:
            bool: 是否成功移除
        """
        if detector_id in self.detectors:
            del self.detectors[detector_id]
            return True
        return False

    def process_multi_roi_detection(self, frame, roi_configs, global_settings=None):
        """
        处理多ROI检测

        Args:
            frame: 视频帧
            roi_configs: ROI配置列表
            global_settings: 全局设置参数（动态传入）

        Returns:
            dict: 包含所有ROI检测结果
        """
        # 动态更新全局参数
        if global_settings:
            self.global_settings.update(global_settings)

        roi_results = {}

        # 处理每个ROI的检测
        for roi_config in roi_configs:
            roi_id = roi_config.get('roi_id')
            roi_type = roi_config.get('roi_type', 'rectangle')
            algorithm_type = roi_config.get('algorithm_type', 'motion')
            algorithm_params = roi_config.get('algorithm_params', {})
            coordinates = roi_config.get('coordinates', [])

            if not roi_id or not coordinates:
                continue

            # 创建ROI掩码
            roi_mask = self._create_roi_mask(frame, coordinates, roi_type)

            # 优先使用WebSocket中的ROI专用检测器
            detector = None
            detector_source = "未知"

            # 尝试从WebSocket检测器实例中获取ROI专用检测器
            try:
                from app.api.websocket_router import detection_instances
                for client_id, instance in detection_instances.items():
                    if roi_id in instance.get("roi_detectors", {}):
                        detector = instance["roi_detectors"][roi_id]
                        detector_source = f"WebSocket专用检测器({type(detector).__name__})"
                        break
            except Exception:
                pass

            # 如果没有找到WebSocket检测器，使用本地检测器
            if detector is None:
                detector = self.get_detector(roi_id, algorithm_type, algorithm_params)
                detector_source = f"本地检测器({type(detector).__name__})"

            # 执行检测
            if algorithm_type == 'motion':
                motion_detected, contours = detector.detect(frame, roi_mask)
                roi_results[roi_id] = {
                    'roi_type': roi_config.get('attribute', 'pailiao'),  # yazhu 或 pailiao
                    'motion_detected': motion_detected,
                    'contours': contours,
                    'algorithm_type': algorithm_type
                }
            elif algorithm_type == 'direction':
                # 根据检测器来源和类型选择调用方法
                if "WebSocket专用检测器" in detector_source and hasattr(detector, 'detect_roi'):
                    # WebSocket的DirectionDetector使用detect_roi方法
                    detection_result = detector.detect_roi(frame, roi_mask)
                else:
                    # 本地检测器或其他检测器使用detect方法
                    roi_info = {
                        'type': roi_config.get('roi_type', 'polygon'),
                        'coords': roi_config.get('coordinates', [])
                    }
                    detection_result = detector.detect(frame, roi_info)

                if len(detection_result) == 3:
                    direction, contours, direction_info = detection_result
                else:
                    direction, contours = detection_result
                    direction_info = None

                # 确保direction字段格式正确
                if isinstance(direction_info, dict):
                    direction_data = direction_info
                else:
                    direction_data = {'direction': direction, 'center': (0, 0), 'confidence': 0.0}

                roi_results[roi_id] = {
                    'roi_type': roi_config.get('attribute', 'yazhu'),  # yazhu 或 pailiao
                    'motion_detected': len(contours) > 0,
                    'direction': direction_data,
                    'is_triggered': direction == 'MOVING_DOWN',
                    'contours': contours,
                    'direction_info': direction_info,
                    'algorithm_type': algorithm_type
                }

        return {
            'roi_results': roi_results
        }

    def _create_roi_mask(self, frame, coordinates, roi_type):
        """创建ROI掩码"""
        mask = np.zeros(frame.shape[:2], dtype=np.uint8)

        if roi_type == 'rectangle' and len(coordinates) >= 4:
            try:
                x, y, w, h = coordinates[:4]
                cv2.rectangle(mask, (int(x), int(y)), (int(x + w), int(y + h)), 255, -1)
            except Exception:
                return mask

        elif roi_type == 'polygon' and len(coordinates) >= 3:
            # 🔥 修复：处理不同格式的坐标数据
            try:
                # 检查坐标格式并转换
                if isinstance(coordinates[0], dict):
                    # 格式: [{"x": 100, "y": 100}, {"x": 200, "y": 100}, ...]
                    points_list = []
                    for i, coord in enumerate(coordinates):
                        if isinstance(coord, dict) and "x" in coord and "y" in coord:
                            x_val = coord["x"]
                            y_val = coord["y"]
                            # 确保坐标值是数字类型
                            if isinstance(x_val, (int, float)) and isinstance(y_val, (int, float)):
                                points_list.append([int(x_val), int(y_val)])

                    if len(points_list) >= 3:
                        points = np.array(points_list, dtype=np.int32)
                    else:
                        return mask

                elif isinstance(coordinates[0], (list, tuple)) and len(coordinates[0]) == 2:
                    # 格式: [[100, 100], [200, 100], ...]
                    points = np.array(coordinates, dtype=np.int32)

                else:
                    # 格式: [100, 100, 200, 100, ...] (展平格式)
                    if len(coordinates) >= 6 and len(coordinates) % 2 == 0:
                        points = np.array(coordinates, dtype=np.float32).reshape(-1, 2).astype(np.int32)
                    else:
                        return mask

                # 确保points是正确的形状用于OpenCV
                points = points.reshape((-1, 1, 2))

                # 填充多边形
                cv2.fillPoly(mask, [points], (255,))

            except Exception:
                return mask

        return mask

    def update_global_settings(self, settings):
        """更新全局设置参数"""
        if settings:
            self.global_settings.update(settings)
            return True
        return False

    def get_global_settings(self):
        """获取全局设置参数"""
        return self.global_settings
        
    def handle_message(self, message_type, message_data):
        """处理WebSocket消息"""
        if message_type == 'update_config':
            # 处理配置更新
            if 'global_settings' in message_data:
                self.update_global_settings(message_data['global_settings'])
                
            # 处理其他配置更新
            # ...
            
        elif message_type == 'request_global_settings':
            # 返回全局设置
            return {
                'type': 'global_settings',
                'settings': self.get_global_settings()
            }
            
        # 处理其他消息类型
        # ...
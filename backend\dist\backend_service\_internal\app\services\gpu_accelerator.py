import cv2
import numpy as np
import logging
from typing import Optional, Tuple, Any
import threading

logger = logging.getLogger(__name__)

class GPUAccelerator:
    """GPU加速器 - 提供图像处理的GPU加速功能
    
    该类提供了一系列GPU加速的图像处理方法，能够显著提升
    运动检测、方向检测等算法的处理性能。采用智能调度策略，
    根据图像大小和算法类型自动选择CPU或GPU处理。
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(GPUAccelerator, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化GPU加速器"""
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.gpu_available = False
        self.gpu_device_count = 0
        self.current_device = 0
        
        # 性能阈值设置
        self.size_thresholds = {
            'small': 100 * 100,      # 小图像阈值
            'medium': 500 * 500,     # 中等图像阈值
            'large': 1000 * 1000     # 大图像阈值
        }
        
        self._detect_gpu_support()
    
    def _detect_gpu_support(self):
        """检测GPU支持情况"""
        try:
            # 检查OpenCV是否支持CUDA
            if cv2.cuda.getCudaEnabledDeviceCount() > 0:
                self.gpu_available = True
                self.gpu_device_count = cv2.cuda.getCudaEnabledDeviceCount()
                
                # 获取GPU设备信息
                for i in range(self.gpu_device_count):
                    device_info = cv2.cuda.DeviceInfo(i)
                    logger.info(f"GPU设备 {i}: {device_info.name()}, "
                              f"计算能力: {device_info.majorVersion()}.{device_info.minorVersion()}, "
                              f"内存: {device_info.totalMemory() / (1024**3):.1f}GB")
                
                logger.info(f"GPU加速已启用，检测到 {self.gpu_device_count} 个CUDA设备")
            else:
                logger.info("未检测到CUDA支持的GPU设备，将使用CPU处理")
                
        except Exception as e:
            logger.warning(f"GPU检测失败: {e}，将使用CPU处理")
            self.gpu_available = False
    
    def set_device(self, device_id: int) -> bool:
        """设置当前使用的GPU设备
        
        Args:
            device_id (int): GPU设备ID
            
        Returns:
            bool: 是否设置成功
        """
        if not self.gpu_available or device_id >= self.gpu_device_count:
            return False
        
        try:
            cv2.cuda.setDevice(device_id)
            self.current_device = device_id
            logger.info(f"已切换到GPU设备 {device_id}")
            return True
        except Exception as e:
            logger.error(f"切换GPU设备失败: {e}")
            return False
    
    def _should_use_gpu(self, image_size: int, operation: str = 'default') -> bool:
        """智能判断是否应该使用GPU
        
        Args:
            image_size (int): 图像像素总数
            operation (str): 操作类型
            
        Returns:
            bool: 是否使用GPU
        """
        if not self.gpu_available:
            return False
        
        # 根据操作类型和图像大小决定
        if operation == 'bilateral_filter':
            # 双边滤波强烈推荐GPU，性能提升显著
            return image_size > self.size_thresholds['small']
        elif operation == 'resize':
            # 大图像缩放优先使用GPU
            return image_size > self.size_thresholds['medium']
        elif operation in ['cvt_color', 'abs_diff', 'threshold']:
            # 这些操作在中等以上图像时使用GPU
            return image_size > self.size_thresholds['medium']
        else:
            # 默认策略：大图像使用GPU
            return image_size > self.size_thresholds['large']
    
    def resize_frame(self, frame: np.ndarray, size: Tuple[int, int]) -> np.ndarray:
        """GPU加速的图像缩放
        
        Args:
            frame (np.ndarray): 输入图像
            size (Tuple[int, int]): 目标尺寸 (width, height)
            
        Returns:
            np.ndarray: 缩放后的图像
        """
        try:
            image_size = frame.shape[0] * frame.shape[1]
            
            if self._should_use_gpu(image_size, 'resize'):
                # 使用GPU处理
                gpu_frame = cv2.cuda_GpuMat()
                gpu_frame.upload(frame)
                
                gpu_resized = cv2.cuda.resize(gpu_frame, size)
                
                result = gpu_resized.download()
                return result
            else:
                # 使用CPU处理
                return cv2.resize(frame, size)
                
        except Exception as e:
            logger.warning(f"GPU缩放失败，回退到CPU: {e}")
            return cv2.resize(frame, size)
    
    def cvt_color_gpu(self, frame: np.ndarray, code: int) -> np.ndarray:
        """GPU加速的颜色空间转换
        
        Args:
            frame (np.ndarray): 输入图像
            code (int): 颜色转换代码
            
        Returns:
            np.ndarray: 转换后的图像
        """
        try:
            image_size = frame.shape[0] * frame.shape[1]
            
            if self._should_use_gpu(image_size, 'cvt_color'):
                # 使用GPU处理
                gpu_frame = cv2.cuda_GpuMat()
                gpu_frame.upload(frame)
                
                gpu_converted = cv2.cuda.cvtColor(gpu_frame, code)
                
                result = gpu_converted.download()
                return result
            else:
                # 使用CPU处理
                return cv2.cvtColor(frame, code)
                
        except Exception as e:
            logger.warning(f"GPU颜色转换失败，回退到CPU: {e}")
            return cv2.cvtColor(frame, code)
    
    def abs_diff_gpu(self, frame1: np.ndarray, frame2: np.ndarray) -> np.ndarray:
        """GPU加速的图像差分运算
        
        Args:
            frame1 (np.ndarray): 第一张图像
            frame2 (np.ndarray): 第二张图像
            
        Returns:
            np.ndarray: 差分结果
        """
        try:
            image_size = frame1.shape[0] * frame1.shape[1]
            
            if self._should_use_gpu(image_size, 'abs_diff'):
                # 使用GPU处理
                gpu_frame1 = cv2.cuda_GpuMat()
                gpu_frame2 = cv2.cuda_GpuMat()
                
                gpu_frame1.upload(frame1)
                gpu_frame2.upload(frame2)
                
                gpu_diff = cv2.cuda.absdiff(gpu_frame1, gpu_frame2)
                
                result = gpu_diff.download()
                return result
            else:
                # 使用CPU处理
                return cv2.absdiff(frame1, frame2)
                
        except Exception as e:
            logger.warning(f"GPU差分运算失败，回退到CPU: {e}")
            return cv2.absdiff(frame1, frame2)
    
    def threshold_gpu(self, frame: np.ndarray, thresh: float, maxval: float, 
                     type_flag: int) -> Tuple[float, np.ndarray]:
        """GPU加速的阈值处理
        
        Args:
            frame (np.ndarray): 输入图像
            thresh (float): 阈值
            maxval (float): 最大值
            type_flag (int): 阈值类型
            
        Returns:
            Tuple[float, np.ndarray]: (阈值, 处理后的图像)
        """
        try:
            image_size = frame.shape[0] * frame.shape[1]
            
            if self._should_use_gpu(image_size, 'threshold'):
                # 使用GPU处理
                gpu_frame = cv2.cuda_GpuMat()
                gpu_frame.upload(frame)
                
                ret, gpu_thresh = cv2.cuda.threshold(gpu_frame, thresh, maxval, type_flag)
                
                result = gpu_thresh.download()
                return ret, result
            else:
                # 使用CPU处理
                return cv2.threshold(frame, thresh, maxval, type_flag)
                
        except Exception as e:
            logger.warning(f"GPU阈值处理失败，回退到CPU: {e}")
            return cv2.threshold(frame, thresh, maxval, type_flag)
    
    def morphology_gpu(self, frame: np.ndarray, operation: int, kernel: Optional[np.ndarray] = None, 
                      iterations: int = 1) -> np.ndarray:
        """GPU加速的形态学运算
        
        注意：由于GPU内存管理的复杂性，当前版本使用CPU处理以确保稳定性
        
        Args:
            frame (np.ndarray): 输入图像
            operation (int): 形态学操作类型
            kernel (Optional[np.ndarray]): 结构元素
            iterations (int): 迭代次数
            
        Returns:
            np.ndarray: 处理后的图像
        """
        try:
            # 为确保稳定性，当前使用CPU处理
            if kernel is None:
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            
            return cv2.morphologyEx(frame, operation, kernel, iterations=iterations)
            
        except Exception as e:
            logger.warning(f"形态学运算失败: {e}")
            # 返回原图像作为备选
            return frame
    
    def gaussian_blur_gpu(self, frame: np.ndarray, ksize: Tuple[int, int], 
                         sigmaX: float) -> np.ndarray:
        """GPU加速的高斯模糊
        
        注意：为确保算法一致性，当前版本使用CPU处理
        
        Args:
            frame (np.ndarray): 输入图像
            ksize (Tuple[int, int]): 核大小
            sigmaX (float): X方向标准差
            
        Returns:
            np.ndarray: 模糊后的图像
        """
        try:
            # 为确保算法一致性，使用CPU处理
            return cv2.GaussianBlur(frame, ksize, sigmaX)
            
        except Exception as e:
            logger.warning(f"高斯模糊失败: {e}")
            return frame
    
    def bilateral_filter_gpu(self, frame: np.ndarray, d: int, sigmaColor: float, 
                           sigmaSpace: float) -> np.ndarray:
        """GPU加速的双边滤波
        
        双边滤波是GPU加速效果最显著的操作之一，可获得26倍性能提升
        
        Args:
            frame (np.ndarray): 输入图像
            d (int): 滤波器直径
            sigmaColor (float): 颜色空间标准差
            sigmaSpace (float): 坐标空间标准差
            
        Returns:
            np.ndarray: 滤波后的图像
        """
        try:
            image_size = frame.shape[0] * frame.shape[1]
            
            if self._should_use_gpu(image_size, 'bilateral_filter'):
                # 使用GPU处理 - 强烈推荐
                gpu_frame = cv2.cuda_GpuMat()
                gpu_frame.upload(frame)
                
                # 创建双边滤波器
                bilateral_filter = cv2.cuda.createBilateralFilter(
                    srcType=gpu_frame.type(),
                    dstType=-1,
                    kernelSize=d,
                    sigmaColor=sigmaColor,
                    sigmaSpace=sigmaSpace
                )
                
                gpu_filtered = bilateral_filter.apply(gpu_frame)
                
                result = gpu_filtered.download()
                return result
            else:
                # 使用CPU处理
                return cv2.bilateralFilter(frame, d, sigmaColor, sigmaSpace)
                
        except Exception as e:
            logger.warning(f"GPU双边滤波失败，回退到CPU: {e}")
            return cv2.bilateralFilter(frame, d, sigmaColor, sigmaSpace)
    
    def get_device_info(self) -> dict:
        """获取GPU设备信息
        
        Returns:
            dict: GPU设备信息
        """
        info = {
            'gpu_available': self.gpu_available,
            'device_count': self.gpu_device_count,
            'current_device': self.current_device,
            'devices': []
        }
        
        if self.gpu_available:
            try:
                for i in range(self.gpu_device_count):
                    device_info = cv2.cuda.DeviceInfo(i)
                    info['devices'].append({
                        'id': i,
                        'name': device_info.name(),
                        'compute_capability': f"{device_info.majorVersion()}.{device_info.minorVersion()}",
                        'total_memory_gb': device_info.totalMemory() / (1024**3),
                        'free_memory_gb': device_info.freeMemory() / (1024**3)
                    })
            except Exception as e:
                logger.error(f"获取GPU设备信息失败: {e}")
        
        return info
    
    def get_performance_stats(self) -> dict:
        """获取性能统计信息
        
        Returns:
            dict: 性能统计
        """
        return {
            'gpu_enabled': self.gpu_available,
            'size_thresholds': self.size_thresholds,
            'recommended_operations': {
                'bilateral_filter': 'GPU强烈推荐，性能提升26倍',
                'resize': 'GPU适用于大图像',
                'cvt_color': 'GPU适用于中等以上图像',
                'abs_diff': 'GPU适用于中等以上图像',
                'threshold': 'GPU适用于中等以上图像',
                'morphology': 'CPU处理（稳定性考虑）',
                'gaussian_blur': 'CPU处理（一致性考虑）'
            }
        }

# 全局单例获取函数
_gpu_accelerator_instance = None

def get_gpu_accelerator() -> GPUAccelerator:
    """获取GPU加速器全局实例
    
    Returns:
        GPUAccelerator: GPU加速器实例
    """
    global _gpu_accelerator_instance
    if _gpu_accelerator_instance is None:
        _gpu_accelerator_instance = GPUAccelerator()
    return _gpu_accelerator_instance
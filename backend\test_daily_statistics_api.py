#!/usr/bin/env python3
"""
每日检测统计API测试脚本

用于测试新增的每日检测统计API功能
"""

import requests
import json
from datetime import datetime, timedelta

# API基础URL
BASE_URL = "http://localhost:8000/api"

def test_daily_statistics_api():
    """测试每日检测统计API"""
    print("=== 每日检测统计API测试 ===")
    
    # 测试1: 获取今天的统计数据
    print("\n1. 测试获取今天的统计数据")
    try:
        response = requests.get(f"{BASE_URL}/card-detection/daily-statistics")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"日期: {data['date']}")
            print(f"模板数量: {len(data['templates'])}")
            
            for template in data['templates']:
                print(f"  模板: {template['template_name']} (ID: {template['template_id']})")
                print(f"    总检测: {template['total_detections']}, 卡料: {template['jam_detections']}, 成功率: {template['success_rate']:.2%}")
                
                for die_caster in template['die_casters']:
                    print(f"    压铸机: {die_caster['die_caster_name']} (ID: {die_caster['die_caster_id']})")
                    print(f"      总检测: {die_caster['total_detections']}, 卡料: {die_caster['jam_detections']}, 成功率: {die_caster['success_rate']:.2%}")
                    
                    for group in die_caster['detection_groups']:
                        print(f"      检测组: {group['group_name']} (ID: {group['group_id']})")
                        print(f"        总检测: {group['total_detections']}, 卡料: {group['jam_detections']}, 成功率: {group['success_rate']:.2%}")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {e}")
    
    # 测试2: 获取指定日期的统计数据
    print("\n2. 测试获取指定日期的统计数据")
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    try:
        response = requests.get(f"{BASE_URL}/card-detection/daily-statistics?date={yesterday}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"日期: {data['date']}")
            print(f"模板数量: {len(data['templates'])}")
            
            if data['templates']:
                print("有统计数据")
            else:
                print("无统计数据")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {e}")
    
    # 测试3: 测试无效日期格式
    print("\n3. 测试无效日期格式")
    try:
        response = requests.get(f"{BASE_URL}/card-detection/daily-statistics?date=invalid-date")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 400:
            print("正确返回400错误")
            print(f"错误信息: {response.json()}")
        else:
            print(f"意外的响应: {response.text}")
    except Exception as e:
        print(f"请求异常: {e}")

def test_existing_apis():
    """测试现有的相关API"""
    print("\n=== 测试现有相关API ===")
    
    # 测试检测组列表
    print("\n1. 测试检测组列表API")
    try:
        response = requests.get(f"{BASE_URL}/detection-groups/")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"检测组数量: {len(data)}")
            for group in data[:3]:  # 只显示前3个
                print(f"  检测组: {group['name']} (ID: {group['id']}, 模板ID: {group.get('template_id', 'None')})")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {e}")
    
    # 测试检测模板列表
    print("\n2. 测试检测模板列表API")
    try:
        response = requests.get(f"{BASE_URL}/detection-templates/")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"检测模板数量: {len(data)}")
            for template in data[:3]:  # 只显示前3个
                print(f"  模板: {template['name']} (ID: {template['id']})")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {e}")
    
    # 测试卡料检测结果列表
    print("\n3. 测试卡料检测结果列表API")
    try:
        response = requests.get(f"{BASE_URL}/card-detection/?page=1&page_size=5")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"总记录数: {data['total']}")
            print(f"当前页记录数: {len(data['items'])}")
            for item in data['items']:
                print(f"  检测记录: 组ID {item['detection_group_id']}, 时间 {item['timestamp']}, 正常: {item['is_normal']}")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {e}")

def main():
    """主函数"""
    print("每日检测统计API测试脚本")
    print("确保后端服务已启动在 http://localhost:8000")
    print("="*50)
    
    # 测试现有API
    test_existing_apis()
    
    # 测试新的统计API
    test_daily_statistics_api()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
<template>
  <div class="detection-info" :class="{ 'detection-info--compact': detectionMode }">
    <!-- 紧凑的标题和控制区域 -->
    <div class="detection-header" v-if="!detectionMode">
      <div class="detection-header__left">
        <h4 class="detection-info__title">检测信息</h4>
        <div 
          class="status-indicator" 
          :class="`status-indicator--${isDetectionActive ? 'running' : 'stopped'}`"
        >
          <div class="status-indicator__dot"></div>
          <span class="status-text">{{ isDetectionActive ? '运行中' : '未启动' }}</span>
        </div>
      </div>
      
      <!-- 控制按钮 -->
      <div class="detection-controls">
        <button
          class="detection-controls__button detection-controls__button--start"
          :disabled="isDetectionActive || !canControl"
          @click="$emit('start')"
        >
          开始
        </button>
        <button
          class="detection-controls__button detection-controls__button--stop"
          :disabled="!isDetectionActive || !canControl"
          @click="$emit('stop')"
        >
          停止
        </button>
      </div>
    </div>
    
    <!-- 检测模式下的紧凑卡料检测显示 -->
    <div class="card-detection-compact" v-if="detectionMode && cardDetectionState.isActive">
      <div class="compact-stats">
        <div class="stat-item">
          <span class="stat-label">检测次数:</span>
          <span class="stat-value">{{ cardDetectionStats.totalDetections }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">卡料次数:</span>
          <span class="stat-value error">{{ cardDetectionStats.cardCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">成功率:</span>
          <span class="stat-value">{{ cardDetectionStats.successRate }}%</span>
        </div>
      </div>
      
      <!-- 最近检测结果（紧凑显示） -->
      <div class="recent-results-compact" v-if="cardDetectionStats.recentResults.length > 0">
        <div class="result-item-compact" 
             v-for="(result, index) in cardDetectionStats.recentResults.slice(0, 3)" 
             :key="index"
             :class="result.isNormal ? 'normal' : 'error'">
          <span class="result-time">{{ formatTime(result.timestamp) }}</span>
          <span class="result-status">{{ result.isNormal ? '正常' : '卡料' }}</span>
        </div>
      </div>
    </div>
    
    <!-- 检测模式下的简化状态显示 -->
    <div class="detection-status-compact" v-if="detectionMode">
      <div class="status-indicator" :class="`status-indicator--${isDetectionActive ? 'running' : 'stopped'}`">
        <div class="status-indicator__dot"></div>
        <span class="status-text">卡料检测: {{ isDetectionActive ? '运行中' : '未启动' }}</span>
      </div>
      <div class="card-status-compact" v-if="cardDetectionState.isActive">
        <span class="phase-text">{{ cardDetectionStatus.text }}</span>
        <span v-if="cardDetectionState.remainingTime > 0" class="countdown">{{ cardDetectionState.remainingTime }}s</span>
      </div>
    </div>

    <!-- 主要内容区域：左右两列布局 -->
    <div class="detection-content" v-if="!detectionMode">
      <!-- 左侧：卡料检测模块 -->
      <div class="detection-left">
        <div class="card-detection-module">
          <div class="module-header">
            <h4 class="module-title">卡料检测模块</h4>
            <div class="module-status" :class="cardDetectionStatus.status">
              <div class="status-dot"></div>
              <span>{{ cardDetectionStatus.text }}</span>
            </div>
          </div>
          
          <div class="card-detection-content">
            <!-- 检测状态显示 -->
            <div class="detection-phase">
              <div class="phase-item" :class="{ active: cardDetectionState.phase === 'monitoring' }">
                <span class="phase-label">监测压铸机</span>
                <span class="phase-status">{{ cardDetectionState.phase === 'monitoring' ? '进行中' : '待机' }}</span>
              </div>
              <div class="phase-item" :class="{ active: cardDetectionState.phase === 'detecting' }">
                <span class="phase-label">检测排料口</span>
                <span class="phase-status">{{ cardDetectionState.phase === 'detecting' ? `${cardDetectionState.remainingTime}s` : '待机' }}</span>
              </div>
              <div class="phase-item" :class="{ active: cardDetectionState.phase === 'cooldown' }">
                <span class="phase-label">冷却等待</span>
                <span class="phase-status">{{ cardDetectionState.phase === 'cooldown' ? `${cardDetectionState.remainingTime}s` : '待机' }}</span>
              </div>
            </div>

            <!-- 排料口状态 -->
            <div class="pailiao-status" v-if="cardDetectionState.phase === 'detecting'">
              <h5>排料口检测状态</h5>
              <div class="pailiao-list">
                <div 
                  v-for="pailiao in pailiaoROIs" 
                  :key="pailiao.roi_id"
                  class="pailiao-item"
                  :class="{ detected: cardDetectionState.detectedPailiaoROIs.includes(pailiao.roi_id) }"
                >
                  <span class="pailiao-name">{{ pailiao.name }}</span>
                  <span class="pailiao-result">
                    {{ cardDetectionState.detectedPailiaoROIs.includes(pailiao.roi_id) ? '已检测' : '未检测' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 检测统计 -->
            <div class="card-detection-stats">
              <div class="stat-row">
                <span class="stat-label">总检测次数:</span>
                <span class="stat-value">{{ cardDetectionStats.totalDetections }}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">正常次数:</span>
                <span class="stat-value normal">{{ cardDetectionStats.normalCount }}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">卡料次数:</span>
                <span class="stat-value error">{{ cardDetectionStats.cardCount }}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">成功率:</span>
                <span class="stat-value">{{ cardDetectionStats.successRate }}%</span>
              </div>
            </div>

            <!-- 最近检测结果 -->
            <div class="recent-results" v-if="cardDetectionStats.recentResults.length > 0">
              <h5>最近检测结果</h5>
              <div class="result-list">
                <div 
                  v-for="(result, index) in cardDetectionStats.recentResults.slice(0, 5)" 
                  :key="index"
                  class="result-item"
                  :class="result.isNormal ? 'normal' : 'error'"
                >
                  <span class="result-time">{{ formatTime(result.timestamp) }}</span>
                  <span class="result-status">{{ result.isNormal ? '正常' : '卡料' }}</span>
                  <span class="result-details" v-if="!result.isNormal">
                    未检测: {{ result.undetectedROIs.join(', ') }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：ROI信息状态模块 -->
      <div class="detection-right">
        <div class="roi-info-module">
          <div class="module-header">
            <h4 class="module-title">ROI信息状态</h4>
          </div>
          
          <div class="roi-info-content">
            <!-- ROI检测算法详细统计 -->
    <div class="algorithm-summary" v-if="roiDetailedSummary && roiDetailedSummary.length > 0">
      <div class="section-header">
        <span class="section-title">ROI配置</span>
        <span class="section-count">({{ roiDetailedSummary.length }}个)</span>
        <!-- 算法类型统计 -->
        <div class="algorithm-stats">
          <span
            v-for="(count, algorithm) in roiAlgorithmSummary"
            :key="algorithm"
            class="algorithm-stat"
          >
            {{ formatAlgorithmType(algorithm) }}:{{ count }}
          </span>
        </div>
      </div>

      <!-- ROI详细列表 -->
      <div class="roi-list">
        <div
          v-for="roi in roiDetailedSummary"
          :key="roi.id"
          class="roi-item"
          :class="`roi-item--${roi.attribute}`"
        >
          <div class="roi-item__main">
            <span class="roi-name">{{ roi.name || roi.id }}</span>
            <span class="roi-algorithm">{{ formatAlgorithmType(roi.algorithmType) }}</span>
            <span
              class="roi-status"
              :class="{
                'roi-status--active': roi.isActive,
                'roi-status--inactive': !roi.isActive
              }"
            >
              {{ roi.isActive ? '运行' : '停止' }}
            </span>
          </div>
          <div class="roi-item__params" v-if="roi.keyParams.length > 0">
            <span
              v-for="param in roi.keyParams.slice(0, 3)"
              :key="param.key"
              class="param-item"
              :title="`${param.key}: ${param.value}`"
            >
              {{ param.key }}={{ param.value }}
            </span>
            <span v-if="roi.keyParams.length > 3" class="param-more">+{{ roi.keyParams.length - 3 }}</span>
          </div>
        </div>
      </div>
    </div>



    <!-- 检测结果 -->
    <div class="detection-results" v-if="detectionResult">
      <div class="section-header">
        <span class="section-title">检测结果</span>
      </div>
      <div class="results-grid">
        <div class="result-item">
          <span class="result-label">状态:</span>
          <span 
            class="result-value" 
            :class="{
              'result-value--clear': !detectionResult.motion_detected,
              'result-value--detected': detectionResult.motion_detected
            }"
          >
            {{ detectionResult.motion_detected ? '运动' : '静止' }}
          </span>
        </div>
        
        <div class="result-item" v-if="detectionResult.motion_detected">
          <span class="result-label">轮廓:</span>
          <span class="result-value">{{ detectionResult.contours?.length || 0 }}</span>
        </div>
        
        <div class="result-item" v-if="detectionResult.direction">
          <span class="result-label">方向:</span>
          <span class="result-value">{{ formatDirection(detectionResult.direction) }}</span>
        </div>
      </div>

      <!-- ROI检测结果 -->
      <div class="roi-results" v-if="detectionResult.roi_results && detectionResult.roi_results.length > 0">
        <div 
          v-for="(roi, index) in detectionResult.roi_results" 
          :key="index" 
          class="roi-result-item"
        >
          <span class="roi-result-name">{{ roi.roi_name || `ROI${index + 1}` }}</span>
          <span 
            class="roi-result-status" 
            :class="{
              'roi-result-status--clear': !roi.motion_detected,
              'roi-result-status--detected': roi.motion_detected
            }"
          >
            {{ roi.motion_detected ? '运动' : '静止' }}
          </span>
        </div>
      </div>
    </div>

    <!-- 检测统计 -->
    <div class="detection-stats" v-if="isDetectionActive && detectionStats">
      <div class="section-header">
        <span class="section-title">统计信息</span>
      </div>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ detectionStats.totalDetections }}</div>
          <div class="stat-label">检测次数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ (detectionStats.motionDetectionRate * 100).toFixed(1) }}%</div>
          <div class="stat-label">运动率</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ formatDuration(detectionStats.runningTime) }}</div>
          <div class="stat-label">运行时间</div>
        </div>
      </div>
    </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref, reactive, onMounted, onUnmounted, inject } from 'vue'
import type { ROI } from '@/types/roi'
import { cardDetectionApi } from '@/api/card-detection'

// 卡料检测相关接口定义
interface CardDetectionState {
  phase: 'idle' | 'monitoring' | 'detecting' | 'cooldown'
  remainingTime: number
  detectedPailiaoROIs: string[]
  isActive: boolean
}

interface CardDetectionResult {
  timestamp: number
  isNormal: boolean
  undetectedROIs: string[]
  detectionTime: number
}

interface CardDetectionStats {
  totalDetections: number
  normalCount: number
  cardCount: number
  successRate: number
  recentResults: CardDetectionResult[]
}

// 定义组件属性
const props = defineProps({
  // 检测状态
  isDetectionActive: {
    type: Boolean,
    default: false
  },
  // ROI列表（用于算法统计）
  roiList: {
    type: Array as any,
    default: () => []
  },
  // ROI检测器配置（与后端一致的参数）
  roiDetectors: {
    type: Object as any,
    default: () => ({})
  },
  // ROI检测控制器状态
  roiDetectionStates: {
    type: Object as any,
    default: () => ({})
  },
  // 检测结果
  detectionResult: {
    type: Object as any,
    default: () => ({
      motion_detected: false,
      contours: [],
      direction: null,
      roi_results: [],
      roi_violations: []
    })
  },
  // 检测统计信息
  detectionStats: {
    type: Object as any,
    default: null
  },
  // 是否可以控制检测
  canControl: {
    type: Boolean,
    default: true
  },
  // 检测模式（true时显示紧凑界面）
  detectionMode: {
    type: Boolean,
    default: false
  },
  // 全局设置参数
  globalSettings: {
    type: Object as any,
    default: () => ({
      delayTime: 5,
      cooldownTime: 3
    })
  },
  // 检测组ID
  detectionGroupId: {
    type: Number,
    default: null
  }
})

// 定义事件
const emit = defineEmits(['start', 'stop'])

// 注入ROI控制方法
const getROIActiveStatus = inject<(roiId: string) => boolean>('getROIActiveStatus')
const setROIActiveStatus = inject<(roiId: string, isActive: boolean) => void>('setROIActiveStatus')
const toggleROIActive = inject<(roiId: string) => void>('toggleROIActive')

// 卡料检测状态管理
const cardDetectionState = reactive<CardDetectionState>({
  phase: 'idle',
  remainingTime: 0,
  detectedPailiaoROIs: [],
  isActive: false
})

// 卡料检测统计
const cardDetectionStats = reactive<CardDetectionStats>({
  totalDetections: 0,
  normalCount: 0,
  cardCount: 0,
  successRate: 0,
  recentResults: []
})

// 定时器引用
const detectionTimer = ref<NodeJS.Timeout | null>(null)
const cooldownTimer = ref<NodeJS.Timeout | null>(null)

// 计算属性：排料口ROI列表
const pailiaoROIs = computed(() => {
  return props.roiList.filter((roi: any) => roi.attribute === 'pailiao')
})

// 计算属性：压铸机ROI列表
const yazhuROIs = computed(() => {
  return props.roiList.filter((roi: any) => roi.attribute === 'yazhu')
})

// 计算属性：卡料检测状态显示
const cardDetectionStatus = computed(() => {
  if (!cardDetectionState.isActive) {
    return { status: 'idle', text: '未启动' }
  }
  
  switch (cardDetectionState.phase) {
    case 'monitoring':
      return { status: 'monitoring', text: '监测中' }
    case 'detecting':
      return { status: 'detecting', text: '检测中' }
    case 'cooldown':
      return { status: 'cooldown', text: '冷却中' }
    default:
      return { status: 'idle', text: '待机' }
  }
})

// 计算ROI算法分布统计
const roiAlgorithmSummary = computed(() => {
  if (!props.roiList || props.roiList.length === 0) return null

  const summary: Record<string, number> = {}

  props.roiList.forEach((roi: any) => {
    if (roi.params && roi.params.type) {
      const algorithmType = roi.params.type
      summary[algorithmType] = (summary[algorithmType] || 0) + 1
    } else {
      // 根据ROI属性推断默认算法
      const defaultType = roi.attribute === 'yazhu' ? 'direction' : 'motion'
      summary[defaultType] = (summary[defaultType] || 0) + 1
    }
  })

  return Object.keys(summary).length > 0 ? summary : null
})

// 计算ROI详细信息统计
const roiDetailedSummary = computed(() => {
  if (!props.roiList || props.roiList.length === 0) return []

  return props.roiList.map((roi: any) => {
    // 优先使用roiDetectors中的参数（与后端一致），其次使用roi.params
    const detectorConfig = props.roiDetectors[roi.roi_id] || roi.params || {}

    // 获取算法类型
    const algorithmType = detectorConfig.type || (roi.attribute === 'yazhu' ? 'direction' : 'motion')

    // 获取算法名称和关键参数
    let algorithmName = ''
    let keyParams: Array<{key: string, value: any}> = []

    if (algorithmType === 'direction') {
      algorithmName = '方向检测'

      // 从roiDetectors或roi.params中提取参数
      const motionDetection = detectorConfig.motion_detection || {}
      const directionDetection = detectorConfig.direction_detection || {}

      keyParams = [
        { key: 'minArea', value: motionDetection.minArea || 500 },
        { key: 'motionThreshold', value: motionDetection.motionThreshold || 50 },
        { key: 'backgroundUpdateRate', value: motionDetection.backgroundUpdateRate || 0.01 },
        { key: 'minDisplacement', value: directionDetection.minDisplacement || 2 },
        { key: 'maxPatience', value: directionDetection.maxPatience || 3 },
        { key: 'consecutiveThreshold', value: directionDetection.consecutiveDetectionThreshold || 3 }
      ]
    } else if (algorithmType === 'motion') {
      const motionDetection = detectorConfig.motion_detection || {}
      const algorithm = motionDetection.algorithm || 'frame_difference'

      if (algorithm === 'background_subtraction') {
        algorithmName = '背景减除法'
        keyParams = [
          { key: 'minArea', value: motionDetection.minArea || 300 },
          { key: 'detectionThreshold', value: motionDetection.detectionThreshold || 50 },
          { key: 'learningRate', value: motionDetection.learningRate || 0.01 },
          { key: 'shadowRemoval', value: motionDetection.shadowRemoval || 0.5 }
        ]
      } else {
        algorithmName = '帧差法'
        keyParams = [
          { key: 'minArea', value: motionDetection.minArea || 300 },
          { key: 'threshold', value: motionDetection.threshold || 30 },
          { key: 'frameInterval', value: motionDetection.frameInterval || 2 }
        ]
      }
    }

    // 检查ROI是否处于活动状态（优先使用ROI检测控制器状态，其次基于检测结果）
    const isActive = props.roiDetectionStates[roi.roi_id]?.is_active || 
      (props.isDetectionActive && props.detectionResult?.roi_results?.some((result: any) => result.roi_id === roi.roi_id))

    return {
      id: roi.roi_id,
      name: roi.name,
      attribute: roi.attribute,
      algorithmType,
      algorithmName,
      keyParams: keyParams.filter(param => param.value !== undefined), // 过滤掉undefined的参数
      isActive,
      params: detectorConfig, // 使用实际的检测器配置
      lastUpdated: Date.now() // 添加更新时间戳，用于参数变化检测
    }
  })
})

// 格式化方向信息
const formatDirection = (direction: any) => {
  if (!direction) return '无方向'
  
  const dirMap: Record<string, string> = {
    'MOVING_UP': '向上',
    'MOVING_DOWN': '向下',
    'STATIONARY': '静止',
  }
  
  const dirText = dirMap[direction.direction] || direction.direction
  
  const confidence = Math.round(direction.confidence * 100)
  
  return `${dirText} (置信度: ${confidence}%)`
}

// 格式化算法类型
const formatAlgorithmType = (algorithmType: string) => {
  const algorithmMap: Record<string, string> = {
    'motion': '运动检测',
    'direction': '方向检测'
  }

  return algorithmMap[algorithmType] || algorithmType
}

// 格式化时间
const formatDuration = (seconds: number) => {
  if (!seconds) return '0秒'
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  
  if (minutes === 0) {
    return `${remainingSeconds}秒`
  }
  
  return `${minutes}分${remainingSeconds}秒`
}

// 格式化时间戳
const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString()
}

// 卡料检测核心逻辑方法

// 启动卡料检测
const startCardDetection = () => {
  if (!getROIActiveStatus || !setROIActiveStatus) {
    console.error('ROI控制方法未注入')
    return
  }
  
  cardDetectionState.isActive = true
  cardDetectionState.phase = 'monitoring'
  
  // 关闭所有排料口ROI的检测
  pailiaoROIs.value.forEach(roi => {
    if (getROIActiveStatus(roi.roi_id)) {
      setROIActiveStatus(roi.roi_id, false)
    }
  })
  
  console.log('[卡料检测] 已启动，开始监测压铸机')
}

// 停止卡料检测
const stopCardDetection = () => {
  cardDetectionState.isActive = false
  cardDetectionState.phase = 'idle'
  cardDetectionState.remainingTime = 0
  cardDetectionState.detectedPailiaoROIs = []
  
  // 清除定时器
  if (detectionTimer.value) {
    clearInterval(detectionTimer.value)
    detectionTimer.value = null
  }
  if (cooldownTimer.value) {
    clearInterval(cooldownTimer.value)
    cooldownTimer.value = null
  }
  
  console.log('[卡料检测] 已停止')
}

// 检测到压铸机MOVING_DOWN时触发
const onYazhuMovingDown = () => {
  if (!cardDetectionState.isActive || cardDetectionState.phase !== 'monitoring') {
    return
  }
  
  console.log('[卡料检测] 检测到压铸机MOVING_DOWN，开始排料口检测')
  
  // 开启所有排料口ROI的检测
  pailiaoROIs.value.forEach(roi => {
    if (setROIActiveStatus && !getROIActiveStatus!(roi.roi_id)) {
      setROIActiveStatus(roi.roi_id, true)
    }
  })
  
  // 进入检测阶段
  cardDetectionState.phase = 'detecting'
  cardDetectionState.remainingTime = props.globalSettings.delayTime || 5
  cardDetectionState.detectedPailiaoROIs = []
  
  // 启动检测倒计时
  detectionTimer.value = setInterval(() => {
    cardDetectionState.remainingTime--
    
    if (cardDetectionState.remainingTime <= 0) {
      finishDetection()
    }
  }, 1000)
}

// 完成检测阶段
const finishDetection = () => {
  if (detectionTimer.value) {
    clearInterval(detectionTimer.value)
    detectionTimer.value = null
  }
  
  // 关闭所有排料口ROI的检测
  pailiaoROIs.value.forEach(roi => {
    if (setROIActiveStatus && getROIActiveStatus!(roi.roi_id)) {
      setROIActiveStatus(roi.roi_id, false)
    }
  })
  
  // 判断检测结果
  const allPailiaoROIs = pailiaoROIs.value.map(roi => roi.roi_id)
  const undetectedROIs = allPailiaoROIs.filter(roiId => 
    !cardDetectionState.detectedPailiaoROIs.includes(roiId)
  )
  
  const isNormal = undetectedROIs.length === 0
  
  // 记录检测结果
  const result: CardDetectionResult = {
    timestamp: Date.now(),
    isNormal,
    undetectedROIs: undetectedROIs,
    detectionTime: props.globalSettings.delayTime || 5
  }
  
  // 更新统计信息
  cardDetectionStats.totalDetections++
  if (isNormal) {
    cardDetectionStats.normalCount++
  } else {
    cardDetectionStats.cardCount++
  }
  cardDetectionStats.successRate = Math.round(
    (cardDetectionStats.normalCount / cardDetectionStats.totalDetections) * 100
  )
  cardDetectionStats.recentResults.unshift(result)
  
  // 保持最近10条记录
  if (cardDetectionStats.recentResults.length > 10) {
    cardDetectionStats.recentResults = cardDetectionStats.recentResults.slice(0, 10)
  }
  
  console.log(`[卡料检测] 检测完成: ${isNormal ? '正常' : '卡料'}`, {
    undetectedROIs,
    detectedROIs: cardDetectionState.detectedPailiaoROIs
  })
  
  // 保存检测结果到数据库
  saveDetectionResultToDatabase(result)
  
  // 进入冷却阶段
  startCooldown()
}

// 开始冷却阶段
const startCooldown = () => {
  cardDetectionState.phase = 'cooldown'
  cardDetectionState.remainingTime = props.globalSettings.cooldownTime || 3
  
  // 关闭压铸机ROI的检测
  yazhuROIs.value.forEach(roi => {
    if (setROIActiveStatus && getROIActiveStatus!(roi.roi_id)) {
      setROIActiveStatus(roi.roi_id, false)
    }
  })
  
  // 启动冷却倒计时
  cooldownTimer.value = setInterval(() => {
    cardDetectionState.remainingTime--
    
    if (cardDetectionState.remainingTime <= 0) {
      finishCooldown()
    }
  }, 1000)
}

// 完成冷却阶段
const finishCooldown = () => {
  if (cooldownTimer.value) {
    clearInterval(cooldownTimer.value)
    cooldownTimer.value = null
  }
  
  // 重新开启压铸机ROI的检测
  yazhuROIs.value.forEach(roi => {
    if (setROIActiveStatus && !getROIActiveStatus!(roi.roi_id)) {
      setROIActiveStatus(roi.roi_id, true)
    }
  })
  
  // 回到监测阶段
  cardDetectionState.phase = 'monitoring'
  cardDetectionState.remainingTime = 0
  
  console.log('[卡料检测] 冷却完成，重新开始监测压铸机')
}

// 排料口检测到运动时调用
const onPailiaoDetected = (roiId: string) => {
  if (cardDetectionState.phase === 'detecting' && 
      !cardDetectionState.detectedPailiaoROIs.includes(roiId)) {
    cardDetectionState.detectedPailiaoROIs.push(roiId)
    console.log(`[卡料检测] 排料口 ${roiId} 检测到运动`)
  }
}

// 保存检测结果到数据库
const saveDetectionResultToDatabase = async (result: CardDetectionResult) => {
  try {
    // 检查是否有有效的检测组ID
    if (!props.detectionGroupId) {
      console.warn('[卡料检测] 未提供检测组ID，跳过保存检测结果到数据库')
      return
    }
    
    // 生成本地时间的ISO字符串（不带时区标识）
    const localDate = new Date(result.timestamp)
    const localISOString = new Date(localDate.getTime() - localDate.getTimezoneOffset() * 60000).toISOString().slice(0, -1)

    const detectionData = {
      detection_group_id: props.detectionGroupId,
      is_normal: result.isNormal,
      detection_time: result.detectionTime,
      timestamp: localISOString,  // 使用本地时间而不是UTC时间
      undetected_rois: result.undetectedROIs,
      detected_rois: cardDetectionState.detectedPailiaoROIs,
      trigger_roi_id: cardDetectionState.detectedPailiaoROIs.length > 0 ? cardDetectionState.detectedPailiaoROIs[0] : null,
      result_details: {
        total_pailiao_rois: pailiaoROIs.value.length,
        detected_pailiao_count: cardDetectionState.detectedPailiaoROIs.length,
        notes: result.isNormal ? '检测正常' : `卡料检测异常，未检测到的ROI: ${result.undetectedROIs.join(', ')}`
      }
    }
    
    console.log('[卡料检测] 正在保存检测结果到数据库:', detectionData)
    
    const response = await cardDetectionApi.createResult(detectionData)
    
    console.log('[卡料检测] 检测结果已成功保存到数据库:', response)
  } catch (error) {
    console.error('[卡料检测] 保存检测结果到数据库失败:', error)
    // 即使保存失败也不影响检测流程继续
  }
}

// 监听ROI参数变化，确保显示信息与后端保持同步
watch(
  () => [props.roiList, props.roiDetectors],
  ([newRoiList, newRoiDetectors], [oldRoiList, oldRoiDetectors]) => {
    // 检测参数变化
    if (newRoiDetectors && oldRoiDetectors) {
      Object.keys(newRoiDetectors).forEach(roiId => {
        const newConfig = newRoiDetectors[roiId]
        const oldConfig = oldRoiDetectors?.[roiId]

        if (JSON.stringify(newConfig) !== JSON.stringify(oldConfig)) {
          console.log(`[ROI-PARAMS-SYNC] ROI ${roiId} 参数已更新:`, {
            old: oldConfig,
            new: newConfig
          })
        }
      })
    }
  },
  { deep: true }
)

// 监听检测结果，处理压铸机和排料口的检测事件
watch(
  () => props.detectionResult,
  (newResult) => {
    if (!newResult || !cardDetectionState.isActive) return
    
    // 检查压铸机MOVING_DOWN事件
    if (newResult.roi_results) {
      newResult.roi_results.forEach((roiResult: any) => {
        const roi = yazhuROIs.value.find(r => r.roi_id === roiResult.roi_id)
        const isActive = getROIActiveStatus ? getROIActiveStatus(roiResult.roi_id) : true
        
        if (roi && isActive && roiResult.direction && roiResult.direction.direction === 'MOVING_DOWN') {
          console.log(`[卡料检测] 检测到压铸机MOVING_DOWN: ${roiResult.roi_id}`)
          onYazhuMovingDown()
        }
      })
      
      // 检查排料口检测事件
      newResult.roi_results.forEach((roiResult: any) => {
        const roi = pailiaoROIs.value.find(r => r.roi_id === roiResult.roi_id)
        const isActive = getROIActiveStatus ? getROIActiveStatus(roiResult.roi_id) : true
        
        if (roi && isActive && roiResult.motion_detected) {
          console.log(`[卡料检测] 检测到排料口运动: ${roiResult.roi_id}`)
          onPailiaoDetected(roiResult.roi_id)
        }
      })
    }
  },
  { deep: true }
)

// 监听检测状态变化
watch(
  () => props.isDetectionActive,
  (newActive, oldActive) => {
    if (newActive && !oldActive) {
      startCardDetection()
    } else if (!newActive && oldActive) {
      stopCardDetection()
    }
  }
)

// 组件挂载时的初始化
onMounted(() => {
  console.log('[卡料检测] 组件已挂载')
  
  // 如果检测已经激活，启动卡料检测
  if (props.isDetectionActive) {
    startCardDetection()
  }
})

// 组件卸载时的清理
onUnmounted(() => {
  stopCardDetection()
  console.log('[卡料检测] 组件已卸载')
})

// 暴露方法给父组件
defineExpose({
  startCardDetection,
  stopCardDetection,
  onYazhuMovingDown,
  onPailiaoDetected,
  cardDetectionState,
  cardDetectionStats
})


</script>

<style scoped>
.detection-info {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detection-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 12px;
}

.detection-left,
.detection-right {
  background: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #dee2e6;
}

.card-detection-module,
.roi-info-module {
  height: 100%;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
}

.module-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.module-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.module-status .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #6c757d;
}

.module-status.monitoring .status-dot {
  background-color: #007bff;
  animation: pulse 2s infinite;
}

.module-status.detecting .status-dot {
  background-color: #ffc107;
  animation: pulse 1s infinite;
}

.module-status.cooldown .status-dot {
  background-color: #17a2b8;
  animation: pulse 1.5s infinite;
}

.detection-phase {
  margin-bottom: 16px;
}

.phase-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #dee2e6;
  transition: all 0.3s;
}

.phase-item.active {
  background: #e3f2fd;
  border-left-color: #2196f3;
}

.phase-label {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.phase-status {
  font-size: 11px;
  color: #6c757d;
  font-weight: 500;
}

.phase-item.active .phase-status {
  color: #1976d2;
  font-weight: 600;
}

.pailiao-status {
  margin-bottom: 16px;
}

.pailiao-status h5 {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.pailiao-list {
  display: grid;
  gap: 4px;
}

.pailiao-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: #f8f9fa;
  border-radius: 3px;
  border-left: 3px solid #dee2e6;
  transition: all 0.3s;
}

.pailiao-item.detected {
  background: #d4edda;
  border-left-color: #28a745;
}

.pailiao-name {
  font-size: 11px;
  font-weight: 500;
  color: #333;
}

.pailiao-result {
  font-size: 10px;
  font-weight: 500;
  color: #6c757d;
}

.pailiao-item.detected .pailiao-result {
  color: #155724;
}

.card-detection-stats {
  margin-bottom: 16px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 12px;
}

.stat-label {
  color: #6c757d;
  font-weight: 500;
}

.stat-value {
  font-weight: 600;
  color: #333;
}

.stat-value.normal {
  color: #28a745;
}

.stat-value.error {
  color: #dc3545;
}

.recent-results {
  margin-top: 16px;
}

.recent-results h5 {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.result-list {
  display: grid;
  gap: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.result-item {
  padding: 6px 8px;
  background: #f8f9fa;
  border-radius: 3px;
  border-left: 3px solid #dee2e6;
  font-size: 11px;
}

.result-item.normal {
  background: #d4edda;
  border-left-color: #28a745;
}

.result-item.error {
  background: #f8d7da;
  border-left-color: #dc3545;
}

.result-time {
  font-weight: 500;
  color: #6c757d;
  margin-right: 8px;
}

.result-status {
  font-weight: 600;
  margin-right: 8px;
}

.result-item.normal .result-status {
  color: #155724;
}

.result-item.error .result-status {
  color: #721c24;
}

.result-details {
  font-size: 10px;
  color: #6c757d;
  font-style: italic;
}

.roi-info-content {
  height: calc(100% - 40px);
  overflow-y: auto;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .detection-content {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .detection-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .detection-header__left {
    flex-direction: column;
    gap: 6px;
    align-items: flex-start;
  }
  
  .roi-list {
    grid-template-columns: 1fr;
  }
  
  .results-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

.detection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e9ecef;
}

.detection-header__left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.detection-info__title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator__dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #dc3545;
}

.status-indicator--running .status-indicator__dot {
  background-color: #28a745;
  animation: pulse 2s infinite;
}

.status-indicator--stopped .status-indicator__dot {
  background-color: #6c757d;
}

.status-text {
  color: #495057;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.detection-controls {
  display: flex;
  gap: 6px;
}

.detection-controls__button {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.detection-controls__button--start {
  background-color: #28a745;
  color: white;
}

.detection-controls__button--start:hover:not(:disabled) {
  background-color: #218838;
}

.detection-controls__button--stop {
  background-color: #dc3545;
  color: white;
}

.detection-controls__button--stop:hover:not(:disabled) {
  background-color: #c82333;
}

.detection-controls__button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.algorithm-summary {
  margin-bottom: 12px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
}

.section-count {
  font-size: 11px;
  color: #6c757d;
}

.algorithm-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-left: auto;
}

.algorithm-stat {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  color: #495057;
}

.roi-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 6px;
}

.roi-item {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  padding: 6px;
  transition: border-color 0.2s;
}

.roi-item--yazhu {
  border-left: 3px solid #007bff;
}

.roi-item--normal {
  border-left: 3px solid #28a745;
}

.roi-item__main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.roi-name {
  font-size: 11px;
  font-weight: 600;
  color: #333;
}

.roi-algorithm {
  font-size: 10px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 1px 4px;
  border-radius: 2px;
}

.roi-status {
  font-size: 10px;
  font-weight: 500;
  padding: 1px 4px;
  border-radius: 2px;
}

.roi-status--active {
  background-color: #d4edda;
  color: #155724;
}

.roi-status--inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.roi-item__params {
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
}

.param-item {
  font-size: 9px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 1px 3px;
  border-radius: 2px;
  white-space: nowrap;
}

.param-more {
  font-size: 9px;
  color: #007bff;
  font-weight: 500;
}

.detection-results {
  margin-bottom: 12px;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 6px;
  margin-bottom: 8px;
}

.result-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 4px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  font-size: 11px;
}

.result-label {
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 2px;
}

.result-value {
  font-weight: 600;
  padding: 2px 4px;
  border-radius: 2px;
}

.result-value--clear {
  background-color: #d4edda;
  color: #155724;
}

.result-value--detected {
  background-color: #f8d7da;
  color: #721c24;
}

.roi-results {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 4px;
}

.roi-result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 6px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  font-size: 10px;
}

.roi-result-name {
  font-weight: 500;
  color: #333;
}

.roi-result-status {
  font-weight: 500;
  padding: 1px 4px;
  border-radius: 2px;
}

.roi-result-status--clear {
  background-color: #d4edda;
  color: #155724;
}

.roi-result-status--detected {
  background-color: #f8d7da;
  color: #721c24;
}

.detection-stats {
  margin-bottom: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 6px;
}

.stat-item {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  padding: 6px;
  text-align: center;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 10px;
  color: #6c757d;
}

/* 检测模式下的紧凑样式 */
.detection-info--compact {
  background: transparent;
  padding: 8px;
  margin-bottom: 8px;
  box-shadow: none;
}

.detection-status-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border-radius: 4px;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  margin-bottom: 8px;
}

.card-status-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.phase-text {
  color: #333;
  font-weight: 500;
}

.countdown {
  background: #ffc107;
  color: #212529;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 600;
  font-size: 11px;
}

.card-detection-compact {
  background: white;
  border-radius: 4px;
  padding: 12px;
  border: 1px solid #dee2e6;
  margin-bottom: 8px;
}

.compact-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 12px;
}

.compact-stats .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 8px 12px;
  min-width: 80px;
}

.compact-stats .stat-label {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 4px;
}

.compact-stats .stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.compact-stats .stat-value.error {
  color: #dc3545;
}

.recent-results-compact {
  border-top: 1px solid #dee2e6;
  padding-top: 8px;
}

.result-item-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  margin-bottom: 4px;
  border-radius: 3px;
  font-size: 11px;
}

.result-item-compact.normal {
  background: #d4edda;
  color: #155724;
}

.result-item-compact.error {
  background: #f8d7da;
  color: #721c24;
}

.result-item-compact .result-time {
  font-size: 10px;
  opacity: 0.8;
}

.result-item-compact .result-status {
  font-weight: 500;
}
</style>
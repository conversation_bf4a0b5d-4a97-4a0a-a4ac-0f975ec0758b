import axios, { type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import router from '@/router'

// 动态获取API基础URL
const getApiBaseUrl = () => {
  // 如果是通过IP访问，使用当前域名的API地址
  if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
    return `http://${window.location.hostname}:8000/api`
  }
  // 本地开发使用环境变量
  return import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'
}

// 创建axios实例
const service = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 从localStorage直接获取token，避免store缓存问题
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    console.error('响应错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          const userStore = useUserStore()
          userStore.logout()
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.detail || data?.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络错误，请检查网络连接')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// 通用请求方法
export const request = <T = any>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
  return service(config)
}

// GET请求
export const get = <T = any>(url: string, params?: any): Promise<AxiosResponse<T>> => {
  return request<T>({
    method: 'GET',
    url,
    params
  })
}

// POST请求
export const post = <T = any>(url: string, data?: any): Promise<AxiosResponse<T>> => {
  return request<T>({
    method: 'POST',
    url,
    data
  })
}

// PUT请求
export const put = <T = any>(url: string, data?: any): Promise<AxiosResponse<T>> => {
  return request<T>({
    method: 'PUT',
    url,
    data
  })
}

// DELETE请求
export const del = <T = any>(url: string): Promise<AxiosResponse<T>> => {
  return request<T>({
    method: 'DELETE',
    url
  })
}

export default service
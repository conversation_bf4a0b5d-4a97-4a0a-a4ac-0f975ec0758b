import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import $ from 'jquery'

// 声明全局类型
declare global {
  interface Window {
    WebVideoCtrl: any
    $: any
  }
}

// 设备配置类型
interface DeviceConfig {
  ip: string
  port: number
  username: string
  password: string
  channel: number
  streamType: string
  protocol: number
}

// 设备信息类型
interface DeviceInfo {
  deviceName: string
  deviceID: string
  deviceType: string
  channelNum: number
  firmwareVersion: string
  serialNumber: string
}

// 操作日志类型
interface OperationLog {
  time: string
  message: string
  type: string
}

export function useWebSDK() {
  // 状态管理
  const isConnecting = ref(false)
  const isLoggedIn = ref(false)
  const isPreviewActive = ref(false)
  const soundEnabled = ref(false)
  const deviceInfo = ref<DeviceInfo | null>(null)
  const operationLogs = ref<OperationLog[]>([])

  // WebSDK相关
  let g_iWndIndex = 0
  let currentDevice: any = null
  let isWebSDKInitialized = false
  let statusCheckTimer: number | null = null

  // 计算属性
  const canLogin = computed(() => {
    return !isConnecting.value
  })

  // 添加操作日志
  const addLog = (message: string, type: string = 'info') => {
    const now = new Date()
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
    
    operationLogs.value.unshift({
      time,
      message,
      type
    })
    
    // 限制日志数量
    if (operationLogs.value.length > 100) {
      operationLogs.value = operationLogs.value.slice(0, 100)
    }
  }

  // 清空日志
  const clearLogs = () => {
    operationLogs.value = []
    addLog('日志已清空', 'info')
  }

  // 错误码映射
  const getErrorMessage = (errorCode: number): string => {
    const ErrorCodes: Record<number, string> = {
      1001: "码流传输过程异常",
      1002: "回放结束",
      1003: "取流失败，连接被动断开",
      1004: "对讲连接被动断开",
      1005: "广播连接被动断开",
      1006: "视频编码格式不支持 目前只支持h264 和 h265",
      1007: "网络异常导致websocket断开",
      1008: "首帧回调超时",
      1009: "对讲码流传输过程异常",
      1010: "广播码流传输过程异常",
      1011: "数据接收异常，请检查是否修改了视频格式",
      1012: "播放资源不足",
      1013: "当前环境不支持该鱼眼展开模式",
      1014: "外部强制关闭了",
      1015: "获取播放url失败",
      1016: "文件下载完成",
      1017: "密码错误",
      1018: "链接到萤石平台失败",
      1019: "未找到录像片段",
      1020: "水印模式等场景，当前通道需要重新播放",
      1021: "缓存溢出",
      1022: "采集音频失败，可能是在非https/localhost域下使用对讲导致,或者没有插耳机等"
    }
    return ErrorCodes[errorCode] || `未知错误码: ${errorCode}`
  }

  // 获取WebSDK实例
  const getWebSDK = () => {
    if (window.WebVideoCtrl) {
      return window.WebVideoCtrl
    }
    addLog('WebSDK未加载', 'error')
    return null
  }

  // 检查窗口状态
  const checkWindowStatus = () => {
    const webVideoCtrl = getWebSDK()
    if (!webVideoCtrl || !isWebSDKInitialized) {
      return
    }

    try {
      console.log(`检查窗口状态，窗口索引: ${g_iWndIndex}`) // 调试信息
      const oWndInfo = webVideoCtrl.I_GetWindowStatus(g_iWndIndex)
      console.log('窗口状态检查结果:', oWndInfo) // 调试信息

      if (oWndInfo != null) {
        console.log(`窗口信息: IP=${oWndInfo.szIP}, 通道=${oWndInfo.iChannelID}, 播放状态=${oWndInfo.iPlayStatus}`) // 调试信息

        // iPlayStatus: 0-没有播放，1-预览，2-回放，3-暂停，4-单帧，5-倒放，6-倒放暂停
        const newPreviewActive = oWndInfo.iPlayStatus === 1 || oWndInfo.iPlayStatus === 2

        // 只有状态真正改变时才更新和记录日志
        if (newPreviewActive !== isPreviewActive.value) {
          isPreviewActive.value = newPreviewActive
          const statusText = newPreviewActive ? '预览中' : '未预览'
          addLog(`窗口状态更新: ${statusText} (状态码: ${oWndInfo.iPlayStatus})`, 'info')
          console.log('预览状态已更新:', statusText, oWndInfo.iPlayStatus) // 调试信息
        }
      } else {
        console.log(`窗口${g_iWndIndex}信息为空`) // 调试信息
        // 窗口信息为空，说明没有播放
        if (isPreviewActive.value) {
          isPreviewActive.value = false
          addLog('窗口状态更新: 未预览 (窗口信息为空)', 'info')
        }
      }
    } catch (error) {
      console.error('状态检查错误:', error) // 调试信息
    }
  }

  // 开始状态检查定时器
  const startStatusCheck = () => {
    if (statusCheckTimer) {
      clearInterval(statusCheckTimer)
    }
    // 每5秒检查一次窗口状态（用于异常检测）
    statusCheckTimer = window.setInterval(checkWindowStatus, 5000)
    console.log('状态异常检测定时器已启动')
    addLog('状态异常检测定时器已启动', 'info')
  }

  // 停止状态检查定时器
  const stopStatusCheck = () => {
    if (statusCheckTimer) {
      clearInterval(statusCheckTimer)
      statusCheckTimer = null
    }
  }

  // 初始化WebSDK
  const initWebSDK = async (): Promise<boolean> => {
    try {
      addLog('开始初始化WebSDK...', 'info')
      
      // 检查WebSDK是否已加载
      if (!window.WebVideoCtrl) {
        addLog('WebSDK未加载，请检查webVideoCtrl.js是否正确引入', 'error')
        return false
      }
      
      // 检查浏览器兼容性
      const iRet = window.WebVideoCtrl.I_SupportNoPlugin()
      if (!iRet) {
        addLog('当前浏览器版本过低，不支持无插件模式', 'error')
        return false
      }
      
      addLog('浏览器兼容性检查通过', 'success')
      
      // 初始化插件参数及插入无插件
      window.WebVideoCtrl.I_InitPlugin("100%", "100%", {
        bWndFull: true,     // 是否支持单窗口双击全屏
        iPackageType: 2,    // 2:PS 11:MP4
        iWndowType: 1,
        bNoPlugin: true,
        cbSelWnd: function (xmlDoc: any) {
          g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10)
          addLog(`当前选择的窗口编号：${g_iWndIndex}`, 'info')
        },
        cbDoubleClickWnd: function (iWndIndex: number, bFullScreen: boolean) {
          const szInfo = bFullScreen ? `当前放大的窗口编号：${iWndIndex}` : `当前还原的窗口编号：${iWndIndex}`
          addLog(szInfo, 'info')
        },
        cbEvent: function (iEventType: number, iParam1: number, iParam2: number) {
          if (2 == iEventType) {// 回放正常结束
            addLog(`窗口${iParam1}回放结束！`, 'info')
          } else if (-1 == iEventType) {
            addLog(`设备${iParam1}网络错误！`, 'error')
          }
        },
        cbInitPluginComplete: function () {
          addLog('插件初始化完成，正在嵌入容器...', 'success')
          window.WebVideoCtrl.I_InsertOBJECTPlugin("divPlugin")
          isWebSDKInitialized = true
          addLog('WebSDK初始化完成', 'success')

          // 延迟启动状态检查定时器，确保WebSDK完全初始化
          setTimeout(() => {
            startStatusCheck()
          }, 1000)
        },
        cbPluginErrorHandler: function (iWndIndex: number, iErrorCode: number, oError: any) {
          addLog(`窗口${iWndIndex}发生错误：${iErrorCode}`, 'error')
          
          // 发生错误时停止当前窗口的播放
          const oWndInfo = window.WebVideoCtrl.I_GetWindowStatus(iWndIndex)
          if (oWndInfo != null) {
            window.WebVideoCtrl.I_Stop({
              success: function () {
                addLog(`窗口${iWndIndex}停止预览成功`, 'info')
              },
              error: function () {
                addLog(`窗口${iWndIndex}停止预览失败`, 'error')
              }
            })
          }
        },
        cbPerformanceLack: function () {
          addLog('性能不足！', 'error')
        }
      })
      
      return true
      
    } catch (error: any) {
      addLog(`WebSDK初始化异常：${error.message}`, 'error')
      return false
    }
  }

  // 登录设备
  const loginDevice = async (deviceConfig: DeviceConfig) => {
    if (!isWebSDKInitialized) {
      addLog('WebSDK未初始化，请先初始化', 'error')
      return
    }
    
    const webVideoCtrl = getWebSDK()
    if (!webVideoCtrl) {
      return
    }
    
    const szIP = deviceConfig.ip
    const szPort = deviceConfig.port
    const szUsername = deviceConfig.username
    const szPassword = deviceConfig.password
    
    if (!szIP || !szPort) {
      addLog('请输入正确的IP地址和端口', 'error')
      return
    }
    
    isConnecting.value = true
    addLog(`开始登录设备: ${szIP}:${szPort}`, 'info')
    
    const szDeviceIdentify = szIP + "_" + szPort
    
    try {
      // 设置Cookie以供Vite代理使用（在登录前设置）
      document.cookie = `websdk_ip=${szIP}; path=/; SameSite=Lax`
      document.cookie = `websdk_port=${szPort}; path=/; SameSite=Lax`
      console.log(`[COOKIE] 设置设备信息Cookie: IP=${szIP}, Port=${szPort}`)
      addLog(`设置设备信息Cookie: IP=${szIP}, Port=${szPort}`, 'info')
      
      const iRet = webVideoCtrl.I_Login(szIP, deviceConfig.protocol, szPort, szUsername, szPassword, {
        success: function (xmlDoc: any) {
          addLog(`${szDeviceIdentify} 登录成功！`, 'success')
          isLoggedIn.value = true
          currentDevice = {
            szIP: szIP,
            iPort: szPort,
            szUserName: szUsername,
            szPassword: szPassword
          }
          
          ElMessage.success('设备登录成功')
          
          // 获取设备信息
          setTimeout(() => {
            getDeviceInfo()
          }, 100)
        },
        error: function (status: number, xmlDoc: any) {
          let errorMsg = '登录失败'
          if (xmlDoc) {
            const statusString = $(xmlDoc).find("statusString").eq(0).text()
            const subStatusCode = $(xmlDoc).find("subStatusCode").eq(0).text()
            if (statusString) {
              errorMsg = statusString
            } else if (subStatusCode) {
              errorMsg = `错误码: ${subStatusCode}`
            }
          }
          addLog(`${szDeviceIdentify} 登录失败！${errorMsg} (${status})`, 'error')
          isLoggedIn.value = false
          currentDevice = null
          ElMessage.error(`设备登录失败: ${errorMsg}`)
        }
      })
      
      if (iRet === -1) {
        addLog(`${szDeviceIdentify} 已登录过！`, 'warning')
        isLoggedIn.value = true
        currentDevice = {
          szIP: szIP,
          iPort: szPort,
          szUserName: szUsername,
          szPassword: szPassword
        }
        ElMessage.success('设备已登录')
      }
      
    } catch (error: any) {
      addLog(`登录异常: ${error.message}`, 'error')
      isLoggedIn.value = false
      currentDevice = null
      ElMessage.error(`登录异常: ${error.message}`)
    } finally {
      isConnecting.value = false
    }
  }

  // 获取设备信息
  const getDeviceInfo = () => {
    const webVideoCtrl = getWebSDK()
    if (!webVideoCtrl || !currentDevice) {
      addLog('设备未登录', 'error')
      return
    }
    
    const szDeviceIdentify = currentDevice.szIP + "_" + currentDevice.iPort
    
    webVideoCtrl.I_GetDeviceInfo(szDeviceIdentify, {
      success: function (xmlDoc: any) {
        if (xmlDoc != null) {
          const deviceName = $(xmlDoc).find("deviceName").eq(0).text()
          const deviceID = $(xmlDoc).find("deviceID").eq(0).text()
          const deviceType = $(xmlDoc).find("deviceType").eq(0).text()
          const channelNum = $(xmlDoc).find("channelNum").eq(0).text()
          const firmwareVersion = $(xmlDoc).find("firmwareVersion").eq(0).text()
          const serialNumber = $(xmlDoc).find("serialNumber").eq(0).text()
          
          deviceInfo.value = {
            deviceName: deviceName || '未知设备',
            deviceID: deviceID || '',
            deviceType: deviceType || 'IP Camera',
            channelNum: parseInt(channelNum) || 1,
            firmwareVersion: firmwareVersion || '未知版本',
            serialNumber: serialNumber || '未知序列号'
          }
          
          addLog(`获取设备信息成功: ${deviceName}`, 'success')
        }
      },
      error: function (status: number, xmlDoc: any) {
        addLog(`获取设备信息失败 (${status})`, 'error')
      }
    })
  }

  // 开始预览
  const startPreview = (deviceConfig: DeviceConfig) => {
    const webVideoCtrl = getWebSDK()
    if (!webVideoCtrl || !currentDevice) {
      addLog('设备未登录', 'error')
      return
    }
    
    const oWndInfo = webVideoCtrl.I_GetWindowStatus(g_iWndIndex)
    const szDeviceIdentify = currentDevice.szIP + "_" + currentDevice.iPort
    const iChannelID = parseInt(deviceConfig.channel.toString())
    const iStreamType = parseInt(deviceConfig.streamType)
    let iRtspPort = 554 // 默认RTSP端口
    const bZeroChannel = false // 是否为零通道
    
    // 获取设备端口信息，与参考文件保持一致
    addLog('正在获取设备端口信息...', 'info')
    const oPort = webVideoCtrl.I_GetDevicePort(szDeviceIdentify)
    if (oPort != null) {
      iRtspPort = oPort.iRtspPort || iRtspPort
      addLog(`获取设备端口成功: RTSP端口=${iRtspPort}, 设备端口=${oPort.iDevicePort}`, 'success')
    } else {
      addLog(`获取设备端口失败，使用默认RTSP端口: ${iRtspPort}`, 'warning')
    }
    
    // 先尝试直连模式
    addLog('尝试直连模式 (bProxy: false)...', 'info')
    webVideoCtrl.I_StartRealPlay(szDeviceIdentify, {
      iWndIndex: g_iWndIndex,    // 明确指定播放窗口
      iRtspPort: iRtspPort,
      iStreamType: iStreamType,
      iChannelID: iChannelID,
      bZeroChannel: bZeroChannel,
      bProxy: false,             // 先尝试直连模式
      success: function () {
        isPreviewActive.value = true
        addLog(`通道 ${iChannelID} 预览开始成功 (直连模式)`, 'success')
        ElMessage.success('预览开始成功（直连模式）')
        console.log('直连模式预览成功，状态已更新为:', isPreviewActive.value)
      },
      error: function (status: number, xmlDoc: any) {
        let errorMsg = '开始预览失败'
        if (xmlDoc) {
          const statusString = $(xmlDoc).find("statusString").eq(0).text()
          if (statusString) {
            errorMsg = statusString
          }
        }
        addLog(`直连模式失败: ${errorMsg} (${status})`, 'error')
        
        // 如果直连失败，尝试代理模式
        if (status === 403 || status === undefined) {
          addLog('尝试代理模式 (bProxy: true)...', 'info')
          webVideoCtrl.I_StartRealPlay(szDeviceIdentify, {
            iWndIndex: g_iWndIndex,
            iRtspPort: iRtspPort,
            iStreamType: iStreamType,
            iChannelID: iChannelID,
            bZeroChannel: bZeroChannel,
            bProxy: true,              // 代理模式
            success: function () {
              isPreviewActive.value = true
              addLog(`通道 ${iChannelID} 预览开始成功 (代理模式)`, 'success')
              ElMessage.success('预览开始成功（代理模式）')
              console.log('代理模式预览成功，状态已更新为:', isPreviewActive.value)
            },
            error: function (status: number, xmlDoc: any) {
              let errorMsg = '开始预览失败'
              if (xmlDoc) {
                const statusString = $(xmlDoc).find("statusString").eq(0).text()
                if (statusString) {
                  errorMsg = statusString
                }
              }
              addLog(`代理模式也失败: ${errorMsg} (${status})`, 'error')
              ElMessage.error(`预览失败: ${errorMsg}`)
            }
          })
        } else {
          ElMessage.error(`预览失败: ${errorMsg}`)
        }
      }
    })
  }

  // 停止预览
  const stopPreview = () => {
    const webVideoCtrl = getWebSDK()
    if (!webVideoCtrl) {
      return
    }
    
    const oWndInfo = webVideoCtrl.I_GetWindowStatus(g_iWndIndex)
    if (oWndInfo != null) {
      webVideoCtrl.I_Stop({
        success: function () {
          isPreviewActive.value = false
          soundEnabled.value = false
          addLog('停止预览成功', 'success')
          ElMessage.success('停止预览成功')
          console.log('停止预览成功，状态已更新为:', isPreviewActive.value)
        },
        error: function () {
          addLog('停止预览失败', 'error')
          ElMessage.error('停止预览失败')
        }
      })
    }
  }

  // 开启声音
  const openSound = () => {
    const webVideoCtrl = getWebSDK()
    if (!webVideoCtrl) {
      return
    }
    
    const oWndInfo = webVideoCtrl.I_GetWindowStatus(g_iWndIndex)
    if (oWndInfo != null) {
      webVideoCtrl.I_OpenSound({
        success: function () {
          soundEnabled.value = true
          addLog('开启声音成功', 'success')
          ElMessage.success('开启声音成功')
        },
        error: function () {
          addLog('开启声音失败', 'error')
          ElMessage.error('开启声音失败')
        }
      })
    }
  }

  // 关闭声音
  const closeSound = () => {
    const webVideoCtrl = getWebSDK()
    if (!webVideoCtrl) {
      return
    }
    
    const oWndInfo = webVideoCtrl.I_GetWindowStatus(g_iWndIndex)
    if (oWndInfo != null) {
      webVideoCtrl.I_CloseSound({
        success: function () {
          soundEnabled.value = false
          addLog('关闭声音成功', 'success')
          ElMessage.success('关闭声音成功')
        },
        error: function () {
          addLog('关闭声音失败', 'error')
          ElMessage.error('关闭声音失败')
        }
      })
    }
  }

  // 清理资源
  const cleanup = () => {
    stopStatusCheck()
    if (window.WebVideoCtrl) {
      try {
        window.WebVideoCtrl.I_Uninit()
      } catch (error) {
        console.warn('WebSDK清理时出现错误:', error)
      }
    }
  }

  return {
    // 状态
    isConnecting,
    isLoggedIn,
    isPreviewActive,
    soundEnabled,
    deviceInfo,
    operationLogs,
    canLogin,

    // 方法
    addLog,
    clearLogs,
    getErrorMessage,
    initWebSDK,
    loginDevice,
    getDeviceInfo,
    startPreview,
    stopPreview,
    openSound,
    closeSound,
    cleanup
  }
}
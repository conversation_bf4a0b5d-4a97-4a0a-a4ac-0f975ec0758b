import os
import logging
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pathlib import Path

# 禁用 watchfiles 日志
logging.getLogger('watchfiles').setLevel(logging.ERROR)
logging.getLogger('watchfiles.main').setLevel(logging.ERROR)

# 创建日志目录
LOG_DIR = Path("logs")
if not LOG_DIR.exists():
    LOG_DIR.mkdir(parents=True, exist_ok=True)

LOG_FILE = LOG_DIR / "system.log"

# 配置日志级别，确保能看到INFO级别的日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOG_FILE, encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.responses import HTMLResponse

from app.core.config import settings
from app.api.api import api_router
from app.api.websocket_router import router as websocket_router
from app.api import rtsp
from app.db.session import engine
from app.models import models

# 创建数据库表
models.Base.metadata.create_all(bind=engine)

# 创建FastAPI应用
app = FastAPI(
    title="智能监控系统 API",
    description="基于FastAPI的智能监控系统后端接口",
    version="1.0.0",
    # 禁用尾部斜杠自动重定向
    redirect_slashes=False,
    # 禁用默认的docs，我们将创建自定义的
    docs_url=None,
    redoc_url=None
)

# 获取本机IP地址
import socket

def get_local_ip():
    try:
        # 连接到一个远程地址来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "localhost"

local_ip = get_local_ip()
logger.info(f"本机IP地址: {local_ip}")

# 配置CORS - 支持本地和网络访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173", 
        "http://localhost:4173", 
        f"http://{local_ip}:5173",  # 添加本机IP
        f"http://{local_ip}:4173",  # 添加本机IP
        "*"  # 开发环境允许所有来源
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件目录
static_dir = Path(__file__).parent / "static"
if not static_dir.exists():
    os.makedirs(static_dir)

app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# 挂载上传文件目录
uploads_dir = Path(__file__).parent / "uploads"
if not uploads_dir.exists():
    os.makedirs(uploads_dir)

app.mount("/uploads", StaticFiles(directory=str(uploads_dir)), name="uploads")

# 创建检测图片目录
detection_images_dir = Path(__file__).parent.parent / "detection_images"
if not detection_images_dir.exists():
    os.makedirs(detection_images_dir)

app.mount("/detection_images", StaticFiles(directory=str(detection_images_dir)), name="detection_images")

# 自定义文档路由
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        swagger_js_url="/static/swagger-ui/swagger-ui-bundle.min.js",
        swagger_css_url="/static/swagger-ui/swagger-ui.min.css",
    )

@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    return get_redoc_html(
        openapi_url=app.openapi_url,
        title=app.title + " - ReDoc",
        redoc_js_url="/static/swagger-ui/redoc.standalone.js",
    )

# 包含API路由
app.include_router(api_router, prefix="/api")

# 包含WebSocket路由
app.include_router(websocket_router)

# 包含RTSP路由
app.include_router(rtsp.router)

@app.get("/")
def read_root():
    return {"message": "欢迎使用压铸件智能检测系统API"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
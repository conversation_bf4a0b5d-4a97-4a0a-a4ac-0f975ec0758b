"""extend_video_sources_for_websdk_devices

Revision ID: 95da47b06815
Revises: 2edca0f184a0
Create Date: 2025-07-06 06:43:47.055490

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '95da47b06815'
down_revision = '2edca0f184a0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # 只添加新的列，跳过SQLite不支持的ALTER COLUMN操作
    op.add_column('video_sources', sa.Column('device_ip', sa.String(), nullable=True))
    op.add_column('video_sources', sa.Column('device_port', sa.Integer(), nullable=True))
    op.add_column('video_sources', sa.Column('device_username', sa.String(), nullable=True))
    op.add_column('video_sources', sa.Column('device_password', sa.String(), nullable=True))
    op.add_column('video_sources', sa.Column('device_protocol', sa.Integer(), nullable=True))
    op.add_column('video_sources', sa.Column('channel_id', sa.Integer(), nullable=True))
    op.add_column('video_sources', sa.Column('stream_type', sa.Integer(), nullable=True))
    op.add_column('video_sources', sa.Column('device_status', sa.String(), nullable=True))
    op.add_column('video_sources', sa.Column('last_connected_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # 只删除新添加的列
    op.drop_column('video_sources', 'last_connected_at')
    op.drop_column('video_sources', 'device_status')
    op.drop_column('video_sources', 'stream_type')
    op.drop_column('video_sources', 'channel_id')
    op.drop_column('video_sources', 'device_protocol')
    op.drop_column('video_sources', 'device_password')
    op.drop_column('video_sources', 'device_username')
    op.drop_column('video_sources', 'device_port')
    op.drop_column('video_sources', 'device_ip')
    # ### end Alembic commands ###
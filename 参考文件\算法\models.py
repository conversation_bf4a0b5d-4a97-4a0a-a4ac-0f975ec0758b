from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Table, Boolean, Float, UniqueConstraint
from sqlalchemy.orm import relationship, declarative_base
import json

# SQLAlchemy声明式基类
Base = declarative_base()

# 关联表：用于检测模板和压铸机之间的多对多关系
# 一个模板可以应用于多台压铸机，一台压铸机可以使用多个模板
template_die_caster_association = Table(
    'template_die_caster_association',
    Base.metadata,
    Column('template_id', Integer, ForeignKey('detection_templates.id', ondelete='CASCADE')),
    Column('die_caster_id', Integer, ForeignKey('die_casters.id', ondelete='CASCADE'))
)

class VideoSource(Base):
    """
    视频源数据模型

    存储系统中所有可用的视频源信息，包括本地文件和网络流。
    每个视频源可以被多个检测组使用，但每个检测组只能关联一个视频源。

    字段说明：
    - name: 视频源名称，用于用户识别
    - description: 详细描述信息
    - source_type: 视频源类型（"本地文件"、"RTSP流"、"websdk_device"等）
    - path: 视频源路径或URL
    """
    __tablename__ = 'video_sources'

    id = Column(Integer, primary_key=True, comment='视频源唯一标识')
    name = Column(String, nullable=False, unique=True, comment='视频源名称')
    description = Column(String, comment='视频源描述')
    source_type = Column(String, nullable=False, comment='视频源类型：本地文件/RTSP流/websdk_device')
    path = Column(String, nullable=False, comment='视频源路径或URL')

    # 关联关系：一个视频源对应一个检测组（一对一关系）
    detection_group = relationship("DetectionGroup", back_populates="video_source", uselist=False)

class DieCaster(Base):
    """
    压铸机数据模型

    存储压铸机的基本信息和网络配置。
    压铸机是检测系统的核心设备，每台压铸机可以关联多个检测模板，
    同时可以有多个检测组对其进行监控。

    字段说明：
    - name: 压铸机名称，用于识别不同的设备
    - description: 设备详细描述
    - ip_address: 压铸机的IP地址（用于网络通信）
    - port: 通信端口号
    """
    __tablename__ = 'die_casters'

    id = Column(Integer, primary_key=True, comment='压铸机唯一标识')
    name = Column(String, nullable=False, unique=True, comment='压铸机名称')
    description = Column(String, comment='压铸机描述信息')
    ip_address = Column(String, comment='压铸机IP地址')
    port = Column(Integer, comment='通信端口号')

    # 关联关系：一台压铸机可以有多个检测组（一对多关系）
    detection_groups = relationship("DetectionGroup", back_populates="die_caster")

    # 关联关系：压铸机与检测模板的多对多关系
    templates = relationship(
        "DetectionTemplate",
        secondary=template_die_caster_association,
        back_populates="die_casters"
    )

class DetectionTemplate(Base):
    """
    检测模板数据模型

    检测模板定义了一套完整的检测配置，包括ROI设置、算法参数等。
    模板可以被多个检测组复用，实现配置的标准化和复用。

    字段说明：
    - name: 模板名称，用于识别不同的检测配置
    - description: 模板的详细描述
    """
    __tablename__ = 'detection_templates'

    id = Column(Integer, primary_key=True, comment='检测模板唯一标识')
    name = Column(String, nullable=False, unique=True, comment='模板名称')
    description = Column(String, comment='模板描述信息')

    # 关联关系：模板与压铸机的多对多关系
    die_casters = relationship(
        "DieCaster",
        secondary=template_die_caster_association,
        back_populates="templates"
    )

    # 关联关系：一个模板可以有多个检测组（一对多关系）
    detection_groups = relationship("DetectionGroup", back_populates="template", cascade="all, delete-orphan")

class DetectionGroup(Base):
    """
    检测组数据模型

    检测组是系统的核心实体，将检测模板、压铸机和视频源关联起来，
    形成一个完整的检测任务。每个检测组包含具体的ROI配置和检测参数。

    字段说明：
    - name: 检测组名称
    - template_id: 关联的检测模板ID
    - die_caster_id: 关联的压铸机ID
    - video_source_id: 关联的视频源ID
    - config_json: ROI配置和其他参数的JSON存储

    约束说明：
    - 同一个模板和视频源的组合必须唯一，避免重复配置
    """
    __tablename__ = 'detection_groups'

    id = Column(Integer, primary_key=True, comment='检测组唯一标识')
    name = Column(String, nullable=False, comment='检测组名称')

    # 外键关联
    template_id = Column(Integer, ForeignKey('detection_templates.id'), nullable=False, comment='检测模板ID')
    die_caster_id = Column(Integer, ForeignKey('die_casters.id'), nullable=False, comment='压铸机ID')
    video_source_id = Column(Integer, ForeignKey('video_sources.id'), comment='视频源ID')

    # ROI配置和其他参数以JSON格式存储，提供灵活的配置能力
    config_json = Column(Text, default='{}', comment='ROI和参数配置的JSON数据')

    # 关联关系定义
    template = relationship("DetectionTemplate", back_populates="detection_groups")
    die_caster = relationship("DieCaster", back_populates="detection_groups")
    video_source = relationship("VideoSource", back_populates="detection_group")
    alarms = relationship("Alarm", back_populates="detection_group", cascade="all, delete-orphan")

    # 表级约束：确保模板和视频源的组合唯一性
    __table_args__ = (
        UniqueConstraint('template_id', 'video_source_id', name='_template_video_uc'),
    )

    @property
    def config(self):
        """
        获取解析后的配置数据

        将存储的JSON字符串解析为Python字典对象，
        便于程序中直接使用配置数据。

        Returns:
            dict: 解析后的配置字典
        """
        return json.loads(self.config_json or '{}')

    @config.setter
    def config(self, value):
        """
        设置配置数据

        将Python字典对象序列化为JSON字符串存储，
        确保中文字符正确显示。

        Args:
            value (dict): 要存储的配置字典
        """
        self.config_json = json.dumps(value, ensure_ascii=False)

class Alarm(Base):
    """
    报警记录数据模型

    存储系统产生的所有报警信息，包括卡料、设备异常等。
    每条报警记录关联到具体的检测组，便于追溯和管理。

    字段说明：
    - timestamp: 报警发生的时间戳
    - detection_group_id: 产生报警的检测组ID
    - status: 报警处理状态
    - image_path: 报警时刻的图片保存路径
    - notes: 处理备注信息
    - operator: 处理操作员

    状态流转：
    待确认 -> 已确认 -> 已处理
           -> 误报
    """
    __tablename__ = 'alarms'

    id = Column(Integer, primary_key=True, comment='报警记录唯一标识')
    timestamp = Column(String, nullable=False, comment='报警发生时间')
    detection_group_id = Column(Integer, ForeignKey('detection_groups.id'), nullable=False, comment='检测组ID')

    # 报警状态：'待确认', '已确认', '误报', '已处理'
    status = Column(String, default='待确认', comment='报警处理状态')

    image_path = Column(String, comment='报警图片存储路径')
    notes = Column(String, comment='处理备注信息')
    operator = Column(String, comment='处理操作员')

    # 关联关系：报警记录属于某个检测组
    detection_group = relationship("DetectionGroup", back_populates="alarms")

class PLCStation(Base):
    """
    PLC站点数据模型

    存储PLC（可编程逻辑控制器）的连接配置和基本信息。
    PLC用于与工业设备进行通信，实现自动化控制和数据采集。

    字段说明：
    - name: PLC站点名称，用于识别不同的PLC设备
    - description: PLC站点描述信息
    - ip_address: PLC的IP地址
    - port: 通信端口（西门子S7默认102）
    - rack: 机架号（西门子S7参数）
    - slot: 插槽号（西门子S7参数）
    - is_enabled: 是否启用该PLC站点
    - connection_timeout: 连接超时时间（毫秒）
    """
    __tablename__ = 'plc_stations'

    id = Column(Integer, primary_key=True, comment='PLC站点唯一标识')
    name = Column(String, nullable=False, unique=True, comment='PLC站点名称')
    description = Column(String, comment='PLC站点描述')
    ip_address = Column(String, nullable=False, comment='PLC IP地址')
    port = Column(Integer, default=102, comment='通信端口，西门子S7默认102')
    rack = Column(Integer, default=0, comment='机架号')
    slot = Column(Integer, default=1, comment='插槽号')
    is_enabled = Column(Boolean, default=True, comment='是否启用')
    connection_timeout = Column(Integer, default=5000, comment='连接超时时间（毫秒）')

    # 关联关系：PLC站点包含多个输入输出信号和规则
    input_signals = relationship("PLCInputSignal", back_populates="station", cascade="all, delete-orphan")
    output_signals = relationship("PLCOutputSignal", back_populates="station", cascade="all, delete-orphan")
    template_switch_rules = relationship("TemplateSwitchRule", back_populates="station", cascade="all, delete-orphan")
    alarm_output_rules = relationship("AlarmOutputRule", back_populates="station", cascade="all, delete-orphan")

class PLCInputSignal(Base):
    """
    PLC输入信号配置模型

    定义从PLC读取的输入信号配置，用于监控设备状态和触发条件。
    输入信号可以用于模板切换、状态监控等功能。

    字段说明：
    - station_id: 所属PLC站点ID
    - name: 信号名称，便于识别
    - description: 信号描述信息
    - address: PLC地址（如DB1.DBX0.0, M0.0, I0.0等）
    - data_type: 数据类型（BOOL, INT, REAL等）
    - is_enabled: 是否启用该信号
    """
    __tablename__ = 'plc_input_signals'

    id = Column(Integer, primary_key=True, comment='输入信号唯一标识')
    station_id = Column(Integer, ForeignKey('plc_stations.id'), nullable=False, comment='PLC站点ID')
    name = Column(String, nullable=False, comment='信号名称')
    description = Column(String, comment='信号描述')
    address = Column(String, nullable=False, comment='PLC地址，如DB1.DBX0.0, M0.0, I0.0等')
    data_type = Column(String, default='BOOL', comment='数据类型：BOOL, INT, REAL等')
    is_enabled = Column(Boolean, default=True, comment='是否启用')

    # 关联关系
    station = relationship("PLCStation", back_populates="input_signals")
    template_switch_rules = relationship("TemplateSwitchRule", back_populates="trigger_signal")

class PLCOutputSignal(Base):
    """
    PLC输出信号配置模型

    定义向PLC写入的输出信号配置，用于控制设备动作和状态指示。
    输出信号可以用于报警输出、设备控制等功能。

    字段说明：
    - station_id: 所属PLC站点ID
    - name: 信号名称，便于识别
    - description: 信号描述信息
    - address: PLC地址（如DB1.DBX0.1, Q0.0等）
    - data_type: 数据类型（BOOL, INT, REAL等）
    - is_enabled: 是否启用该信号
    """
    __tablename__ = 'plc_output_signals'

    id = Column(Integer, primary_key=True, comment='输出信号唯一标识')
    station_id = Column(Integer, ForeignKey('plc_stations.id'), nullable=False, comment='PLC站点ID')
    name = Column(String, nullable=False, comment='信号名称')
    description = Column(String, comment='信号描述')
    address = Column(String, nullable=False, comment='PLC地址，如DB1.DBX0.1, Q0.0等')
    data_type = Column(String, default='BOOL', comment='数据类型：BOOL, INT, REAL等')
    is_enabled = Column(Boolean, default=True, comment='是否启用')

    # 关联关系
    station = relationship("PLCStation", back_populates="output_signals")
    alarm_output_rules = relationship("AlarmOutputRule", back_populates="output_signal")

class TemplateSwitchRule(Base):
    """
    模板切换规则模型

    定义基于PLC输入信号自动切换检测模板的规则。
    当指定的PLC输入信号达到触发值时，系统自动切换到目标检测模板。

    应用场景：
    - 根据生产线状态自动切换检测配置
    - 不同产品使用不同的检测模板
    - 基于设备状态的智能切换

    字段说明：
    - name: 规则名称
    - description: 规则描述
    - station_id: PLC站点ID
    - trigger_signal_id: 触发信号ID
    - target_template_id: 目标模板ID
    - trigger_value: 触发值（BOOL类型通常是'1'或'0'）
    - is_enabled: 是否启用该规则
    - priority: 优先级，数值越大优先级越高
    """
    __tablename__ = 'template_switch_rules'

    id = Column(Integer, primary_key=True, comment='模板切换规则唯一标识')
    name = Column(String, nullable=False, comment='规则名称')
    description = Column(String, comment='规则描述')
    station_id = Column(Integer, ForeignKey('plc_stations.id'), nullable=False, comment='PLC站点ID')
    trigger_signal_id = Column(Integer, ForeignKey('plc_input_signals.id'), nullable=False, comment='触发信号ID')
    target_template_id = Column(Integer, ForeignKey('detection_templates.id'), nullable=False, comment='目标模板ID')
    trigger_value = Column(String, default='1', comment='触发值，BOOL类型通常是1或0')
    is_enabled = Column(Boolean, default=True, comment='是否启用')
    priority = Column(Integer, default=0, comment='优先级，数值越大优先级越高')

    # 关联关系
    station = relationship("PLCStation", back_populates="template_switch_rules")
    trigger_signal = relationship("PLCInputSignal", back_populates="template_switch_rules")
    target_template = relationship("DetectionTemplate")

class AlarmOutputRule(Base):
    """
    报警输出规则模型

    定义当检测到报警时向PLC输出信号的规则。
    可以根据不同的触发条件向指定的PLC输出信号写入特定值。

    应用场景：
    - 卡料报警时点亮警示灯
    - 设备异常时停止生产线
    - 报警确认后复位输出信号

    字段说明：
    - name: 规则名称
    - description: 规则描述
    - station_id: PLC站点ID
    - output_signal_id: 输出信号ID
    - detection_group_id: 检测组ID
    - trigger_condition: 触发条件（alarm_detected, alarm_confirmed等）
    - output_value: 输出值
    - output_duration: 输出持续时间（毫秒），0表示持续输出
    - is_enabled: 是否启用该规则
    """
    __tablename__ = 'alarm_output_rules'

    id = Column(Integer, primary_key=True, comment='报警输出规则唯一标识')
    name = Column(String, nullable=False, comment='规则名称')
    description = Column(String, comment='规则描述')
    station_id = Column(Integer, ForeignKey('plc_stations.id'), nullable=False, comment='PLC站点ID')
    output_signal_id = Column(Integer, ForeignKey('plc_output_signals.id'), nullable=False, comment='输出信号ID')
    detection_group_id = Column(Integer, ForeignKey('detection_groups.id'), nullable=False, comment='检测组ID')
    trigger_condition = Column(String, default='alarm_detected', comment='触发条件：alarm_detected, alarm_confirmed等')
    output_value = Column(String, default='1', comment='输出值')
    output_duration = Column(Integer, default=5000, comment='输出持续时间（毫秒），0表示持续输出')
    is_enabled = Column(Boolean, default=True, comment='是否启用')

    # 关联关系
    station = relationship("PLCStation", back_populates="alarm_output_rules")
    output_signal = relationship("PLCOutputSignal", back_populates="alarm_output_rules")
    detection_group = relationship("DetectionGroup")
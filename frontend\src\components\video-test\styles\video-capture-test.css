/* 抓图功能测试页面样式 */
.capture-test {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0;
  margin: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
}

/* 配置区域 */
.config-section {
  flex-shrink: 0;
}

/* 预览区域 */
.preview-section {
  flex: 1;
  min-height: 0;
}

/* 卡片样式 */
.config-card,
.preview-card,
.history-card,
.log-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(var(--text-color-rgb, 0, 0, 0), 0.1);
  transition: box-shadow 0.3s ease;
  background-color: var(--bg-color-soft);
  border: 1px solid var(--border-color);
}

.config-card:hover,
.preview-card:hover,
.history-card:hover,
.log-card:hover {
  box-shadow: 0 4px 16px rgba(var(--text-color-rgb, 0, 0, 0), 0.15);
  border-color: var(--primary-color-light);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: var(--text-color);
  padding: 0;
  margin: 0;
}

/* 预览控制按钮组 */
.preview-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.preview-controls .el-button {
  min-width: 80px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.preview-controls .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.3);
}

/* 抓图预览容器 */
.capture-preview-container {
  border: 2px solid var(--border-color);
  border-radius: 6px;
  overflow: hidden;
  background: linear-gradient(135deg, var(--bg-color-mute) 0%, var(--bg-color-soft) 100%);
  position: relative;
  transition: border-color 0.3s ease;
  min-height: 400px;
}

.capture-preview-container:hover {
  border-color: var(--primary-color);
}

/* 抓图占位符 */
.capture-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: var(--text-color-mute);
  text-align: center;
  background: radial-gradient(circle at center, var(--bg-color-soft) 0%, var(--bg-color-mute) 70%);
}

.capture-placeholder .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--text-color-mute);
}

.capture-placeholder p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 抓图结果 */
.capture-result {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.capture-image {
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
  border-radius: 4px;
  margin-bottom: 16px;
}

.image-info {
  padding: 16px;
  background-color: var(--bg-color-soft);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.image-info p {
  margin: 6px 0;
  font-size: 13px;
  color: var(--text-color-soft);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 抓图统计 */
.capture-statistics {
  padding: 16px;
  background-color: var(--bg-color-soft);
  border-radius: 6px;
  margin-bottom: 16px;
  border: 1px solid var(--border-color);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item .label {
  font-weight: 500;
  color: var(--text-color-soft);
}

.stat-item .value {
  font-weight: 600;
  color: var(--text-color);
}

.stat-item .value.success {
  color: var(--success-color);
}

.stat-item .value.error {
  color: var(--error-color);
}

/* 历史列表 */
.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.no-history {
  text-align: center;
  color: var(--text-color-mute);
  padding: 20px;
  font-size: 14px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.history-item:hover {
  background-color: var(--bg-color-soft);
}

.history-item:last-child {
  border-bottom: none;
}

.history-info {
  flex: 1;
}

.history-filename {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 4px;
}

.history-time {
  font-size: 12px;
  color: var(--text-color-soft);
}

.history-status {
  flex-shrink: 0;
}

/* 日志容器 */
.log-container {
  height: 200px;
  overflow-y: auto;
  background-color: var(--bg-color-mute);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-item {
  margin-bottom: 4px;
  padding: 2px 0;
  word-break: break-all;
  color: var(--text-color-soft);
  display: flex;
  gap: 8px;
}

.log-item:last-child {
  margin-bottom: 0;
}

.log-time {
  flex-shrink: 0;
  color: var(--text-color-mute);
  font-family: monospace;
  min-width: 80px;
}

.log-message {
  flex: 1;
  word-break: break-all;
}

.log-item.error .log-message {
  color: var(--error-color);
}

.log-item.success .log-message {
  color: var(--success-color);
}

.log-item.warning .log-message {
  color: var(--warning-color);
}

.log-item.info .log-message {
  color: var(--text-color);
}

.no-logs {
  text-align: center;
  color: var(--text-color-mute);
  padding: 20px;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .capture-test {
    gap: 16px;
  }
  
  .preview-section .el-row {
    flex-direction: column;
  }
  
  .preview-section .el-col {
    margin-bottom: 16px;
  }
  
  .preview-controls {
    justify-content: center;
  }
  
  .preview-controls .el-button {
    min-width: 70px;
    font-size: 11px;
  }
  
  .capture-statistics,
  .image-info {
    padding: 12px;
  }
  
  .log-container {
    height: 150px;
  }
}

@media (max-width: 480px) {
  .capture-test {
    gap: 12px;
  }
  
  .config-section .el-form-item {
    margin-bottom: 16px;
  }
  
  .preview-controls {
    gap: 6px;
  }
  
  .preview-controls .el-button {
    min-width: 60px;
    font-size: 10px;
    padding: 6px 8px;
  }
  
  .capture-statistics,
  .image-info {
    padding: 10px;
  }
  
  .log-container {
    height: 120px;
    font-size: 11px;
  }
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 平滑过渡效果 */
.capture-test * {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
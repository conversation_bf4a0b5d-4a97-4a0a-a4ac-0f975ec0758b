/**
 * ROI绘制工具类
 * 负责处理ROI区域的绘制、编辑和管理
 */

export class ROIDrawer {
  constructor(canvas, options = {}) {
    this.canvas = canvas
    this.ctx = canvas.getContext('2d')
    this.options = {
      videoWidth: 640,
      videoHeight: 360,
      colors: {
        pailiao: '#00ffff',  // 青色
        yazhu: '#ff0000',    // 红色
        default: ['#00ff00', '#ffff00', '#ff00ff', '#0066ff']  // 备用颜色
      },
      lineWidth: 2,
      pointRadius: 4,
      debugMode: false,  // 调试模式开关
      ...options
    }

    // 调试模式
    this.debugMode = this.options.debugMode
    
    // 绘制状态
    this.isDrawing = false
    this.drawMode = null // 'rectangle' | 'polygon'
    this.isEnabled = true  // 默认启用
    this.currentAttribute = null // 当前ROI属性

    // ROI数据
    this.rois = []
    this.currentROI = null
    this.colorIndex = 0
    
    // 绘制临时数据
    this.startPoint = null
    this.polygonPoints = []
    this.previewPoint = null
    
    // 事件回调
    this.onROIAdded = null
    this.onROIUpdated = null
    this.onROIDeleted = null

    // 高亮显示相关
    this.highlightedROI = null
    this.highlightColor = '#ffff00' // 高亮颜色（黄色）
    this.highlightLineWidth = 4

    // 事件监听器绑定状态
    this.eventsAttached = false
    this.boundEventHandlers = {}

    this.initCanvas()
    this.attachEvents()
  }
  
  initCanvas() {
    if (!this.canvas) return

    // 设置画布实际像素尺寸
    this.canvas.width = this.options.videoWidth
    this.canvas.height = this.options.videoHeight

    // 设置画布显示尺寸，保持宽高比
    const container = this.canvas.parentElement
    if (container) {
      const containerRect = container.getBoundingClientRect()
      const videoAspectRatio = this.options.videoWidth / this.options.videoHeight
      const containerAspectRatio = containerRect.width / containerRect.height

      if (containerAspectRatio > videoAspectRatio) {
        // 容器更宽，以高度为准
        this.canvas.style.height = '100%'
        this.canvas.style.width = 'auto'
      } else {
        // 容器更高，以宽度为准
        this.canvas.style.width = '100%'
        this.canvas.style.height = 'auto'
      }
    }

    console.log('ROI绘制器初始化完成', {
      canvasWidth: this.canvas.width,
      canvasHeight: this.canvas.height,
      canvasStyleWidth: this.canvas.style.width,
      canvasStyleHeight: this.canvas.style.height,
      videoWidth: this.options.videoWidth,
      videoHeight: this.options.videoHeight,
      enabled: this.isEnabled
    })
  }
  
  // 设置绘制模式
  setDrawMode(mode) {
    this.drawMode = mode
    this.resetDrawing()
    console.log('设置绘制模式:', mode)
  }

  // 设置当前ROI属性
  setCurrentAttribute(attribute) {
    this.currentAttribute = attribute
    console.log('设置ROI属性:', attribute)
  }

  // 绑定事件监听器
  attachEvents() {
    if (this.eventsAttached || !this.canvas) return

    // 创建绑定的事件处理器
    this.boundEventHandlers = {
      mousedown: (e) => this.onMouseDown(e),
      mousemove: (e) => this.onMouseMove(e),
      mouseup: (e) => this.onMouseUp(e),
      dblclick: (e) => this.onDoubleClick(e),
      contextmenu: (e) => this.onContextMenu(e),
      click: (e) => this.onCanvasClick(e)
    }

    // 绑定事件
    Object.entries(this.boundEventHandlers).forEach(([event, handler]) => {
      this.canvas.addEventListener(event, handler)
    })

    this.eventsAttached = true
    console.log('ROI绘制器事件监听器已绑定')
  }

  // 解绑事件监听器
  detachEvents() {
    if (!this.eventsAttached || !this.canvas) return

    // 解绑事件
    Object.entries(this.boundEventHandlers).forEach(([event, handler]) => {
      this.canvas.removeEventListener(event, handler)
    })

    this.eventsAttached = false
    this.boundEventHandlers = {}
    console.log('ROI绘制器事件监听器已解绑')
  }

  // 重新绑定事件（用于重新启用绘制器）
  reattachEvents() {
    this.detachEvents()
    this.attachEvents()
  }
  
  // 启用/禁用绘制
  setEnabled(enabled) {
    this.isEnabled = enabled
    if (this.canvas) {
      this.canvas.style.pointerEvents = 'auto' // 始终允许事件，由内部逻辑控制
      this.canvas.style.cursor = enabled ? 'crosshair' : 'default'
    }

    // 确保事件监听器正确绑定
    if (enabled && !this.eventsAttached) {
      this.attachEvents()
    }

    if (!enabled) {
      this.resetDrawing()
    }

    console.log('ROI绘制', enabled ? '已启用' : '已禁用', {
      canvas: !!this.canvas,
      eventsAttached: this.eventsAttached,
      pointerEvents: this.canvas?.style.pointerEvents
    })
  }
  
  // 重置绘制状态
  resetDrawing() {
    this.isDrawing = false
    this.currentROI = null
    this.startPoint = null
    this.polygonPoints = []
    this.previewPoint = null
    this.redraw()
  }
  
  // 获取鼠标在画布上的坐标
  getCanvasPosition(event) {
    const rect = this.canvas.getBoundingClientRect()
    const scaleX = this.options.videoWidth / rect.width
    const scaleY = this.options.videoHeight / rect.height

    const screenX = event.clientX - rect.left
    const screenY = event.clientY - rect.top

    // 四舍五入为整数坐标，便于后续计算
    const videoX = Math.round(screenX * scaleX)
    const videoY = Math.round(screenY * scaleY)

    // 调试信息（仅在开发模式下显示）
    if (this.debugMode) {
      console.log('坐标转换:', {
        screen: { x: screenX, y: screenY },
        video: { x: videoX, y: videoY },
        scale: { x: scaleX, y: scaleY },
        canvasSize: { width: rect.width, height: rect.height },
        videoSize: { width: this.options.videoWidth, height: this.options.videoHeight }
      })
    }

    return { x: videoX, y: videoY }
  }
  
  // 鼠标按下事件
  onMouseDown(event) {
    if (!this.isEnabled || !this.drawMode) return

    // 🔥 修复：只阻止默认行为，不阻止事件传播，避免与其他事件处理器冲突
    event.preventDefault()

    // 检查是否是右键点击
    if (event.button === 2) {
      // 右键点击 - 完成多边形绘制
      if (this.drawMode === 'polygon' && this.isDrawing && this.currentROI && this.polygonPoints.length >= 3) {
        console.log('右键完成多边形绘制，当前点数:', this.polygonPoints.length)
        this.finishPolygon()
        event.stopPropagation() // 只在实际处理时阻止传播
      }
      return
    }

    // 左键点击
    if (event.button === 0) {
      const pos = this.getCanvasPosition(event)
      console.log('开始绘制:', this.drawMode, pos)

      if (this.drawMode === 'rectangle') {
        this.startRectangle(pos)
      } else if (this.drawMode === 'polygon') {
        this.addPolygonPoint(pos)
      }
      event.stopPropagation() // 只在实际处理时阻止传播
    }
  }
  
  // 鼠标移动事件
  onMouseMove(event) {
    if (!this.isEnabled || !this.drawMode) return

    const pos = this.getCanvasPosition(event)
    this.previewPoint = pos

    if (this.isDrawing && this.currentROI) {
      if (this.drawMode === 'rectangle' && this.startPoint) {
        this.updateRectangle(pos)
      } else if (this.drawMode === 'polygon') {
        this.redraw()
        this.drawPreviewLine(pos)
      }
    } else if (this.drawMode === 'polygon' && this.polygonPoints.length > 0) {
      // 多边形绘制时显示预览线
      this.redraw()
      this.drawPreviewLine(pos)
    }
  }
  
  // 鼠标抬起事件
  onMouseUp(event) {
    if (!this.isEnabled || !this.drawMode) return
    
    if (this.drawMode === 'rectangle' && this.isDrawing && this.currentROI) {
      this.finishRectangle()
    }
  }
  
  // 双击事件（保留作为备用）
  onDoubleClick(event) {
    if (!this.isEnabled || !this.drawMode) return

    // 双击事件现在作为备用，主要使用右键完成多边形
    if (this.drawMode === 'polygon' && this.isDrawing && this.currentROI) {
      this.finishPolygon()
    }
  }

  // 右键菜单事件（禁用默认菜单）
  onContextMenu(event) {
    event.preventDefault()

    if (!this.isEnabled || !this.drawMode) {
      return false
    }

    // 🔥 修复：增强右键完成多边形的逻辑
    if (this.drawMode === 'polygon' && this.isDrawing && this.polygonPoints.length >= 3) {
      console.log('右键菜单完成多边形绘制，当前点数:', this.polygonPoints.length)
      this.finishPolygon()
      event.stopPropagation()
    }

    return false
  }

  // 画布点击事件（用于ROI选择）
  onCanvasClick(event) {
    // 如果正在绘制，不处理选择事件
    if (this.isDrawing || !this.rois.length) return

    const pos = this.getCanvasPosition(event)
    const clickedROI = this.findROIAtPosition(pos)

    if (clickedROI && this.onROIClicked) {
      this.onROIClicked(clickedROI)
      console.log('点击ROI:', clickedROI.name)
    }
  }
  
  // 开始绘制矩形
  startRectangle(pos) {
    this.isDrawing = true
    this.startPoint = pos
    this.currentROI = {
      roi_id: this.generateId(), // 🔥 使用数据库字段名roi_id
      name: `矩形ROI ${this.rois.length + 1}`,
      roi_type: 'rectangle',
      coordinates: [pos], // 🔥 使用数据库字段名coordinates
      points: [pos],      // 兼容性字段
      color: this.getColorByAttribute(this.currentAttribute)
    }
  }
  
  // 更新矩形
  updateRectangle(pos) {
    if (!this.currentROI || !this.startPoint) return

    this.currentROI.coordinates = [this.startPoint, pos] // 🔥 使用coordinates
    this.currentROI.points = [this.startPoint, pos]      // 兼容性
    this.redraw()
  }
  
  // 完成矩形绘制
  finishRectangle() {
    if (!this.currentROI) return
    
    // 验证矩形大小
    const [start, end] = this.currentROI.points
    const width = Math.abs(end.x - start.x)
    const height = Math.abs(end.y - start.y)
    
    if (width < 10 || height < 10) {
      console.log('矩形太小，取消绘制')
      this.resetDrawing()
      return
    }
    
    // 标准化矩形坐标（左上角到右下角）
    const minX = Math.round(Math.min(start.x, end.x))
    const minY = Math.round(Math.min(start.y, end.y))
    const maxX = Math.round(Math.max(start.x, end.x))
    const maxY = Math.round(Math.max(start.y, end.y))

    const rectPoints = [
      { x: minX, y: minY },
      { x: maxX, y: minY },
      { x: maxX, y: maxY },
      { x: minX, y: maxY }
    ]
    this.currentROI.coordinates = rectPoints // 🔥 使用coordinates
    this.currentROI.points = rectPoints       // 兼容性
    
    this.addROI(this.currentROI)
    this.resetDrawing()
  }
  
  // 添加多边形点
  addPolygonPoint(pos) {
    // 🔥 修复：检查是否点击在起始点附近，如果是则自动闭环
    if (this.isDrawing && this.polygonPoints.length >= 3) {
      const firstPoint = this.polygonPoints[0]
      const distance = Math.sqrt(Math.pow(pos.x - firstPoint.x, 2) + Math.pow(pos.y - firstPoint.y, 2))
      
      // 如果点击距离起始点小于15像素，自动闭环
      if (distance < 15) {
        console.log('检测到闭环点击，自动完成多边形，距离:', distance)
        this.finishPolygon()
        return
      }
    }

    if (!this.isDrawing) {
      // 开始绘制多边形
      this.isDrawing = true
      this.polygonPoints = [pos]
      this.currentROI = {
        roi_id: this.generateId(), // 🔥 使用数据库字段名roi_id
        name: `多边形ROI ${this.rois.length + 1}`,
        roi_type: 'polygon',
        coordinates: [pos], // 🔥 使用coordinates
        points: [pos],      // 兼容性
        color: this.getColorByAttribute(this.currentAttribute)
      }
      console.log('开始绘制多边形，起始点:', pos)
    } else {
      // 🔥 修复：检查是否重复添加相同的点
      const lastPoint = this.polygonPoints[this.polygonPoints.length - 1]
      const distance = Math.sqrt(Math.pow(pos.x - lastPoint.x, 2) + Math.pow(pos.y - lastPoint.y, 2))
      
      // 如果距离上一个点太近（小于5像素），忽略这个点
      if (distance < 5) {
        console.log('点击距离上一个点太近，忽略，距离:', distance)
        return
      }
      
      // 添加新点
      this.polygonPoints.push(pos)
      this.currentROI.coordinates = [...this.polygonPoints] // 🔥 使用coordinates
      this.currentROI.points = [...this.polygonPoints]      // 兼容性
      console.log('添加多边形点，当前点数:', this.polygonPoints.length, '新点:', pos)
    }

    this.redraw()
  }
  
  // 完成多边形绘制
  finishPolygon() {
    if (!this.currentROI || this.polygonPoints.length < 3) {
      console.log('多边形点数不足，取消绘制，当前点数:', this.polygonPoints.length)
      this.resetDrawing()
      return
    }

    console.log('完成多边形绘制，最终点数:', this.polygonPoints.length)
    
    // 🔥 修复：确保多边形数据完整性
    const finalPoints = [...this.polygonPoints]
    
    // 标准化多边形坐标
    const normalizedPoints = this.normalizePoints(finalPoints)
    this.currentROI.coordinates = normalizedPoints // 🔥 使用coordinates
    this.currentROI.points = normalizedPoints       // 兼容性

    console.log('多边形标准化完成，坐标点:', normalizedPoints.length)
    
    this.addROI(this.currentROI)
    this.resetDrawing()
  }
  
  // 添加ROI到列表
  addROI(roi) {
    this.rois.push(roi)
    console.log('添加ROI:', roi)
    
    if (this.onROIAdded) {
      this.onROIAdded(roi)
    }
    
    this.redraw()
  }
  
  // 删除ROI
  deleteROI(index) {
    if (index >= 0 && index < this.rois.length) {
      const deletedROI = this.rois.splice(index, 1)[0]
      console.log('删除ROI:', deletedROI)
      
      if (this.onROIDeleted) {
        this.onROIDeleted(deletedROI, index)
      }
      
      this.redraw()
    }
  }
  
  // 清空所有ROI
  clearROIs() {
    this.rois = []
    this.resetDrawing()
    console.log('清空所有ROI')
  }
  
  // 重绘所有ROI
  redraw() {
    if (!this.ctx) {
      console.warn('画布上下文不存在，无法重绘')
      return
    }

    console.log(`重绘开始，ROI数量: ${this.rois.length}`)

    // 清空画布
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)

    // 绘制已保存的ROI
    this.rois.forEach((roi, index) => {
      console.log(`绘制ROI ${index + 1}:`, roi.name, roi.points.length, '个点')
      this.drawROI(roi)
    })

    // 绘制当前正在绘制的ROI
    if (this.currentROI) {
      console.log('绘制当前ROI:', this.currentROI.name)
      this.drawROI(this.currentROI, true)
    }

    console.log('重绘完成')
  }
  
  // 绘制单个ROI
  drawROI(roi, isPreview = false) {
    // 🔥 优先使用coordinates字段，兼容points字段
    const roiPoints = roi.coordinates || roi.points
    if (!this.ctx || !roiPoints || roiPoints.length === 0) return

    // 检查是否为高亮显示的ROI
    const isHighlighted = this.highlightedROI && this.highlightedROI.roi_id === roi.roi_id

    // 🔍 调试信息
    if (roi.roi_id && this.highlightedROI) {
      console.log('🔍 [DEBUG] 绘制ROI:', {
        roiId: roi.roi_id,
        roiName: roi.name,
        highlightedROIId: this.highlightedROI.roi_id,
        isHighlighted,
        roiColor: roi.color,
        highlightColor: this.highlightColor
      })
    }

    // 设置绘制样式
    this.ctx.strokeStyle = isHighlighted ? this.highlightColor : roi.color
    this.ctx.lineWidth = isHighlighted ? this.highlightLineWidth : this.options.lineWidth
    this.ctx.setLineDash(isPreview ? [5, 5] : [])

    if (roi.roi_type === 'rectangle' && roiPoints.length >= 2) {
      this.drawRectangle(roiPoints, isPreview)
    } else if (roi.roi_type === 'polygon' && roiPoints.length >= 2) {
      this.drawPolygon(roiPoints, isPreview)
    }

    this.ctx.setLineDash([])

    // 绘制ROI名称标签
    if (!isPreview && roiPoints.length > 0) {
      this.ctx.fillStyle = isHighlighted ? this.highlightColor : roi.color
      this.ctx.font = isHighlighted ? 'bold 14px Arial' : '12px Arial'

      // 高亮时添加背景
      if (isHighlighted) {
        const textMetrics = this.ctx.measureText(roi.name)
        this.ctx.fillStyle = 'rgba(255, 255, 0, 0.3)'
        this.ctx.fillRect(roiPoints[0].x + 3, roiPoints[0].y - 17, textMetrics.width + 4, 16)
        this.ctx.fillStyle = this.highlightColor
      }

      this.ctx.fillText(roi.name, roiPoints[0].x + 5, roiPoints[0].y - 5)
    }
  }
  
  // 绘制矩形
  drawRectangle(points, isPreview = false) {
    if (points.length < 2) return

    if (points.length === 2) {
      // 绘制过程中，使用起始点和结束点
      const [start, end] = points
      const width = end.x - start.x
      const height = end.y - start.y

      this.ctx.beginPath()
      this.ctx.rect(start.x, start.y, width, height)
      this.ctx.stroke()
    } else if (points.length === 4) {
      // 完成的矩形，使用四个角点绘制
      this.ctx.beginPath()
      this.ctx.moveTo(points[0].x, points[0].y)
      for (let i = 1; i < points.length; i++) {
        this.ctx.lineTo(points[i].x, points[i].y)
      }
      this.ctx.closePath()
      this.ctx.stroke()

      // 绘制角点
      if (!isPreview) {
        this.drawPoints(points)
      }
    }
  }
  
  // 绘制多边形
  drawPolygon(points, isPreview = false) {
    if (points.length < 2) return
    
    this.ctx.beginPath()
    this.ctx.moveTo(points[0].x, points[0].y)
    
    for (let i = 1; i < points.length; i++) {
      this.ctx.lineTo(points[i].x, points[i].y)
    }
    
    if (!isPreview && points.length >= 3) {
      this.ctx.closePath()
    }
    
    this.ctx.stroke()
    
    // 绘制顶点
    if (!isPreview) {
      this.drawPoints(points)
      
      // 🔥 修复：在绘制过程中高亮显示起始点，便于用户识别闭环位置
      if (isPreview && points.length >= 3) {
        this.ctx.fillStyle = '#ffff00' // 黄色高亮起始点
        this.ctx.beginPath()
        this.ctx.arc(points[0].x, points[0].y, this.options.pointRadius + 2, 0, 2 * Math.PI)
        this.ctx.fill()
      }
    }
  }
  
  // 绘制点
  drawPoints(points) {
    this.ctx.fillStyle = this.ctx.strokeStyle
    
    points.forEach(point => {
      this.ctx.beginPath()
      this.ctx.arc(point.x, point.y, this.options.pointRadius, 0, 2 * Math.PI)
      this.ctx.fill()
    })
  }
  
  // 绘制预览线
  drawPreviewLine(currentPos) {
    if (!this.currentROI || this.polygonPoints.length === 0) return
    
    // 🔥 修复：增强预览线显示，包括闭环提示
    this.ctx.strokeStyle = this.currentROI.color
    this.ctx.lineWidth = 1
    this.ctx.setLineDash([5, 5])
    
    const lastPoint = this.polygonPoints[this.polygonPoints.length - 1]
    
    // 绘制从最后一个点到当前鼠标位置的预览线
    this.ctx.beginPath()
    this.ctx.moveTo(lastPoint.x, lastPoint.y)
    this.ctx.lineTo(currentPos.x, currentPos.y)
    this.ctx.stroke()
    
    // 🔥 修复：如果有3个或以上的点，显示闭环预览线
    if (this.polygonPoints.length >= 3) {
      const firstPoint = this.polygonPoints[0]
      const distance = Math.sqrt(Math.pow(currentPos.x - firstPoint.x, 2) + Math.pow(currentPos.y - firstPoint.y, 2))
      
      // 如果鼠标接近起始点，显示闭环预览
      if (distance < 20) {
        this.ctx.strokeStyle = '#ffff00' // 黄色闭环提示线
        this.ctx.lineWidth = 2
        this.ctx.setLineDash([3, 3])
        
        this.ctx.beginPath()
        this.ctx.moveTo(currentPos.x, currentPos.y)
        this.ctx.lineTo(firstPoint.x, firstPoint.y)
        this.ctx.stroke()
        
        // 高亮起始点
        this.ctx.fillStyle = '#ffff00'
        this.ctx.beginPath()
        this.ctx.arc(firstPoint.x, firstPoint.y, this.options.pointRadius + 3, 0, 2 * Math.PI)
        this.ctx.fill()
      }
    }
    
    this.ctx.setLineDash([])
  }
  
  // 根据属性获取颜色
  getColorByAttribute(attribute) {
    if (this.options.colors[attribute]) {
      return this.options.colors[attribute]
    }
    // 如果没有指定属性颜色，使用默认颜色
    const defaultColors = this.options.colors.default || ['#00ff00']
    return defaultColors[this.colorIndex % defaultColors.length]
  }

  // 标准化坐标点（确保为整数）
  normalizePoints(points) {
    return points.map(point => ({
      x: Math.round(point.x),
      y: Math.round(point.y)
    }))
  }

  // 生成唯一ID（简化版8位随机字符串）
  generateId() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  // 高亮显示指定ROI
  highlightROI(roiId) {
    console.log('🔍 [DEBUG] roiDrawer.highlightROI调用:', {
      roiId,
      totalROIs: this.rois.length,
      roiIds: this.rois.map(r => r.roi_id),
      currentHighlighted: this.highlightedROI?.roi_id
    })

    // 查找ROI - 🔥 使用roi_id字段
    const roi = this.rois.find(r => r.roi_id === roiId)
    if (!roi) {
      console.warn('未找到指定的ROI:', roiId, '可用ROI:', this.rois.map(r => ({ roi_id: r.roi_id, name: r.name })))
      return
    }

    this.highlightedROI = roi
    this.redraw()
    console.log('🔍 [DEBUG] 高亮显示ROI成功:', roi.name, roi.roi_id)
  }

  // 清除高亮显示
  clearHighlight() {
    if (this.highlightedROI) {
      this.highlightedROI = null
      this.redraw()
      console.log('清除ROI高亮显示')
    }
  }

  // 切换ROI高亮状态
  toggleROIHighlight(roiId) {
    if (this.highlightedROI && this.highlightedROI.roi_id === roiId) {
      this.clearHighlight()
    } else {
      this.highlightROI(roiId)
    }
  }

  // 加载ROI列表到视频中显示
  loadROIList(roiList) {
    console.log('开始加载ROI列表:', roiList)
    console.log('画布状态:', {
      canvas: !!this.canvas,
      ctx: !!this.ctx,
      canvasWidth: this.canvas?.width,
      canvasHeight: this.canvas?.height,
      videoWidth: this.options.videoWidth,
      videoHeight: this.options.videoHeight
    })

    if (!Array.isArray(roiList)) {
      console.warn('ROI列表必须是数组格式')
      return false
    }

    if (!this.canvas || !this.ctx) {
      console.error('画布未初始化，无法加载ROI')
      return false
    }

    // 验证ROI数据格式
    const validROIs = roiList.filter(roi => {
      // 🔥 优先使用coordinates字段，兼容points字段
      const roiPoints = roi.coordinates || roi.points
      const roiId = roi.roi_id || roi.id // 🔥 优先使用roi_id
      if (!roiId || !roiPoints || !Array.isArray(roiPoints) || roiPoints.length === 0) {
        console.warn('无效的ROI数据:', roi)
        return false
      }
      console.log('验证ROI数据:', roi.name, roiPoints.length, '个点')
      return true
    })

    console.log(`验证完成，有效ROI数量: ${validROIs.length}`)

    // 设置ROI列表
    this.rois = validROIs.map(roi => ({
      ...roi,
      // 🔥 确保使用roi_id字段
      roi_id: roi.roi_id || roi.id,
      // 确保必要的属性存在
      name: roi.name || `ROI_${roi.roi_id || roi.id}`,
      roi_type: roi.roi_type || 'polygon',
      color: roi.color || this.getColorByAttribute(roi.attribute),
      attribute: roi.attribute || 'pailiao'
    }))

    console.log('处理后的ROI列表:', this.rois)

    // 重新绘制
    console.log('开始重新绘制...')
    this.redraw()
    console.log('重新绘制完成')

    console.log(`成功加载 ${this.rois.length} 个ROI到视频中`)
    return true
  }

  // 添加单个ROI到视频中
  addROIToVideo(roi) {
    // 🔥 优先使用coordinates字段，兼容points字段
    const roiPoints = roi.coordinates || roi.points
    const roiId = roi.roi_id || roi.id // 🔥 优先使用roi_id
    if (!roi || !roiId || !roiPoints || !Array.isArray(roiPoints)) {
      console.warn('无效的ROI数据:', roi)
      return false
    }

    // 检查是否已存在
    const existingIndex = this.rois.findIndex(r => (r.roi_id || r.id) === roiId)
    if (existingIndex >= 0) {
      // 更新现有ROI
      this.rois[existingIndex] = {
        ...roi,
        roi_id: roiId, // 🔥 确保使用roi_id字段
        name: roi.name || `ROI_${roiId}`,
        roi_type: roi.roi_type || 'polygon',
        color: roi.color || this.getColorByAttribute(roi.attribute),
        attribute: roi.attribute || 'pailiao'
      }
      console.log('更新ROI:', roi.name || roiId)
    } else {
      // 添加新ROI
      this.rois.push({
        ...roi,
        roi_id: roiId, // 🔥 确保使用roi_id字段
        name: roi.name || `ROI_${roiId}`,
        roi_type: roi.roi_type || 'polygon',
        color: roi.color || this.getColorByAttribute(roi.attribute),
        attribute: roi.attribute || 'pailiao'
      })
      console.log('添加ROI到视频:', roi.name || roiId)
    }

    // 重新绘制
    this.redraw()
    return true
  }

  // 从视频中移除ROI
  removeROIFromVideo(roiId) {
    const index = this.rois.findIndex(roi => roi.roi_id === roiId)
    if (index >= 0) {
      const removedROI = this.rois.splice(index, 1)[0]

      // 如果移除的是高亮ROI，清除高亮
      if (this.highlightedROI && this.highlightedROI.roi_id === roiId) {
        this.clearHighlight()
      }

      this.redraw()
      console.log('从视频中移除ROI:', removedROI.name || roiId)
      return true
    }
    return false
  }

  // 同步ROI列表（用于与外部数据保持同步）
  syncROIList(externalROIList) {
    if (!Array.isArray(externalROIList)) {
      console.warn('外部ROI列表必须是数组格式')
      return false
    }

    // 创建ID映射
    const externalROIMap = new Map()
    externalROIList.forEach(roi => {
      const roiId = roi.roi_id || roi.id // 兼容性处理
      if (roiId) {
        externalROIMap.set(roiId, roi)
      }
    })

    // 移除不在外部列表中的ROI
    this.rois = this.rois.filter(roi => {
      const roiId = roi.roi_id || roi.id // 兼容性处理
      if (externalROIMap.has(roiId)) {
        return true
      } else {
        console.log('移除不存在的ROI:', roi.name || roiId)
        return false
      }
    })

    // 添加或更新外部列表中的ROI
    externalROIList.forEach(externalROI => {
      // 🔥 优先使用coordinates字段，兼容points字段
      const roiPoints = externalROI.coordinates || externalROI.points
      const roiId = externalROI.roi_id || externalROI.id // 🔥 优先使用roi_id
      if (roiId && roiPoints && Array.isArray(roiPoints)) {
        const existingIndex = this.rois.findIndex(r => (r.roi_id || r.id) === roiId)
        const processedROI = {
          ...externalROI,
          roi_id: roiId, // 🔥 确保使用roi_id字段
          name: externalROI.name || `ROI_${roiId}`,
          roi_type: externalROI.roi_type || 'polygon',
          color: externalROI.color || this.getColorByAttribute(externalROI.attribute),
          attribute: externalROI.attribute || 'pailiao',
          // 🔥 确保同时设置coordinates和points字段
          coordinates: roiPoints,
          points: roiPoints
        }

        if (existingIndex >= 0) {
          // 更新现有ROI
          this.rois[existingIndex] = processedROI
        } else {
          // 添加新ROI
          this.rois.push(processedROI)
        }
      }
    })

    // 清除可能无效的高亮
    if (this.highlightedROI && !this.rois.find(r => r.roi_id === this.highlightedROI.roi_id)) {
      this.clearHighlight()
    }

    // 重新绘制
    this.redraw()

    console.log(`同步完成，当前视频中有 ${this.rois.length} 个ROI`)
    return true
  }
  
  // 获取ROI列表
  getROIs() {
    return [...this.rois]
  }
  
  // 设置ROI列表
  setROIs(rois) {
    this.rois = [...rois]
    this.redraw()
  }
  
  // 销毁绘制器
  destroy() {
    this.detachEvents()
    this.resetDrawing()
    this.clearHighlight()
    this.rois = []
    if (this.ctx) {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
    }
    console.log('ROI绘制器已销毁')
  }
}

<template>
  <div class="system-settings">
    <h1>系统设置</h1>
    <div class="settings-container">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本设置" name="basic">
          <el-card class="settings-card">
            <template #header>
              <h3>基本设置</h3>
            </template>
            <el-form :model="basicSettings" label-width="180px">
              <el-form-item label="系统名称">
                <el-input v-model="basicSettings.systemName" />
              </el-form-item>
              <el-form-item label="系统描述">
                <el-input v-model="basicSettings.systemDescription" type="textarea" rows="3" />
              </el-form-item>
              <el-form-item label="管理员邮箱">
                <el-input v-model="basicSettings.adminEmail" />
              </el-form-item>
              <el-form-item label="日志保留天数">
                <el-input-number v-model="basicSettings.logRetentionDays" :min="1" :max="365" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveBasicSettings">保存设置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>
        

        
        <el-tab-pane label="存储设置" name="storage">
          <el-card class="settings-card">
            <template #header>
              <h3>存储设置</h3>
            </template>
            <el-form :model="storageSettings" label-width="180px">
              <el-form-item label="视频存储路径">
                <el-input v-model="storageSettings.videoStoragePath" />
              </el-form-item>
              <el-form-item label="报警图像存储路径">
                <el-input v-model="storageSettings.detectionImagePath" />
              </el-form-item>
              <el-form-item label="自动清理旧文件">
                <el-switch v-model="storageSettings.autoCleanup" />
              </el-form-item>
              <el-form-item label="文件保留天数" v-if="storageSettings.autoCleanup">
                <el-input-number v-model="storageSettings.fileRetentionDays" :min="1" :max="365" />
              </el-form-item>
              <el-form-item label="磁盘空间阈值 (%)">
                <el-slider v-model="storageSettings.diskSpaceThreshold" :min="50" :max="95" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveStorageSettings">保存设置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>
        
        <el-tab-pane label="用户管理" name="user">
          <el-card class="settings-card">
            <template #header>
              <div class="card-header">
                <h3>用户管理</h3>
                <el-button type="primary" @click="addUser">添加用户</el-button>
              </div>
            </template>
            <el-table :data="users" style="width: 100%">
              <el-table-column prop="username" label="用户名" width="180" />
              <el-table-column prop="email" label="邮箱" width="220" />
              <el-table-column prop="role" label="角色" width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.role === 'admin' ? 'danger' : 'info'">
                    {{ scope.row.role === 'admin' ? '管理员' : '普通用户' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                    {{ scope.row.is_active ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button size="small" @click="editUser(scope.row)">编辑</el-button>
                  <el-button
                  size="small"
                  :type="scope.row.is_active ? 'warning' : 'success'"
                  @click="toggleUserStatus(scope.row)"
                >
                  {{ scope.row.is_active ? '禁用' : '启用' }}
                </el-button>
                  <el-button 
                    size="small" 
                    type="danger" 
                    @click="deleteUser(scope.row)"
                    :disabled="scope.row.username === 'admin'"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 添加/编辑用户对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
      width="50%"
    >
      <el-form :model="userForm" label-width="120px" :rules="userRules" ref="userFormRef">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" :disabled="dialogType === 'edit'" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="dialogType === 'add'">
          <el-input v-model="userForm.password" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword" v-if="dialogType === 'add'">
          <el-input v-model="userForm.confirmPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role">
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="userForm.status">
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveUser">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { 
  getBasicSettings, 
  updateBasicSettings, 
  getStorageSettings, 
  updateStorageSettings,
  type BasicSettings,
  type StorageSettings
} from '@/api/system-settings'
import { getUsers, createUser, updateUser, deleteUser, type User } from '@/api/users'

// 当前标签页
const activeTab = ref('basic')

// 基本设置
const basicSettings = reactive({
  systemName: '压铸件智能检测系统',
  systemDescription: '用于压铸件生产过程的智能监控、检测、报警和管理的系统',
  adminEmail: '<EMAIL>',
  logRetentionDays: 30
})



// 存储设置
const storageSettings = reactive({
  videoStoragePath: '/data/videos',
  detectionImagePath: '/data/detection_images',
  autoCleanup: true,
  fileRetentionDays: 90,
  diskSpaceThreshold: 80
})

// 用户管理数据
const users = ref<User[]>([])
const loading = ref(false)

// 用户对话框
const userDialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const userFormRef = ref<FormInstance>()
const userForm = reactive({
  id: 0,
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  role: 'user',
  status: 'active'
})

// 用户表单验证规则
const userRules = reactive<FormRules>({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于 6 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== userForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
})

// 保存基本设置
const saveBasicSettings = async () => {
  try {
    await updateBasicSettings({
      system_name: basicSettings.systemName,
      system_description: basicSettings.systemDescription,
      admin_email: basicSettings.adminEmail,
      log_retention_days: basicSettings.logRetentionDays
    })
    ElMessage.success('基本设置已保存')
  } catch (error) {
    console.error('保存基本设置失败:', error)
    ElMessage.error('保存基本设置失败')
  }
}



// 保存存储设置
const saveStorageSettings = async () => {
  try {
    await updateStorageSettings({
      video_storage_path: storageSettings.videoStoragePath,
      detection_image_path: storageSettings.detectionImagePath,
      auto_cleanup: storageSettings.autoCleanup,
      file_retention_days: storageSettings.fileRetentionDays,
      disk_space_threshold: storageSettings.diskSpaceThreshold
    })
    ElMessage.success('存储设置已保存')
  } catch (error) {
    console.error('保存存储设置失败:', error)
    ElMessage.error('保存存储设置失败')
  }
}

// 加载用户数据
const loadUsers = async () => {
  try {
    loading.value = true
    const response = await getUsers()
    users.value = response.data
  } catch (error) {
    console.error('加载用户数据失败:', error)
    ElMessage.error('加载用户数据失败')
  } finally {
    loading.value = false
  }
}

// 加载基本设置数据
const loadBasicSettings = async () => {
  try {
    const response = await getBasicSettings()
    const data = response.data
    basicSettings.systemName = data.system_name
    basicSettings.systemDescription = data.system_description
    basicSettings.adminEmail = data.admin_email
    basicSettings.logRetentionDays = data.log_retention_days
  } catch (error) {
    console.error('加载基本设置失败:', error)
  }
}

// 加载存储设置数据
const loadStorageSettings = async () => {
  try {
    const response = await getStorageSettings()
    const data = response.data
    storageSettings.videoStoragePath = data.video_storage_path
    storageSettings.detectionImagePath = data.detection_image_path
    storageSettings.autoCleanup = data.auto_cleanup
    storageSettings.fileRetentionDays = data.file_retention_days
    storageSettings.diskSpaceThreshold = data.disk_space_threshold
  } catch (error) {
    console.error('加载存储设置失败:', error)
  }
}

// 添加用户
const addUser = () => {
  dialogType.value = 'add'
  resetUserForm()
  userDialogVisible.value = true
}

// 编辑用户
const editUser = (user: any) => {
  dialogType.value = 'edit'
  Object.assign(userForm, {
    id: user.id,
    username: user.username,
    email: user.email,
    role: user.role,
    status: user.is_active ? 'active' : 'inactive'
  })
  userDialogVisible.value = true
}

// 切换用户状态
const toggleUserStatus = async (user: any) => {
  try {
    const newStatus = !user.is_active
    await updateUser(user.id, {
      username: user.username,
      email: user.email,
      role: user.role,
      is_active: newStatus
    })
    
    const index = users.value.findIndex(item => item.id === user.id)
    if (index !== -1) {
      users.value[index].is_active = newStatus
    }
    
    ElMessage.success(`用户 ${user.username} 已${newStatus ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('切换用户状态失败:', error)
    ElMessage.error('切换用户状态失败')
  }
}

// 删除用户
const deleteUser = (user: any) => {
  if (user.username === 'admin') {
    ElMessage.warning('无法删除管理员账户')
    return
  }
  
  ElMessageBox.confirm(`确认删除用户 "${user.username}"?`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteUser(user.id)
      const index = users.value.findIndex(item => item.id === user.id)
      if (index !== -1) {
        users.value.splice(index, 1)
      }
      ElMessage.success('删除成功')
    } catch (error) {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 保存用户
const saveUser = async () => {
  if (!userFormRef.value) return
  
  try {
    await userFormRef.value.validate()
    
    if (dialogType.value === 'edit') {
      // 编辑用户
      await updateUser(userForm.id, {
        username: userForm.username,
        email: userForm.email,
        role: userForm.role,
        is_active: userForm.status === 'active'
      })
      ElMessage.success('用户更新成功')
    } else {
      // 添加用户
      await createUser({
        username: userForm.username,
        email: userForm.email,
        password: userForm.password,
        role: userForm.role,
        is_active: userForm.status === 'active'
      })
      ElMessage.success('用户添加成功')
    }
    
    userDialogVisible.value = false
    resetUserForm()
    await loadUsers() // 重新加载用户列表
  } catch (error) {
    console.error('保存用户失败:', error)
    ElMessage.error('保存用户失败')
  }
}

// 重置用户表单
const resetUserForm = () => {
  userForm.id = 0
  userForm.username = ''
  userForm.email = ''
  userForm.password = ''
  userForm.confirmPassword = ''
  userForm.role = 'user'
  userForm.status = 'active'
}

onMounted(() => {
  loadBasicSettings()
  loadStorageSettings()
  loadUsers()
})
</script>

<style scoped>
.system-settings {
  padding: 20px;
}

.settings-container {
  margin-top: 20px;
}

.settings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
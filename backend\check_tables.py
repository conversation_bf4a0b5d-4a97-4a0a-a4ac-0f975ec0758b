#!/usr/bin/env python3
import sqlite3

try:
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    # 获取所有表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    
    print("数据库中的表:")
    for table in tables:
        print(f"  - {table}")
    
    # 如果有ROI相关的表，显示其结构
    for table in tables:
        if 'roi' in table.lower():
            print(f"\n表 {table} 的结构:")
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            for column in columns:
                cid, name, type_, notnull, default_value, pk = column
                print(f"  {name}: {type_} (默认值: {default_value})")
    
    conn.close()
    
except Exception as e:
    print(f"错误: {e}")

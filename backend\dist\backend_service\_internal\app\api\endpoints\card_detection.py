from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc

from app.api.deps import get_db
from app.models.models import CardDetectionResult, DetectionGroup, DetectionTemplate, DieCaster
from app.schemas.card_detection import (
    CardDetectionResult as CardDetectionResultSchema,
    CardDetectionResultCreate,
    CardDetectionResultUpdate,
    CardDetectionResultList,
    CardDetectionQuery,
    CardDetectionResultSimple,
    CardDetectionStatistics,
    CardDetectionDashboard,
    CardDetectionTrend,
    DailyDetectionStatistics,
    TemplateStatistics,
    DieCasterStatistics,
    DetectionGroupStatistics
)

router = APIRouter()


@router.post("/", response_model=CardDetectionResultSchema)
def create_card_detection_result(
    result: CardDetectionResultCreate,
    db: Session = Depends(get_db)
):
    """创建卡料检测结果记录"""
    # 验证检测组是否存在
    detection_group = db.query(DetectionGroup).filter(DetectionGroup.id == result.detection_group_id).first()
    if not detection_group:
        raise HTTPException(status_code=404, detail="Detection group not found")
    
    # 创建检测结果记录
    db_result = CardDetectionResult(
        detection_group_id=result.detection_group_id,
        timestamp=result.timestamp,
        is_normal=result.is_normal,
        detection_time=result.detection_time,
        undetected_rois=result.undetected_rois,
        detected_rois=result.detected_rois,
        trigger_roi_id=result.trigger_roi_id,
        result_details=result.result_details
    )
    
    db.add(db_result)
    db.commit()
    db.refresh(db_result)
    
    return db_result


@router.get("/", response_model=CardDetectionResultList)
def get_card_detection_results(
    detection_group_id: Optional[int] = Query(None, description="检测组ID"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    is_normal: Optional[bool] = Query(None, description="是否正常"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取卡料检测结果列表"""
    query = db.query(CardDetectionResult)
    
    # 应用过滤条件
    if detection_group_id is not None:
        query = query.filter(CardDetectionResult.detection_group_id == detection_group_id)
    
    if start_time is not None:
        query = query.filter(CardDetectionResult.timestamp >= start_time)
    
    if end_time is not None:
        query = query.filter(CardDetectionResult.timestamp <= end_time)
    
    if is_normal is not None:
        query = query.filter(CardDetectionResult.is_normal == is_normal)
    
    # 获取总数
    total = query.count()
    
    # 分页和排序
    results = query.order_by(desc(CardDetectionResult.timestamp)).offset((page - 1) * page_size).limit(page_size).all()
    
    return CardDetectionResultList(
        total=total,
        page=page,
        page_size=page_size,
        items=results
    )


@router.get("/recent", response_model=List[CardDetectionResultSimple])
def get_recent_card_detection_results(
    limit: int = Query(10, ge=1, le=50, description="返回结果数量"),
    detection_group_id: Optional[int] = Query(None, description="检测组ID"),
    db: Session = Depends(get_db)
):
    """获取最新的卡料检测结果 - 返回核心字段：id、detection_group_id、timestamp、is_normal、undetected_rois"""
    query = db.query(CardDetectionResult)
    
    # 应用检测组过滤
    if detection_group_id is not None:
        query = query.filter(CardDetectionResult.detection_group_id == detection_group_id)
    
    # 按时间倒序排列并限制数量
    results = query.order_by(desc(CardDetectionResult.timestamp)).limit(limit).all()
    
    return results


@router.get("/daily-statistics", response_model=DailyDetectionStatistics)
def get_daily_detection_statistics(
    date: Optional[str] = Query(None, description="日期 (YYYY-MM-DD)，默认为今天"),
    db: Session = Depends(get_db)
):
    """获取每日检测统计信息
    
    从卡料检测结果中获取每日检测的检测组，通过检测组ID可以获取到检测模板，
    以及关联的压铸机，最后获取到每日检测的检测模板的检测情况（检测次数，卡料次数），
    每个检测模板下面，每台压铸机的检测情况（检测次数、卡料次数），
    每个压铸机下面的检测组的检测情况（检测次数，卡料次数）
    """
    try:
        # 解析日期参数，默认为今天
        if date:
            target_date = datetime.strptime(date, "%Y-%m-%d").date()
        else:
            target_date = datetime.now().date()
        
        # 计算当天的时间范围
        day_start = datetime.combine(target_date, datetime.min.time())
        day_end = day_start + timedelta(days=1)
        
        # 获取当天所有的检测结果
        detection_results = db.query(CardDetectionResult).filter(
            and_(
                CardDetectionResult.timestamp >= day_start,
                CardDetectionResult.timestamp < day_end
            )
        ).all()
        
        # 按检测组分组统计
        group_stats = {}
        for result in detection_results:
            group_id = result.detection_group_id
            if group_id not in group_stats:
                group_stats[group_id] = {
                    'total_detections': 0,
                    'jam_detections': 0
                }
            
            group_stats[group_id]['total_detections'] += 1
            if not result.is_normal:
                group_stats[group_id]['jam_detections'] += 1
        
        # 获取所有相关的检测组信息
        group_ids = list(group_stats.keys())
        detection_groups = db.query(DetectionGroup).filter(
            DetectionGroup.id.in_(group_ids)
        ).all() if group_ids else []
        
        # 按模板分组
        template_stats = {}
        
        for group in detection_groups:
            template_id = group.template_id
            die_caster_id = group.die_caster_id
            group_id = group.id
            
            # 如果没有模板ID，跳过
            if not template_id:
                continue
                
            # 初始化模板统计
            if template_id not in template_stats:
                template_stats[template_id] = {
                    'template': None,
                    'total_detections': 0,
                    'jam_detections': 0,
                    'die_casters': {}
                }
            
            # 初始化压铸机统计
            if die_caster_id not in template_stats[template_id]['die_casters']:
                template_stats[template_id]['die_casters'][die_caster_id] = {
                    'die_caster': None,
                    'total_detections': 0,
                    'jam_detections': 0,
                    'detection_groups': {}
                }
            
            # 获取当前组的统计数据
            group_total = group_stats.get(group_id, {}).get('total_detections', 0)
            group_jam = group_stats.get(group_id, {}).get('jam_detections', 0)
            
            # 累加到模板统计
            template_stats[template_id]['total_detections'] += group_total
            template_stats[template_id]['jam_detections'] += group_jam
            
            # 累加到压铸机统计
            template_stats[template_id]['die_casters'][die_caster_id]['total_detections'] += group_total
            template_stats[template_id]['die_casters'][die_caster_id]['jam_detections'] += group_jam
            
            # 添加检测组统计
            template_stats[template_id]['die_casters'][die_caster_id]['detection_groups'][group_id] = DetectionGroupStatistics(
                group_id=group_id,
                group_name=group.name,
                total_detections=group_total,
                jam_detections=group_jam,
                success_rate=(group_total - group_jam) / group_total if group_total > 0 else 0.0
            )
        
        # 获取模板和压铸机的详细信息
        template_ids = list(template_stats.keys())
        templates = db.query(DetectionTemplate).filter(
            DetectionTemplate.id.in_(template_ids)
        ).all() if template_ids else []
        
        die_caster_ids = set()
        for template_data in template_stats.values():
            die_caster_ids.update(template_data['die_casters'].keys())
        
        die_casters = db.query(DieCaster).filter(
            DieCaster.id.in_(list(die_caster_ids))
        ).all() if die_caster_ids else []
        
        # 创建模板和压铸机的映射
        template_map = {t.id: t for t in templates}
        die_caster_map = {d.id: d for d in die_casters}
        
        # 构建响应数据
        result_templates = []
        
        for template_id, template_data in template_stats.items():
            template = template_map.get(template_id)
            if not template:
                continue
                
            # 构建压铸机统计列表
            die_caster_list = []
            for die_caster_id, die_caster_data in template_data['die_casters'].items():
                die_caster = die_caster_map.get(die_caster_id)
                if not die_caster:
                    continue
                    
                # 构建检测组统计列表
                detection_groups_list = list(die_caster_data['detection_groups'].values())
                
                die_caster_stats = DieCasterStatistics(
                    die_caster_id=die_caster_id,
                    die_caster_name=die_caster.name,
                    total_detections=die_caster_data['total_detections'],
                    jam_detections=die_caster_data['jam_detections'],
                    success_rate=(die_caster_data['total_detections'] - die_caster_data['jam_detections']) / die_caster_data['total_detections'] if die_caster_data['total_detections'] > 0 else 0.0,
                    detection_groups=detection_groups_list
                )
                die_caster_list.append(die_caster_stats)
            
            template_statistics = TemplateStatistics(
                template_id=template_id,
                template_name=template.name,
                total_detections=template_data['total_detections'],
                jam_detections=template_data['jam_detections'],
                success_rate=(template_data['total_detections'] - template_data['jam_detections']) / template_data['total_detections'] if template_data['total_detections'] > 0 else 0.0,
                die_casters=die_caster_list
            )
            result_templates.append(template_statistics)
        
        return DailyDetectionStatistics(
            date=target_date.isoformat(),
            templates=result_templates
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Statistics error: {str(e)}")


@router.get("/{result_id}", response_model=CardDetectionResultSchema)
def get_card_detection_result(
    result_id: int,
    db: Session = Depends(get_db)
):
    """获取单个卡料检测结果"""
    result = db.query(CardDetectionResult).filter(CardDetectionResult.id == result_id).first()
    if not result:
        raise HTTPException(status_code=404, detail="Card detection result not found")
    return result


@router.put("/{result_id}", response_model=CardDetectionResultSchema)
def update_card_detection_result(
    result_id: int,
    result_update: CardDetectionResultUpdate,
    db: Session = Depends(get_db)
):
    """更新卡料检测结果"""
    db_result = db.query(CardDetectionResult).filter(CardDetectionResult.id == result_id).first()
    if not db_result:
        raise HTTPException(status_code=404, detail="Card detection result not found")
    
    # 更新字段
    if result_update.result_details is not None:
        db_result.result_details = result_update.result_details
    
    db.commit()
    db.refresh(db_result)
    
    return db_result


@router.delete("/{result_id}")
def delete_card_detection_result(
    result_id: int,
    db: Session = Depends(get_db)
):
    """删除卡料检测结果"""
    db_result = db.query(CardDetectionResult).filter(CardDetectionResult.id == result_id).first()
    if not db_result:
        raise HTTPException(status_code=404, detail="Card detection result not found")
    
    db.delete(db_result)
    db.commit()
    
    return {"message": "Card detection result deleted successfully"}


@router.get("/statistics/{detection_group_id}", response_model=CardDetectionStatistics)
def get_card_detection_statistics(
    detection_group_id: int,
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    recent_limit: int = Query(10, ge=1, le=50, description="最近结果数量"),
    db: Session = Depends(get_db)
):
    """获取卡料检测统计信息"""
    # 验证检测组是否存在
    detection_group = db.query(DetectionGroup).filter(DetectionGroup.id == detection_group_id).first()
    if not detection_group:
        raise HTTPException(status_code=404, detail="Detection group not found")
    
    query = db.query(CardDetectionResult).filter(CardDetectionResult.detection_group_id == detection_group_id)
    
    # 应用时间过滤
    if start_time is not None:
        query = query.filter(CardDetectionResult.timestamp >= start_time)
    if end_time is not None:
        query = query.filter(CardDetectionResult.timestamp <= end_time)
    
    # 统计数据
    total_count = query.count()
    normal_count = query.filter(CardDetectionResult.is_normal == True).count()
    jam_count = query.filter(CardDetectionResult.is_normal == False).count()
    
    # 计算成功率
    success_rate = normal_count / total_count if total_count > 0 else 0.0
    
    # 计算平均检测时间
    avg_detection_time = db.query(func.avg(CardDetectionResult.detection_time)).filter(
        CardDetectionResult.detection_group_id == detection_group_id
    ).scalar() or 0.0
    
    # 获取最后检测时间
    last_detection = query.order_by(desc(CardDetectionResult.timestamp)).first()
    last_detection_time = last_detection.timestamp if last_detection else None
    
    # 获取最近的检测结果
    recent_results = query.order_by(desc(CardDetectionResult.timestamp)).limit(recent_limit).all()
    
    return CardDetectionStatistics(
        detection_group_id=detection_group_id,
        total_count=total_count,
        normal_count=normal_count,
        jam_count=jam_count,
        success_rate=success_rate,
        avg_detection_time=avg_detection_time,
        last_detection_time=last_detection_time,
        recent_results=recent_results
    )


@router.get("/dashboard/overview", response_model=CardDetectionDashboard)
def get_card_detection_dashboard(
    detection_group_id: Optional[int] = Query(None, description="检测组ID，不指定则统计所有"),
    db: Session = Depends(get_db)
):
    """获取卡料检测看板数据"""
    try:
        now = datetime.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        week_start = today_start - timedelta(days=now.weekday())
        
        base_query = db.query(CardDetectionResult)
        if detection_group_id is not None:
            base_query = base_query.filter(CardDetectionResult.detection_group_id == detection_group_id)
        
        # 今日统计
        today_query = base_query.filter(CardDetectionResult.timestamp >= today_start)
        total_detections_today = today_query.count()
        normal_detections_today = today_query.filter(CardDetectionResult.is_normal == True).count()
        jam_detections_today = today_query.filter(CardDetectionResult.is_normal == False).count()
        success_rate_today = normal_detections_today / total_detections_today if total_detections_today > 0 else 0.0
        
        # 本周统计
        week_query = base_query.filter(CardDetectionResult.timestamp >= week_start)
        total_detections_week = week_query.count()
        normal_detections_week = week_query.filter(CardDetectionResult.is_normal == True).count()
        jam_detections_week = week_query.filter(CardDetectionResult.is_normal == False).count()
        success_rate_week = normal_detections_week / total_detections_week if total_detections_week > 0 else 0.0
        
        # 平均检测时间
        avg_detection_time = db.query(func.avg(CardDetectionResult.detection_time)).scalar() or 0.0
        
        # 最近检测结果
        recent_results = base_query.order_by(desc(CardDetectionResult.timestamp)).limit(10).all()
        
        # 简化的按小时统计
        hourly_stats = []
        
        # 简化的按检测组统计
        group_stats = []
        
        return CardDetectionDashboard(
            total_detections_today=total_detections_today,
            normal_detections_today=normal_detections_today,
            jam_detections_today=jam_detections_today,
            success_rate_today=success_rate_today,
            total_detections_week=total_detections_week,
            normal_detections_week=normal_detections_week,
            jam_detections_week=jam_detections_week,
            success_rate_week=success_rate_week,
            avg_detection_time=avg_detection_time,
            recent_results=recent_results,
            hourly_stats=hourly_stats,
            group_stats=group_stats
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Dashboard error: {str(e)}")


@router.get("/trends/daily", response_model=List[CardDetectionTrend])
def get_card_detection_trends(
    detection_group_id: Optional[int] = Query(None, description="检测组ID"),
    days: int = Query(7, ge=1, le=30, description="天数"),
    db: Session = Depends(get_db)
):
    """获取卡料检测趋势数据"""
    now = datetime.now()
    start_date = now.date() - timedelta(days=days-1)
    
    trends = []
    for i in range(days):
        current_date = start_date + timedelta(days=i)
        day_start = datetime.combine(current_date, datetime.min.time())
        day_end = day_start + timedelta(days=1)
        
        query = db.query(CardDetectionResult).filter(
            and_(
                CardDetectionResult.timestamp >= day_start,
                CardDetectionResult.timestamp < day_end
            )
        )
        
        if detection_group_id is not None:
            query = query.filter(CardDetectionResult.detection_group_id == detection_group_id)
        
        total_count = query.count()
        normal_count = query.filter(CardDetectionResult.is_normal == True).count()
        jam_count = query.filter(CardDetectionResult.is_normal == False).count()
        success_rate = normal_count / total_count if total_count > 0 else 0.0
        
        avg_detection_time = db.query(func.avg(CardDetectionResult.detection_time)).filter(
            and_(
                CardDetectionResult.timestamp >= day_start,
                CardDetectionResult.timestamp < day_end
            )
        )
        
        if detection_group_id is not None:
            avg_detection_time = avg_detection_time.filter(
                CardDetectionResult.detection_group_id == detection_group_id
            )
        
        avg_time = avg_detection_time.scalar() or 0.0
        
        trends.append(CardDetectionTrend(
            date=current_date.isoformat(),
            total_count=total_count,
            normal_count=normal_count,
            jam_count=jam_count,
            success_rate=success_rate,
            avg_detection_time=avg_time
        ))
    
    return trends


@router.delete("/cleanup")
def cleanup_old_results(
    days: int = Query(30, ge=1, description="保留天数"),
    detection_group_id: Optional[int] = Query(None, description="检测组ID，不指定则清理所有"),
    db: Session = Depends(get_db)
):
    """清理旧的检测结果"""
    cutoff_date = datetime.now() - timedelta(days=days)
    
    query = db.query(CardDetectionResult).filter(CardDetectionResult.created_at < cutoff_date)
    
    if detection_group_id is not None:
        query = query.filter(CardDetectionResult.detection_group_id == detection_group_id)
    
    deleted_count = query.count()
    query.delete(synchronize_session=False)
    db.commit()
    
    return {
        "message": f"Deleted {deleted_count} old detection results",
        "deleted_count": deleted_count,
        "cutoff_date": cutoff_date.isoformat()
    }
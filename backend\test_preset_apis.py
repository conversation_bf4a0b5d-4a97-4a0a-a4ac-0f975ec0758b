#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

BASE_URL = "http://localhost:8000/api/preset-schedules"
HEADERS = {
    "Authorization": "Bearer test",
    "Content-Type": "application/json"
}

def test_api(method, url, data=None):
    """测试API接口"""
    try:
        if method == "GET":
            response = requests.get(url, headers=HEADERS)
        elif method == "POST":
            response = requests.post(url, headers=HEADERS, json=data)
        elif method == "PUT":
            response = requests.put(url, headers=HEADERS, json=data)
        elif method == "DELETE":
            response = requests.delete(url, headers=HEADERS)
        
        print(f"{method} {url}")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        print("-" * 50)
        return response
    except Exception as e:
        print(f"请求失败: {str(e)}")
        print("-" * 50)
        return None

def main():
    print("测试预设检测计划API...\n")
    
    # 1. 测试获取所有预设检测计划
    test_api("GET", BASE_URL + "/")
    
    # 2. 测试获取特定预设检测计划
    test_api("GET", BASE_URL + "/1")
    
    # 3. 测试检查时间冲突（修复后的接口）
    conflict_data = {
        "date": "2025-07-23",
        "startTime": "05:50",
        "endTime": "05:58"
    }
    test_api("POST", BASE_URL + "/check-conflict", conflict_data)
    
    # 4. 测试更新预设检测计划
    update_data = {
        "name": "测试更新",
        "description": "测试描述"
    }
    test_api("PUT", BASE_URL + "/1", update_data)
    
    # 5. 测试切换启用状态
    toggle_data = {"enabled": False}
    test_api("PUT", BASE_URL + "/1/toggle", toggle_data)
    
    # 6. 测试执行预设检测计划
    test_api("POST", BASE_URL + "/1/execute")
    
    # 7. 测试停止预设检测计划
    test_api("POST", BASE_URL + "/1/stop")
    
    # 8. 测试获取执行状态
    test_api("GET", BASE_URL + "/1/status")
    
    # 9. 测试按日期获取
    test_api("GET", BASE_URL + "/by-date/2025-07-23")

if __name__ == "__main__":
    main()
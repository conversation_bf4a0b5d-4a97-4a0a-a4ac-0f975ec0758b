import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def test_route_without_auth(url):
    """测试路由是否存在（不使用认证）"""
    try:
        response = requests.get(url)
        print(f"GET {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ 路由存在，但需要认证")
            return True
        elif response.status_code == 404:
            print("❌ 路由不存在 (404)")
            return False
        elif response.status_code == 200:
            print("✅ 路由存在且可访问")
            return True
        else:
            print(f"⚠️  其他状态码: {response.status_code}")
            return True
            
        try:
            response_data = response.json()
            print(f"响应: {response_data}")
        except:
            print(f"响应: {response.text}")
        print("-" * 50)
        
    except Exception as e:
        print(f"请求失败: {e}")
        print("-" * 50)
        return False

def get_auth_token(username="admin", password="123456"):
    """获取认证token"""
    login_url = f"{API_BASE}/auth/login/json"
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data["access_token"]
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_route_with_auth(url):
    """测试路由（使用认证）"""
    token = get_auth_token()
    if not token:
        print(f"无法获取token，跳过 {url}")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"GET {url} (with auth)")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 成功")
            return True
        elif response.status_code == 404:
            print("❌ 路由不存在 (404)")
            return False
        else:
            print(f"⚠️  其他错误: {response.status_code}")
            
        try:
            response_data = response.json()
            print(f"响应: {response_data}")
        except:
            print(f"响应: {response.text}")
        print("-" * 50)
        
        return response.status_code != 404
    except Exception as e:
        print(f"请求失败: {e}")
        print("-" * 50)
        return False

def main():
    print("调试路由访问问题...\n")
    
    # 测试基础路由
    print("1. 测试基础路由:")
    test_route_without_auth(f"{BASE_URL}/")
    test_route_without_auth(f"{API_BASE}/")
    
    # 测试认证路由
    print("\n2. 测试认证路由:")
    test_route_without_auth(f"{API_BASE}/auth/login/json")
    
    # 测试预设计划路由（不同格式）
    print("\n3. 测试预设计划路由（无认证）:")
    test_urls = [
        f"{API_BASE}/preset-schedules",
        f"{API_BASE}/preset-schedules/",
    ]
    
    for url in test_urls:
        test_route_without_auth(url)
    
    # 测试预设计划路由（有认证）
    print("\n4. 测试预设计划路由（有认证）:")
    for url in test_urls:
        test_route_with_auth(url)
    
    # 测试其他已知工作的路由
    print("\n5. 测试其他API路由:")
    other_routes = [
        f"{API_BASE}/users",
        f"{API_BASE}/detection-templates",
        f"{API_BASE}/system-settings"
    ]
    
    for url in other_routes:
        test_route_with_auth(url)
    
    # 检查服务器是否正在运行
    print("\n6. 检查服务器状态:")
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"服务器响应: {response.status_code}")
        if response.status_code == 200:
            print("✅ 后端服务器正在运行")
        else:
            print(f"⚠️  服务器响应异常: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器")
        print("请确保后端服务器在 http://localhost:8000 运行")
    except Exception as e:
        print(f"❌ 服务器检查失败: {e}")
    
    print("\n调试完成！")
    print("\n如果所有路由都返回404，可能的原因:")
    print("1. 后端服务器未启动")
    print("2. 路由注册有问题")
    print("3. FastAPI配置有问题")
    print("4. 端口冲突")

if __name__ == "__main__":
    main()
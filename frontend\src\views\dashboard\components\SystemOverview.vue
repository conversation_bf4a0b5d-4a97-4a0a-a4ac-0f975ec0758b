<template>
  <div class="system-overview">
    <div class="overview-cards">
      <!-- 设备状态卡片 -->
      <div class="overview-card">
        <div class="card-icon device-icon">
          <el-icon><monitor /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">设备状态</div>
          <div class="card-value">
            <span class="primary-value">{{ overviewData.activeDevices }}</span>
            <span class="secondary-value">/ {{ overviewData.totalDevices }}</span>
          </div>
          <div class="card-subtitle">在线设备</div>
        </div>
        <div class="card-trend">
          <el-icon class="trend-icon up"><arrow-up /></el-icon>
          <span class="trend-text">正常</span>
        </div>
      </div>

      <!-- 检测组状态卡片 -->
      <div class="overview-card">
        <div class="card-icon detection-icon">
          <el-icon><view /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">检测组</div>
          <div class="card-value">
            <span class="primary-value">{{ overviewData.activeDetectionGroups }}</span>
            <span class="secondary-value">/ {{ overviewData.totalDetectionGroups }}</span>
          </div>
          <div class="card-subtitle">活跃检测组</div>
        </div>
        <div class="card-trend">
          <el-icon class="trend-icon up"><arrow-up /></el-icon>
          <span class="trend-text">运行中</span>
        </div>
      </div>

      <!-- 报警状态卡片 -->
      <div class="overview-card">
        <div class="card-icon alarm-icon">
          <el-icon><warning /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">报警信息</div>
          <div class="card-value">
            <span class="primary-value">{{ overviewData.unhandledAlarms }}</span>
            <span class="secondary-value">/ {{ overviewData.totalAlarms }}</span>
          </div>
          <div class="card-subtitle">待处理报警</div>
        </div>
        <div class="card-trend">
          <el-icon class="trend-icon" :class="overviewData.unhandledAlarms > 0 ? 'warning' : 'normal'">
            <warning v-if="overviewData.unhandledAlarms > 0" />
            <check v-else />
          </el-icon>
          <span class="trend-text" :class="overviewData.unhandledAlarms > 0 ? 'warning' : 'normal'">
            {{ overviewData.unhandledAlarms > 0 ? '需处理' : '正常' }}
          </span>
        </div>
      </div>

      <!-- 系统状态卡片 -->
      <div class="overview-card">
        <div class="card-icon system-icon">
          <el-icon><cpu /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">系统状态</div>
          <div class="card-value">
            <span class="primary-value status-indicator" :class="overviewData.systemStatus">
              {{ getSystemStatusText(overviewData.systemStatus) }}
            </span>
          </div>
          <div class="card-subtitle">运行状态</div>
        </div>
        <div class="card-trend">
          <el-icon class="trend-icon" :class="overviewData.systemStatus">
            <success-filled v-if="overviewData.systemStatus === 'normal'" />
            <warning-filled v-else-if="overviewData.systemStatus === 'warning'" />
            <circle-close-filled v-else />
          </el-icon>
          <span class="trend-text" :class="overviewData.systemStatus">
            {{ getSystemStatusText(overviewData.systemStatus) }}
          </span>
        </div>
      </div>

      <!-- GPU加速状态卡片 -->
      <div class="overview-card">
        <div class="card-icon gpu-icon">
          <el-icon><lightning /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">GPU加速</div>
          <div class="card-value">
            <span class="primary-value">{{ gpuStatus.enabled ? '已启用' : '未启用' }}</span>
          </div>
          <div class="card-subtitle">{{ gpuStatus.deviceName || '检测中...' }}</div>
        </div>
        <div class="card-trend">
          <el-icon class="trend-icon" :class="gpuStatus.enabled ? 'up' : 'normal'">
            <lightning v-if="gpuStatus.enabled" />
            <cpu v-else />
          </el-icon>
          <span class="trend-text">{{ gpuStatus.enabled ? '加速中' : 'CPU模式' }}</span>
        </div>
      </div>

      <!-- 算法性能卡片 -->
      <div class="overview-card">
        <div class="card-icon performance-icon">
          <el-icon><odometer /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">检测性能</div>
          <div class="card-value">
            <span class="primary-value">{{ algorithmPerformance.fps }}</span>
            <span class="secondary-value">FPS</span>
          </div>
          <div class="card-subtitle">平均处理速度</div>
        </div>
        <div class="card-trend">
          <el-icon class="trend-icon up"><timer /></el-icon>
          <span class="trend-text">{{ algorithmPerformance.latency }}ms</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Monitor, View, Warning, Cpu, Lightning, Odometer, Timer,
  ArrowUp, Check, SuccessFilled, WarningFilled, CircleCloseFilled
} from '@element-plus/icons-vue'

// Props
interface OverviewData {
  totalDevices: number
  activeDevices: number
  totalDetectionGroups: number
  activeDetectionGroups: number
  totalAlarms: number
  unhandledAlarms: number
  systemStatus: 'normal' | 'warning' | 'error'
}

const props = defineProps<{
  overviewData: OverviewData
}>()

// 模拟GPU状态数据
const gpuStatus = ref({
  enabled: true,
  deviceName: 'NVIDIA GeForce RTX 3080',
  usage: 78,
  memory: 8192,
  memoryUsed: 6144
})

// 模拟算法性能数据
const algorithmPerformance = ref({
  fps: 25,
  latency: 120,
  accuracy: 98.5,
  detectionCount: 1250
})

// 方法
const getSystemStatusText = (status: string) => {
  switch (status) {
    case 'normal':
      return '正常'
    case 'warning':
      return '警告'
    case 'error':
      return '错误'
    default:
      return '未知'
  }
}
</script>

<style scoped>
.system-overview {
  width: 100%;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
}

.overview-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-align: center;
  min-height: 100px;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.15);
  border-color: var(--primary-color);
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  font-size: 18px;
  flex-shrink: 0;
}

.device-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.detection-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.alarm-icon {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #d4691a;
}

.system-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #2d5aa0;
}

.gpu-icon {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #8e44ad;
}

.performance-icon {
  background: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%);
  color: #3498db;
}

.card-content {
  flex: 1;
  min-width: 0;
  width: 100%;
}

.card-title {
  font-size: 12px;
  color: var(--text-color-soft);
  margin-bottom: 4px;
  font-weight: 500;
}

.card-value {
  display: flex;
  align-items: baseline;
  margin-bottom: 4px;
}

.primary-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-color);
  margin-right: 2px;
}

.secondary-value {
  font-size: 12px;
  color: var(--text-color-mute);
  font-weight: 500;
}

.card-subtitle {
  font-size: 10px;
  color: var(--text-color-mute);
}

.card-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-top: 4px;
}

.trend-icon {
  font-size: 14px;
}

.trend-icon.up {
  color: var(--success-color);
}

.trend-icon.warning {
  color: var(--warning-color);
}

.trend-icon.normal {
  color: var(--success-color);
}

.trend-text {
  font-size: 10px;
  font-weight: 500;
}

.trend-text.warning {
  color: var(--warning-color);
}

.trend-text.normal {
  color: var(--success-color);
}

.status-indicator.normal {
  color: var(--success-color);
}

.status-indicator.warning {
  color: var(--warning-color);
}

.status-indicator.error {
  color: var(--danger-color);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }

  .overview-card {
    padding: 10px 6px;
    min-height: 90px;
  }

  .card-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
    margin-bottom: 6px;
  }

  .primary-value {
    font-size: 16px;
  }

  .card-title {
    font-size: 11px;
  }

  .trend-text {
    font-size: 9px;
  }
}

@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .overview-card {
    padding: 8px 4px;
    min-height: 80px;
  }

  .card-icon {
    width: 28px;
    height: 28px;
    font-size: 12px;
    margin-bottom: 4px;
  }

  .primary-value {
    font-size: 14px;
  }

  .card-title {
    font-size: 10px;
  }

  .card-subtitle {
    font-size: 9px;
  }

  .trend-icon {
    font-size: 12px;
  }

  .trend-text {
    font-size: 8px;
  }
}
</style>

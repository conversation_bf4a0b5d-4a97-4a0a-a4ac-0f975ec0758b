// 检测系统相关类型定义

// 检测模式类型
export type DetectionMode = 'motion' | 'direction'

// 检测算法类型
export type DetectionAlgorithm = 'background_subtraction' | 'frame_difference'

// 检测状态类型
export interface DetectionStatus {
  isActive: boolean
  mode: DetectionMode
  algorithm: DetectionAlgorithm
  startTime?: number
  runningTime?: number
}

// 检测结果类型
export interface DetectionResult {
  motion_detected: boolean
  contours?: any[]
  direction?: {
    direction: 'MOVING_UP' | 'MOVING_DOWN' | 'STATIONARY'
    confidence: number
  }
  roi_results?: ROIDetectionResult[]
  roi_violations?: any[]
  timestamp?: number
}

// ROI检测结果类型
export interface ROIDetectionResult {
  roi_id: string
  roi_name?: string
  motion_detected: boolean
  contours?: any[]
  direction?: {
    direction: 'MOVING_UP' | 'MOVING_DOWN' | 'STATIONARY'
    confidence: number
  }
  algorithm_type?: DetectionAlgorithm
}

// 检测统计信息类型
export interface DetectionStats {
  totalDetections: number
  motionDetectionRate: number
  runningTime: number
  lastDetectionTime?: number
  averageProcessingTime?: number
}

// 全局检测设置类型
export interface GlobalDetectionSettings {

  pauseThreshold: number   // 设备暂停阈值（秒）
  cooldownTime: number     // 检测冷却时间（秒）
}

// 检测控制事件类型
export interface DetectionControlEvents {
  'start': () => void
  'stop': () => void
  'mode-change': (mode: DetectionMode) => void
  'algorithm-change': (algorithm: DetectionAlgorithm) => void
  'settings-change': (settings: GlobalDetectionSettings) => void
}

// 检测信息组件属性类型
export interface DetectionInfoProps {
  isDetectionActive: boolean
  currentMode: DetectionMode
  currentAlgorithm: DetectionAlgorithm
  detectionResult?: DetectionResult
  detectionStats?: DetectionStats
  canChangeMode: boolean
  canControl: boolean
  showControls?: boolean  // 新增：是否显示控制按钮
  compact?: boolean       // 新增：紧凑模式
}

// ROI轮播状态类型
export interface ROICarouselState {
  currentIndex: number
  autoPlay: boolean
  interval: number
  totalROIs: number
}

// 检测模式配置类型
export interface DetectionModeConfig {
  mode: DetectionMode
  label: string
  description: string
  algorithms: {
    type: DetectionAlgorithm
    label: string
    description: string
  }[]
}

// 检测器配置类型
export interface DetectorConfig {
  type: DetectionAlgorithm
  enabled: boolean
  params: Record<string, any>
}

// ROI检测器配置类型
export interface ROIDetectorConfig {
  roiId: string
  roiName?: string
  roiType: 'yazhu' | 'pailiao'
  detector: DetectorConfig
  lastResult?: ROIDetectionResult
}
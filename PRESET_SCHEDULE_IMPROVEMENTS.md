# 预设检测计划组件改进方案

## 问题分析

根据用户反馈，原有的预设检测计划组件存在以下关键问题：

1. **缺乏全局状态管理**：无法确保同时只有一个检测模板处于执行状态
2. **状态持久化缺失**：关闭检测页面后，无法恢复之前的执行状态
3. **启动方式不统一**：检测看板启动方式不规范，缺乏弹窗机制
4. **状态同步问题**：预设计划和检测模板状态不同步

## 解决方案

### 1. 全局状态管理 (detection-state.ts)

创建了全局状态管理store，实现以下功能：

- **单例检测控制**：确保同时只能有一个检测模板运行
- **状态持久化**：使用localStorage保存检测状态，支持页面刷新后恢复
- **弹窗管理**：统一管理检测看板弹窗的打开和关闭
- **冲突检测**：启动新检测前自动检查并处理冲突

#### 核心接口：
```typescript
interface RunningDetectionState {
  templateId: string
  templateName: string
  scheduleId?: string
  startTime: string
  status: 'running' | 'paused' | 'stopped'
}

// 主要方法
startDetection(templateId, templateName, scheduleId?) // 启动检测
stopDetection() // 停止检测
pauseDetection() // 暂停检测
resumeDetection() // 恢复检测
canStartTemplate(templateId) // 检查是否可启动
```

### 2. 预设计划组件改进

#### usePresetSchedule.ts 改进：
- 集成全局状态管理
- 执行前检查冲突并提示用户
- 使用统一的弹窗启动机制
- 停止计划时同步停止检测

#### PresetScheduleView.vue 改进：
- 显示当前运行状态
- 实时显示运行时长
- 提供快速停止按钮
- 优化用户界面体验

### 3. 模板切换组件改进

#### TemplateSwitch.vue 改进：
- 状态切换前检查冲突
- 禁用运行中的模板时自动停止检测
- 启用模板前检查是否有其他检测运行
- 提供用户确认对话框

### 4. 检测看板改进

#### index.vue 改进：
- 集成全局状态恢复
- 优化自动启动逻辑
- 支持从预设计划和直接启动两种方式
- 窗口关闭时自动清理状态

## 技术特性

### 状态持久化
- 使用localStorage存储检测状态
- 自动过期机制（24小时）
- 页面刷新后状态恢复
- 异常情况下的状态清理

### 冲突处理
- 启动前自动检测冲突
- 用户友好的确认对话框
- 自动停止冲突的检测
- 状态同步更新

### 弹窗管理
- 统一的窗口打开机制
- 窗口引用管理
- 自动焦点处理
- 窗口关闭监听

### 用户体验
- 实时状态显示
- 运行时长计算
- 直观的操作反馈
- 错误处理和提示

## 使用方式

### 1. 启动检测
```typescript
// 从预设计划启动
const success = await detectionStateStore.startDetection(
  'template-id',
  'Template Name',
  'schedule-id'
)

// 直接启动
const success = await detectionStateStore.startDetection(
  'template-id',
  'Template Name'
)
```

### 2. 检查状态
```typescript
// 检查是否有检测运行
if (detectionStateStore.isDetectionRunning) {
  const current = detectionStateStore.currentRunningDetection
  console.log(`当前运行: ${current.templateName}`)
}

// 检查模板是否可启动
if (detectionStateStore.canStartTemplate('template-id')) {
  // 可以启动
}
```

### 3. 停止检测
```typescript
await detectionStateStore.stopDetection()
```

## 测试覆盖

创建了完整的单元测试：
- 状态管理功能测试
- 冲突检测测试
- 持久化功能测试
- 过期状态清理测试

## 部署说明

1. 确保所有依赖已安装
2. 运行测试确保功能正常
3. 部署到生产环境
4. 验证状态持久化功能

## 后续优化建议

1. **WebSocket集成**：实时同步检测状态
2. **多用户支持**：考虑多用户环境下的状态隔离
3. **性能优化**：大量计划时的性能优化
4. **监控告警**：添加检测异常监控
5. **日志记录**：详细的操作日志记录

## 总结

通过引入全局状态管理，我们成功解决了预设检测计划组件的核心问题：

✅ 确保同时只有一个检测模板运行
✅ 实现状态持久化和恢复
✅ 统一检测看板启动方式
✅ 提供良好的用户体验
✅ 完善的错误处理和冲突解决

这些改进大大提升了系统的可靠性和用户体验，为后续功能扩展奠定了坚实基础。

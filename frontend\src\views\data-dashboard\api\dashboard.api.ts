/**
 * @file dashboard.api.ts
 * @description
 * This file contains all the API call functions for the data dashboard.
 * It uses the generic 'get' function from the global api utility
 * and the types defined in 'dashboard.types.ts' to fetch data from the backend.
 */

import { get } from '@/utils/api'
import type {
  DashboardOverview,
  TrendData,
  CardDetectionResultList,
  DetectionGroup,
  DetectionGroupStatistics,
  DetectionTemplate,
  SystemPerformance,
  DeviceStatus,
  AlarmList,
  DetectionEfficiency
} from '../types/dashboard.types'

// 1. 核心指标面板 (MetricsPanel.vue)
/**
 * @description 获取看板概览数据
 */
export const getDashboardOverview = () => {
  return get<DashboardOverview>('/card-detection/dashboard/overview')
}

// 2. 趋势分析区 (TrendChart.vue)
/**
 * @description 获取趋势数据
 * @param days 获取最近几日的数据，默认为 7
 */
export const getTrends = (days = 7) => {
  return get<TrendData[]>(`/card-detection/trends/daily`, { params: { days } })
}

// 3. 检测详情 (DetectionLog.vue)
/**
 * @description 获取卡料检测结果列表
 * @param page 页码
 * @param pageSize 每页数量
 */
export const getDetectionLogs = (page = 1, pageSize = 10) => {
  return get<CardDetectionResultList>('/card-detection/', { params: { page, page_size: pageSize } })
}

// 4. 检测组状态监控 (GroupMonitor.vue)
/**
 * @description 获取所有检测组
 */
export const getDetectionGroups = () => {
  return get<DetectionGroup[]>('/detection-groups/')
}

/**
 * @description 获取指定检测组的统计信息
 * @param groupId 检测组ID
 */
export const getGroupStatistics = (groupId: number) => {
  return get<DetectionGroupStatistics>(`/card-detection/statistics/${groupId}`)
}

// 5. 智能报警中心 (AlarmCenter.vue)
/**
 * @description 获取报警列表
 * @param page 页码
 * @param limit 每页数量
 */
export const getAlarms = (page = 1, limit = 10) => {
  return get<AlarmList>('/alarms/', { params: { page, limit } })
}

// 6. 系统资源 (SystemResources.vue)
/**
 * @description 获取系统性能数据
 */
export const getSystemPerformance = () => {
  return get<SystemPerformance>('/admin/system-performance')
}

// 7. 实时状态栏 (StatusBar.vue)
/**
 * @description 获取当前运行的监测模板名称
 */
export const getCurrentRunningTemplate = () => {
  return get<DetectionTemplate[]>('/detection-templates/running/current')
}

/**
 * @description 获取设备状态
 */
export const getDeviceStatus = () => {
  return get<DeviceStatus>('/device/device-status')
}

// 8. 其他可能用到的API
/**
 * @description 获取检测效率分析
 */
export const getDetectionEfficiency = () => {
  return get<DetectionEfficiency>('/analytics/detection-efficiency')
} 
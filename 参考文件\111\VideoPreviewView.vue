<template>
  <div class="video-preview-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>视频预览</h1>
      <p class="page-description">选择视频源并进行实时预览，支持WebSDK设备连接、ROI绘制和运动检测功能</p>
    </div>

    <div class="preview-content">
      <!-- 视频源选择区域 -->
      <div class="video-source-section">
        <div class="source-controls">
          <label>选择视频源:</label>
          <select 
            v-model="selectedVideoSource" 
            @change="onVideoSourceChange"
            class="source-select"
            :disabled="isConnecting"
          >
            <option value="">请选择视频源</option>
            <option 
              v-for="source in videoSources" 
              :key="source.id" 
              :value="source.id"
            >
              {{ source.name }}
            </option>
          </select>
          <button 
            @click="refreshVideoSources" 
            class="refresh-btn"
            :disabled="isConnecting"
          >
            {{ isConnecting ? '连接中...' : '刷新列表' }}
          </button>
        </div>
      </div>

      <!-- 视频预览区域 -->
      <div class="video-preview-section">
        <div 
          ref="videoContainer" 
          class="video-container"
          :style="{
            width: '640px',
            height: '360px',
            cursor: (showROIDrawer && isDrawingEnabled && drawMode) ? 'crosshair' : 'default'
          }"
          @mousedown="onVideoMouseDown"
          @mousemove="onVideoMouseMove"
          @mouseup="onVideoMouseUp"
          @dblclick="onVideoDoubleClick"
        >
          <!-- WebSDK视频播放器容器 -->
          <div id="playWnd" class="video-player"></div>
          
          <!-- ROI显示画布 -->
          <canvas 
            v-if="showROIDrawer"
            ref="roiDisplayCanvas"
            class="roi-display-canvas"
            width="640"
            height="360"
          ></canvas>
          
          <!-- 运动检测叠加层 -->
          <div v-if="showMotionDetection && detectionResult" class="motion-overlay">
            <!-- 这里可以显示运动检测结果 -->
          </div>
        </div>
      </div>

      <!-- 控制按钮区域 -->
      <div class="control-section">
        <button 
          @click="startPreview" 
          :disabled="!selectedVideoSource || isConnecting || isPreviewActive"
          class="control-btn primary"
        >
          {{ isConnecting ? '连接中...' : '开始预览' }}
        </button>
        
        <button 
          @click="stopPreview" 
          :disabled="!isPreviewActive"
          class="control-btn danger"
        >
          停止预览
        </button>
        
        <button 
          @click="toggleMotionDetection" 
          :disabled="!selectedVideoSource"
          :class="['control-btn', showMotionDetection ? 'active' : 'secondary']"
        >
          {{ showMotionDetection ? '停止检测' : '运动检测' }}
        </button>
        
        <button 
          @click="toggleROIDrawer" 
          :class="['control-btn', showROIDrawer ? 'active' : 'secondary']"
        >
          {{ showROIDrawer ? '关闭ROI' : 'ROI工具' }}
        </button>
        
        <button 
          @click="debugWebSDK" 
          class="control-btn secondary"
        >
          调试信息
        </button>
        
        <button 
          @click="testWebSocketSupport" 
          :disabled="!selectedVideoSource"
          class="control-btn secondary"
        >
          测试WebSocket
        </button>
      </div>

      <!-- ROI绘制工具栏 -->
      <div v-if="showROIDrawer" class="roi-toolbar-section">
        <div class="roi-toolbar">
          <div class="toolbar-title">
            <h4>🎯 ROI绘制工具</h4>
          </div>
          <div class="toolbar-controls">
            <button 
              @click="toggleDrawMode" 
              :class="['tool-btn', 'toggle-btn', isDrawingEnabled ? 'active' : '']"
            >
              {{ isDrawingEnabled ? '🔓 解锁绘制' : '🔒 锁定绘制' }}
            </button>
            
            <button 
              @click="setDrawMode('rectangle')" 
              :disabled="!isDrawingEnabled"
              :class="['tool-btn', drawMode === 'rectangle' ? 'active' : '']"
            >
              📐 矩形
            </button>
            
            <button 
              @click="setDrawMode('polygon')" 
              :disabled="!isDrawingEnabled"
              :class="['tool-btn', drawMode === 'polygon' ? 'active' : '']"
            >
              🔷 多边形
            </button>
            
            <button @click="clearROIs" class="tool-btn clear-btn">
              🗑️ 清空
            </button>
            
            <button @click="saveROIs" class="tool-btn save-btn">
              💾 保存
            </button>
            
            <button @click="exportROIs" class="tool-btn export-btn">
              📤 导出
            </button>
            
            <button @click="importROIs" class="tool-btn import-btn">
              📥 导入
            </button>
          </div>
        </div>
        
        <!-- ROI管理面板 -->
        <div class="roi-management-section">
          <div class="roi-list-panel">
            <div class="panel-header">
              <h4>ROI区域管理</h4>
              <span :class="[
                'panel-status',
                !showROIDrawer ? 'status-disabled' :
                !isDrawingEnabled ? 'status-locked' :
                !drawMode ? 'status-ready' : 'status-drawing'
              ]">
                {{ 
                  !showROIDrawer ? '已禁用' :
                  !isDrawingEnabled ? '已锁定' :
                  !drawMode ? '就绪' : 
                  drawMode === 'rectangle' ? '绘制矩形' : '绘制多边形'
                }}
              </span>
            </div>
            
            <div class="panel-content">
              <div class="roi-list">
                <div v-if="roiList.length === 0" class="empty-message">
                  <div class="empty-icon">🎯</div>
                  <div class="empty-text">
                    <p>暂无ROI区域</p>
                    <small>启用绘制模式并选择工具开始绘制</small>
                  </div>
                </div>
                
                <div 
                  v-for="(roi, index) in roiList" 
                  :key="roi.id" 
                  class="roi-item"
                >
                  <div class="roi-info">
                    <div 
                      class="roi-color" 
                      :style="{ backgroundColor: roi.color }"
                    ></div>
                    <div class="roi-details">
                      <div class="roi-name">{{ roi.name }}</div>
                      <div class="roi-type">{{ roi.type === 'rectangle' ? '矩形' : '多边形' }}</div>
                      <div class="roi-points">{{ roi.points.length }} 个点</div>
                    </div>
                  </div>
                  <div class="roi-actions">
                    <button @click="editROI(index)" class="edit-btn" title="编辑">
                      ✏️
                    </button>
                    <button @click="deleteROI(index)" class="delete-btn" title="删除">
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
              
              <div class="help-section">
                <div class="help-header">💡 使用说明</div>
                <div class="help-content">
                  <ol>
                    <li>点击"解锁绘制"启用绘制模式</li>
                    <li>选择"矩形"或"多边形"工具</li>
                    <li>在视频区域点击开始绘制</li>
                    <li>矩形：点击并拖拽；多边形：多次点击，双击结束</li>
                    <li>使用"保存"、"导出"、"导入"管理ROI配置</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作信息区域 -->
      <div class="operation-info-section">
        <div class="info-header">
          <h3>操作日志</h3>
          <button @click="clearOperationInfo" class="clear-btn">清空</button>
        </div>
        <div class="info-content">
          <div 
            v-for="(info, index) in operationInfo" 
            :key="index" 
            class="info-item"
          >
            <span class="info-time">{{ info.time }}</span>
            <span class="info-message">{{ info.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 引入外部CSS文件
import './VideoPreviewView.css'

// 类型定义
interface VideoSource {
  id: string
  name: string
  description: string
  ip: string
  port: number
  username: string
  password: string
  protocol: string
  channelId: number
  streamType: number
  rtspPort?: string
}

interface ROI {
  id: string
  name: string
  type: 'rectangle' | 'polygon'
  points: Array<{ x: number; y: number }>
  color: string
}

interface OperationInfo {
  time: string
  message: string
}

// 响应式数据
const videoSources = ref<VideoSource[]>([])
const selectedVideoSource = ref<string>('')
const isConnecting = ref(false)
const isPreviewActive = ref(false)
const operationInfo = ref<OperationInfo[]>([])

// WebSDK相关
let webVideoCtrl: any = null
let isWebSDKInitialized = false
let currentDevice: any = null
let currentChannel: any = null
const g_iWndIndex = 0 // 播放窗口索引

// 运动检测相关
const showMotionDetection = ref(false)
const detectionResult = ref<any>(null)
let motionDetectionWS: WebSocket | null = null

// ROI绘制相关
const showROIDrawer = ref(false)
const isDrawingEnabled = ref(false)
const drawMode = ref<'rectangle' | 'polygon' | null>(null)
const isDrawing = ref(false)
const currentROI = ref<ROI | null>(null)
const startPoint = ref<{ x: number; y: number } | null>(null)
const polygonPoints = ref<Array<{ x: number; y: number }>>([])
const roiList = ref<ROI[]>([])
const colors = ['#ff4757', '#2ed573', '#1e90ff', '#ffa502', '#ff6b81', '#70a1ff', '#5352ed', '#ff9ff3']
let colorIndex = 0

// DOM引用
const videoContainer = ref<HTMLElement | null>(null)
const roiDisplayCanvas = ref<HTMLCanvasElement | null>(null)

// 工具函数
const addOperationInfo = (message: string) => {
  const now = new Date()
  const timeStr = now.toLocaleTimeString()
  operationInfo.value.unshift({
    time: timeStr,
    message
  })
  
  // 限制日志数量
  if (operationInfo.value.length > 100) {
    operationInfo.value = operationInfo.value.slice(0, 100)
  }
}

const clearOperationInfo = () => {
  operationInfo.value = []
}

const getWebVideoCtrl = () => {
  return (window as any).WebVideoCtrl || webVideoCtrl
}

// WebSDK初始化
const initWebSDK = async (): Promise<boolean> => {
  return new Promise((resolve) => {
    addOperationInfo('[INIT] 开始初始化WebSDK...')
    
    // 检查WebSDK是否已加载
    const WebVideoCtrl = (window as any).WebVideoCtrl
    if (!WebVideoCtrl) {
      addOperationInfo('[ERROR] WebSDK脚本未加载，请检查页面配置')
      resolve(false)
      return
    }

    webVideoCtrl = WebVideoCtrl
    addOperationInfo('[INIT] WebSDK脚本已加载')

    // 初始化WebSDK插件
    const initResult = webVideoCtrl.I_InitPlugin({
      bWndFull: true,     // 是否全屏
      iWndowType: 1,      // 窗口类型
      cbInitPluginComplete: () => {
        addOperationInfo('[INIT] WebSDK插件初始化完成')
        isWebSDKInitialized = true
        
        // 插入播放窗口
        const insertResult = webVideoCtrl.I_InsertOBJECTPlugin('playWnd')
        if (insertResult === -1) {
          addOperationInfo('[INIT] 播放窗口已存在，跳过创建')
        } else if (insertResult === 0) {
          addOperationInfo('[INIT] 播放窗口创建成功')
        } else {
          addOperationInfo(`[WARNING] 播放窗口创建返回码: ${insertResult}`)
        }
        
        resolve(true)
      }
    })

    if (initResult === -1) {
      addOperationInfo('[INIT] WebSDK插件已初始化过')
      isWebSDKInitialized = true
      
      // 尝试插入播放窗口
      const insertResult = webVideoCtrl.I_InsertOBJECTPlugin('playWnd')
      if (insertResult === -1) {
        addOperationInfo('[INIT] 播放窗口已存在')
      }
      
      resolve(true)
    } else if (initResult !== 0) {
      addOperationInfo(`[ERROR] WebSDK插件初始化失败，错误码: ${initResult}`)
      resolve(false)
    }
  })
}

// 刷新视频源列表
const refreshVideoSources = async () => {
  addOperationInfo('[API] 正在获取视频源列表...')
  
  try {
    const token = localStorage.getItem('token')

    const response = await fetch('/api/video-sources/', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      }
    })

    if (response.ok) {
      const data = await response.json()
      // 只显示WebSDK设备类型的视频源
      const websdkSources = data.filter((source: any) => source.source_type === 'websdk_device')

      // 转换数据格式以适配WebSDK登录需求
      videoSources.value = websdkSources.map((source: any) => ({
        id: source.id.toString(),
        name: source.name || (source.description + ' - ' + source.device_ip),
        description: source.description || '',
        ip: source.device_ip,
        port: source.device_port || 80,
        username: source.device_username || 'admin',
        password: source.device_password || '',
        protocol: source.device_protocol || 'HTTP',
        channelId: source.channel_id || 1,
        streamType: source.stream_type || 1
      }))

      console.log('获取到WebSDK视频源列表:', videoSources.value)
      ElMessage.success(`已加载 ${videoSources.value.length} 个WebSDK设备`)
      addOperationInfo(`[API] 成功加载 ${videoSources.value.length} 个WebSDK设备`)
    } else {
      console.error('获取视频源失败:', response.status, response.statusText)

      if (response.status === 401) {
        ElMessage.warning('请先登录系统后再获取视频源列表')
        addOperationInfo('[WARNING] 请先登录系统后再获取视频源列表')
        // 提供一个临时的测试视频源
        videoSources.value = [
          {
            id: 'temp-1',
            name: '临时测试设备 - ************',
            description: '临时测试用，请先登录系统获取真实设备列表',
            ip: '************',
            port: 80,
            username: 'admin',
            password: 'admin123',  // 使用更常见的默认密码
            protocol: 'HTTP',
            channelId: 1,
            streamType: 1
          },
          {
            id: 'temp-2',
            name: '临时测试设备 - *************',
            description: '备用测试设备',
            ip: '*************',
            port: 80,
            username: 'admin',
            password: '12345',
            protocol: 'HTTP',
            channelId: 1,
            streamType: 1
          }
        ]
        addOperationInfo('[INFO] 已提供临时测试设备，请先登录系统获取完整设备列表')
      } else {
        ElMessage.error('获取视频源列表失败')
        addOperationInfo('[ERROR] 获取视频源列表失败')
        videoSources.value = []
      }
    }
  } catch (error) {
    console.error('刷新视频源失败:', error)
    ElMessage.error('网络错误，无法获取视频源列表')
    addOperationInfo('[ERROR] 网络错误，无法获取视频源列表')

    // 如果网络错误，提供临时测试设备
    videoSources.value = [
      {
        id: 'temp-1',
        name: '临时测试设备 - ************',
        description: '网络错误时的临时测试设备',
        ip: '************',
        port: 80,
        username: 'admin',
        password: 'admin123',
        protocol: 'HTTP',
        channelId: 1,
        streamType: 1
      }
    ]
    addOperationInfo('[INFO] 网络错误，已提供临时测试设备')
  }
}

// 视频源选择变化处理 - 自动执行完整流程
const onVideoSourceChange = async () => {
  if (!selectedVideoSource.value) {
    addOperationInfo('[SOURCE] 清空视频源选择')
    return
  }

  const source = videoSources.value.find(s => s.id === selectedVideoSource.value)
  if (!source) {
    addOperationInfo('[ERROR] 未找到选择的视频源')
    return
  }

  console.log('选择视频源:', selectedVideoSource.value)
  addOperationInfo(`[SOURCE] 选择视频源: ${source.name} (${source.ip}:${source.port})`)
  addOperationInfo('[AUTO] 开始自动化流程: 登录设备 → 获取通道 → 开始预览')

  // 如果当前正在预览，先停止
  if (isPreviewActive.value) {
    addOperationInfo('[AUTO] 检测到正在预览，先停止当前预览')
    stopPreview()
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  // 自动开始预览
  setTimeout(() => {
    startPreview()
  }, 100)
}

// 设备登录 - 完全基于WebSDKLogin组件的正确实现
const loginDevice = async (source: any): Promise<boolean> => {
  return new Promise((resolve) => {
    const ctrl = getWebVideoCtrl()
    if (!ctrl) {
      addOperationInfo('[ERROR] WebSDK未初始化，无法登录设备')
      resolve(false)
      return
    }

    const szIP = source.ip
    const szPort = source.port
    const szUsername = source.username
    const szPassword = source.password
    const szDeviceIdentify = `${szIP}_${szPort}`
    const protocol = source.protocol === 'HTTPS' ? 2 : 1

    addOperationInfo(`[LOGIN] 正在连接设备: ${szIP}:${szPort} (${source.protocol})`)

    // 保存登录信息
    sessionStorage.setItem('loginip', szIP)
    sessionStorage.setItem('password', szPassword)
    localStorage.setItem('currentWebSDKDevice', JSON.stringify({
      ip: szIP,
      port: szPort,
      username: szUsername,
      password: szPassword
    }))

    // 调用WebSDK登录API - 完全基于demo.js的实现
    const iRet = ctrl.I_Login(szIP, protocol, parseInt(szPort, 10), szUsername, szPassword, {
      success: (xmlDoc: any) => {
        console.log('设备登录成功:', xmlDoc)
        addOperationInfo(`[LOGIN] 设备登录成功: ${szIP}`)

        // 解析登录成功的XML响应
        try {
          const parser = new DOMParser()
          const doc = parser.parseFromString(xmlDoc, 'text/xml')
          const sessionID = doc.getElementsByTagName('sessionID')[0]?.textContent
          if (sessionID) {
            addOperationInfo(`[LOGIN] 获得会话ID: ${sessionID}`)
          }
        } catch (parseError) {
          console.warn('解析登录响应XML失败:', parseError)
        }

        currentDevice = {
          id: szDeviceIdentify,
          ip: szIP,
          port: szPort,
          username: szUsername,
          password: szPassword,
          protocol: source.protocol,
          xmlDoc
        }

        // 登录成功后获取通道信息和设备端口
        addOperationInfo('[LOGIN] 登录成功，正在获取通道信息...')
        setTimeout(() => {
          getChannelInfo(szDeviceIdentify, source.channelId)
        }, 100) // 增加延迟确保登录完全完成

        addOperationInfo('[LOGIN] 设备连接完成，可以开始预览')
        resolve(true)
      },
      error: (status: any, xmlDoc: any) => {
        console.error('设备登录失败:', status, xmlDoc)

        // 解析错误信息 - 基于demo.js的错误处理
        let errorMsg = `登录失败 (状态码: ${status})`
        try {
          if (xmlDoc) {
            const parser = new DOMParser()
            const doc = parser.parseFromString(xmlDoc, 'text/xml')
            const statusString = doc.getElementsByTagName('statusString')[0]?.textContent
            const subStatusCode = doc.getElementsByTagName('subStatusCode')[0]?.textContent

            if (statusString) {
              errorMsg = `登录失败: ${statusString}`
            }
            if (subStatusCode) {
              errorMsg += ` (子状态码: ${subStatusCode})`
            }
          }
        } catch (parseError) {
          console.warn('解析错误响应XML失败:', parseError)
        }

        addOperationInfo(`[ERROR] ${errorMsg}`)
        resolve(false)
      }
    })

    if (iRet === -1) {
      addOperationInfo(`[LOGIN] 设备 ${szDeviceIdentify} 已登录过！`)
      resolve(true)
    }
  })
}

// 获取通道信息 - 完全基于WebSDKLogin组件的正确实现
const getChannelInfo = (deviceIdentify: string, targetChannelId: number) => {
  const ctrl = getWebVideoCtrl()
  if (!ctrl) {
    addOperationInfo('[ERROR] WebSDK不可用，无法获取通道信息')
    return
  }

  let hasFoundChannel = false
  addOperationInfo('[CHANNEL] 正在获取通道信息（优先选择模拟通道）...')

  // 获取模拟通道信息 - 基于demo.js的实现
  ctrl.I_GetAnalogChannelInfo(deviceIdentify, {
    async: false,
    success: (xmlDoc: any) => {
      console.log('获取模拟通道信息成功:', xmlDoc)
      addOperationInfo('[CHANNEL] 成功获取模拟通道信息')

      if (!hasFoundChannel) {
        try {
          // 使用jQuery解析XML，与demo.js保持一致
          if (typeof $ !== 'undefined') {
            const $doc = $(xmlDoc)
            const channels = $doc.find('VideoInputChannel')

            addOperationInfo(`[CHANNEL] 找到 ${channels.length} 个模拟通道`)

            if (channels.length > 0) {
              // 查找指定通道或使用第一个通道
              let targetChannel = null
              channels.each(function(index) {
                const channelId = $(this).find('id').text()
                if (channelId && parseInt(channelId) === targetChannelId) {
                  targetChannel = $(this)
                  return false // 跳出循环
                }
              })

              if (!targetChannel) {
                targetChannel = channels.eq(0) // 使用第一个通道
              }

              if (targetChannel && targetChannel.length > 0) {
                const channelId = targetChannel.find('id').text()
                let channelName = targetChannel.find('name').text()

                // 如果名称为空，使用默认名称格式
                if (!channelName) {
                  const index = parseInt(channelId || '1') - 1
                  channelName = 'Camera ' + (index < 9 ? '0' + (index + 1) : (index + 1))
                }

                if (channelId) {
                  currentChannel = {
                    id: channelId,
                    name: channelName,
                    channelNo: channelId,
                    type: 'analog',
                    deviceId: deviceIdentify
                  }
                  hasFoundChannel = true
                  addOperationInfo(`[CHANNEL] 自动选择模拟通道: ${channelName} (ID: ${channelId})`)
                  console.log('自动选择模拟通道:', currentChannel)
                }
              }
            }
          } else {
            // 如果jQuery不可用，使用原生DOM解析
            const parser = new DOMParser()
            const doc = parser.parseFromString(xmlDoc, 'text/xml')
            const channels = doc.querySelectorAll('VideoInputChannel')

            addOperationInfo(`[CHANNEL] 找到 ${channels.length} 个模拟通道`)

            if (channels.length > 0) {
              let targetChannel = null
              for (const channel of channels) {
                const channelId = channel.querySelector('id')?.textContent
                if (channelId && parseInt(channelId) === targetChannelId) {
                  targetChannel = channel
                  break
                }
              }

              if (!targetChannel) {
                targetChannel = channels[0]
              }

              if (targetChannel) {
                const channelId = targetChannel.querySelector('id')?.textContent
                let channelName = targetChannel.querySelector('name')?.textContent

                if (!channelName) {
                  const index = parseInt(channelId || '1') - 1
                  channelName = 'Camera ' + (index < 9 ? '0' + (index + 1) : (index + 1))
                }

                if (channelId) {
                  currentChannel = {
                    id: channelId,
                    name: channelName,
                    channelNo: channelId,
                    type: 'analog',
                    deviceId: deviceIdentify
                  }
                  hasFoundChannel = true
                  addOperationInfo(`[CHANNEL] 自动选择模拟通道: ${channelName} (ID: ${channelId})`)
                  console.log('自动选择模拟通道:', currentChannel)
                }
              }
            }
          }
        } catch (error) {
          console.error('解析模拟通道信息失败:', error)
          addOperationInfo('[ERROR] 解析模拟通道信息失败')
        }
      }
    },
    error: (status: any, xmlDoc: any) => {
      console.warn('获取模拟通道信息失败:', { status, xmlDoc })
      addOperationInfo('[WARNING] 获取模拟通道信息失败')
    }
  })

  // 获取数字通道信息 - 基于demo.js的实现
  ctrl.I_GetDigitalChannelInfo(deviceIdentify, {
    async: false,
    success: (xmlDoc: any) => {
      console.log('获取数字通道信息成功:', xmlDoc)
      addOperationInfo('[CHANNEL] 成功获取数字通道信息')

      if (!hasFoundChannel) {
        try {
          // 使用jQuery解析XML，与demo.js保持一致 - 修复：使用正确的XML节点名称
          if (typeof $ !== 'undefined') {
            const $doc = $(xmlDoc)
            // 修复：数字通道的正确XML节点名称是 InputProxyChannelStatus
            const channels = $doc.find('InputProxyChannelStatus')

            addOperationInfo(`[CHANNEL] 找到 ${channels.length} 个数字通道`)

            if (channels.length > 0) {
              // 查找指定通道或使用第一个通道
              let targetChannel = null
              channels.each(function(index) {
                const channelId = $(this).find('id').text()
                const online = $(this).find('online').text() // 检查通道是否在线

                // 过滤离线的数字通道，与demo.js保持一致
                if (online === "false") {
                  return true // 继续下一个
                }

                if (channelId && parseInt(channelId) === targetChannelId) {
                  targetChannel = $(this)
                  return false // 跳出循环
                }
              })

              if (!targetChannel) {
                // 如果没找到指定通道，使用第一个在线通道
                channels.each(function(index) {
                  const online = $(this).find('online').text()
                  if (online !== "false") {
                    targetChannel = $(this)
                    return false // 跳出循环
                  }
                })
              }

              if (targetChannel && targetChannel.length > 0) {
                const channelId = targetChannel.find('id').text()
                let channelName = targetChannel.find('name').text()

                // 如果名称为空，使用默认名称格式，与demo.js保持一致
                if (!channelName) {
                  const index = parseInt(channelId || '1') - 1
                  channelName = 'IPCamera ' + (index < 9 ? '0' + (index + 1) : (index + 1))
                }

                if (channelId) {
                  currentChannel = {
                    id: channelId,
                    name: channelName,
                    channelNo: channelId,
                    type: 'digital',
                    deviceId: deviceIdentify
                  }
                  hasFoundChannel = true
                  addOperationInfo(`[CHANNEL] 自动选择数字通道: ${channelName} (ID: ${channelId})`)
                  console.log('自动选择数字通道:', currentChannel)
                }
              }
            }
          } else {
            // 如果jQuery不可用，使用原生DOM解析
            const parser = new DOMParser()
            const doc = parser.parseFromString(xmlDoc, 'text/xml')
            // 修复：使用正确的XML节点名称
            const channels = doc.querySelectorAll('InputProxyChannelStatus')

            addOperationInfo(`[CHANNEL] 找到 ${channels.length} 个数字通道`)

            if (channels.length > 0) {
              let targetChannel = null
              for (const channel of channels) {
                const channelId = channel.querySelector('id')?.textContent
                const online = channel.querySelector('online')?.textContent

                // 过滤离线的数字通道
                if (online === "false") {
                  continue
                }

                if (channelId && parseInt(channelId) === targetChannelId) {
                  targetChannel = channel
                  break
                }
              }

              if (!targetChannel) {
                // 使用第一个在线通道
                for (const channel of channels) {
                  const online = channel.querySelector('online')?.textContent
                  if (online !== "false") {
                    targetChannel = channel
                    break
                  }
                }
              }

              if (targetChannel) {
                const channelId = targetChannel.querySelector('id')?.textContent
                let channelName = targetChannel.querySelector('name')?.textContent

                if (!channelName) {
                  const index = parseInt(channelId || '1') - 1
                  channelName = 'IPCamera ' + (index < 9 ? '0' + (index + 1) : (index + 1))
                }

                if (channelId) {
                  currentChannel = {
                    id: channelId,
                    name: channelName,
                    channelNo: channelId,
                    type: 'digital',
                    deviceId: deviceIdentify
                  }
                  hasFoundChannel = true
                  addOperationInfo(`[CHANNEL] 自动选择数字通道: ${channelName} (ID: ${channelId})`)
                  console.log('自动选择数字通道:', currentChannel)
                }
              }
            }
          }
        } catch (error) {
          console.error('解析数字通道信息失败:', error)
          addOperationInfo('[ERROR] 解析数字通道信息失败')
        }
      }
    },
    error: (status: any, xmlDoc: any) => {
      console.warn('获取数字通道信息失败:', { status, xmlDoc })
      addOperationInfo('[WARNING] 获取数字通道信息失败')
    }
  })

  // 获取零通道信息（如果需要）
  ctrl.I_GetZeroChannelInfo(deviceIdentify, {
    async: false,
    success: (xmlDoc: any) => {
      console.log('获取零通道信息成功:', xmlDoc)
      addOperationInfo('[CHANNEL] 成功获取零通道信息')
    },
    error: (status: any, xmlDoc: any) => {
      console.warn('获取零通道信息失败:', { status, xmlDoc })
    }
  })

  // 如果没有找到任何通道，使用默认值
  setTimeout(() => {
    if (!hasFoundChannel) {
      const defaultChannelId = targetChannelId || 1
      currentChannel = {
        id: defaultChannelId.toString(),
        name: '默认通道' + defaultChannelId,
        channelNo: defaultChannelId.toString(),
        type: 'default',
        deviceId: deviceIdentify
      }
      addOperationInfo('[CHANNEL] 未找到通道信息，使用默认通道: ' + defaultChannelId)
      console.log('使用默认通道:', currentChannel)
    }
    addOperationInfo('[CHANNEL] 通道配置完成，当前选择通道: ' + (currentChannel?.name || '未知'))
  }, 300) // 增加延迟确保所有通道查询完成
}

// 开始预览 - 完全基于WebSDKPlayer组件的正确实现
const startPreview = async () => {
  if (!selectedVideoSource.value) {
    ElMessage.warning('请先选择视频源')
    addOperationInfo('[WARNING] 请先选择视频源')
    return
  }

  const source = videoSources.value.find(s => s.id === selectedVideoSource.value)
  if (!source) {
    ElMessage.warning('视频源不存在')
    addOperationInfo('[ERROR] 视频源不存在')
    return
  }

  isConnecting.value = true
  addOperationInfo('[PREVIEW] 开始连接设备...')

  try {
    // 1. 登录设备
    addOperationInfo('[PREVIEW] 正在登录设备...')
    const loginSuccess = await loginDevice(source)
    if (!loginSuccess) {
      isConnecting.value = false
      addOperationInfo('[ERROR] 设备登录失败，无法开始预览')
      return
    }

    // 等待通道信息获取完成 - 增加等待时间确保通道信息完全获取
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (!currentDevice || !currentChannel) {
      addOperationInfo('[ERROR] 获取设备通道信息失败，无法开始预览')
      addOperationInfo(`[DEBUG] currentDevice: ${currentDevice ? '已设置' : '未设置'}, currentChannel: ${currentChannel ? '已设置' : '未设置'}`)
      isConnecting.value = false
      return
    }

    // 2. 开始预览
    const ctrl = getWebVideoCtrl()
    if (!ctrl) {
      addOperationInfo('[ERROR] WebSDK不可用，无法开始预览')
      isConnecting.value = false
      return
    }

    // 2. 开始预览
    const szDeviceIdentify = currentDevice.id
    const iChannelID = parseInt(currentChannel.channelNo || currentChannel.id)
    const iStreamType = 1 // 自动选择主码流
    const bZeroChannel = currentChannel.type === 'zero'

    // 关键修复：先获取设备端口信息，与demo.js保持一致
    addOperationInfo('[PREVIEW] 正在获取设备端口信息...')
    const oPort = ctrl.I_GetDevicePort(szDeviceIdentify)
    let iRtspPort = parseInt(source.rtspPort || '554', 10) // 默认值

    if (oPort != null) {
      iRtspPort = oPort.iRtspPort || iRtspPort
      addOperationInfo(`[PREVIEW] 获取设备端口成功: RTSP端口=${iRtspPort}, 设备端口=${oPort.iDevicePort}`)
    } else {
      addOperationInfo(`[PREVIEW] 获取设备端口失败，使用默认RTSP端口: ${iRtspPort}`)
    }

    addOperationInfo(`[PREVIEW] 开始播放通道 ${iChannelID} (${currentChannel.name}) - 主码流`)
    addOperationInfo(`[DEBUG] 播放参数: 设备=${szDeviceIdentify}, 通道=${iChannelID}, 码流=${iStreamType}, RTSP端口=${iRtspPort}`)

    // 定义预览函数，严格按照编程指南的参数结构
    const startRealPlay = () => {
      // 根据编程指南：明确传递所有参数，使用正确的参数名称
      addOperationInfo('[PREVIEW] 尝试直连模式 (bProxy: false)...')
      addOperationInfo(`[DEBUG] 参数详情: 设备=${szDeviceIdentify}, 窗口=${g_iWndIndex}, 通道=${iChannelID}, 码流=${iStreamType}, RTSP端口=${iRtspPort}`)

      ctrl.I_StartRealPlay(szDeviceIdentify, {
        iWndIndex: g_iWndIndex,    // 明确指定播放窗口
        iStreamType: iStreamType,   // 码流类型
        iChannelID: iChannelID,     // 播放通道号
        bZeroChannel: bZeroChannel, // 是否播放零通道
        iRtspPort: iRtspPort,      // 修复：使用demo.js中的正确参数名 iRtspPort
        bProxy: false,             // 先尝试直连模式
        success: () => {
          console.log('直连模式预览成功')
          addOperationInfo('[PREVIEW] ✅ 直连模式预览成功')
          isPreviewActive.value = true
          isConnecting.value = false
          ElMessage.success('预览成功（直连模式）')
        },
        error: (status: any, xmlDoc: any) => {
          console.error('直连模式失败:', { status, xmlDoc })

          // 详细的错误信息解析
          let errorDetail = `状态码: ${status || 'undefined'}`
          if (xmlDoc) {
            try {
              const parser = new DOMParser()
              const doc = parser.parseFromString(xmlDoc, 'text/xml')
              const statusString = doc.getElementsByTagName('statusString')[0]?.textContent
              const subStatusCode = doc.getElementsByTagName('subStatusCode')[0]?.textContent
              if (statusString) errorDetail += `, 错误: ${statusString}`
              if (subStatusCode) errorDetail += `, 子状态: ${subStatusCode}`
            } catch (e) {
              console.warn('解析错误XML失败:', e)
            }
          }

          addOperationInfo(`[PREVIEW] ❌ 直连模式失败: ${errorDetail}`)

          // 特殊处理undefined状态码
          if (status === undefined || status === null) {
            addOperationInfo('[ERROR] 状态码为undefined，可能的原因：')
            addOperationInfo('[ERROR] 1. 设备不支持WebSocket协议')
            addOperationInfo('[ERROR] 2. 设备固件版本过低')
            addOperationInfo('[ERROR] 3. 网络连接问题或防火墙阻止')
            addOperationInfo('[ERROR] 4. 设备WebSocket服务未启用')
            addOperationInfo('[INFO] 建议：检查设备型号和固件版本，确认是否支持WebSocket')
          }

          if (status === 403 || status === undefined) {
            // 根据编程指南：某些设备在HTTP下也需要代理模式
            addOperationInfo('[PREVIEW] 设备不支持直连，尝试代理模式 (bProxy: true)...')
            addOperationInfo('[INFO] 根据编程指南：某些设备在HTTP环境下需要代理模式')

            ctrl.I_StartRealPlay(szDeviceIdentify, {
              iWndIndex: g_iWndIndex,    // 明确指定播放窗口
              iStreamType: iStreamType,   // 码流类型
              iChannelID: iChannelID,     // 播放通道号
              bZeroChannel: bZeroChannel, // 是否播放零通道
              iRtspPort: iRtspPort,      // 修复：使用demo.js中的正确参数名 iRtspPort
              bProxy: true, // 使用代理模式
              success: () => {
                console.log('代理模式预览成功')
                addOperationInfo('[PREVIEW] ✅ 代理模式预览成功')
                addOperationInfo('[INFO] 建议在nginx配置中添加WebSocket代理规则')
                isPreviewActive.value = true
                isConnecting.value = false
                ElMessage.success('预览成功（代理模式）')
              },
              error: (status2: any, xmlDoc2: any) => {
                console.error('代理模式也失败:', { status2, xmlDoc2 })

                // 解析代理模式错误
                let proxyErrorDetail = `状态码: ${status2}`
                if (xmlDoc2) {
                  try {
                    const parser = new DOMParser()
                    const doc = parser.parseFromString(xmlDoc2, 'text/xml')
                    const statusString = doc.getElementsByTagName('statusString')[0]?.textContent
                    if (statusString) proxyErrorDetail += `, 错误: ${statusString}`
                  } catch (e) {
                    console.warn('解析代理错误XML失败:', e)
                  }
                }

                addOperationInfo(`[ERROR] 代理模式也失败: ${proxyErrorDetail}`)
                addOperationInfo('[ERROR] 设备不支持WebSocket取流，请检查以下项目：')
                addOperationInfo('[ERROR] 1. 设备固件版本是否支持WebSocket')
                addOperationInfo('[ERROR] 2. 设备网络配置是否正确')
                addOperationInfo('[ERROR] 3. 是否需要nginx代理配置')
                addOperationInfo('[ERROR] 4. 防火墙是否阻止了WebSocket连接')
                ElMessage.error('设备不支持WebSocket取流')
                isConnecting.value = false
              }
            })
          } else {
            addOperationInfo(`[ERROR] 预览失败: ${errorDetail}`)
            addOperationInfo('[ERROR] 请检查设备连接和网络配置')
            ElMessage.error(`预览失败: ${status}`)
            isConnecting.value = false
          }
        }
      })
    }

    // 检查当前窗口状态，如果正在播放则先停止，与demo.js保持一致
    const currentWndInfo = ctrl.I_GetWindowStatus(g_iWndIndex)
    if (currentWndInfo && currentWndInfo.bPlaying) {
      addOperationInfo('[PREVIEW] 当前窗口正在播放，先停止...')
      ctrl.I_Stop({
        success: () => {
          addOperationInfo('[PREVIEW] 停止成功，开始新的预览')
          startRealPlay()
        },
        error: () => {
          addOperationInfo('[ERROR] 停止当前播放失败')
          isConnecting.value = false
        }
      })
    } else {
      startRealPlay()
    }

  } catch (error) {
    console.error('开始预览异常:', error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    addOperationInfo(`[ERROR] 开始预览异常: ${errorMessage}`)
    ElMessage.error(`开始预览失败: ${errorMessage}`)
    isConnecting.value = false
  }
}

// 停止预览 - 完全基于WebSDKPlayer组件的正确实现
const stopPreview = () => {
  const ctrl = getWebVideoCtrl()
  if (!ctrl) {
    ElMessage.error('WebSDK未初始化')
    addOperationInfo('[ERROR] WebSDK未初始化，无法停止预览')
    return
  }

  try {
    addOperationInfo('[PREVIEW] 正在停止预览...')

    // 检查当前窗口状态
    const currentWndInfo = ctrl.I_GetWindowStatus(g_iWndIndex)
    if (currentWndInfo && currentWndInfo.bPlaying) {
      addOperationInfo('[PREVIEW] 检测到正在播放的视频，正在停止...')

      // 停止预览 - 基于demo.js的实现
      const iRet = ctrl.I_Stop()

      if (iRet === 0) {
        addOperationInfo('[PREVIEW] 视频预览已停止')
        ElMessage.success('视频预览已停止')
      } else {
        addOperationInfo(`[WARNING] 停止预览返回码: ${iRet}`)
        ElMessage.success('视频预览已停止')
      }
    } else {
      addOperationInfo('[PREVIEW] 当前没有正在播放的视频')
      ElMessage.info('当前没有正在播放的视频')
    }

    isPreviewActive.value = false
    addOperationInfo('[PREVIEW] 预览停止完成')
  } catch (error) {
    console.error('停止预览失败:', error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    ElMessage.error(`停止预览失败: ${errorMessage}`)
    addOperationInfo(`[ERROR] 停止预览失败: ${errorMessage}`)

    // 强制重置状态
    isPreviewActive.value = false
  }
}

// 调试WebSDK状态
const debugWebSDK = () => {
  addOperationInfo('[DEBUG] ========== WebSDK调试信息 ==========')

  // 检查WebSDK对象
  const ctrl = getWebVideoCtrl()
  addOperationInfo(`[DEBUG] WebSDK对象: ${ctrl ? '已加载' : '未加载'}`)

  // 检查jQuery
  addOperationInfo(`[DEBUG] jQuery: ${typeof $ !== 'undefined' ? '已加载' : '未加载'}`)

  // 检查初始化状态
  addOperationInfo(`[DEBUG] WebSDK初始化状态: ${isWebSDKInitialized ? '已初始化' : '未初始化'}`)

  // 检查当前设备和通道
  addOperationInfo(`[DEBUG] 当前设备: ${currentDevice ? JSON.stringify(currentDevice) : '未设置'}`)
  addOperationInfo(`[DEBUG] 当前通道: ${currentChannel ? JSON.stringify(currentChannel) : '未设置'}`)

  // 检查窗口状态
  if (ctrl) {
    try {
      const windowStatus = ctrl.I_GetWindowStatus(g_iWndIndex)
      addOperationInfo(`[DEBUG] 窗口${g_iWndIndex}状态: ${windowStatus ? JSON.stringify(windowStatus) : '无状态'}`)
    } catch (error) {
      addOperationInfo(`[DEBUG] 获取窗口状态失败: ${error}`)
    }
  }

  // 检查视频源
  addOperationInfo(`[DEBUG] 视频源数量: ${videoSources.value.length}`)
  addOperationInfo(`[DEBUG] 选中视频源: ${selectedVideoSource.value}`)

  addOperationInfo('[DEBUG] ========== 调试信息结束 ==========')
}

// 测试设备WebSocket支持
const testWebSocketSupport = async () => {
  if (!selectedVideoSource.value) {
    addOperationInfo('[ERROR] 请先选择视频源')
    return
  }

  const source = videoSources.value.find(s => s.id === selectedVideoSource.value)
  if (!source) {
    addOperationInfo('[ERROR] 未找到选择的视频源')
    return
  }

  addOperationInfo('[TEST] ========== WebSocket支持测试开始 ==========')
  addOperationInfo(`[TEST] 测试设备: ${source.ip}:${source.port}`)

  try {
    // 首先尝试登录设备
    const loginSuccess = await loginDevice(source)
    if (!loginSuccess) {
      addOperationInfo('[TEST] 设备登录失败，无法测试WebSocket支持')
      return
    }

    // 等待通道信息获取
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (!currentDevice || !currentChannel) {
      addOperationInfo('[TEST] 获取设备通道信息失败，无法测试WebSocket')
      return
    }

    const ctrl = getWebVideoCtrl()
    if (!ctrl) {
      addOperationInfo('[TEST] WebSDK不可用')
      return
    }

    // 测试WebSocket连接
    addOperationInfo('[TEST] 开始测试WebSocket取流...')

    const szDeviceIdentify = currentDevice.id
    const iChannelID = parseInt(currentChannel.channelNo || currentChannel.id)
    const iRtspPort = parseInt(source.rtspPort || '554', 10)

    // 尝试直连模式
    addOperationInfo('[TEST] 测试直连模式 (bProxy: false)...')
    ctrl.I_StartRealPlay(szDeviceIdentify, {
      iRtspPort: iRtspPort,
      iStreamType: 1,
      iChannelID: iChannelID,
      bZeroChannel: false,
      bProxy: false,
      success: () => {
        addOperationInfo('[TEST] ✅ 直连模式WebSocket取流成功！')
        addOperationInfo('[TEST] 设备支持WebSocket直连取流')
        // 立即停止测试预览
        setTimeout(() => {
          ctrl.I_Stop()
          addOperationInfo('[TEST] 测试预览已停止')
        }, 1000)
      },
      error: (status: any, xmlDoc: any) => {
        addOperationInfo(`[TEST] ❌ 直连模式失败: ${status}`)
        if (status === 403) {
          addOperationInfo('[TEST] 设备不支持WebSocket直连取流')
          // 尝试代理模式
          addOperationInfo('[TEST] 尝试代理模式 (bProxy: true)...')
          ctrl.I_StartRealPlay(szDeviceIdentify, {
            iRtspPort: iRtspPort,
            iStreamType: 1,
            iChannelID: iChannelID,
            bZeroChannel: false,
            bProxy: true,
            success: () => {
              addOperationInfo('[TEST] ✅ 代理模式WebSocket取流成功！')
              addOperationInfo('[TEST] 设备需要通过代理进行WebSocket取流')
              // 立即停止测试预览
              setTimeout(() => {
                ctrl.I_Stop()
                addOperationInfo('[TEST] 测试预览已停止')
              }, 1000)
            },
            error: (status2: any, xmlDoc2: any) => {
              addOperationInfo(`[TEST] ❌ 代理模式也失败: ${status2}`)
              addOperationInfo('[TEST] 设备不支持WebSocket取流')
              addOperationInfo('[TEST] 建议检查设备固件版本和网络配置')
            }
          })
        } else {
          addOperationInfo('[TEST] 设备不支持WebSocket取流')
          addOperationInfo('[TEST] 建议检查设备固件版本和网络配置')
        }
      }
    })

  } catch (error) {
    console.error('测试WebSocket支持失败:', error)
    addOperationInfo(`[TEST] 测试异常: ${error}`)
  }

  addOperationInfo('[TEST] ========== WebSocket支持测试结束 ==========')
}

// 运动检测相关方法
const toggleMotionDetection = () => {
  if (showMotionDetection.value) {
    stopMotionDetection()
  } else {
    startMotionDetection()
  }
}

const startMotionDetection = async () => {
  if (!selectedVideoSource.value) {
    ElMessage.warning('请先选择视频源')
    return
  }

  try {
    addOperationInfo('[MOTION] 正在启动运动检测...')
    
    const response = await fetch('/api/motion-detection/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        video_source_id: selectedVideoSource.value
      })
    })

    if (response.ok) {
      showMotionDetection.value = true
      addOperationInfo('[MOTION] 运动检测已启动')
      ElMessage.success('运动检测已启动')
      
      // 连接WebSocket接收检测结果
      connectMotionDetectionWS()
    } else {
      addOperationInfo('[ERROR] 启动运动检测失败')
      ElMessage.error('启动运动检测失败')
    }
  } catch (error) {
    console.error('启动运动检测失败:', error)
    addOperationInfo(`[ERROR] 启动运动检测异常: ${error}`)
    ElMessage.error('启动运动检测失败')
  }
}

const stopMotionDetection = async () => {
  try {
    addOperationInfo('[MOTION] 正在停止运动检测...')
    
    const response = await fetch('/api/motion-detection/stop', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        video_source_id: selectedVideoSource.value
      })
    })

    if (response.ok) {
      showMotionDetection.value = false
      detectionResult.value = null
      addOperationInfo('[MOTION] 运动检测已停止')
      ElMessage.success('运动检测已停止')
      
      // 关闭WebSocket连接
      if (motionDetectionWS) {
        motionDetectionWS.close()
        motionDetectionWS = null
      }
    } else {
      addOperationInfo('[ERROR] 停止运动检测失败')
      ElMessage.error('停止运动检测失败')
    }
  } catch (error) {
    console.error('停止运动检测失败:', error)
    addOperationInfo(`[ERROR] 停止运动检测异常: ${error}`)
    ElMessage.error('停止运动检测失败')
  }
}

const connectMotionDetectionWS = () => {
  const wsUrl = `ws://localhost:8000/ws/motion-detection/${selectedVideoSource.value}/`
  motionDetectionWS = new WebSocket(wsUrl)
  
  motionDetectionWS.onopen = () => {
    addOperationInfo('[MOTION] WebSocket连接已建立')
  }
  
  motionDetectionWS.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)
      detectionResult.value = data
      addOperationInfo(`[MOTION] 检测到运动: ${data.confidence || 'N/A'}`)
    } catch (error) {
      console.error('解析运动检测数据失败:', error)
    }
  }
  
  motionDetectionWS.onerror = (error) => {
    console.error('运动检测WebSocket错误:', error)
    addOperationInfo('[ERROR] 运动检测WebSocket连接错误')
  }
  
  motionDetectionWS.onclose = () => {
    addOperationInfo('[MOTION] 运动检测WebSocket连接已关闭')
  }
}

// ROI绘制相关方法
const toggleROIDrawer = () => {
  showROIDrawer.value = !showROIDrawer.value
  if (!showROIDrawer.value) {
    // 关闭ROI工具时重置状态
    isDrawingEnabled.value = false
    drawMode.value = null
    isDrawing.value = false
    currentROI.value = null
    startPoint.value = null
    polygonPoints.value = []
  }
  addOperationInfo(`[ROI] ROI工具${showROIDrawer.value ? '已开启' : '已关闭'}`)
}

const toggleDrawMode = () => {
  isDrawingEnabled.value = !isDrawingEnabled.value
  if (!isDrawingEnabled.value) {
    drawMode.value = null
    isDrawing.value = false
    currentROI.value = null
    startPoint.value = null
    polygonPoints.value = []
  }
  addOperationInfo(`[ROI] 绘制模式${isDrawingEnabled.value ? '已解锁' : '已锁定'}`)
}

const setDrawMode = (mode: 'rectangle' | 'polygon') => {
  if (!isDrawingEnabled.value) {
    ElMessage.warning('请先解锁绘制模式')
    return
  }
  
  drawMode.value = mode
  isDrawing.value = false
  currentROI.value = null
  startPoint.value = null
  polygonPoints.value = []
  
  addOperationInfo(`[ROI] 切换到${mode === 'rectangle' ? '矩形' : '多边形'}绘制模式`)
}

const clearROIs = () => {
  roiList.value = []
  redrawROIDisplay()
  addOperationInfo('[ROI] 已清空所有ROI区域')
  ElMessage.success('已清空所有ROI区域')
}

const saveROIs = async () => {
  if (roiList.value.length === 0) {
    ElMessage.warning('没有ROI区域可保存')
    return
  }
  
  try {
    await saveROIConfigToBackend()
    addOperationInfo(`[ROI] 已保存${roiList.value.length}个ROI区域到后端`)
    ElMessage.success('ROI配置已保存')
  } catch (error) {
    console.error('保存ROI失败:', error)
    addOperationInfo(`[ERROR] 保存ROI失败: ${error}`)
    ElMessage.error('保存ROI失败')
  }
}

const editROI = (index: number) => {
  const roi = roiList.value[index]
  if (roi) {
    addOperationInfo(`[ROI] 编辑ROI: ${roi.name}`)
    // 这里可以添加编辑ROI的逻辑
    ElMessage.info('ROI编辑功能待实现')
  }
}

const deleteROI = (index: number) => {
  const roi = roiList.value[index]
  if (roi) {
    roiList.value.splice(index, 1)
    redrawROIDisplay()
    addOperationInfo(`[ROI] 已删除ROI: ${roi.name}`)
    ElMessage.success('ROI区域已删除')
  }
}

// 获取视频区域内的鼠标位置
const getVideoMousePosition = (event: MouseEvent) => {
  if (!videoContainer.value) return null
  
  const rect = videoContainer.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  // 确保坐标在视频区域内
  if (x >= 0 && x <= 640 && y >= 0 && y <= 360) {
    return { x, y }
  }
  return null
}

// 鼠标按下事件
const onVideoMouseDown = (event: MouseEvent) => {
  if (!showROIDrawer.value || !isDrawingEnabled.value || !drawMode.value) {
    return
  }
  
  const position = getVideoMousePosition(event)
  if (!position) return
  
  if (drawMode.value === 'rectangle') {
    // 矩形绘制开始
    isDrawing.value = true
    startPoint.value = position
    currentROI.value = {
      id: Date.now().toString(),
      name: `矩形${roiList.value.length + 1}`,
      type: 'rectangle',
      points: [position],
      color: colors[colorIndex % colors.length]
    }
    colorIndex++
  } else if (drawMode.value === 'polygon') {
    // 多边形绘制点击
    if (!isDrawing.value) {
      // 开始新的多边形
      isDrawing.value = true
      polygonPoints.value = [position]
      currentROI.value = {
        id: Date.now().toString(),
        name: `多边形${roiList.value.length + 1}`,
        type: 'polygon',
        points: [position],
        color: colors[colorIndex % colors.length]
      }
      colorIndex++
    } else {
      // 添加多边形点
      polygonPoints.value.push(position)
      if (currentROI.value) {
        currentROI.value.points = [...polygonPoints.value]
      }
    }
    redrawROIDisplay()
  }
}

// 鼠标移动事件
const onVideoMouseMove = (event: MouseEvent) => {
  if (!showROIDrawer.value || !isDrawingEnabled.value || !drawMode.value) {
    return
  }
  
  const position = getVideoMousePosition(event)
  if (!position) return
  
  if (drawMode.value === 'rectangle' && isDrawing.value && startPoint.value && currentROI.value) {
    // 矩形绘制中
    currentROI.value.points = [
      startPoint.value,
      { x: position.x, y: startPoint.value.y },
      position,
      { x: startPoint.value.x, y: position.y }
    ]
    redrawROIDisplay()
  } else if (drawMode.value === 'polygon' && isDrawing.value && polygonPoints.value.length > 0) {
    // 多边形绘制预览线
    redrawROIDisplay()
    drawPreviewLine(polygonPoints.value[polygonPoints.value.length - 1], position)
  }
}

// 鼠标抬起事件
const onVideoMouseUp = (event: MouseEvent) => {
  if (!showROIDrawer.value || !isDrawingEnabled.value || !drawMode.value) {
    return
  }
  
  if (drawMode.value === 'rectangle' && isDrawing.value && currentROI.value) {
    // 矩形绘制完成
    isDrawing.value = false
    roiList.value.push(currentROI.value)
    currentROI.value = null
    startPoint.value = null
    addOperationInfo(`[ROI] 完成矩形绘制: ${roiList.value[roiList.value.length - 1].name}`)
    redrawROIDisplay()
  }
}

// 双击事件（完成多边形绘制）
const onVideoDoubleClick = (event: MouseEvent) => {
  if (!showROIDrawer.value || !isDrawingEnabled.value || drawMode.value !== 'polygon' || !isDrawing.value) {
    return
  }
  
  if (polygonPoints.value.length >= 3 && currentROI.value) {
    // 多边形绘制完成
    isDrawing.value = false
    roiList.value.push(currentROI.value)
    currentROI.value = null
    addOperationInfo(`[ROI] 完成多边形绘制: ${roiList.value[roiList.value.length - 1].name}`)
    polygonPoints.value = []
    redrawROIDisplay()
  } else {
    ElMessage.warning('多边形至少需要3个点')
  }
}

// 保存ROI配置到后端
const saveROIConfigToBackend = async () => {
  try {
    const response = await fetch('/api/roi-config/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        video_source_id: selectedVideoSource.value,
        roi_config: roiList.value
      })
    })
    
    if (!response.ok) {
      throw new Error('保存ROI配置失败')
    }
    
    return await response.json()
  } catch (error) {
    console.error('保存ROI配置失败:', error)
    throw error
  }
}

// 导出ROI配置
const exportROIs = () => {
  if (roiList.value.length === 0) {
    ElMessage.warning('没有ROI区域可导出')
    return
  }
  
  const config = {
    version: '1.0',
    timestamp: new Date().toISOString(),
    video_source: selectedVideoSource.value,
    rois: roiList.value
  }
  
  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `roi_config_${new Date().getTime()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  addOperationInfo(`[ROI] 已导出${roiList.value.length}个ROI区域配置`)
  ElMessage.success('ROI配置已导出')
}

// 导入ROI配置
const importROIs = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target?.result as string)
        if (config.rois && Array.isArray(config.rois)) {
          roiList.value = config.rois
          redrawROIDisplay()
          addOperationInfo(`[ROI] 已导入${config.rois.length}个ROI区域配置`)
          ElMessage.success('ROI配置已导入')
        } else {
          ElMessage.error('无效的ROI配置文件')
        }
      } catch (error) {
        console.error('导入ROI配置失败:', error)
        ElMessage.error('导入ROI配置失败')
      }
    }
    reader.readAsText(file)
  }
  input.click()
}

// 重绘ROI显示
const redrawROIDisplay = () => {
  if (!roiDisplayCanvas.value) return
  
  const canvas = roiDisplayCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  
  // 绘制所有ROI
  roiList.value.forEach(roi => {
    drawROI(ctx, roi, false)
  })
  
  // 绘制当前正在绘制的ROI
  if (currentROI.value) {
    drawROI(ctx, currentROI.value, true)
  }
}

// 绘制单个ROI
const drawROI = (ctx: CanvasRenderingContext2D, roi: ROI, isPreview: boolean) => {
  if (roi.points.length === 0) return
  
  ctx.strokeStyle = roi.color
  ctx.fillStyle = roi.color + '20' // 半透明填充
  ctx.lineWidth = isPreview ? 3 : 2
  
  if (roi.type === 'rectangle' && roi.points.length >= 2) {
    // 绘制矩形
    const startPoint = roi.points[0]
    const endPoint = roi.points[roi.points.length - 1]
    const width = endPoint.x - startPoint.x
    const height = endPoint.y - startPoint.y
    
    ctx.beginPath()
    ctx.rect(startPoint.x, startPoint.y, width, height)
    ctx.fill()
    ctx.stroke()
    
    // 绘制标签
    ctx.fillStyle = roi.color
    ctx.font = '12px Arial'
    ctx.fillText(roi.name, startPoint.x, startPoint.y - 5)
  } else if (roi.type === 'polygon' && roi.points.length >= 2) {
    // 绘制多边形
    ctx.beginPath()
    ctx.moveTo(roi.points[0].x, roi.points[0].y)
    
    for (let i = 1; i < roi.points.length; i++) {
      ctx.lineTo(roi.points[i].x, roi.points[i].y)
    }
    
    if (!isPreview && roi.points.length >= 3) {
      ctx.closePath()
      ctx.fill()
    }
    ctx.stroke()
    
    // 绘制标签
    ctx.fillStyle = roi.color
    ctx.font = '12px Arial'
    ctx.fillText(roi.name, roi.points[0].x, roi.points[0].y - 5)
    
    // 绘制顶点
    roi.points.forEach(point => {
      ctx.beginPath()
      ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI)
      ctx.fillStyle = roi.color
      ctx.fill()
    })
  }
}

// 绘制多边形预览线
const drawPreviewLine = (fromPoint: { x: number; y: number }, toPoint: { x: number; y: number }) => {
  if (!roiDisplayCanvas.value) return
  
  const canvas = roiDisplayCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  ctx.strokeStyle = '#999'
  ctx.lineWidth = 1
  ctx.setLineDash([5, 5])
  
  ctx.beginPath()
  ctx.moveTo(fromPoint.x, fromPoint.y)
  ctx.lineTo(toPoint.x, toPoint.y)
  ctx.stroke()
  
  ctx.setLineDash([])
}

// 监听ROI相关状态变化
watch([showROIDrawer, roiList], () => {
  nextTick(() => {
    redrawROIDisplay()
  })
}, { deep: true })

// 组件挂载
onMounted(async () => {
  addOperationInfo('[INIT] 组件正在初始化...')
  
  // 检查WebSDK和jQuery是否已加载
  if (typeof (window as any).WebVideoCtrl === 'undefined') {
    addOperationInfo('[ERROR] WebSDK脚本未加载，请检查页面配置')
    ElMessage.error('WebSDK脚本未加载，请检查页面配置')
    return
  }
  
  if (typeof $ === 'undefined') {
    addOperationInfo('[WARNING] jQuery未加载，某些功能可能受限')
  }
  
  // 初始化WebSDK
  const initSuccess = await initWebSDK()
  if (initSuccess) {
    addOperationInfo('[INIT] WebSDK初始化成功')
    // 自动刷新视频源列表
    await refreshVideoSources()
    addOperationInfo('[INIT] 组件初始化完成')
  } else {
    addOperationInfo('[ERROR] WebSDK初始化失败')
    ElMessage.error('WebSDK初始化失败')
  }
})

// 组件卸载
onUnmounted(() => {
  addOperationInfo('[CLEANUP] 正在清理资源...')
  
  // 停止预览
  if (isPreviewActive.value) {
    stopPreview()
  }
  
  // 关闭运动检测WebSocket
  if (motionDetectionWS) {
    motionDetectionWS.close()
    motionDetectionWS = null
  }
  
  // 销毁WebSDK实例
  const ctrl = getWebVideoCtrl()
  if (ctrl && isWebSDKInitialized) {
    try {
      ctrl.I_Uninit()
      addOperationInfo('[CLEANUP] WebSDK实例已销毁')
    } catch (error) {
      console.error('销毁WebSDK实例失败:', error)
    }
  }
  
  addOperationInfo('[CLEANUP] 资源清理完成')
})
</script>
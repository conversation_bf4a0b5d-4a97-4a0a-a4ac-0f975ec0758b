<template>
  <div class="admin-container">
    <div class="admin-header">
      <h1>后端管理</h1>
      <p>系统后端管理和API文档</p>
    </div>
    
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="admin-card" shadow="hover" @click="openApiDocs">
          <div class="card-content">
            <el-icon class="card-icon"><Document /></el-icon>
            <h3>API文档</h3>
            <p>查看系统API接口文档</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="admin-card" shadow="hover" @click="openSwagger">
          <div class="card-content">
            <el-icon class="card-icon"><Connection /></el-icon>
            <h3>Swagger UI</h3>
            <p>交互式API测试界面</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="admin-card" shadow="hover" @click="openSystemInfo">
          <div class="card-content">
            <el-icon class="card-icon"><Monitor /></el-icon>
            <h3>系统信息</h3>
            <p>查看系统运行状态和信息</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="8">
        <el-card class="admin-card" shadow="hover" @click="openLogs">
          <div class="card-content">
            <el-icon class="card-icon"><Files /></el-icon>
            <h3>系统日志</h3>
            <p>查看系统运行日志</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="admin-card" shadow="hover" @click="openMetrics">
          <div class="card-content">
            <el-icon class="card-icon"><TrendCharts /></el-icon>
            <h3>性能监控</h3>
            <p>查看系统性能指标</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="admin-card" shadow="hover" @click="openBackup">
          <div class="card-content">
            <el-icon class="card-icon"><FolderOpened /></el-icon>
            <h3>数据备份</h3>
            <p>数据库备份和恢复</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- API文档弹窗 -->
    <el-dialog
      v-model="apiDocsVisible"
      title="API文档"
      width="80%"
      :before-close="handleClose"
    >
      <div class="api-docs-content">
        <iframe 
          :src="apiDocsUrl" 
          width="100%" 
          height="600px" 
          frameborder="0"
        ></iframe>
      </div>
    </el-dialog>
    
    <!-- 系统信息弹窗 -->
    <el-dialog
      v-model="systemInfoVisible"
      title="系统信息"
      width="80%"
      :close-on-click-modal="false"
      draggable
    >
      <template #header>
        <div class="dialog-header">
          <span>系统信息</span>
          <el-button type="primary" size="small" @click="loadSystemInfo" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <div class="system-info">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-card class="info-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <el-icon><Monitor /></el-icon>
                  <span>基本信息</span>
                </div>
              </template>
              <el-descriptions :column="1" border size="small">
                <el-descriptions-item label="系统版本">{{ systemInfo.version }}</el-descriptions-item>
                <el-descriptions-item label="Python版本">{{ systemInfo.pythonVersion }}</el-descriptions-item>
                <el-descriptions-item label="操作系统">{{ systemInfo.platform }}</el-descriptions-item>
                <el-descriptions-item label="系统架构">{{ systemInfo.architecture }}</el-descriptions-item>
                <el-descriptions-item label="系统启动时间">{{ systemInfo.bootTime }}</el-descriptions-item>
                <el-descriptions-item label="运行时间">{{ systemInfo.uptime }}</el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
          
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-card class="info-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <el-icon><TrendCharts /></el-icon>
                  <span>性能监控</span>
                </div>
              </template>
              <el-descriptions :column="1" border size="small">
                <el-descriptions-item label="CPU核心数">{{ systemInfo.cpuCount }}</el-descriptions-item>
                <el-descriptions-item label="CPU使用率">
                  <div class="progress-container">
                    <el-progress :percentage="Math.round(systemInfo.cpuUsage)" :color="getProgressColor(systemInfo.cpuUsage)" :show-text="false" />
                    <span class="progress-text">{{ Math.round(systemInfo.cpuUsage) }}%</span>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="内存使用率">
                  <div class="progress-container">
                    <el-progress :percentage="Math.round(systemInfo.memoryUsage)" :color="getProgressColor(systemInfo.memoryUsage)" :show-text="false" />
                    <span class="progress-text">{{ Math.round(systemInfo.memoryUsage) }}%</span>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="内存总量">{{ systemInfo.memoryTotal }}</el-descriptions-item>
                <el-descriptions-item label="可用内存">{{ systemInfo.memoryAvailable }}</el-descriptions-item>
                <el-descriptions-item label="磁盘使用率">
                  <div class="progress-container">
                    <el-progress :percentage="Math.round(systemInfo.diskUsage)" :color="getProgressColor(systemInfo.diskUsage)" :show-text="false" />
                    <span class="progress-text">{{ Math.round(systemInfo.diskUsage) }}%</span>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="磁盘总量">{{ systemInfo.diskTotal }}</el-descriptions-item>
                <el-descriptions-item label="磁盘可用">{{ systemInfo.diskFree }}</el-descriptions-item>
                <el-descriptions-item label="进程数量">{{ systemInfo.processCount }}</el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row style="margin-top: 20px;" v-if="systemInfo.networkInfo">
          <el-col :span="24">
            <el-card class="info-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <el-icon><Connection /></el-icon>
                  <span>网络信息</span>
                </div>
              </template>
              <el-descriptions :column="2" border size="small" v-if="!systemInfo.networkInfo.error">
                <el-descriptions-item label="发送数据">{{ systemInfo.networkInfo.bytes_sent }}</el-descriptions-item>
                <el-descriptions-item label="接收数据">{{ systemInfo.networkInfo.bytes_recv }}</el-descriptions-item>
                <el-descriptions-item label="发送包数">{{ systemInfo.networkInfo.packets_sent }}</el-descriptions-item>
                <el-descriptions-item label="接收包数">{{ systemInfo.networkInfo.packets_recv }}</el-descriptions-item>
              </el-descriptions>
              <div v-else class="network-error">
                <el-alert title="网络信息获取失败" type="warning" :closable="false" />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 系统日志管理器 -->
    <SystemLogManager v-model="logManagerVisible" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  Connection,
  Monitor,
  Files,
  TrendCharts,
  FolderOpened,
  Refresh
} from '@element-plus/icons-vue'
import SystemLogManager from '@/components/SystemLogManager.vue'

export default defineComponent({
  name: 'AdminView',
  components: {
    Document,
    Connection,
    Monitor,
    Files,
    TrendCharts,
    FolderOpened,
    Refresh,
    SystemLogManager
  },
  setup() {
    const apiDocsVisible = ref(false)
    const systemInfoVisible = ref(false)
    const logManagerVisible = ref(false)
    const apiDocsUrl = ref('')
    const loading = ref(false)
    
    const systemInfo = ref({
      version: '1.0.0',
      pythonVersion: '3.9.0',
      uptime: '2天 3小时 45分钟',
      cpuUsage: 25,
      memoryUsage: 68,
      diskUsage: 45
    })
    
    // 获取后端API基础URL
    const getBackendUrl = () => {
      // 从环境变量获取API URL，然后去掉/api后缀得到根URL
      const apiUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'
      return apiUrl.replace('/api', '')
    }
    
    const openApiDocs = () => {
      const backendUrl = getBackendUrl()
      // 检查后端是否可访问
      fetch(`${backendUrl}/docs`)
        .then(response => {
          if (response.ok) {
            apiDocsUrl.value = `${backendUrl}/docs`
            apiDocsVisible.value = true
          } else {
            throw new Error('API文档服务不可用')
          }
        })
        .catch(() => {
          ElMessage.warning('API文档服务暂时不可用，请确保后端服务已启动')
        })
    }
    
    const openSwagger = () => {
      const backendUrl = getBackendUrl()
      // 检查后端是否可访问
      fetch(`${backendUrl}/docs`)
        .then(response => {
          if (response.ok) {
            window.open(`${backendUrl}/docs`, '_blank')
          } else {
            throw new Error('Swagger UI服务不可用')
          }
        })
        .catch(() => {
          ElMessage.warning('Swagger UI服务暂时不可用，请确保后端服务已启动')
        })
    }
    
    const openSystemInfo = () => {
      // 这里可以调用API获取实际的系统信息
      loadSystemInfo()
      systemInfoVisible.value = true
    }
    
    const openLogs = () => {
      logManagerVisible.value = true
    }
    
    const openMetrics = () => {
      ElMessage({
        message: '性能监控功能开发中，建议集成 Prometheus + Grafana',
        type: 'info',
        duration: 3000
      })
      // 临时方案：显示基础性能信息
      console.log('提示：可以在系统信息中查看基础性能数据')
    }
    
    const openBackup = () => {
      ElMessage({
        message: '数据备份功能开发中，建议后端添加 /admin/backup 接口',
        type: 'info',
        duration: 3000
      })
      // 临时方案：提示手动备份方法
      console.log('提示：当前可以手动备份数据库文件：backend/app.db')
    }
    
    const loadSystemInfo = async () => {
      loading.value = true
      try {
        // 调用实际的API获取系统信息
        const apiUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'
        const response = await fetch(`${apiUrl}/admin/system-info`)
        
        if (!response.ok) {
          throw new Error('API请求失败')
        }
        
        const data = await response.json()
        
        // 转换API数据格式以匹配前端显示
        systemInfo.value = {
          version: data.version || '未知',
          pythonVersion: data.python_version || '未知',
          uptime: data.uptime || '未知',
          cpuUsage: parseFloat(data.cpu_usage) || 0,
          memoryUsage: parseFloat(data.memory_usage) || 0,
          diskUsage: parseFloat(data.disk_usage) || 0,
          platform: data.platform || '未知',
          architecture: data.architecture || '未知',
          cpuCount: data.cpu_count || 0,
          memoryTotal: data.memory_total || '未知',
          memoryAvailable: data.memory_available || '未知',
          diskTotal: data.disk_total || '未知',
          diskFree: data.disk_free || '未知',
          processCount: data.process_count || 0,
          bootTime: data.boot_time || '未知',
          networkInfo: data.network_info || null
        }
      } catch (error) {
        console.error('获取系统信息失败:', error)
        ElMessage.error('获取系统信息失败，请确保后端服务正常运行')
        
        // 如果API调用失败，使用默认值
        systemInfo.value = {
          version: '1.0.0',
          pythonVersion: '未知',
          uptime: '未知',
          cpuUsage: 0,
          memoryUsage: 0,
          diskUsage: 0
        }
      } finally {
        loading.value = false
      }
    }
    
    const handleClose = () => {
      apiDocsVisible.value = false
    }
    
    // 根据使用率返回进度条颜色
    const getProgressColor = (percentage: number) => {
      if (percentage < 50) {
        return '#67c23a' // 绿色
      } else if (percentage < 80) {
        return '#e6a23c' // 橙色
      } else {
        return '#f56c6c' // 红色
      }
    }
    
    onMounted(() => {
      // 页面加载时可以预加载一些信息
    })
    
    return {
      apiDocsVisible,
      systemInfoVisible,
      logManagerVisible,
      apiDocsUrl,
      systemInfo,
      loading,
      openApiDocs,
      openSwagger,
      openSystemInfo,
      openLogs,
      openMetrics,
      openBackup,
      handleClose,
      getProgressColor,
      loadSystemInfo
    }
  }
})
</script>

<style scoped>
.admin-container {
  padding: 20px;
}

.admin-header {
  margin-bottom: 30px;
  text-align: center;
}

.admin-header h1 {
  font-size: 28px;
  color: var(--text-color);
  margin-bottom: 10px;
}

.admin-header p {
  color: var(--text-color-soft);
  font-size: 16px;
}

.admin-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 180px;
}

.admin-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-content {
  text-align: center;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.card-icon {
  font-size: 48px;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.card-content h3 {
  font-size: 18px;
  color: var(--text-color);
  margin-bottom: 10px;
}

.card-content p {
  color: var(--text-color-soft);
  font-size: 14px;
  line-height: 1.5;
}

.api-docs-content {
  width: 100%;
  height: 600px;
}

.system-info {
  padding: 20px;
}

.info-card {
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  color: var(--el-color-primary);
}

.card-header .el-icon {
  font-size: 18px;
}

.info-card .el-card__header {
  background-color: var(--el-color-primary-light-9);
  font-weight: bold;
  color: var(--el-color-primary);
}

/* 对话框头部样式 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dialog-header span {
  font-size: 18px;
  font-weight: bold;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .system-info {
    padding: 10px;
  }
  
  .info-card {
    margin-bottom: 15px;
  }
  
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto;
  }
  
  .dialog-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}

.network-error {
  text-align: center;
  padding: 20px;
}

/* 进度条样式调整 */
.progress-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-container .el-progress {
  flex: 1;
  margin-right: 10px;
}

.progress-text {
  font-weight: bold;
  min-width: 45px;
  text-align: right;
}

.el-descriptions__cell .el-progress {
  margin-bottom: 5px;
}

/* 暗色主题适配 */
.dark-theme .admin-card {
  background-color: var(--bg-color-soft);
}

.dark-theme .admin-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.dark-theme .info-card .el-card__header {
  background-color: var(--el-color-primary-dark-2);
  color: var(--el-color-primary-light-3);
}
</style>
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
压铸检测系统后端启动器 - 独立版本
支持数据库外置和自动检查
"""

import os
import sys
import time
import uvicorn
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置工作目录
os.chdir(current_dir)

def check_database():
    """
    检查数据库是否存在，如果不存在则提示初始化
    支持外置数据库文件
    """
    # 获取可执行文件的实际位置（而不是_internal目录）
    if getattr(sys, 'frozen', False):
        # 打包后的可执行文件
        exe_dir = Path(sys.executable).parent
    else:
        # 开发环境
        exe_dir = current_dir
    
    # 尝试多个可能的数据库文件位置（相对于可执行文件目录）
    possible_db_paths = [
        exe_dir / "../../die_casting_detection.db",  # 外置数据库位置
        exe_dir / "../die_casting_detection.db",     # 备选位置1
        exe_dir / "die_casting_detection.db",        # 可执行文件目录
        Path("die_casting_detection.db"),            # 当前工作目录
    ]
    
    db_file = None
    for db_path in possible_db_paths:
        try:
            if db_path.exists():
                db_file = db_path.resolve()  # 获取绝对路径
                break
        except Exception:
            continue
    
    if db_file is None:
        print("❌ 数据库文件不存在！")
        print("请确保数据库文件存在于以下任一位置：")
        for path in possible_db_paths:
            try:
                print(f"  - {path.resolve()}")
            except Exception:
                print(f"  - {path} (路径无效)")
        print("\n或者将现有的数据库文件复制到上述位置之一")
        input("\n按回车键退出...")
        sys.exit(1)
    
    # 设置环境变量，让应用使用找到的数据库文件
    os.environ["DATABASE_URL"] = f"sqlite:///{db_file}"
    print(f"✅ 数据库文件检查通过: {db_file}")
    return db_file

def check_required_directories():
    """
    检查并创建必要的目录
    """
    required_dirs = [
        "logs",
        "uploads", 
        "detection_images",
        "static",
        "alarm_images"
    ]
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {dir_name}")
        else:
            print(f"✅ 目录检查通过: {dir_name}")

def check_app_files():
    """
    检查应用核心文件
    """
    required_files = [
        "app",
        "alembic.ini"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ 文件检查通过: {file_path}")
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"  - {file}")
        print("\n请确保所有必要文件都存在")
        input("按回车键退出...")
        sys.exit(1)

def show_startup_info(db_file=None):
    """
    显示启动信息
    """
    print("=" * 60)
    print("🚀 压铸检测系统后端服务")
    print("=" * 60)
    print(f"📁 工作目录: {current_dir}")
    if db_file:
        print(f"🗄️  数据库文件: {db_file.absolute()}")
    else:
        print(f"🗄️  数据库文件: 待检测")
    print(f"🌐 服务地址: http://127.0.0.1:8000")
    print(f"📚 API文档: http://127.0.0.1:8000/docs")
    print("=" * 60)

def main():
    """
    主函数
    """
    try:
        # 显示启动信息
        show_startup_info()
        
        print("\n🔍 正在进行系统检查...")
        
        # 检查应用文件
        print("\n📋 检查应用文件...")
        check_app_files()
        
        # 检查并创建必要目录
        print("\n📁 检查必要目录...")
        check_required_directories()
        
        # 检查数据库
        print("\n🗄️  检查数据库...")
        db_file = check_database()
        
        # 重新显示启动信息（包含实际数据库路径）
        print("\n" + "=" * 60)
        print("📋 最终配置信息")
        print("=" * 60)
        print(f"📁 工作目录: {current_dir}")
        print(f"🗄️  数据库文件: {db_file}")
        print(f"🌐 服务地址: http://127.0.0.1:8000")
        print(f"📚 API文档: http://127.0.0.1:8000/docs")
        print("=" * 60)
        
        print("\n✅ 所有检查通过，正在启动服务...")
        print("\n" + "=" * 60)
        print("🎯 服务启动中，请稍候...")
        print("💡 提示: 按 Ctrl+C 可以停止服务")
        print("=" * 60 + "\n")
        
        # 启动FastAPI应用
        uvicorn.run(
            "app.main:app",
            host="127.0.0.1",
            port=8000,
            reload=False,
            log_level="info",
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 服务已停止")
        print("👋 感谢使用压铸检测系统！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n请检查错误信息并重试")
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()
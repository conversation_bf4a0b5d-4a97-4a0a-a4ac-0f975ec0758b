import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def get_auth_token(username="admin", password="123456"):
    """获取认证token"""
    login_url = f"{API_BASE}/auth/login/json"
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data["access_token"]
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_api_with_auth(method, url, data=None):
    """使用认证测试API接口"""
    token = get_auth_token()
    if not token:
        print(f"无法获取token，跳过 {method} {url}")
        return None
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers)
        
        print(f"{method} {url}")
        print(f"状态码: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"响应: {response_data}")
        except:
            print(f"响应: {response.text}")
        print("-" * 50)
        
        return response
    except Exception as e:
        print(f"请求失败: {e}")
        print("-" * 50)
        return None

def main():
    print("测试删除API功能...\n")
    
    # 1. 首先获取所有计划，看看有哪些可以删除
    print("1. 获取所有预设检测计划:")
    response = test_api_with_auth("GET", f"{API_BASE}/preset-schedules/")
    
    schedules = []
    if response and response.status_code == 200:
        schedules = response.json()
        print(f"找到 {len(schedules)} 个计划")
        for schedule in schedules:
            print(f"  - ID: {schedule.get('id')}, 名称: {schedule.get('name')}")
    
    if not schedules:
        print("没有找到任何计划，创建一个测试计划...")
        # 创建一个测试计划
        test_schedule_data = {
            "name": "删除测试计划",
            "description": "用于测试删除功能的计划",
            "templateId": 1,
            "date": "2025-01-25",
            "startTime": "10:00",
            "endTime": "11:00",
            "isEnabled": True
        }
        
        print("\n2. 创建测试计划:")
        response = test_api_with_auth("POST", f"{API_BASE}/preset-schedules/", test_schedule_data)
        
        if response and response.status_code == 200:
            new_schedule = response.json()
            schedules = [new_schedule]
            print(f"创建成功，ID: {new_schedule.get('id')}")
        else:
            print("创建失败，无法继续测试删除功能")
            return
    
    # 3. 测试删除最后一个计划
    if schedules:
        test_schedule_id = schedules[-1].get('id')
        print(f"\n3. 测试删除计划 ID: {test_schedule_id}")
        
        # 测试不同的URL格式
        test_urls = [
            f"{API_BASE}/preset-schedules/{test_schedule_id}",  # 不带斜杠
            f"{API_BASE}/preset-schedules/{test_schedule_id}/",  # 带斜杠
        ]
        
        for url in test_urls:
            print(f"\n测试URL: {url}")
            response = test_api_with_auth("DELETE", url)
            
            if response and response.status_code == 200:
                print("✅ 删除成功！")
                break
            elif response and response.status_code == 404:
                print("❌ 404错误 - 计划不存在或路由问题")
            else:
                print(f"❌ 删除失败，状态码: {response.status_code if response else 'None'}")
    
    # 4. 验证删除结果
    print("\n4. 验证删除结果:")
    test_api_with_auth("GET", f"{API_BASE}/preset-schedules/")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
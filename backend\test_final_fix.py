import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def get_auth_token(username="admin", password="123456"):
    """获取认证token"""
    login_url = f"{API_BASE}/auth/login/json"
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data["access_token"]
        else:
            print(f"登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_api_with_auth(method, url, data=None):
    """使用认证测试API接口"""
    token = get_auth_token()
    if not token:
        print(f"无法获取token，跳过 {method} {url}")
        return None
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers)
        
        print(f"{method} {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code < 400:
            print("✅ 成功")
            success = True
        else:
            print("❌ 失败")
            success = False
            
        try:
            response_data = response.json()
            if success and method == "GET" and isinstance(response_data, list):
                print(f"响应: 获取到 {len(response_data)} 条记录")
            elif success:
                print(f"响应: 操作成功")
            else:
                print(f"错误响应: {response_data}")
        except:
            print(f"响应: {response.text}")
        print("-" * 50)
        
        return response
    except Exception as e:
        print(f"请求失败: {e}")
        print("-" * 50)
        return None

def main():
    print("最终测试：验证前后端API修复...\n")
    
    # 检查服务器状态
    print("1. 检查服务器状态:")
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务器正在运行")
        else:
            print(f"⚠️  服务器响应异常: {response.status_code}")
            return
    except:
        print("❌ 无法连接到后端服务器")
        return
    
    # 测试认证
    print("\n2. 测试认证:")
    token = get_auth_token()
    if not token:
        print("❌ 认证失败，无法继续测试")
        return
    print("✅ 认证成功")
    
    # 测试URL格式兼容性
    print("\n3. 测试URL格式兼容性:")
    
    # 测试获取所有计划的不同URL格式
    get_urls = [
        (f"{API_BASE}/preset-schedules", "不带斜杠（前端修复后格式）"),
        (f"{API_BASE}/preset-schedules/", "带斜杠（原前端格式）")
    ]
    
    working_get_url = None
    for url, desc in get_urls:
        print(f"\n测试 {desc}:")
        response = test_api_with_auth("GET", url)
        if response and response.status_code == 200:
            working_get_url = url
            print(f"✅ {desc} 工作正常")
            break
        else:
            print(f"❌ {desc} 失败")
    
    if not working_get_url:
        print("\n❌ 所有GET URL格式都失败，无法继续测试")
        return
    
    # 测试创建计划的不同URL格式
    test_schedule_data = {
        "name": "最终测试计划",
        "description": "验证前后端API修复",
        "templateId": 1,
        "date": "2025-01-26",
        "startTime": "16:00",
        "endTime": "17:00",
        "isEnabled": True
    }
    
    create_urls = [
        (f"{API_BASE}/preset-schedules", "不带斜杠（前端修复后格式）"),
        (f"{API_BASE}/preset-schedules/", "带斜杠（原前端格式）")
    ]
    
    working_create_url = None
    test_id = None
    
    for url, desc in create_urls:
        print(f"\n测试创建计划 {desc}:")
        response = test_api_with_auth("POST", url, test_schedule_data)
        if response and response.status_code == 200:
            working_create_url = url
            new_schedule = response.json()
            test_id = new_schedule.get('id')
            print(f"✅ {desc} 工作正常，创建计划ID: {test_id}")
            break
        else:
            print(f"❌ {desc} 失败")
    
    if not working_create_url or not test_id:
        print("\n❌ 所有CREATE URL格式都失败")
        return
    
    # 测试其他操作的URL格式
    print(f"\n4. 测试其他操作 (使用计划ID: {test_id}):")
    
    # 测试获取特定计划
    get_specific_urls = [
        f"{API_BASE}/preset-schedules/{test_id}",
        f"{API_BASE}/preset-schedules/{test_id}/"
    ]
    
    for url in get_specific_urls:
        print(f"\n测试获取特定计划: {url}")
        response = test_api_with_auth("GET", url)
        if response and response.status_code == 200:
            print("✅ 获取特定计划成功")
            break
    
    # 测试更新计划
    update_data = {
        "name": "最终测试计划 - 已更新",
        "description": "验证前后端API修复 - 更新版本",
        "templateId": 1,
        "date": "2025-01-26",
        "startTime": "16:30",
        "endTime": "17:30",
        "isEnabled": True
    }
    
    update_urls = [
        f"{API_BASE}/preset-schedules/{test_id}",
        f"{API_BASE}/preset-schedules/{test_id}/"
    ]
    
    for url in update_urls:
        print(f"\n测试更新计划: {url}")
        response = test_api_with_auth("PUT", url, update_data)
        if response and response.status_code == 200:
            print("✅ 更新计划成功")
            break
    
    # 测试删除计划
    delete_urls = [
        f"{API_BASE}/preset-schedules/{test_id}",
        f"{API_BASE}/preset-schedules/{test_id}/"
    ]
    
    for url in delete_urls:
        print(f"\n测试删除计划: {url}")
        response = test_api_with_auth("DELETE", url)
        if response and response.status_code == 200:
            print("✅ 删除计划成功")
            break
    
    # 最终总结
    print("\n" + "=" * 60)
    print("最终测试结果总结:")
    print("=" * 60)
    
    print(f"✅ 工作的GET URL: {working_get_url}")
    print(f"✅ 工作的CREATE URL: {working_create_url}")
    
    print("\n修复状态:")
    if working_get_url.endswith('/'):
        print("- 后端需要尾部斜杠的URL格式")
        print("- 前端API调用需要使用带斜杠的URL")
        print("- 建议恢复前端API调用中的尾部斜杠")
    else:
        print("- 后端支持不带斜杠的URL格式")
        print("- 前端API修复成功")
        print("- 404错误已解决")
    
    print("\n建议:")
    print("1. 确保前后端URL格式一致")
    print("2. 测试前端应用的实际API调用")
    print("3. 检查浏览器开发者工具中的网络请求")

if __name__ == "__main__":
    main()
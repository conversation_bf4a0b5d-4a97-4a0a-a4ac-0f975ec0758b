import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import * as dieCastersApi from '@/api/die-casters'
import type { DieCaster } from '@/types'

export const useDieCasterStore = defineStore('dieCaster', () => {
  const dieCasters = ref<DieCaster[]>([])
  const loading = ref(false)
  const currentDieCaster = ref<DieCaster | null>(null)
  
  // 获取所有压铸机
  const fetchDieCasters = async () => {
    loading.value = true
    try {
      const response = await dieCastersApi.getDieCasters()
      dieCasters.value = response.items || response || []
      return dieCasters.value
    } catch (error: any) {
      console.error('获取压铸机列表失败', error)
      ElMessage.error('获取压铸机列表失败')
      return []
    } finally {
      loading.value = false
    }
  }
  
  // 获取单个压铸机
  const fetchDieCaster = async (id: number) => {
    loading.value = true
    try {
      const response = await dieCastersApi.getDieCaster(id)
      currentDieCaster.value = response
      return response
    } catch (error: any) {
      console.error(`获取压铸机(ID: ${id})失败`, error)
      ElMessage.error('获取压铸机详情失败')
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 创建压铸机
  const createDieCaster = async (data: Partial<DieCaster>) => {
    loading.value = true
    try {
      const response = await dieCastersApi.createDieCaster(data)
      dieCasters.value.push(response)
      ElMessage.success('压铸机创建成功')
      return response
    } catch (error: any) {
      console.error('创建压铸机失败', error)
      ElMessage.error('创建压铸机失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 更新压铸机
  const updateDieCaster = async (id: number, data: Partial<DieCaster>) => {
    loading.value = true
    try {
      const response = await dieCastersApi.updateDieCaster(id, data)
      const index = dieCasters.value.findIndex(item => item.id === id)
      if (index !== -1) {
        dieCasters.value[index] = { ...dieCasters.value[index], ...response }
      }
      ElMessage.success('压铸机更新成功')
      return response
    } catch (error: any) {
      console.error(`更新压铸机(ID: ${id})失败`, error)
      ElMessage.error('更新压铸机失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 删除压铸机
  const deleteDieCaster = async (id: number) => {
    loading.value = true
    try {
      await dieCastersApi.deleteDieCaster(id)
      dieCasters.value = dieCasters.value.filter(item => item.id !== id)
      ElMessage.success('压铸机删除成功')
      return true
    } catch (error: any) {
      console.error(`删除压铸机(ID: ${id})失败`, error)
      ElMessage.error('删除压铸机失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  return {
    dieCasters,
    loading,
    currentDieCaster,
    fetchDieCasters,
    fetchDieCaster,
    createDieCaster,
    updateDieCaster,
    deleteDieCaster
  }
}) 